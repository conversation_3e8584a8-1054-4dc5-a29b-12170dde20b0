# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build Azure mytaylormadehealthcheck

on:
  push:
    branches:
      - health

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@master

      - name: Set up Node.js version
        uses: actions/setup-node@v1
        with:
          node-version: '12.x'

      - name: Download env config file
        run: curl "${{ secrets.ENV_CONFIG_PATH }}" -o ./.env.prod

      - name: Download health config file
        run: curl "${{ secrets.HEALTH_CONFIG_PATH }}" -o ./src/config/health.ts

      - name: Download Apple Auth Key
        run: curl "${{ secrets.APPLE_AUTH_KEY_PATH }}" -o ./AuthKey_8D4KK36N9J.p8

      - name: Copy env files
        run: cp .env.prod .env

      - name: Install dependencies
        run: yarn

      - name: Build
        run: yarn build

      - name: Clean dependencies
        run: |
          rm -rf node_modules

      - name: Install prod dependencies
        run: yarn --prod

      - name: Update .env for health check
        run: sed -i 's/TAG=APIS/TAG=HEALTH_CHECK/g' .env

      - name: 'Deploy to Azure Web App'
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'app-prd-mytmhealth-app-001'
          slot-name: 'production'
          publish-profile: ${{ secrets.AZURE_HEALTH_APP_PUBLISH_PROFILE_PROD }}

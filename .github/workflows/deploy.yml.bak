name: Deployment CI

on:
  push:
    branches:
      - master

jobs:
  deploy:
    if: github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [12.x]

    steps:
      - uses: actions/checkout@v1

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}

      - uses: actions/cache@v2
        with:
          path: '**/**/node_modules'
          key: ${{ runner.os }}-modules-${{ hashFiles('**/**/yarn.lock') }}

      - name: Install dependencies
        run: yarn

      - name: Build alpha
        run: yarn build

      - name: Clean build for prepared to ship built files to remote server
        run: rm -rf node_modules .git .github

      - name: Ship built files to remote servers
        uses: appleboy/scp-action@master
        with:
          host: '*************'
          username: 'ubuntu'
          key: ${{ secrets.PRIVATE_KEY }}
          overwrite: 1
          source: './*'
          target: '/home/<USER>/mTMPOC/'

      - name: Install & Reload Processes
        uses: fifsky/ssh-action@master
        with:
          command: |
            cd /home/<USER>/mTMPOC
            yarn
            pm2 restart mTMPOC || pm2 start dist/main.js --name mTMPOC -i 1
          host: '*************'
          user: 'ubuntu'
          key: ${{ secrets.PRIVATE_KEY }}

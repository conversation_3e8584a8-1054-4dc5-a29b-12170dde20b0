# Docs for the Azure Web Apps Deploy action: https://github.com/Azure/webapps-deploy
# More GitHub Actions for Azure: https://github.com/Azure/actions

name: Build Azure mytaylormadestg

on:
  workflow_dispatch:
    push:
      branches:
        - staging

env:
  API_URL: https://mytaylormadestg.azurewebsites.net

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@master

      - name: Set up Node.js version
        uses: actions/setup-node@v1
        with:
          node-version: '12.x'

      - name: Copy env files
        run: cp .env.staging .env

      - name: Install dependencies
        run: yarn

      - name: Build
        run: yarn build

      - name: Clean dependencies
        run: |
          rm -rf node_modules

      - name: Install prod dependencies
        run: yarn --prod

      - name: 'Deploy to Azure Web App'
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'mytaylormadestg'
          slot-name: 'production'
          publish-profile: ${{ secrets.AZURE_WEB_APP_PUBLISH_PROFILE_STG }}

      - name: Update .env for cron jobs
        run: sed -i 's/CRON_JOB_HANDLERS=disabled/CRON_JOB_HANDLERS=enabled/g' .env

      - name: Update .env for health check
        run: sed -i 's/TAG=APIS/TAG=JOBS/g' .env

      - name: 'Deploy to Azure Job App'
        uses: azure/webapps-deploy@v2
        with:
          app-name: 'app-stg-mytmjobs-app-001'
          slot-name: 'production'
          publish-profile: ${{ secrets.AZURE_JOB_APP_PUBLISH_PROFILE_STG }}

      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Get full git history

      - name: Get Git Information
        id: git-info
        run: |
          echo "branch=$(git rev-parse --abbrev-ref HEAD)" >> $GITHUB_OUTPUT
          echo "commit_hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "author=$(git log -1 --pretty=format:'%an')" >> $GITHUB_OUTPUT
          echo "commit_message=$(git log -1 --pretty=%s)" >> $GITHUB_OUTPUT

      - name: Create Version via API
        run: |
          curl -X POST ${{ env.API_URL }}/v1/version \
          -H "Content-Type: application/json" \
          -d '{
            "branch": "${{ steps.git-info.outputs.branch }}",
            "commitHash": "${{ steps.git-info.outputs.commit_hash }}",
            "commitMessage": "${{ steps.git-info.outputs.commit_message }}",
            "author": "${{ steps.git-info.outputs.author }}"
          }'

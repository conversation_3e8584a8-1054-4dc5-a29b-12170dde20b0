{"name": "@mytmplus/backend", "version": "0.0.1", "description": "", "author": "<PERSON>", "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "node main.js", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "test": "jest", "nest:version": "nest -v", "migration": "typeorm migration:run", "test:watch": "jest --watch", "test:cov": "jest --coverage", "lint": "eslint \"src/**/*.ts\" --fix", "lint:check": "eslint \"src/**/*.ts\"", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json --runInBand"}, "dependencies": {"@aginix/nestjs-firebase-admin": "^2.1.1", "@azure/storage-blob": "^12.12.0", "@bull-board/express": "^3.9.0", "@google-cloud/storage": "^5.18.2", "@nestjs-modules/mailer": "^1.6.0", "@nestjs/bull": "0.6.1", "@nestjs/common": "8.0.6", "@nestjs/core": "8.0.6", "@nestjs/passport": "^9.0.0", "@nestjs/platform-express": "8.0.6", "@nestjs/schedule": "^1.0.1", "@nestjs/swagger": "^5.2.1", "@nestjs/typeorm": "^8.0.2", "@ntegral/nestjs-sentry": "^3.0.6", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^6.13.2", "@types/auth0": "^2.33.1", "@types/cache-manager": "^3.4.2", "@types/cron": "^1.7.2", "@types/ftp": "^0.3.32", "@types/jsonwebtoken": "^8.5.0", "@types/lodash": "^4.14.178", "@types/moment": "^2.13.0", "@types/ms": "^0.7.31", "@types/node-xlsx": "^0.15.2", "@types/unzipper": "^0.10.5", "@types/uuid": "^8.3.0", "agentkeepalive": "^4.5.0", "apollo-server-express": "2.19.0", "auth0": "^2.33.0", "aws-sdk": "^2.1361.0", "axios": "^0.21.1", "body-parser": "^1.19.0", "bull": "4.9.0", "cache-manager": "^3.4.4", "cache-manager-ioredis": "^2.1.0", "cache-manager-redis-store": "^2.0.0", "camelcase-keys": "^6.2.2", "class-transformer": "0.2.3", "class-validator": "0.11.0", "compression": "1.7.4", "connect-ensure-login": "^0.1.1", "cookie-session": "^2.0.0", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "delay": "^5.0.0", "dotenv": "10.0.0", "express-session": "^1.17.2", "fast-xml-parser": "^4.0.0", "firebase-admin": "^9.6.0", "ftp": "^0.3.10", "google-play-billing-validator": "^2.1.3", "google-spreadsheet": "^3.1.15", "handlebars": "^4.7.7", "helmet": "3.21.3", "in-app-purchase": "^1.11.4", "ioredis": "^4.28.2", "jimp": "^0.16.2", "jsonwebtoken": "^8.5.1", "jws": "^4.0.0", "klaviyo-api": "^10.0.1", "klaviyo-sdk": "^1.0.1", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "ms": "^2.1.3", "mssql": "^7.2.1", "nest-console": "^6.7.3", "nestjs-config": "^1.4.7", "node-klaviyo": "^1.1.1", "node-xlsx": "^0.16.1", "nodemailer": "^6.5.0", "nonce": "1.0.4", "object-hash": "^3.0.0", "passport": "^0.5.2", "passport-http": "^0.3.0", "passport-local": "^1.0.0", "path": "^0.12.7", "querystring": "^0.2.1", "reflect-metadata": "0.1.13", "request": "^2.88.2", "rimraf": "3.0.2", "rxjs": "7.3.0", "semver": "^7.6.3", "set-cookie-parser": "^2.4.8", "ssh2-sftp-client": "5.3.2", "statuspage.io": "^3.1.1", "swagger-ui-express": "^4.3.0", "twilio": "^3.80.0", "typeorm": "^0.2.37", "unzipper": "^0.10.11", "uuid": "^8.3.2", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "8.1.1", "@nestjs/schematics": "8.0.3", "@nestjs/testing": "8.0.6", "@trivago/prettier-plugin-sort-imports": "^4.0.0", "@types/bull": "3.15.4", "@types/dotenv": "8.2.0", "@types/express": "^4.17.13", "@types/jest": "27.0.1", "@types/multer": "^1.4.7", "@types/node": "14.17.14", "@types/supertest": "2.0.11", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "eslint": "^8.33.0", "eslint-config-prettier": "8.3.0", "eslint-plugin-import": "2.24.2", "eslint-plugin-prettier": "^4.2.1", "jest": "27.1.0", "prettier": "2.3.2", "supertest": "6.1.6", "ts-jest": "27.0.5", "ts-loader": "9.2.5", "ts-node": "10.2.1", "tsconfig-paths": "3.11.0", "typescript": "4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "./coverage", "testEnvironment": "node"}}
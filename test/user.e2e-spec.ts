import { BullModule } from '@nestjs/bull';
import { GraphQLModule } from '@nestjs/graphql';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from 'nestjs-config';
import path from 'path';
import request from 'supertest';
import { AuthModule } from '../src/auth/auth.module';

describe('Users (e2e)', () => {
  let app;
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AuthModule,
        ConfigModule.load(path.resolve(__dirname, 'config', '**/!(*.d).{ts,js}')),
        BullModule.forRoot({
          redis: {
            host: 'localhost',
            port: 6379,
          },
        }),
        GraphQLModule.forRoot({
          autoSchemaFile: 'schema.gql',
          installSubscriptionHandlers: true,
          context: ({ req }) => ({ req }),
          playground: true,
        }),
      ],
    }).compile();
    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  const credentials = {
    username: '<EMAIL>',
    password: '!somepassword123!',
  };

  const createUserQuery = `
    mutation{
      authUser(email: "${credentials.username}", password: "${credentials.password}"){
        token
      }
    }
  `;

  let token: string;
  let result;
  it('authUser', () => {
    return request(app.getHttpServer())
      .post('/graphql')
      .send({
        operationName: null,
        query: createUserQuery,
      })
      .expect(({ body }) => {
        const data = body.data.register;
        expect(data.token).not.toBeNull();
        result = data;
        token = data.token;
      })
      .expect(200);
  });
});

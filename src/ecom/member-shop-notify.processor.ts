import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { Job } from 'bull';
import { messaging } from 'firebase-admin';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { MemberShopOrderEntity } from './entities/member-shop.entity';

type MemberShopPushJob = Job<{
  userEmail: string;
  userFCMToken: string;
  orderId: string;
  title: string;
  message: string;
  userNotificationId: string;
  ctaLink?: string;
  status?: string;
}>;

export enum MemberShopNotifyProcessorQueueName {
  PUSH = 'member-shop-push',
}

@Processor('member-shop-notify')
export class MemberShopNotifyProcessor {
  private readonly logger = new Logger(MemberShopNotifyProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private firebaseMessaging: FirebaseMessagingService,
    private klaviyoService: KlaviyoService,
    @InjectRepository(MemberShopOrderEntity) private readonly memberShopRepo: Repository<MemberShopOrderEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}

  @Process(MemberShopNotifyProcessorQueueName.PUSH)
  push(job: MemberShopPushJob) {
    this.logger.log(`Member shop push orderId ${job.data.orderId}`);
    if (job.data.userFCMToken) {
      this.pushNotificationKlaviyo(job);
    }
  }

  async pushNotification(job: MemberShopPushJob) {
    try {
      const message: messaging.Message = {
        data: {
          orderId: job.data.orderId,
          userNotificationId: job.data.userNotificationId,
          trackingUrl: job.data?.ctaLink,
        },
        token: job.data.userFCMToken,
        notification: {
          title: job.data.title || 'TaylorMade',
          body: job.data.message,
        },
      };
      const push = await this.firebaseMessaging.send(message);
      console.log(push);
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async pushNotificationKlaviyo(job: MemberShopPushJob) {
    try {
      const payload = {
        title: job.data.title || 'TaylorMade',
        body: job.data.message,
        link: job.data?.ctaLink,
      };
      const push = await this.klaviyoService.track(
        job.data.userEmail,
        KlaviyoTrackEvents.ORDER_PUSH_NOTIFICATION,
        payload
      );
      console.log(push);
    } catch (e) {
      console.log(e);
      return null;
    }
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { EcomService } from './ecom.service';

export enum EComDeliveringProcessorQueueName {
  HANDLER = 'handle-delivering',
}

@Processor('ecom-delivering')
export class EComDeliveringProcessor {
  constructor(private readonly config: ConfigService, private readonly ecomService: EcomService) {}

  @Process({
    name: EComDeliveringProcessorQueueName.HANDLER,
    concurrency: 1,
  })
  async handle(job: Job): Promise<any> {
    return this.ecomService.handleMemberShopOrderDelivering(job.data.orderId);
  }
}

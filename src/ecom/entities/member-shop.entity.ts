import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

export enum MEMBER_SHOP_STATUS {
  ORDER_CONFIRMATION = 'ORDER_CONFIRMATION',
  SHIPPED_CONFIRMATION = 'SHIPPED_CONFIRMATION',
  DELIVERED_CONFIRMATION = 'DELIVERED_CONFIRMATION',
  CANCELLED = 'CANCELLED',
  ALERT = 'ALERT',
  UNDELIVERED = 'UNDELIVERED',
  CLOSED = 'CLOSED',
}
export const MemberShopPushMessage = {
  [MEMBER_SHOP_STATUS.CANCELLED]: null,
  [MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION]: {
    title: 'Your Order Has Shipped!',
    message: 'Tap to view your order and tracking information.',
  },
  [MEMBER_SHOP_STATUS.ORDER_CONFIRMATION]: {
    title: 'Thank You For Your Order!',
    message: "We've received your order and will notify you when your item(s) will ship.",
  },
  [MEMBER_SHOP_STATUS.DELIVERED_CONFIRMATION]: {
    title: 'Your Order Has Been Delivered!',
    message: 'Thank you for your order. Tap to view order details.',
  },
  [MEMBER_SHOP_STATUS.ALERT]: {
    title: 'Important Order Update!',
    message: 'Action required. Check your order status now.',
  },
  [MEMBER_SHOP_STATUS.UNDELIVERED]: {
    title: 'Delivery Update!',
    message: 'There may be a delay with your order. Check tracking for details.',
  },
};
@Entity('MemberShopOrder')
export class MemberShopOrderEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  orderId: string;

  @Column()
  cdmPrimaryEmail: string;

  @Column()
  description: string;

  @Column()
  status: string;

  @Column()
  isRegistered17Track: boolean;

  @Column()
  isTeamTM: boolean;

  @Column()
  isMyTM: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  lastChecked: Date;
}

@Entity('MemberShopOrderTrackingNumber')
export class MemberShopOrderTrackingNumberEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  orderId: string;

  @Column()
  trackingNumber: string;

  @Column()
  status: string;

  @Column()
  carrierKey: string;

  @Column()
  carrier: string;

  @CreateDateColumn()
  createdAt: Date;
}

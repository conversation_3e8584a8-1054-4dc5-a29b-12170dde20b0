import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { In, LessThan, MoreThanOrEqual, Repository } from 'typeorm';
import { KlaviyoService } from 'src/klaviyo/klaviyo.service';
import { SeventeentrackService } from 'src/shared/services/seventeentrack.service';
import { isCronJobHandlersEnabled, isProduction } from 'src/utils/cron';
import { EComAlertProcessorQueueName } from './ecom-alert.processor';
import { EComDeliveringProcessorQueueName } from './ecom-delivering.processor';
import { EComShipmentOverdueProcessorQueueName } from './ecom-shipment-overdue.processor';
import { EComShipmentProcessorQueueName } from './ecom-shipment.processor';
import { EcomService } from './ecom.service';
import {
  MEMBER_SHOP_STATUS,
  MemberShopOrderEntity,
  MemberShopOrderTrackingNumberEntity,
} from './entities/member-shop.entity';

@Injectable()
export class EcomCronService {
  constructor(
    @InjectRepository(MemberShopOrderEntity) private readonly memberShopRepo: Repository<MemberShopOrderEntity>,
    @InjectRepository(MemberShopOrderTrackingNumberEntity)
    private readonly memberShopTrackingNumberRepo: Repository<MemberShopOrderTrackingNumberEntity>,
    private readonly config: ConfigService,
    private readonly klaviyoService: KlaviyoService,
    private readonly seventeentrackService: SeventeentrackService,
    private readonly ecomService: EcomService,
    @InjectQueue('ecom-shipment') private ecomShipmentQueue: Queue,
    @InjectQueue('ecom-shipment-overdue') private ecomShipmentOverdueQueue: Queue,
    @InjectQueue('ecom-delivering') private ecomDeliveringQueue: Queue,
    @InjectQueue('ecom-alert') private ecomAlertQueue: Queue,
    @InjectQueue('ecom-cancelation') private ecomCancelationQueue: Queue
  ) {}

  getDateBefore20days() {
    const currentDate = moment();
    const newDate = currentDate.subtract(20, 'days');
    return newDate.format('YYYY-MM-DD');
  }

  @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  async handleCheckMemberShopCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    // get Now - 20days
    const dateQuery = this.getDateBefore20days();

    const orders = await this.memberShopRepo.find({
      where: { status: MEMBER_SHOP_STATUS.ORDER_CONFIRMATION, createdAt: MoreThanOrEqual(dateQuery), isMyTM: true },
      select: ['orderId'],
      order: { lastChecked: 'ASC', createdAt: 'ASC' },
      take: 400,
    });
    if (orders.length > 0) {
      for (const order of orders) {
        try {
          await this.ecomShipmentQueue.add(EComShipmentProcessorQueueName.HANDLER, { orderId: order.orderId });
        } catch (err) {
          console.log(`ERROR handleCheckMemberShopCron: ${err.message}`);
          console.log(err);
        }
      }
    }
  }

  @Cron(isProduction ? CronExpression.EVERY_3_HOURS : CronExpression.EVERY_10_MINUTES)
  async handleCheckMemberShopOverdueCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    // get Now - 20days
    const dateQuery = this.getDateBefore20days();

    const orders = await this.memberShopRepo.find({
      where: { status: MEMBER_SHOP_STATUS.ORDER_CONFIRMATION, createdAt: LessThan(dateQuery), isMyTM: true },
      select: ['orderId'],
      order: { lastChecked: 'ASC', createdAt: 'ASC' },
      take: 300,
    });
    if (orders.length > 0) {
      for (const order of orders) {
        try {
          await this.ecomShipmentOverdueQueue.add(EComShipmentOverdueProcessorQueueName.HANDLER, {
            orderId: order.orderId,
          });
        } catch (err) {
          console.log(`ERROR handleCheckMemberShopOverdueCron: ${err.message}`);
          console.log(err);
        }
      }
    }
  }

  @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_10_MINUTES)
  async handleCheckDeliveredMemberShopCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    const orders = await this.memberShopRepo.find({
      where: { status: MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION, isMyTM: true },
      select: ['orderId'],
      order: { lastChecked: 'ASC', updatedAt: 'ASC' },
      take: 300,
    });
    if (orders.length > 0) {
      for (const order of orders) {
        try {
          await this.ecomDeliveringQueue.add(EComDeliveringProcessorQueueName.HANDLER, { orderId: order.orderId });
        } catch (err) {
          console.log(`ERROR handleCheckDeliveredMemberShopCron: ${err.message}`);
          console.log(err);
        }
      }
    }
  }
  @Cron(isProduction ? CronExpression.EVERY_6_HOURS : CronExpression.EVERY_30_MINUTES)
  async handleCheckStatusDeliveredMemberShopCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    const orders = await this.memberShopRepo.find({
      where: {
        status: In([MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION, MEMBER_SHOP_STATUS.DELIVERED_CONFIRMATION]),
        createdAt: MoreThanOrEqual('2024-03-05'),
        isMyTM: true,
        isTeamTM: true,
      },
      select: ['orderId'],
      order: { updatedAt: 'ASC' },
      take: 200,
    });
    if (orders.length > 0) {
      const listOrderIds = orders.map((order) => order.orderId);
      console.log(`handleCheckStatusDeliveredMemberShopCron: ${listOrderIds}`);
      for (const order of orders) {
        try {
          await this.ecomAlertQueue.add(EComAlertProcessorQueueName.HANDLER, {
            orderId: order.orderId,
            isCheckStatusAlert: true,
          });
        } catch (err) {
          console.log(`ERROR handleCheckStatusDeliveredMemberShopCron: ${err.message}`);
          console.log(err);
        }
      }
    }
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { EcomService } from './ecom.service';

export enum EComShipmentOverdueProcessorQueueName {
  HANDLER = 'handle-shipment-overdue',
}

@Processor('ecom-shipment-overdue')
export class EComShipmentOverdueProcessor {
  constructor(private readonly config: ConfigService, private readonly ecomService: EcomService) {}

  @Process({
    name: EComShipmentOverdueProcessorQueueName.HANDLER,
    concurrency: 1,
  })
  async handle(job: Job): Promise<any> {
    return this.ecomService.handleMemberShopOrderShipmentOverdue(job.data.orderId, true);
  }
}

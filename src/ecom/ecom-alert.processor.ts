import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { EcomService } from './ecom.service';

export enum EComAlertProcessorQueueName {
  HANDLER = 'handle-alert',
}

@Processor('ecom-alert')
export class EComAlertProcessor {
  constructor(private readonly config: ConfigService, private readonly ecomService: EcomService) {}

  @Process({
    name: EComAlertProcessorQueueName.HANDLER,
    concurrency: 1,
  })
  async handle(job: Job): Promise<any> {
    return this.ecomService.handleMemberShopOrderStatusDelivered(job.data.orderId);
  }
}

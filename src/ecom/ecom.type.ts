import { Is<PERSON><PERSON>, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>String, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class BoughtDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsNotEmpty()
  @IsString()
  @IsEmail()
  @MaxLength(255)
  cdmPrimaryEmail: string;

  @IsOptional()
  country: string;
}

export enum EcomNotificationType {
  BOUGHT = 'BOUGHT',
  TTB = 'TTB',
}

export enum LATEST_STATUS {
  CANCELLED = 'Cancelled',
}

export const PUSH_NOTIFY_ORDER_TEAM_TM = true;

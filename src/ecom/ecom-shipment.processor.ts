import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { EcomService } from './ecom.service';

export enum EComShipmentProcessorQueueName {
  HANDLER = 'handle-shipment',
}

@Processor('ecom-shipment')
export class EComShipmentProcessor {
  constructor(private readonly config: ConfigService, private readonly ecomService: EcomService) {}

  @Process({
    name: EComShipmentProcessorQueueName.HANDLER,
    concurrency: 1,
  })
  async handle(job: Job): Promise<any> {
    return this.ecomService.handleMemberShopOrderShipment(job.data.orderId);
  }
}

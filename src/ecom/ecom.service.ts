import { InjectQueue } from '@nestjs/bull';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Queue } from 'bull';
import camelcaseKeys from 'camelcase-keys';
import _, { result } from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import querystring from 'querystring';
import { In, LessThan, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserEntity } from 'src/auth/entities/user.entity';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { NotificationService } from 'src/notification/notification.service';
import { SeventeentrackService, TRACKING_CARRIERS } from 'src/shared/services/seventeentrack.service';
import { TTBPushEntity } from 'src/ttb/entities/ttb-push.entity';
import { TTBEntity } from 'src/ttb/entities/ttb.entity';
import { TTBService } from 'src/ttb/ttb.service';
import { isCronJobHandlersEnabled, isProduction } from 'src/utils/cron';
import { sleeps } from 'src/utils/datetime';
import { isCanadaCountry } from 'src/utils/transform';
import { LoggingService } from '../logging/logging.service';
import { BoughtDto, EcomNotificationType, LATEST_STATUS, PUSH_NOTIFY_ORDER_TEAM_TM } from './ecom.type';
import { EcomNotificationEntity } from './entities/ecom-notification.entity';
import {
  MEMBER_SHOP_STATUS,
  MemberShopOrderEntity,
  MemberShopOrderTrackingNumberEntity,
  MemberShopPushMessage,
} from './entities/member-shop.entity';
import { MemberShopNotifyProcessorQueueName } from './member-shop-notify.processor';

@Injectable()
export class EcomService {
  private readonly logger = new Logger(EcomService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectQueue('member-shop-notify') private memberShopNotifyQueue: Queue,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(EcomNotificationEntity)
    private readonly ecomNotificationRepo: Repository<EcomNotificationEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(MemberShopOrderEntity)
    private readonly memberShopRepo: Repository<MemberShopOrderEntity>,
    private readonly klaviyoService: KlaviyoService,
    @Inject(forwardRef(() => TTBService)) private ttbService: TTBService,
    private readonly seventeentrackService: SeventeentrackService,
    private readonly loggingService: LoggingService,
    @Inject(forwardRef(() => NotificationService)) private notificationService: NotificationService,
    @InjectRepository(MemberShopOrderTrackingNumberEntity)
    private readonly memberShopTrackingNumberRepo: Repository<MemberShopOrderTrackingNumberEntity>
  ) {
    this.config = config;
  }

  async getMemberShopOrder(orderId: string) {
    return await this.memberShopRepo.find({ orderId });
  }

  async getRequestHeaderConfigs(country?: string) {
    try {
      let response: any = {};
      if (isCanadaCountry(country)) {
        response = await axios.post(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomMyTMCASite'
          )}/dw/shop/v20_10/customers/auth?client_id=${this.config.get('app.ecomClientId')}`,
          {
            type: 'guest',
          }
        );
      } else {
        response = await axios.post(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomMyTMSite'
          )}/dw/shop/v20_10/customers/auth?client_id=${this.config.get('app.ecomClientId')}`,
          {
            type: 'guest',
          }
        );
      }

      if (response.data) {
        const jwtToken = response.headers.authorization;
        return {
          headers: { Authorization: jwtToken },
        };
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getRequestHeaderConfigsForBMAuth(country?: string) {
    try {
      let urlAuth = `${this.config.get('app.ecomEndpoint')}/dw/oauth2/access_token?client_id=${this.config.get(
        'app.ecomClientId'
      )}`;
      if (isCanadaCountry(country)) {
        urlAuth = `${this.config.get('app.ecomCANEndpoint')}/dw/oauth2/access_token?client_id=${this.config.get(
          'app.ecomClientId'
        )}`;
      }
      const response = await axios.post(
        urlAuth,
        querystring.stringify({
          grant_type: 'urn:demandware:params:oauth:grant-type:client-id:dwsid:dwsecuretoken',
        }),
        {
          auth: {
            username: `${this.config.get('app.ecomBMUser')}:${this.config.get('app.ecomBMPassword')}:${this.config.get(
              'app.ecomBMClientSecret'
            )}`,
            password: '',
          },
        }
      );
      if (response.data) {
        const jwtToken = response.data.access_token;
        return {
          headers: { Authorization: `Bearer ${jwtToken}` },
        };
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async updateCustomer(payload: any, country?: string) {
    try {
      let urlCreateOrUpdate = `${this.config.get('app.ecomEndpoint')}/on/demandware.store/Sites-${this.config.get(
        'app.ecomMyTMSite'
      )}-Site/en_US/CDM-CreateOrUpdateCustomer`;
      if (isCanadaCountry(country)) {
        urlCreateOrUpdate = `${this.config.get('app.ecomCANEndpoint')}/on/demandware.store/Sites-${this.config.get(
          'app.ecomMyTMCASite'
        )}-Site/en_US/CDM-CreateOrUpdateCustomer`;
      }
      console.log(`urlCreateOrUpdate: ${urlCreateOrUpdate}`);

      const response = await axios.post(urlCreateOrUpdate, payload, {
        auth: {
          username: this.config.get('app.ecomBMUser'),
          password: this.config.get('app.ecomBMPassword'),
        },
      });
      console.log(`Response eCom CreateOrUpdateCustomer`);
      console.log(response.data);

      return response.data;
    } catch (e) {
      console.log(`ERROR eCom CreateOrUpdateCustomer`);
      console.log(e);
      return {};
    }
  }

  async getProduct(productId: string, country?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(country);
      let response: any = {};
      if (isCanadaCountry(country)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomMyTMCASite'
          )}/dw/shop/v20_10/products/${productId}?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomMyTMSite'
          )}/dw/shop/v20_10/products/${productId}?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return false;
    } catch (e) {
      return null;
    }
  }

  async getProductMulti(productIds: string, country?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(country);
      let response: any = {};
      if (isCanadaCountry(country)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomMyTMCASite'
          )}/dw/shop/v20_10/products/(${productIds})?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomMyTMSite'
          )}/dw/shop/v20_10/products/(${productIds})?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return false;
    } catch (e) {
      return null;
    }
  }

  async getTeamTMProductMulti(productIds: string, country?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(country);
      let response: any = {};
      if (isCanadaCountry(country)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/products/(${productIds})?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMSite'
          )}/dw/shop/v20_10/products/(${productIds})?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return false;
    } catch (e) {
      return null;
    }
  }

  async getOrder(orderId: string, country?: string, isTeamTM?: boolean) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigsForBMAuth(country);
      let response: any = {};
      let site;
      if (isCanadaCountry(country)) {
        site = isTeamTM ? this.config.get('app.ecomTeamTMCASite') : this.config.get('app.ecomMyTMCASite');
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${site}/dw/shop/v20_10/orders/${
            orderId.split('-')[0]
          }?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      } else {
        site = isTeamTM ? this.config.get('app.ecomTeamTMSite') : this.config.get('app.ecomMyTMSite');
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${site}/dw/shop/v20_10/orders/${
            orderId.split('-')[0]
          }?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      }
      // const response = await axios.get(
      //   `${this.config.get('app.ecomEndpoint')}/s/${this.config.get('app.ecomMyTMSite')}/dw/shop/v20_10/orders/${
      //     orderId.split('-')[0]
      //   }?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
      //   headerConfigs
      // );
      if (response.data) {
        return camelcaseKeys({ ...response.data, order_no: orderId }, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  async handleMemberShopOrderShipment(orderId: number) {
    try {
      let isClosed = false;
      const order = await this.memberShopRepo.findOne({ where: { orderId } });
      if (!order || order.status !== MEMBER_SHOP_STATUS.ORDER_CONFIRMATION) {
        return;
      }
      // update lastChecked
      await this.memberShopRepo.update({ id: order.id }, { lastChecked: new Date(), updatedAt: order?.updatedAt });

      const orderInfoEBS = await this.getOrderInfoEBS(order.orderId);
      if (orderInfoEBS?.data?.orderInfo?.status === 'Unknown') {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CANCELLED });
        return;
      } else if (orderInfoEBS?.data?.orderInfo?.status?.toUpperCase() === MEMBER_SHOP_STATUS.CLOSED) {
        isClosed = true;
      }
      const orderLines = orderInfoEBS?.data?.orderInfo?.orderLines;
      if (!orderLines || orderLines?.length === 0) {
        return;
      }
      let cancelledCount = 0;
      for (const orderLine of orderLines) {
        const latestStatus = orderLine?.latestStatus;
        if (latestStatus === LATEST_STATUS.CANCELLED) {
          cancelledCount += 1;
        }
      }
      if (cancelledCount > 0) {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CANCELLED });
        if (!order.isTeamTM) {
          const orderData = JSON.parse(order.description);
          const payload = this.getPayloadShopOrder(orderData);
          await this.klaviyoService.track(order.cdmPrimaryEmail, KlaviyoTrackEvents.MYTM_MEMBER_SHOP_CANCELLED, {
            event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_CANCELLED,
            ...payload,
          });
        }

        return;
      }
      const trackingNumber = orderLines.find((orderLine) => !!orderLine.trackingNumber)?.trackingNumber;
      const carrierName = orderLines.find((orderLine) => !!orderLine.carrierName)?.carrierName;
      if (!trackingNumber) {
        if (isClosed) {
          await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CLOSED });
        }
        return;
      }

      let shippedCount = 0;
      for (const orderLine of orderLines) {
        if (orderLine?.statusCol?.find((col) => col.statusValue?.includes('We have shipped') && col.date)) {
          shippedCount += 1;
        }
      }
      if (shippedCount === 0) {
        return;
      }

      const carrierKey = await this.seventeentrackService.registerTrackingNumber(trackingNumber, carrierName, true);
      if (!carrierKey) {
        return;
      }
      const trackingUrl = this.getOrderShipmentTrackingUrl(trackingNumber, carrierName, carrierKey);
      if (!order.isTeamTM) {
        const orderData = JSON.parse(order.description);
        const payload = this.getPayloadShopOrder(orderData);
        await this.klaviyoService.track(order.cdmPrimaryEmail, KlaviyoTrackEvents.MYTM_MEMBER_SHOP_SHIP_CONFIRMATION, {
          event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_SHIP_CONFIRMATION,
          ...payload,
          tracking_url: trackingUrl,
          tracking_number: trackingNumber,
        });
      } else {
        await this.ecomPushNotification(
          { orderId: orderId },
          order.cdmPrimaryEmail,
          MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION,
          { trackingUrl }
        );
      }

      await this.memberShopRepo.update(
        { id: order.id },
        {
          status: MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION,
          isRegistered17Track: true,
        }
      );
      return orderId;
    } catch (err) {
      this.logger.error('Error ECOM SHIPPING JOB', JSON.stringify(err));
      await this.loggingService.save({
        event: 'ERR_ECOM_SHIPPING_JOB',
        data: {
          error: err,
          orderId: orderId,
        },
      });
      return;
    }
  }

  async handleMemberShopOrderShipmentOverdue(orderId: number, isOverdue = false) {
    try {
      let isClosed = false;
      const order = await this.memberShopRepo.findOne({ where: { orderId } });
      if (!order || order.status !== MEMBER_SHOP_STATUS.ORDER_CONFIRMATION) {
        return;
      }
      // update lastChecked
      await this.memberShopRepo.update({ id: order.id }, { lastChecked: new Date(), updatedAt: order?.updatedAt });

      const orderInfoEBS = await this.getOrderInfoEBS(order.orderId);
      if (orderInfoEBS?.data?.orderInfo?.status === 'Unknown') {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CANCELLED });
        return;
      } else if (orderInfoEBS?.data?.orderInfo?.status?.toUpperCase() === MEMBER_SHOP_STATUS.CLOSED) {
        isClosed = true;
      }
      const orderLines = orderInfoEBS?.data?.orderInfo?.orderLines;
      if (!orderLines || orderLines?.length === 0) {
        return;
      }
      let cancelledCount = 0;
      for (const orderLine of orderLines) {
        const latestStatus = orderLine?.latestStatus;
        if (latestStatus === LATEST_STATUS.CANCELLED) {
          cancelledCount += 1;
        }
      }
      if (cancelledCount > 0) {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CANCELLED });
        if (!order.isTeamTM) {
          const orderData = JSON.parse(order.description);
          const payload = this.getPayloadShopOrder(orderData);
          await this.klaviyoService.track(order.cdmPrimaryEmail, KlaviyoTrackEvents.MYTM_MEMBER_SHOP_CANCELLED, {
            event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_CANCELLED,
            ...payload,
          });
        }

        return;
      }
      const trackingNumber = orderLines.find((orderLine) => !!orderLine.trackingNumber)?.trackingNumber;
      const carrierName = orderLines.find((orderLine) => !!orderLine.carrierName)?.carrierName;
      if (!trackingNumber) {
        if (isClosed) {
          await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CLOSED });
        }
        return;
      }

      let shippedCount = 0;
      for (const orderLine of orderLines) {
        if (orderLine?.statusCol?.find((col) => col.statusValue?.includes('We have shipped') && col.date)) {
          shippedCount += 1;
        }
      }
      if (shippedCount === 0) {
        return;
      }

      const carrierKey = await this.seventeentrackService.registerTrackingNumber(trackingNumber, carrierName, true);
      if (!carrierKey) {
        return;
      }
      const trackingUrl = this.getOrderShipmentTrackingUrl(trackingNumber, carrierName, carrierKey);
      if (!order.isTeamTM) {
        const orderData = JSON.parse(order.description);
        const payload = this.getPayloadShopOrder(orderData);
        await this.klaviyoService.track(order.cdmPrimaryEmail, KlaviyoTrackEvents.MYTM_MEMBER_SHOP_SHIP_CONFIRMATION, {
          event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_SHIP_CONFIRMATION,
          ...payload,
          tracking_url: trackingUrl,
          tracking_number: trackingNumber,
        });
      } else {
        await this.ecomPushNotification(
          { orderId: orderId },
          order.cdmPrimaryEmail,
          MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION,
          { trackingUrl }
        );
      }

      await this.memberShopRepo.update(
        { id: order.id },
        {
          status: MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION,
          isRegistered17Track: true,
        }
      );
      return orderId;
    } catch (err) {
      let event = 'ERR_ECOM_SHIPPING_JOB';
      if (isOverdue) event = 'ERR_ECOM_SHIPPING_OVERDUE_JOB';
      this.logger.error('Error ECOM SHIPPING JOB', JSON.stringify(err));
      await this.loggingService.save({
        event: event,
        data: {
          error: err,
          orderId: orderId,
        },
      });
      return;
    }
  }

  async handleMemberShopOrderDelivering(orderId: string) {
    try {
      let isClosed = false;
      const order = await this.memberShopRepo.findOne({ where: { orderId } });
      if (!order || order.status !== MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION) {
        return;
      }
      await this.memberShopRepo.update({ id: order.id }, { lastChecked: new Date(), updatedAt: order?.updatedAt });
      const orderInfoEBS = await this.getOrderInfoEBS(order.orderId);
      const orderLines = orderInfoEBS?.data?.orderInfo?.orderLines;
      if (orderInfoEBS?.data?.orderInfo?.status === 'Unknown') {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CANCELLED });
        return;
      } else if (orderInfoEBS?.data?.orderInfo?.status?.toUpperCase() === MEMBER_SHOP_STATUS.CLOSED) {
        isClosed = true;
      }

      if (!orderLines || orderLines?.length === 0) {
        return;
      }
      let deliveredCount = 0;
      for (const orderLine of orderLines) {
        if (!orderLine.trackingNumber) {
          continue;
        }
        const is17TrackOrderDelivered = await this.is17TrackOrderDelivered(
          orderLine.trackingNumber,
          orderLine.carrierName
        );
        if (is17TrackOrderDelivered) {
          deliveredCount += 1;
        }
      }

      const trackingNumber = orderLines.find((orderLine) => !!orderLine.trackingNumber)?.trackingNumber;
      const carrierName = orderLines.find((orderLine) => !!orderLine.carrierName)?.carrierName;
      if (!trackingNumber) {
        if (isClosed) {
          await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CLOSED });
        }
        return;
      }
      if (!carrierName) {
        return;
      }
      if (deliveredCount === 0) {
        const orderUpdatedAt = order.updatedAt;
        const isOldOrder = moment().diff(moment(orderUpdatedAt), 'days') > 14;
        if (isOldOrder) {
          await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CLOSED });
        }
        return;
      }
      const carrier = await this.seventeentrackService.getMatchCarrier(carrierName);
      if (!carrier) {
        return;
      }
      const trackingUrl = this.getOrderShipmentTrackingUrl(trackingNumber, carrierName, carrier?.key);
      if (!order.isTeamTM) {
        const orderData = JSON.parse(order.description);
        const payload = this.getPayloadShopOrder(orderData);
        await this.klaviyoService.track(
          order.cdmPrimaryEmail,
          KlaviyoTrackEvents.MYTM_MEMBER_SHOP_DELIVERY_CONFIRMATION,
          {
            event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_DELIVERY_CONFIRMATION,
            ...payload,
            tracking_url: trackingUrl,
            tracking_number: trackingNumber,
          }
        );
      } else {
        await this.ecomPushNotification(
          { orderId: orderId },
          order.cdmPrimaryEmail,
          MEMBER_SHOP_STATUS.DELIVERED_CONFIRMATION,
          { trackingUrl }
        );
      }

      await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.DELIVERED_CONFIRMATION });
      return orderId;
    } catch (err) {
      this.logger.error('Error ECOM DELIVERING JOB:', JSON.stringify(err));
      await this.loggingService.save({
        event: 'ERR_ECOM_DELIVERING_JOB',
        data: {
          error: err,
          orderId: orderId,
        },
      });
      return;
    }
  }
  async handleMemberShopOrderStatusDelivered(orderId: string) {
    try {
      const order = await this.memberShopRepo.findOne({ where: { orderId } });
      if (
        !order ||
        ![MEMBER_SHOP_STATUS.DELIVERED_CONFIRMATION, MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION].includes(
          order.status as any
        )
      ) {
        return;
      }
      await this.memberShopRepo.update({ id: order.id }, { updatedAt: new Date() });
      const orderInfoEBS = await this.getOrderInfoEBS(order.orderId);
      const orderLines = orderInfoEBS?.data?.orderInfo?.orderLines;
      if (!orderLines || orderLines?.length === 0) {
        return;
      }
      let deliveredCount = 0;
      for (const orderLine of orderLines) {
        const trackingNumber = orderLine.trackingNumber;
        const carrierName = orderLine.carrierName;
        if (!trackingNumber || !carrierName) {
          continue;
        }

        const statusOrder17Track = await this.getStatusOrderFrom17Track(trackingNumber, carrierName);
        if (statusOrder17Track != null && order.isTeamTM) {
          const orderItemId = `${orderId}-${orderLine?.sku?.trim()?.toUpperCase()}`;
          // for testing
          if (!isProduction) {
            if ('N7702701' == orderLine?.sku?.trim()?.toUpperCase()) {
              statusOrder17Track.isAlert = true;
            }
            if ('N7600501' == orderLine?.sku?.trim()?.toUpperCase()) {
              statusOrder17Track.isUndelivered = true;
            }
            console.log(`orderItemId: ${orderItemId} ${trackingNumber}: ${JSON.stringify(statusOrder17Track)}`);
          }

          const carrier = await this.seventeentrackService.getMatchCarrier(carrierName);
          if (!carrier) {
            continue;
          }
          const trackingUrl = this.getOrderShipmentTrackingUrl(trackingNumber, carrierName, carrier?.key);

          if (statusOrder17Track?.isAlert) {
            await this.ecomPushNotification({ orderId: orderItemId }, order.cdmPrimaryEmail, MEMBER_SHOP_STATUS.ALERT, {
              trackingUrl,
            });
          }
          if (statusOrder17Track?.isUndelivered) {
            await this.ecomPushNotification(
              { orderId: orderItemId },
              order.cdmPrimaryEmail,
              MEMBER_SHOP_STATUS.UNDELIVERED,
              {
                trackingUrl,
              }
            );
          }
          if (statusOrder17Track?.isDelivered) {
            deliveredCount += 1;
          }
        }
      }

      if (orderInfoEBS?.data?.orderInfo?.status?.toUpperCase() === MEMBER_SHOP_STATUS.CLOSED && deliveredCount) {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CLOSED });
      }
      return orderId;
    } catch (err) {
      this.logger.error('Error ECOM DELIVERING ALERT JOB:', JSON.stringify(err));
      await this.loggingService.save({
        event: 'ERR_ECOM_DELIVERING_ALERT_JOB',
        data: {
          error: err,
          orderId: orderId,
        },
      });
      return;
    }
  }

  async handleMemberShopOrderCancelation(orderId: string) {
    const order = await this.memberShopRepo.findOne({ where: { orderId } });
    if (!order || order.status !== MEMBER_SHOP_STATUS.ORDER_CONFIRMATION) {
      return;
    }
    const orderInfoEBS = await this.getOrderInfoEBS(order.orderId);
    const orderLines = orderInfoEBS?.data?.orderInfo?.orderLines;
    if (orderLines && orderLines.length) {
      let cancelledCount = 0;
      for (const orderLine of orderLines) {
        const latestStatus = orderLine?.latestStatus;
        if (latestStatus === LATEST_STATUS.CANCELLED) {
          cancelledCount += 1;
        }
      }
      if (cancelledCount > 0) {
        await this.memberShopRepo.update({ id: order.id }, { status: MEMBER_SHOP_STATUS.CANCELLED });
        if (!order.isTeamTM) {
          const orderData = JSON.parse(order.description);
          const payload = this.getPayloadShopOrder(orderData);
          await this.klaviyoService.track(order.cdmPrimaryEmail, KlaviyoTrackEvents.MYTM_MEMBER_SHOP_CANCELLED, {
            event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_CANCELLED,
            ...payload,
          });
        }
      }
    }
  }
  async ecomPushNotification(order, email, status: MEMBER_SHOP_STATUS, extraData?: any) {
    const { trackingUrl } = extraData;
    if (!PUSH_NOTIFY_ORDER_TEAM_TM) {
      return;
    }
    const user = await this.userRepo.findOne({ email });
    if (!user) {
      return;
    }
    const statusPush = status as any;
    const existedPushQueue = await this.ttbPushRepo.findOne({
      where: {
        userId: user.id,
        status: statusPush,
        orderId: order.orderId,
      },
    });
    if (existedPushQueue) {
      return;
    }
    const delayed = await this.ttbService.getDelayedTimeForPushAndTriggerNotifications();
    const ttbPush = new TTBPushEntity();
    ttbPush.id = v4();
    ttbPush.userId = user.id;
    ttbPush.orderId = order.orderId;
    ttbPush.status = status as any;
    ttbPush.pushedAt = new Date();
    ttbPush.createdBy = user.id;
    await this.ttbPushRepo.save(ttbPush);
    const variables = {
      message: MemberShopPushMessage[status].message,
      title: MemberShopPushMessage[status].title,
    };
    const ctaLink = trackingUrl || `${this.config.get('app.deeplinkMyTMEndpoint')}/mytaylormadeplus/shop`;
    const userNotification = await this.notificationService.saveNotificationFromOtherService(
      user.id,
      ctaLink,
      variables
    );
    const userNotificationId = userNotification ? userNotification.id : '';
    if (userNotificationId) {
      await this.memberShopNotifyQueue.add(
        MemberShopNotifyProcessorQueueName.PUSH,
        {
          userEmail: user.email,
          userFCMToken: user.fcmToken,
          orderId: order.orderId,
          status: status,
          userNotificationId,
          ctaLink,
          ...variables,
        },
        { delay: delayed }
      );
    }

    return order;
  }
  async is17TrackOrderDelivered(trackingNumber: string, carrier: string) {
    try {
      const matchCarrier = await this.seventeentrackService.getMatchCarrier(carrier);
      if (!matchCarrier) {
        return false;
      }
      let trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
        trackingNumber,
        matchCarrier.key,
        false
      );
      if (!trackingNumberInfo) {
        const shipmentSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
          trackingNumber,
          carrier,
          true
        );
        if (shipmentSeventeentrackCarrierKey) {
          trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
            trackingNumber,
            matchCarrier.key,
            false
          );
        }
        if (!trackingNumberInfo) {
          return false;
        }
      }
      return this.seventeentrackService.isTrackingNumberDelivered(trackingNumberInfo);
    } catch (err) {
      await this.loggingService.save({
        event: 'ERR_ECOM_17_TRACK_ORDER',
        data: {
          error: err,
          trackingNumber: trackingNumber,
        },
      });
      return false;
    }
  }
  async getStatusOrderFrom17Track(trackingNumber: string, carrier: string) {
    try {
      const matchCarrier = await this.seventeentrackService.getMatchCarrier(carrier);
      if (!matchCarrier) {
        return null;
      }
      let trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
        trackingNumber,
        matchCarrier.key,
        false
      );
      if (!trackingNumberInfo) {
        const shipmentSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
          trackingNumber,
          carrier,
          true
        );
        if (shipmentSeventeentrackCarrierKey) {
          trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
            trackingNumber,
            matchCarrier.key,
            false
          );
        }
        if (!trackingNumberInfo) {
          return null;
        }
      }
      const eventTrack = trackingNumberInfo?.track?.e;
      const PackageStatus = {
        Undelivered: 35,
        Alert: 50,
        Delivered: 40,
      };
      return {
        isAlert: eventTrack === PackageStatus.Alert,
        isUndelivered: eventTrack == PackageStatus.Undelivered,
        isDelivered: eventTrack == PackageStatus.Delivered,
      };
    } catch (err) {
      await this.loggingService.save({
        event: 'ERR_ECOM_getStatusOrderFrom17Track',
        data: {
          error: err,
          trackingNumber: trackingNumber,
        },
      });
      return;
    }
  }
  async get17TrackOrderInfo(trackingNumber: string, carrier: string) {
    const matchCarrier = await this.seventeentrackService.getMatchCarrier(carrier);
    if (!matchCarrier) {
      return false;
    }
    let trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
      trackingNumber,
      matchCarrier.key,
      false
    );
    if (!trackingNumberInfo) {
      const shipmentSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
        trackingNumber,
        carrier,
        true
      );
      if (shipmentSeventeentrackCarrierKey) {
        trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
          trackingNumber,
          matchCarrier.key,
          false
        );
      }
      if (!trackingNumberInfo) {
        return false;
      }
    }
    return trackingNumberInfo;
  }

  async notifyBought(data: BoughtDto) {
    const existingOrder = await this.memberShopRepo.findOne({ where: { orderId: data.orderId } });
    if (existingOrder) {
      return {
        success: true,
      };
    }
    const notification = new EcomNotificationEntity();
    notification.id = v4();
    notification.eventName = EcomNotificationType.BOUGHT;
    notification.orderId = data.orderId;
    await this.ecomNotificationRepo.save(notification);
    let orderData = null;
    const orderMyTMCatalog = await this.getOrder(data.orderId, data.country, false);
    let isTeamTM = false;
    let isMyTM = false;
    if (orderMyTMCatalog) {
      orderData = orderMyTMCatalog;
      orderData.productItems = await this.getProductItemsInfo(orderData?.productItems);
    } else {
      isTeamTM = true;
    }

    const user = await this.userRepo.findOne({
      where: {
        email: data.cdmPrimaryEmail,
      },
    });
    if (user) isMyTM = true;

    const memberShop = new MemberShopOrderEntity();
    memberShop.id = v4();
    memberShop.orderId = data.orderId;
    memberShop.status = MEMBER_SHOP_STATUS.ORDER_CONFIRMATION;
    memberShop.description = isTeamTM ? '' : JSON.stringify(orderData);
    memberShop.cdmPrimaryEmail = data.cdmPrimaryEmail;
    memberShop.isTeamTM = isTeamTM;
    memberShop.isMyTM = isMyTM;

    await this.memberShopRepo.save(memberShop);
    if (!isTeamTM) {
      const payload = this.getPayloadShopOrder(orderData);
      await this.klaviyoService.track(data.cdmPrimaryEmail, KlaviyoTrackEvents.MYTM_MEMBER_SHOP_ORDER_CONFIRMATION, {
        event: KlaviyoTrackEvents.MYTM_MEMBER_SHOP_ORDER_CONFIRMATION,
        ...payload,
      });
    }

    return {
      success: true,
    };
  }

  async getProductItemsInfo(productItems: any) {
    if (!productItems) {
      return undefined;
    }
    return await Promise.all(
      productItems.map(async (productItem: any) => {
        const productInfo = await this.getProduct(productItem?.productId);
        if (!productInfo) {
          return productItem;
        }
        const imageGroups = productInfo?.imageGroups;
        if (imageGroups && imageGroups.length > 0) {
          const images = imageGroups[0]?.images;
          if (images && images.length > 0) {
            return {
              ...productItem,
              productImageUrl: images[0].link || images[0].disBaseLink,
            };
          }
        }
        return productItem;
      })
    );
  }

  getAttribute(data: string) {
    try {
      const exclude = ['Shaft Only?', 'Macro Code', 'TP/NON TP', 'Source Application', 'TTB', 'Operating Unit'];
      const attrs = JSON.parse(data);
      return attrs?.selectedNodes
        ?.filter((item) => !exclude.includes(item.name))
        .map((attr) => ({
          name: attr?.name,
          value: attr?.option?.name,
        }));
    } catch (error) {
      return null;
    }
  }

  getImageUrl(item: any) {
    try {
      if (item.productImageUrl) {
        return item.productImageUrl;
      }
      const attrs = JSON.parse(item?.cTmCustomconfiguratorNodes || '{}');
      const img = attrs?.selectedNodes?.find((item) => item.name == 'Model');
      return (
        img?.option?.imageURL ||
        img?.option?.imageUrl ||
        img?.option?.imageUrlLarge ||
        img?.option?.info?.imageUrl ||
        img?.option?.info?.imageUrlLarge ||
        ''
      );
    } catch (error) {
      return '';
    }
  }

  getPayloadShopOrder(orderData: any) {
    return {
      order_id: orderData?.orderNo,
      order_datetime: orderData?.creationDate,
      customer_number: orderData?.customerInfo?.customerId,
      customer_name: orderData?.customerName,
      billing_address: {
        first_name: orderData?.billingAddress?.firstName,
        last_name: orderData?.billingAddress?.lastName,
        address1: orderData?.billingAddress?.address1,
        address2: orderData?.billingAddress?.address2,
        city: orderData?.billingAddress?.city,
        postal_code: orderData?.billingAddress?.postalCode,
        state_code: orderData?.billingAddress?.stateCode,
        country_code: orderData?.billingAddress?.countryCode,
        phone: orderData?.billingAddress?.phone,
      },
      shipping_address: {
        first_name: orderData?.shipments[0]?.shippingAddress?.firstName,
        last_name: orderData?.shipments[0]?.shippingAddress?.lastName,
        address1: orderData?.shipments[0]?.shippingAddress?.address1,
        address2: orderData?.shipments[0]?.shippingAddress?.address2,
        city: orderData?.shipments[0]?.shippingAddress?.city,
        state_code: orderData?.shipments[0]?.shippingAddress?.stateCode,
        postal_code: orderData?.shipments[0]?.shippingAddress?.postalCode,
        country_code: orderData?.shipments[0]?.shippingAddress?.countryCode,
        phone: orderData?.shipments[0]?.shippingAddress?.phone,
      },
      shipping_method: orderData?.shipments[0]?.shippingMethod?.name,
      payments: orderData?.paymentInstruments?.map((payment) => ({
        payment_id: payment?.paymentMethodId,
        payment_method: 'Credit Card',
        payment_total: payment?.amount,
        payment_card: payment?.paymentCard?.cardType,
        payment_card_last_four: payment?.paymentCard?.numberLastDigits,
      })),
      products: orderData?.productItems?.map((item) => ({
        name: item?.productName,
        image_url: this.getImageUrl(item),
        article_no: '',
        price: item?.price,
        base_price: item?.basePrice,
        gross_price: item?.basePrice,
        net_price: item?.price,
        tax: item?.tax,
        quantity: item?.quantity,
        attributes: this.getAttribute(item?.cTmCustomconfiguratorNodes || '{}'),
      })),
      item_count: orderData?.productItems ? orderData?.productItems?.length : '',
      subtotal: orderData?.productSubTotal,
      shipping_cost: orderData?.shippingTotal,
      ship_discount: '',
      merchandise_tax: orderData?.merchandizeTotalTax,
      sales_tax: orderData?.taxTotal,
      ship_tax: orderData?.shippingTotalTax,
      total_tax: orderData?.taxTotal,
      order_total: orderData?.orderTotal,
    };
  }

  async notifyTTB(data: BoughtDto, extraData: any, shouldRetry = false) {
    const notification = new EcomNotificationEntity();
    notification.id = v4();
    notification.eventName = EcomNotificationType.TTB;
    notification.orderId = data.orderId;
    notification.shouldRetry = shouldRetry;
    extraData['country'] = data?.country;
    notification.extraData = JSON.stringify(extraData);
    await this.ecomNotificationRepo.save(notification);
    return {
      success: true,
    };
  }

  async retryFailedTTBOrders() {
    this.logger.log('START retryFailedTTBOrders....');
    const ecomNotifications: any = await this.ecomNotificationRepo.find({ where: { shouldRetry: true } });

    if (ecomNotifications.length === 0) {
      return true;
    }
    for (const notification of ecomNotifications) {
      try {
        const ttbOrder = await this.ttbService.getOrder(notification.orderId);
        console.log(`Check Order ${notification.orderId}`);

        if (ttbOrder) {
          console.log(`Has order ${notification.orderId}`);
          await this.updateTTBProductInfo(ttbOrder);
          console.log(`UpdateTTBProductInfo done ${notification.orderId}`);
          continue;
        }
        const ecomOrder = await this.getOrder(notification.orderId, notification?.extraData?.country);

        if (!ecomOrder) {
          console.log(`NotFound ecomOrder ${notification.orderId} next...`);
          continue;
        }
        const orderCustomerEmail = ecomOrder.customerInfo?.email;
        if (!orderCustomerEmail) {
          console.log(`NotFound orderCustomerEmail ${notification.orderId} next...`);
          continue;
        }
        const user = await this.userRepo.findOne({ where: { email: orderCustomerEmail } });
        if (!user) {
          console.log(`NotFound user ${notification.orderId} next...`);
          continue;
        }
        console.log('postTry......');
        ecomOrder['country'] = notification?.extraData?.country || 'USA';
        const ttb = await this.ttbService.postTry(user.id, ecomOrder);
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_ORDER_CONFIRMATION,
          user.email,
          ecomOrder,
          ttb?.productInfo
        );
        await this.ecomNotificationRepo.update(
          { orderId: notification.orderId },
          {
            extraData: JSON.stringify({
              success: true,
              response: ttb,
            }),
            shouldRetry: false,
          }
        );
      } catch (error) {
        console.log(error.message);
      }
    }
    return true;
  }
  async updateTTBProductInfo(ttbOrder: TTBEntity) {
    try {
      if (ttbOrder.productInfo) return;
      const ecomOrder = await this.getOrder(ttbOrder.orderId, ttbOrder?.country);
      if (ecomOrder) {
        this.logger.log(`START update product info of order: ${ttbOrder.orderId}`);
        const productInfo = await this.ttbService.getTTBProductFromOrder(ecomOrder);
        await this.ttbService.updateProductInfo(ttbOrder.orderId, JSON.stringify(productInfo));
        await this.updateEcomNotificationSuccess(ttbOrder.orderId);
        this.logger.log(`DONE update product info of order: ${ttbOrder.orderId}`);
      }
    } catch (e) {
      this.logger.error(e.message);
      return null;
    }
  }
  async updateEcomNotificationSuccess(orderId: any) {
    const ttbOrderUpdated = await this.ttbService.getOrder(orderId);
    const ttbInfo = this.ttbService.transformTTBOrder(ttbOrderUpdated);
    ttbInfo.shipmentTrackingUrl = null;
    ttbInfo.GVCProductReturningTrackingUrl = null;
    ttbInfo.specsMismatchedReturnTrackingUrl = null;
    ttbInfo.specsMismatchedReturnWrongComponentsTrackingUrl = null;
    ttbInfo.GVCReceivedSpecsMismatchedInfo = null;
    ttbInfo.endDate = null;
    ttbInfo.updatedAt = null;
    ttbInfo.cardWillBeChangedAt = null;

    await this.ecomNotificationRepo.update(
      { orderId },
      {
        extraData: JSON.stringify({
          success: true,
          response: ttbInfo,
        }),
        shouldRetry: false,
      }
    );
  }

  async getProductAll(countryCode?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(countryCode);
      let response: any = {};
      if (isCanadaCountry(countryCode)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomMyTMCASite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomCategoryId')}`
          )}&expand=images,availability`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomMyTMSite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomCategoryId')}`
          )}&expand=images,availability`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  async getTeamTMProductDetail(productRefId: string, countryCode?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(countryCode);
      let response: any = {};
      if (isCanadaCountry(countryCode)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/products/${productRefId}?client_id=${this.config.get(
            'app.ecomClientId'
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMSite'
          )}/dw/shop/v20_10/products/${productRefId}?client_id=${this.config.get(
            'app.ecomClientId'
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  async getTeamTMProductRewards(countryCode?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(countryCode);
      let response: any = {};
      if (isCanadaCountry(countryCode)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomTeamTMRewardsCategoryId')}`
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMSite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomTeamTMRewardsCategoryId')}`
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      }

      if (response.data) {
        await this.getImagesForProducts(response, countryCode);
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  private async getImagesForProducts(response: any, countryCode: string) {
    if (response?.data?.hits) {
      const lstProductNoImage = response.data.hits.filter((hit) => {
        return hit?.image == null;
      });
      let productRefIds;
      const listProductId = response.data.hits.map((hit) => hit.product_id);
      const lstProductDetails = await this.getListProductEcomDetail(listProductId, countryCode);

      for (const hit of response?.data?.hits) {
        hit['cTmCollaborationLabel'] =
          lstProductDetails?.data?.find((p) => p.id == hit.product_id)?.cTmCollaborationLabel ?? '';
      }
      const arrProductIdsNoImage = [];
      if (lstProductNoImage && lstProductNoImage.length > 0) {
        lstProductNoImage.map((product) => {
          return (arrProductIdsNoImage[product.represented_product?.id] = product.product_id);
        });
        productRefIds = lstProductNoImage.map((product) => product?.represented_product?.id);

        const lstProductRefDetail = await this.getListProductEcomDetail(productRefIds, countryCode);

        if (!lstProductRefDetail || lstProductRefDetail?.data.length == 0) {
          return;
        }
        for (const productRefId of productRefIds) {
          const productRefDetail = lstProductRefDetail.data.find((product) => product.id == productRefId);
          if (productRefDetail && productRefDetail?.imageGroups && productRefDetail?.imageGroups[0]?.images) {
            const productMissImage = lstProductNoImage.find(
              (product) => product.product_id == arrProductIdsNoImage[productRefId]
            );
            productMissImage['image'] = productRefDetail?.imageGroups[0]?.images[0];
          }
        }
      }
    }
  }

  private async getListProductEcomDetail(productIds: any, countryCode: string) {
    if (productIds.length > 20) {
      const splitProductIds = _.chunk(productIds, 20);
      const lstProductEcomDetail = { data: [] };
      for (const ids of splitProductIds) {
        const productDetails = await this.getTeamTMProductMulti(ids.join(','), countryCode);
        if (productDetails) {
          lstProductEcomDetail.data.push(...productDetails.data);
        }
      }
      return lstProductEcomDetail;
    } else {
      return await this.getTeamTMProductMulti(productIds.join(','), countryCode);
    }
  }

  async getProductAllWithPrices(countryCode?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(countryCode);
      let response: any = {};
      if (isCanadaCountry(countryCode)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomTeamTMShopCategoryId')}`
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMSite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomTeamTMShopCategoryId')}`
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  async getHomeProductWithPrices(countryCode?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigs(countryCode);
      let response: any = {};
      if (isCanadaCountry(countryCode)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomTeamTMShopCategoryId')}`
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMSite'
          )}/dw/shop/v20_10/product_search?client_id=${this.config.get(
            'app.ecomClientId'
          )}&refine_1=${encodeURIComponent(
            `cgid=${this.config.get('app.ecomTeamTMShopCategoryId')}`
          )}&expand=images,availability,prices`,
          headerConfigs
        );
      }

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }
  async getOrdersCustomerByEmail(customerEmail: string, countryCode?: string, orderIds?: string) {
    try {
      const payloadSearch = {
        query: {
          text_query: {
            fields: ['customer_email'],
            search_phrase: customerEmail,
          },
        },
        select: '(**)',
        sorts: [
          {
            field: 'order_no',
            sort_order: 'desc',
          },
        ],
      };
      const headerConfigs = await this.getRequestHeaderConfigsForBMAuth(countryCode);
      headerConfigs['headers']['Content-Type'] = 'application/json';
      let response: any = {};
      if (isCanadaCountry(countryCode)) {
        response = await axios.post(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/order_search?client_id=${this.config.get('app.ecomClientId')}`,
          JSON.stringify(payloadSearch),
          headerConfigs
        );
      } else {
        response = await axios.post(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMSite'
          )}/dw/shop/v20_10/order_search?client_id=${this.config.get('app.ecomClientId')}`,
          JSON.stringify(payloadSearch),
          headerConfigs
        );
      }

      if (response.data && response.data?.hits) {
        await this.filterOrderStatusOpen(response);
        this.replaceOrderDevToProductOnlyDev(orderIds, response);
        await Promise.all([this.processFilterOrder(response, countryCode), this.processArrivalDateItems(response)]);
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e.message);
      return null;
    }
  }
  private replaceOrderDevToProductOnlyDev(orderIds: string, response: any) {
    if (!isProduction && orderIds) {
      try {
        const lstOrderIds = orderIds.split(',');
        for (orderIds of lstOrderIds) {
          const [productOrderId, devOrderId, productProductItemId, devProductItemId] = orderIds.split('-');
          const hitDev = response.data?.hits?.find((hit) => hit?.data?.order_no == devOrderId);
          if (hitDev) {
            hitDev.data.order_no = productOrderId;
            if (productProductItemId) {
              const productDetail = hitDev.data.product_items.find((p) => p.product_id == devProductItemId);
              productDetail.product_id = productProductItemId;
            }
          }
        }
      } catch (error) {}
    }
  }

  private async processArrivalDateItems(response: any) {
    const PackageStatus = {
      0: 'Not found',
      10: 'In transit',
      20: 'Expired',
      30: 'Pick up',
      35: 'Undelivered',
      40: 'Delivered',
      50: 'Alert',
    };
    const statusPriorities = [
      { key: 'Delivered', value: 'Delivered' },
      { key: 'NotFound', value: 'Not found' },
      { key: 'InTransit', value: 'In transit' },
      { key: 'InfoReceived', value: 'In transit' },
      { key: 'Pick up', value: 'Pick up' },
      { key: 'Expired', value: 'Expired' },
      { key: 'DeliveryFailure', value: 'Undelivered' },
      { key: 'Exception', value: 'Alert' },
    ];

    // Collect all tracking tasks that need to be processed
    const trackingTasks = [];

    // Gather all tracking information that needs processing
    response.data.hits.forEach((hit) => {
      const hitProductItem = hit.data.product_items;
      const shipments = hit.data.shipments;

      hitProductItem.forEach((item) => {
        const shipItem = shipments.find((s) => s.shipment_id === item.shipment_id);
        if (shipItem?.shipment_id && shipItem?.c_carrier && shipItem?.tracking_number) {
          if (item.shipment_id === 'me') {
            trackingTasks.push({
              trackingNumber: shipItem.tracking_number,
              item,
              orderId: hit.data.order_no,
            });
          } else {
            // Set default values for tracking-related fields
            item['packageStatus'] = 'Unknown';
            item['deliveryDate'] = null;
            item['hidden'] = false;
            trackingTasks.push({
              trackingNumber: shipItem.tracking_number,
              item,
              orderId: hit.data.order_no,
            });
          }
        }
      });
    });

    // Process tracking in batches to avoid API overload
    const batchSize = 5; // Number of concurrent requests
    const batches = [];

    for (let i = 0; i < trackingTasks.length; i += batchSize) {
      batches.push(trackingTasks.slice(i, i + batchSize));
    }

    // Process each batch sequentially, with concurrent requests within each batch
    for (const batch of batches) {
      await Promise.all(
        batch.map(async ({ trackingNumber, item, orderId }) => {
          try {
            // Get tracking information with retry mechanism
            const trackingNumberInfoV2 = await this.getTrackingV2WithRetry(trackingNumber);

            if (trackingNumberInfoV2) {
              // Process estimated delivery date
              const estDeliveryDateTo =
                trackingNumberInfoV2?.track_info?.time_metrics?.estimated_delivery_date?.to ??
                trackingNumberInfoV2?.track_info?.time_metrics?.estimated_delivery_date?.from;

              if (estDeliveryDateTo) {
                item['scheduledArrivalDate'] = moment(estDeliveryDateTo).format('DD-MMM-YYYY HH:mm:ss').toUpperCase();
              }

              // Process package status
              const latestStatus = result(trackingNumberInfoV2, 'track_info.latest_status', null);
              const latestEvent = result(trackingNumberInfoV2, 'track_info.latest_event', null);
              const trackStatus = latestStatus?.status;

              if (trackStatus) {
                // Map tracking status to display status based on priorities
                const matchedStatus = statusPriorities.find((status) => trackStatus.includes(status.key));
                item['packageStatus'] = matchedStatus ? matchedStatus.value : trackStatus;
              }

              // Process actual delivery date and hidden status for delivered items
              if (trackStatus?.includes('Delivered')) {
                const timeIso = `${latestEvent?.time_raw?.date} ${latestEvent?.time_raw?.time}`;
                item['deliveryDate'] = moment(timeIso).format('DD-MMM-YYYY HH:mm:ss').toUpperCase();

                try {
                  // Hide items delivered more than 3 days ago
                  item['hidden'] = moment().diff(moment(timeIso), 'days') > 3;
                } catch (error) {
                  console.log(`Error calculating hidden status: ${error.message}`);
                  item['hidden'] = false;
                }
              }
            }
          } catch (err) {
            console.log(`Error processing tracking for order ${orderId}, tracking ${trackingNumber}: ${err.message}`);
          }
        })
      );

      // Add small delay between batches to prevent API overload
      if (batches.length > 1) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }
  }

  private async getTrackingV2WithRetry(trackingNumber: string, retries = 5, delay = 300) {
    for (let i = 0; i < retries; i++) {
      try {
        // console.log(`Get tracking v2 attempt ${i + 1} for ${trackingNumber}`);
        const trackingInfo = await this.seventeentrackService.getTrackingNumberInfoV2(trackingNumber);
        if (trackingInfo) {
          return trackingInfo;
        }
      } catch (e) {
        console.log(`Tracking v2 API error (${i + 1}/${retries}: ${trackingNumber}): ${e.message}`);
        if (i < retries - 1) {
          // Use exponential backoff to increase delay between retries
          await new Promise((r) => setTimeout(r, delay * Math.pow(2, i)));
        }
      }
    }
    return null;
  }

  private async processFilterOrder(response: any, countryCode) {
    if (!response?.data?.hits) {
      return;
    }
    const totalProductIds = this.getProductOrderIds(response);
    const splitProduct = _.chunk(totalProductIds, 20);
    await Promise.all(
      splitProduct.map(async (productIds) => {
        // console.log(`Get List Product: ${productIds}`);
        const lstProductDetail = await this.getTeamTMProductMulti(productIds.join(','), countryCode);
        this.mapImageByProductId(response, lstProductDetail, productIds);
      })
    );
  }

  private getProductOrderIds(response: any) {
    const lstProductIdSet = new Set();
    for (const order of response.data.hits) {
      const productItems = order.data.product_items;
      productItems?.map((product) => {
        if (product.product_id == 'TM_DRIVER_MODEL') {
          lstProductIdSet.add(product.c_tm_customconfigurator_dwmacro);
        } else {
          lstProductIdSet.add(product.product_id);
        }
      });
    }
    const productIds = Array.from(lstProductIdSet);
    return productIds;
  }

  private mapImageByProductId(response: any, lstProductDetail: any, lstProductsId: any) {
    for (const order of response.data.hits) {
      const productItems = order.data.product_items;

      for (const item of productItems) {
        const productDetail = lstProductDetail?.data?.find((p) => {
          if (item.product_id == 'TM_DRIVER_MODEL') {
            return p.id == item.c_tm_customconfigurator_dwmacro;
          } else {
            return p.id == item.product_id;
          }
        });
        if (productDetail && productDetail?.imageGroups) {
          item['image'] = productDetail?.imageGroups[0]?.images[0];
        } else {
          if (lstProductsId.includes(item.product_id)) {
            console.log(`ProductID: ${item.product_id} missing image`);
          }
        }
      }
    }
  }

  private async filterOrderStatusOpen(response: any) {
    if (response?.data?.hits) {
      // Filter order have status: new, open, created
      const STATUS_OPEN = ['new', 'open', 'created'];
      const lstOrderId = [];
      response.data.hits = response?.data?.hits.filter((hit) => {
        const orderCreated = moment(hit.data.creation_date, 'YYYY-MM-DD');
        const currentDate = moment();
        const diffMonths = currentDate.diff(orderCreated, 'months');
        const isValid = STATUS_OPEN.includes(hit.data.status?.toLowerCase()) && diffMonths >= 0 && diffMonths <= 6;
        if (isValid) {
          lstOrderId.push(hit.data.order_no);
        }
        return isValid;
      });
      const fiveDayAfterDelivery = moment().subtract(5, 'days').format('YYYY-MM-DD');
      // Select order delivery after 5 days from now.
      const lstOrderDelivery = await this.memberShopRepo.find({
        where: {
          orderId: In(lstOrderId),
          updatedAt: LessThan(fiveDayAfterDelivery),
          status: MEMBER_SHOP_STATUS.CLOSED,
        },
        select: ['orderId'],
      });
      if (lstOrderDelivery.length > 0) {
        const idsDelivery = lstOrderDelivery.map((order) => order.orderId);
        response.data.hits = response?.data?.hits.filter((hit) => {
          return !idsDelivery.includes(hit.data.order_no);
        });
      }
    }
  }

  async getTeamTMSiteOrder(orderId: string, country?: string) {
    try {
      const headerConfigs = await this.getRequestHeaderConfigsForBMAuth(country);
      let response: any = {};
      if (isCanadaCountry(country)) {
        response = await axios.get(
          `${this.config.get('app.ecomCANEndpoint')}/s/${this.config.get(
            'app.ecomTeamTMCASite'
          )}/dw/shop/v20_10/orders/${orderId.split('-')[0]}?client_id=${this.config.get(
            'app.ecomClientId'
          )}&expand=images`,
          headerConfigs
        );
      } else {
        response = await axios.get(
          `${this.config.get('app.ecomEndpoint')}/s/${this.config.get('app.ecomTeamTMSite')}/dw/shop/v20_10/orders/${
            orderId.split('-')[0]
          }?client_id=${this.config.get('app.ecomClientId')}&expand=images`,
          headerConfigs
        );
      }
      if (response.data) {
        return camelcaseKeys({ ...response.data, order_no: orderId }, { deep: true });
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  async getOrderInfoEBS(orderId: string) {
    return await axios.get(
      `${this.config.get(
        'app.clubTrackerEndpoint'
      )}/api/v1/Main/GetOrderStatus?RequestSource=B2C&OperatingUnit=US&OracleInstance=${this.config.get(
        'app.ecomClubTrackerInstance'
      )}&PONumber=${orderId}`
    );
  }

  getOrderShipmentTrackingUrl(trackingNumber: string, carrier: string, carrierKey?: string) {
    let shipmentTrackingUrl = null;
    if (carrier && trackingNumber) {
      shipmentTrackingUrl = carrierKey
        ? `https://t.17track.net/en#nums=${trackingNumber}&fc=${carrierKey}`
        : `https://www.fedex.com/fedextrack/?trknbr=${trackingNumber}`;
      if (carrier === TRACKING_CARRIERS.FEDEX) {
        shipmentTrackingUrl = `https://www.fedex.com/fedextrack/?trknbr=${trackingNumber}`;
      }
      if (carrier === TRACKING_CARRIERS.UPS) {
        shipmentTrackingUrl = `https://www.ups.com/mobile/track?trackingNumber=${trackingNumber}`;
      }
    }
    return shipmentTrackingUrl;
  }

  async mapOrderToUser() {
    try {
      const memberShops = await this.memberShopRepo
        .createQueryBuilder('ms')
        .innerJoin('Users', 'user', 'user.email = ms.cdmPrimaryEmail')
        .where('ms.status IN (:...statuses)', {
          statuses: [
            MEMBER_SHOP_STATUS.ORDER_CONFIRMATION,
            MEMBER_SHOP_STATUS.SHIPPED_CONFIRMATION,
            MEMBER_SHOP_STATUS.DELIVERED_CONFIRMATION,
          ],
        })
        .andWhere('ms.createdAt  >= DATEADD(month, -2, GETDATE())')
        .andWhere('ms.isMyTM is null or ms.isMyTM = 0')
        .select('ms.id')
        .orderBy('ms.createdAt', 'DESC')
        .getRawMany();
      if (!memberShops || !memberShops.length) return null;
      const ids = _.map(memberShops, 'ms_id');
      const chunkData = _.chunk(ids, 100);
      let chunk = 0;
      for (const chunkRow of chunkData) {
        chunk = chunk + 1;
        await this.memberShopRepo.update({ id: In(chunkRow) }, { isMyTM: true });
      }
      return { countMP: memberShops.length, isRunChunk: chunk, chunkData: chunkData.length };
    } catch (err) {
      return err;
    }
  }

  @Cron(isProduction ? CronExpression.EVERY_30_MINUTES : CronExpression.EVERY_5_MINUTES)
  async retryFailedTTB() {
    if (isCronJobHandlersEnabled(this.config)) {
      // this.retryFailedTTBOrders();
    }
  }
}

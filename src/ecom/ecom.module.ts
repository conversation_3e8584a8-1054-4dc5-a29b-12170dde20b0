import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { TTBPushEntity } from 'src/ttb/entities/ttb-push.entity';
import { TTBEntity } from 'src/ttb/entities/ttb.entity';
import { TTBService } from 'src/ttb/ttb.service';
import { SYSTEM_TAG } from 'src/utils/constants';
import { EComAlertProcessor } from './ecom-alert.processor';
import { EComCancelationProcessor } from './ecom-cancelation.processor';
import { EComDeliveringProcessor } from './ecom-delivering.processor';
import { EComShipmentOverdueProcessor } from './ecom-shipment-overdue.processor';
import { EComShipmentProcessor } from './ecom-shipment.processor';
import { EcomController } from './ecom.controller';
import { EcomCronService } from './ecom.cron.service';
import { EcomService } from './ecom.service';
import { MemberShopOrderTrackingNumberEntity } from './entities/member-shop.entity';
import { MemberShopNotifyProcessor } from './member-shop-notify.processor';

let processors = [];

if (process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [
    ...processors,
    EComShipmentProcessor,
    EComShipmentOverdueProcessor,
    EComDeliveringProcessor,
    EComCancelationProcessor,
    MemberShopNotifyProcessor,
    EComAlertProcessor,
  ];
}

@Module({
  imports: [
    SharedModule,
    TypeOrmModule.forFeature([TTBEntity, TTBPushEntity, MemberShopOrderTrackingNumberEntity]),
    BullModule.registerQueue({
      name: 'ecom-cancelation',
      defaultJobOptions: {
        removeOnComplete: false,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
  ],
  controllers: [EcomController],
  providers: [EcomService, TTBService, EcomCronService, ...processors],
  exports: [EcomCronService],
})
export class EcomModule {}

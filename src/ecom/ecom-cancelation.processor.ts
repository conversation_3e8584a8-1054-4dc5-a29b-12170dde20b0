import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { EcomService } from './ecom.service';

export enum EComCancelationProcessorQueueName {
  HANDLER = 'handle-cancelation',
}

@Processor('ecom-cancelation')
export class EComCancelationProcessor {
  constructor(private readonly config: ConfigService, private readonly ecomService: EcomService) {}

  @Process({
    name: EComCancelationProcessorQueueName.HANDLER,
    concurrency: 1,
  })
  async handle(job: Job): Promise<any> {
    return this.ecomService.handleMemberShopOrderCancelation(job.data.orderId);
  }
}

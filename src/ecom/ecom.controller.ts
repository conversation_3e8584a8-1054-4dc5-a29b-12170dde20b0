import { BadRequestException, Body, Controller, Get, Logger, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { TTBService } from '../ttb/ttb.service';
import { ERROR_CODES } from '../utils/errors';
import { EcomService } from './ecom.service';
import { BoughtDto } from './ecom.type';

@Controller('ecom')
export class EcomController {
  private readonly logger = new Logger(EcomController.name);
  constructor(
    private ttbService: TTBService,
    private ecomService: EcomService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}

  @Post('notify/bought')
  @AccessedClients(CLIENTS.ECOM)
  @UseGuards(ClientGuard)
  async notifyBought(@Body() payload: BoughtDto) {
    return this.ecomService.notifyBought(payload);
  }

  @Get('member-shop/order/:id')
  async getMemberShopOrder(@Param() params) {
    return this.ecomService.getMemberShopOrder(params.id);
  }

  @Post('notify/ttb')
  @AccessedClients(CLIENTS.ECOM)
  @UseGuards(ClientGuard)
  async notifyTTB(@Body() payload: BoughtDto) {
    const user = await this.userRepo.findOne({ email: payload.cdmPrimaryEmail });
    if (!user) {
      await this.ecomService.notifyTTB(payload, {
        error: {
          internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
          errorMessage: 'User not found!',
        },
      });
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
        errorMessage: 'User not found!',
      });
    }
    this.logger.log(`Ecom call order: ${payload.orderId}`);
    const existed = await this.ttbService.checkTTBExists(user.id, payload.orderId);
    if (existed) {
      await this.ecomService.notifyTTB(payload, {
        error: {
          internalErrorCode: ERROR_CODES.TTB_ORDER_ALREADY_EXIST,
          errorMessage: 'Your try then buy is already exist!',
        },
      });
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_ALREADY_EXIST,
        errorMessage: 'Your try then buy is already exist!',
      });
    }

    payload.country = payload.country || 'USA';

    let order = await this.ecomService.getOrder(payload.orderId, payload.country);
    if (!order) {
      await this.ecomService.notifyTTB(
        payload,
        {
          error: {
            internalErrorCode: ERROR_CODES.TTB_ORDER_ID_IS_NOT_VALID,
            errorMessage: 'Order id is not valid!',
          },
        },
        true
      );
      // Init order with empty productItems, shipments
      order = { orderNo: payload.orderId, productItems: null, shipments: [] };
    }
    order['country'] = payload.country;
    const ttb = await this.ttbService.postTry(user.id, order);
    await this.ttbService.trackingEventKlaviyoTTB(
      KlaviyoTrackEvents.TTB_ORDER_CONFIRMATION,
      user.email,
      order,
      ttb?.productInfo
    );
    if (order.productItems) {
      await this.ecomService.notifyTTB(payload, {
        success: true,
        response: ttb,
      });
    }

    return ttb;
  }

  @Get('product/all')
  @AccessedClients(CLIENTS.ECOM)
  @UseGuards(ClientGuard)
  async getProductAll(@Query('country') country?: string) {
    return this.ecomService.getProductAll(country);
  }

  @Post('retry-failed-ttb-orders')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.CUSTOMER_SERVICE)
  @UseGuards(AuthGuard)
  async getOrderAdmin() {
    const success = await this.ecomService.retryFailedTTBOrders();
    return {
      success,
    };
  }

  @Get('products')
  @UseGuards(AuthGuard)
  async getProducts(@Query('productIds') productIds?: string, @Query('country') country?: string) {
    const countryUser = country || 'USA';
    return this.ecomService.getProductMulti(productIds, countryUser);
  }
  @Get('team-tm-products')
  @UseGuards(AuthGuard)
  async getTeamTMProducts(@Query('productIds') productIds?: string, @Query('country') country?: string) {
    const countryUser = country || 'USA';
    return this.ecomService.getTeamTMProductMulti(productIds, countryUser);
  }

  @Get('order/:orderId')
  @UseGuards(AuthGuard)
  async getOrderInfoEBS(@Param('orderId') orderId?: string) {
    try {
      const orderInfoEBS = await this.ecomService.getOrderInfoEBS(orderId);
      return orderInfoEBS.data;
    } catch (error) {
      return { msg: error.message };
    }
  }

  @Post('member-shop-user')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.CUSTOMER_SERVICE)
  @UseGuards(AuthGuard)
  async mapOrderToUser() {
    const success = await this.ecomService.mapOrderToUser();
    return {
      success,
    };
  }
}

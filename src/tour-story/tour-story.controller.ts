import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { ActiveTourStoryDto, SortTourStoriesDto } from './dto/active-tour-story.dto';
import { CreateTourStoryDto } from './dto/create-tour-story.dto';
import { CreateUserTourStoryDto } from './dto/create-user-tour-story.dto';
import { DeleteMultipleTourStoryDto } from './dto/delete-multiple-tour-story.dto';
import { TourStoryService } from './tour-story.service';

@Controller('tour-story')
export class TourStoryController {
  constructor(private readonly tourStoryService: TourStoryService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('get-list') // paginate
  async getList(@Request() req: any, @Query('country') country?: string) {
    return this.tourStoryService.getListPaginate(req.query, country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createTourStoryDto: CreateTourStoryDto, @Request() req: BaseRequest) {
    return this.tourStoryService.create(createTourStoryDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch('/active/:id')
  patchActiveTourStory(@Param('id') id: string, @Body() activeDTO: ActiveTourStoryDto, @Req() req: any) {
    return this.tourStoryService.patchActiveTourStory(id, activeDTO, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  async postUpdateSortTourStory(@Req() request: BaseRequest, @Body() payload: SortTourStoriesDto) {
    return await this.tourStoryService.postSortTourStory(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('/delete/multiple')
  deleteMultipleTourStory(@Body() deleteMultipleTourStoryDto: DeleteMultipleTourStoryDto, @Req() req: any) {
    return this.tourStoryService.deleteMultipleTourStory(deleteMultipleTourStoryDto, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  deleteTourStory(@Param('id') id: string, @Req() req: any) {
    return this.tourStoryService.deleteTourStory(id, req.user);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('/user')
  createUserTourStory(@Body() createUserTourStoryDto: CreateUserTourStoryDto, @Request() req: BaseRequest) {
    return this.tourStoryService.createUserTourStory(createUserTourStoryDto, req.user.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('/clear-user-history')
  clearUserHistory(@Request() req: BaseRequest) {
    return this.tourStoryService.deleteUserHistory(req?.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('/user-history')
  getUserHistory(@Request() req: BaseRequest) {
    return this.tourStoryService.getUserHistory(req.user.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('/app')
  getTourStoryApp(@Request() req: any, @Query('country') country?: string, @Query('limit') limit?: string) {
    return this.tourStoryService.getTourStoryApp(req.user.uid, country, limit);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get(':id')
  getTourStory(@Param('id') tourStoryId: string) {
    return this.tourStoryService.findOneTourStory(tourStoryId);
  }
}

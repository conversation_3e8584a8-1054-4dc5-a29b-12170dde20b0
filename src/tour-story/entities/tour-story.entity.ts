import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('TourStory')
export class TourStoryEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  type: string;

  @Column()
  url: string;

  @Column()
  options: string;

  @Column()
  countries: string;

  @Column()
  totalView: number;

  @Column()
  disabled: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column()
  sortOrder: number;
}

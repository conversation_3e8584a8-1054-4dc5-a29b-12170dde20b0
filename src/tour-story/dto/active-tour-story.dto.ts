import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class ActiveTourStoryDto {
  @IsNotEmpty()
  @IsBoolean()
  disabled: boolean;
}

export class SortTourStoriesDto {
  @IsNotEmpty()
  @IsArray()
  sortOrderList: TourStory[];

  @IsOptional()
  country: string;
}

export class TourStory {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @IsOptional()
  country: string;
}

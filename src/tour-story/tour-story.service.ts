import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { isEmpty, map, result } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, Repository, getManager } from 'typeorm';
import { v4 } from 'uuid';
import { isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { TilesWidget } from '../tiles-widget/entities/tiles-widget.entity';
import { ERROR_CODES } from '../utils/errors';
import { ActiveTourStoryDto, SortTourStoriesDto } from './dto/active-tour-story.dto';
import { CreateTourStoryDto } from './dto/create-tour-story.dto';
import { CreateUserTourStoryDto } from './dto/create-user-tour-story.dto';
import { DeleteMultipleTourStoryDto } from './dto/delete-multiple-tour-story.dto';
import { TourStoryUserEntity } from './entities/tour-story-user.entity';
import { TourStoryEntity } from './entities/tour-story.entity';

@Injectable()
export class TourStoryService {
  private readonly logger = new Logger(TourStoryService.name);
  @InjectRepository(TourStoryEntity) private readonly tourStoryRepo: Repository<TourStoryEntity>;
  @InjectRepository(TourStoryUserEntity) private readonly tourStoryUserRepo: Repository<TourStoryUserEntity>;
  constructor(private readonly config: ConfigService, private readonly apiVersionsService: ApiVersionsService) {}

  async getListPaginate({ ...options }: any, countryCode?: string) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const skip = (nPage - 1) * nTake;
    const conditionCountry = this.getConditionCountryQuery(countryCode);
    const [tourStories, total] = await this.tourStoryRepo.findAndCount({
      where: conditionCountry,
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
      take: nTake,
      skip,
    });

    if (total === 0) {
      return {
        total: 0,
        tourStories: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      tourStories,
    };
  }

  async create(createTourStoryDto: CreateTourStoryDto, createdBy: string) {
    const { options } = createTourStoryDto;
    const data = {
      id: v4(),
      ...createTourStoryDto,
      createdBy,
    };
    if (options) {
      data.options = JSON.stringify(options);
    }
    try {
      const tourStory = this.tourStoryRepo.save(plainToClass(TourStoryEntity, data));
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_TOUR_STORIES, 'USA', createdBy);
      return tourStory;
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async patchActiveTourStory(id, activeDTO: ActiveTourStoryDto, user) {
    const tourStory = await this.tourStoryRepo.findOne(id);
    if (!tourStory) {
      throw new NotFoundException({ errorMessage: `TourStory NotFound!` });
    }
    try {
      await this.tourStoryRepo.update(id, {
        ...activeDTO,
        updatedBy: user?.uid,
        updatedAt: new Date(),
      });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_TOUR_STORIES, 'USA', user?.uid);
      return this.tourStoryRepo.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async deleteMultipleTourStory(deleteMultipleTourStoryDto: DeleteMultipleTourStoryDto, user) {
    const { ids } = deleteMultipleTourStoryDto;
    if (isEmpty(ids)) {
      throw new BadRequestException({ errorMessage: 'No IDs provided' });
    }

    const deletedIds = [];
    const notFoundIds = [];

    const deletePromises = ids.map(async (id) => {
      const tourStory = await this.tourStoryRepo.findOne(id);
      if (!tourStory) {
        notFoundIds.push(id);
        return;
      }
      await this.tourStoryRepo.update(id, {
        deletedBy: user?.uid,
        deletedAt: new Date(),
      });
      deletedIds.push(id);
    });
    try {
      await Promise.all(deletePromises);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_TOUR_STORIES, 'USA', user?.uid || null);
      return { success: true, deletedIds, notFoundIds };
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async deleteTourStory(id: string, user: any) {
    const tourStory = await this.tourStoryRepo.findOne(id);
    if (!tourStory) {
      throw new NotFoundException({ errorMessage: `TourStory NotFound!` });
    }
    try {
      await this.tourStoryRepo.update(id, {
        deletedBy: user?.uid,
        deletedAt: new Date(),
      });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_TOUR_STORIES, 'USA', user?.uid || null);
      return this.tourStoryRepo.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async getUserHistory(userId: string) {
    return this.tourStoryUserRepo.find({
      where: {
        userId,
        viewed: true,
      },
    });
  }

  async createUserTourStory(createUserTourStoryDto: CreateUserTourStoryDto, userId: string) {
    const { tourStoryId } = createUserTourStoryDto;

    try {
      const tourStory = await this.tourStoryRepo.findOne(tourStoryId);
      if (!tourStory) {
        throw new NotFoundException({ errorMessage: `TourStory NotFound!` });
      }
      const { totalView } = tourStory;
      const userTourStory = await this.tourStoryUserRepo.findOne({
        where: {
          userId,
          tourStoryId,
        },
      });
      let uTStory = null;
      if (userTourStory) {
        await this.tourStoryUserRepo.update(
          { id: userTourStory?.id },
          {
            viewed: true,
            totalView: userTourStory?.totalView + 1,
          }
        );
        uTStory = this.tourStoryUserRepo.findOne(userTourStory?.id);
      } else {
        const addData = {
          id: v4(),
          tourStoryId,
          userId,
          viewed: true,
          totalView: 1,
        };
        const tourStoryUser = plainToClass(TourStoryUserEntity, addData);

        uTStory = await this.tourStoryUserRepo.save(tourStoryUser);
      }
      await this.tourStoryRepo.update(
        { id: tourStoryId },
        {
          totalView: Number(totalView + 1),
        }
      );
      return uTStory;
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async findOneTourStory(id) {
    return this.tourStoryRepo.findOne(id);
  }

  async getTourStoryApp(userId, country, limit) {
    const lm = limit || 50;
    let userTourStoryId: any[] = await this.tourStoryUserRepo.find({
      where: { userId },
      select: ['tourStoryId', 'viewed'],
    });
    if (!isEmpty(userTourStoryId)) {
      userTourStoryId = map(userTourStoryId, 'tourStoryId');
    } else {
      userTourStoryId = [];
    }
    // Check new TourStory
    const queryNewTS = await this.tourStoryRepo.createQueryBuilder('ts').where({});
    if (!isEmpty(userTourStoryId)) {
      queryNewTS.andWhere('ts.id NOT IN (:...tourStoryId)', { tourStoryId: userTourStoryId });
    }
    queryNewTS.andWhere('ts.disabled = 0');
    const [newStories, totalActive] = await queryNewTS
      .select('ts.id')
      .addOrderBy('ts.sortOrder', 'ASC')
      .addOrderBy('ts.createdAt', 'ASC')
      .getManyAndCount();

    // check tour-story with user watched
    const query = this.tourStoryRepo.createQueryBuilder('ts').where({});
    query.andWhere('ts.disabled = 0');
    const [dataTs, countTs] = await query
      .addOrderBy('ts.sortOrder', 'ASC')
      .addOrderBy('ts.createdAt', 'ASC')
      .take(lm)
      .skip(0)
      .getManyAndCount();
    return {
      tourStories: dataTs,
      totalActive: totalActive || 0,
      totalView: countTs,
      newStories: !isEmpty(newStories) ? map(newStories, 'id') : [],
    };
  }

  async postSortTourStory(payload: SortTourStoriesDto, userId) {
    try {
      const listTourStoryId = [];
      const listTourStory = payload.sortOrderList;
      await Promise.all(
        listTourStory.map(async (tourStory) => {
          const checkConstantSortOrder = listTourStory.filter((otherTS) => otherTS.sortOrder === tourStory.sortOrder);
          if (checkConstantSortOrder.length > 1) {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
              errorMessage: `SortOrder exist more than one record`,
            });
          }

          const id = tourStory.id;
          if (isEmpty(id) || id === 'null') {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
              errorMessage: `TourStory ${id} not found`,
            });
          }
          listTourStoryId.push(id);
          const TsNeedUpdate = await this.tourStoryRepo.findOne(id);
          if (!TsNeedUpdate) {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
              errorMessage: `TourStory ${id} not found`,
            });
          }
          await this.tourStoryRepo.update(
            { id: TsNeedUpdate.id },
            { sortOrder: tourStory.sortOrder, updatedBy: userId, updatedAt: new Date() }
          );
        })
      );
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_TOUR_STORIES, 'USA', userId || null);
      return this.tourStoryRepo
        .createQueryBuilder('tourStories')
        .whereInIds(listTourStoryId)
        .orderBy({
          'tourStories.sortOrder': 'ASC',
        })
        .getMany();
    } catch (e) {
      return e;
    }
  }

  public async deleteUserHistory(userId) {
    try {
      await this.tourStoryUserRepo.update(
        { userId },
        {
          viewed: false,
          updatedAt: new Date(),
        }
      );
      return true;
    } catch (err) {
      return false;
    }
  }

  private getConditionCountryQuery(countryCode: string) {
    let conditionCountry = ' deletedAt IS NULL ';
    if (countryCode) {
      if (isUSCountry(countryCode)) {
        conditionCountry += ` AND (countries LIKE '%${countryCode}%' OR countries IS NULL) `;
      } else {
        conditionCountry += ` AND countries LIKE '%${countryCode}%' `;
      }
    } else {
      conditionCountry += ` AND (countries LIKE '%US%' OR countries IS NULL) `;
    }
    return conditionCountry;
  }
}

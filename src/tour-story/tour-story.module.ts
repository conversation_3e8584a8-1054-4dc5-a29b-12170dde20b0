import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CmsService } from 'src/cms/cms.service';
import { SharedModule } from 'src/shared/shared.module';
import { TourStoryUserEntity } from './entities/tour-story-user.entity';
import { TourStoryEntity } from './entities/tour-story.entity';
import { TourStoryController } from './tour-story.controller';
import { TourStoryService } from './tour-story.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([TourStoryUserEntity, TourStoryEntity])],
  controllers: [TourStoryController],
  providers: [TourStoryService, CmsService],
})
export class TourStoryModule {}

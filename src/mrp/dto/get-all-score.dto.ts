import { IsNotEmpty, IsOptional } from 'class-validator';

export class GetAllScoreDto {
  @IsOptional()
  golfer_id?: string;
  @IsOptional()
  course_id?: string;
  @IsOptional()
  tee_set_id?: string;
  @IsOptional()
  from_date_played?: string;
  @IsOptional()
  to_date_played?: string;
  @IsOptional()
  statuses?: string;
  @IsOptional()
  score_types?: string;
  @IsOptional()
  number_of_holes?: number;
  @IsNotEmpty()
  per_page: number;
  @IsNotEmpty()
  page: number;
  @IsOptional()
  date_ranges?: string;
  @IsOptional()
  days_of_week?: [];
  @IsOptional()
  course_name?: string;
  @IsOptional()
  include_9_holes?: boolean;
  @IsOptional()
  include_hole_details?: boolean;
}

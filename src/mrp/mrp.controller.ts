import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AuthGuard } from 'src/guards/auth.guard';
import { ClientGuard } from 'src/guards/client.guard';
import { PlayService } from 'src/play/play.service';
import { BaseRequest } from 'src/types/core';
import { LoyaltyService } from '../loyalty/loyalty.service';
import { GetAllScoreDto } from './dto/get-all-score.dto';
import { MatchingGHINCourseDto } from './dto/matching-ghin-course.dto';
import { PostScoreGHINDto } from './dto/post-score-ghin.dto';
import { PostScoreGolfNetDto } from './dto/post-score-golfnet.dto';
import { RequestAccessGHINDto } from './dto/request-access-ghin.dto';
import { RequestAccessWHSDto } from './dto/request-access-whs.dto';
import { GHINCourseHandicapDto, UpdateCanadaCardIdDto, UpdateGHINIdDto } from './dto/update-ghin-id.dto';
import { MrpService } from './mrp.service';

@Controller('mrp')
@UseGuards(ClientGuard)
export class MrpController {
  constructor(
    private mRPService: MrpService,
    private loyaltyService: LoyaltyService,
    private readonly config: ConfigService,
    private playService: PlayService
  ) {
    this.config = config;
  }

  @Post('add-round')
  @UseGuards(AuthGuard)
  async addRound(@Req() request: BaseRequest, @Body() payload: any) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.addRound(parseInt(mrpUID, 10), payload, request?.client?.name);
  }

  @Post('add-round-usga')
  @UseGuards(AuthGuard)
  async addRoundUSGA(@Req() request: BaseRequest, @Body() payload: any) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.addRoundUSGA(parseInt(mrpUID, 10), payload, request?.client?.name);
  }

  @Get('round/:id')
  @UseGuards(AuthGuard)
  async getRoundDetail(@Req() request: BaseRequest, @Param('id') roundId: number) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.getRoundDetail(roundId, request?.client?.name);
  }

  @Get('round/:id/stats-completed')
  @UseGuards(AuthGuard)
  async checkRoundStatsCompleted(@Req() request: BaseRequest, @Param('id') roundId: number) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.checkRoundStatsCompleted(roundId);
  }

  @Get('rounds/played')
  @UseGuards(AuthGuard)
  async getRoundsPlayed(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.getRoundsPlayed(mrpUID);
  }

  @Get('user/handicap-index')
  @UseGuards(AuthGuard)
  async getGHINHandicapIndex(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGHINHandicapIndex(mrpUID);
  }
  @Get('user/average-scores')
  @UseGuards(AuthGuard)
  async getAverageScore(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    const avgScore = await this.mRPService.getAverageScores(+mrpUID);
    return avgScore;
  }

  @Get('user/average-scores-classic')
  @UseGuards(AuthGuard)
  async getAverageScoreClassic(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    const avgScore = await this.mRPService.getAverageScoresClassic(+mrpUID);
    return avgScore;
  }

  @Get('user/golfer-profile')
  @UseGuards(AuthGuard)
  async getGHINGolferProfile(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    this.loyaltyService.addPointUsgaForOldUser(request?.user).catch((e) => e);
    return await this.mRPService.getGHINGolferProfile(mrpUID);
  }

  @Post('user/request-access-ghin')
  @UseGuards(AuthGuard)
  async requestAccessToGHIN(@Req() request: BaseRequest, @Body() payload: RequestAccessGHINDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.requestAccessToGHIN(mrpUID, payload);
  }

  @Delete('user/revoke-access-ghin')
  @UseGuards(AuthGuard)
  async revokeAccessToGHIN(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.revokeAccessToGHIN(mrpUID);
  }

  @Post('user/ghin')
  @UseGuards(AuthGuard)
  async updateGHINId(@Req() request: BaseRequest, @Body() updateGHINIdDto: UpdateGHINIdDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.updateGHINId(mrpUID, updateGHINIdDto.ghin_id);
  }
  @Get('user/ghin-course-handicap/:courseId')
  @UseGuards(AuthGuard)
  async getGHINCourseHandicap(@Req() request: BaseRequest, @Param() payload: GHINCourseHandicapDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGHINCourseHandicap(mrpUID, payload.courseId);
  }

  @Get('round/:id/post-score')
  @UseGuards(AuthGuard)
  async postGHINScore(@Req() request: BaseRequest, @Param('id') roundId: number) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.postGHINScore(roundId);
  }

  @Post('round/:id/post-score')
  @UseGuards(AuthGuard)
  async confirmPostGHINScore(
    @Req() request: BaseRequest,
    @Param('id') roundId: number,
    @Body() postScoreDto: PostScoreGHINDto
  ) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.confirmPostGHINScore(roundId, postScoreDto);
  }
  @Get('round/ghin/can-post-score-usga')
  @UseGuards(AuthGuard)
  async getDateCanPostScoreToUSGA(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.getDateCanPostScoreToUSGA();
  }

  @Post('ghin/match-igolf-course')
  @UseGuards(AuthGuard)
  async matchIGolfCourse(@Req() request: BaseRequest, @Body() matchCourseDto: MatchingGHINCourseDto) {
    return await this.mRPService.matchingCourseIGolf(matchCourseDto);
  }

  @Get('ghin/search-course')
  @UseGuards(AuthGuard)
  async searchCourseGHIN(
    @Req() request: BaseRequest,
    @Query('name') name: string,
    @Query('latitude') latitude: number,
    @Query('longitude') longitude: number
  ) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    if (latitude && longitude) {
      return await this.mRPService.searchCourseGHINByGEO(latitude, longitude);
    }
    return await this.mRPService.searchCourseGHINByName(name);
  }

  @Get('ghin/course-detail/:courseId')
  @UseGuards(AuthGuard)
  async getGHINCourseDetail(@Req() request: BaseRequest, @Param('courseId') courseId: string) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGHINCourseDetail(courseId);
  }
  @Get('ghin/scores')
  @UseGuards(AuthGuard)
  async getGHINScores(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGHINScores(mrpUID);
  }
  @Get('ghin/all-scores')
  @UseGuards(AuthGuard)
  async getGHINAllScores(@Req() request: BaseRequest, @Query() query: GetAllScoreDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGHINAllScores(mrpUID, query);
  }

  // GolfNet

  @Get('user/golf-net-golfer-profile')
  @UseGuards(AuthGuard)
  async getGolfNetGolferProfile(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGolfNetGolferProfile(mrpUID);
  }

  @Delete('user/revoke-canada-card-id')
  @UseGuards(AuthGuard)
  async revokeCanadaCardId(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.revokeCanadaCardId(mrpUID);
  }

  @Post('user/canada-card-id')
  @UseGuards(AuthGuard)
  async updateCanadaCardId(@Req() request: BaseRequest, @Body() updateCanadaCardId: UpdateCanadaCardIdDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.updateCanadaCardId(mrpUID, updateCanadaCardId.canadaCardId);
  }

  @Post('user/request-access-whs')
  @UseGuards(AuthGuard)
  async requestAccessWHS(@Req() request: BaseRequest, @Body() requestAccessWHSDto: RequestAccessWHSDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.requestAccessWHS(mrpUID, requestAccessWHSDto);
  }

  @Delete('user/revoke-access-whs')
  @UseGuards(AuthGuard)
  async revokeAccessWHS(@Req() request: BaseRequest) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.revokeAccessWHS(mrpUID);
  }

  @Get('round/:id/post-score-golf-net')
  @UseGuards(AuthGuard)
  async postGolfNetScore(@Req() request: BaseRequest, @Param('id') roundId: number) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.postGolfNetScore(roundId);
  }

  @Post('round/:id/post-score-golf-net')
  @UseGuards(AuthGuard)
  async confirmPostGolfNetScore(
    @Req() request: BaseRequest,
    @Param('id') roundId: number,
    @Body() postScoreDto: PostScoreGolfNetDto
  ) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.confirmPostGolfNetScore(roundId, postScoreDto);
  }

  @Get('golf-net/search-course')
  @UseGuards(AuthGuard)
  async searchGolfNetCourse(
    @Req() request: BaseRequest,
    @Query('name') name: string,
    @Query('latitude') latitude: number,
    @Query('longitude') longitude: number
  ) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    if (latitude && longitude) {
      return await this.mRPService.searchGolfNetCourseByGEO(latitude, longitude);
    }
    return await this.mRPService.searchGolfNetCourseByName(name);
  }

  @Get('golf-net/course-detail/:courseId')
  @UseGuards(AuthGuard)
  async getGolfCourseDetail(@Req() request: BaseRequest, @Param('courseId') courseId: string) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGolfNetCourseDetail(courseId);
  }
  @Get('golf-net/scores')
  @UseGuards(AuthGuard)
  async getGolfNetScores(@Req() request: BaseRequest, @Query() query: any) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGolfNetScores(mrpUID, query);
  }

  @Put('update-round/:id')
  @UseGuards(AuthGuard)
  async updateRound(@Req() request: BaseRequest, @Param('id') roundId: number, @Body() payload: any) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }

    return await this.mRPService.mrpUpdateRound(roundId, request.user, payload, request?.client?.name);
  }
}

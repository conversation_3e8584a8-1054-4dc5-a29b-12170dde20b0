import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import camelcaseKeys from 'camelcase-keys';
import * as _ from 'lodash';
import omit from 'lodash/omit';
import { ConfigService } from 'nestjs-config';
import querystring from 'querystring';
import { SIUserAddUpdateDto } from 'src/auth/auth.type';
import { CLIENTS } from 'src/guards/client.guard';
import axiosInstance from 'src/utils/axios.instance';
import { FeatureKey } from '../loyalty/entities/loyalty-action.entity';
import { LoyaltyService } from '../loyalty/loyalty.service';
import { PostScoreDto } from '../play/play.type';
import { GetAllScoreDto } from './dto/get-all-score.dto';
import { MatchingGHINCourseDto } from './dto/matching-ghin-course.dto';
import { PostScoreGHINDto } from './dto/post-score-ghin.dto';
import { PostScoreGolfNetDto } from './dto/post-score-golfnet.dto';
import { RequestAccessGHINDto } from './dto/request-access-ghin.dto';
import { RequestAccessWHSDto } from './dto/request-access-whs.dto';

const USER_IN_COURSE = 400;
@Injectable()
export class MrpService {
  constructor(private readonly config: ConfigService, private readonly loyaltyService: LoyaltyService) {
    this.config = config;
  }

  getRequestHeaderConfigs(clientName?: string) {
    if (clientName) {
      return {
        headers: { Authorization: `${this.config.get('app.mRPToken')}`, client: clientName },
      };
    }
    return {
      headers: { Authorization: `${this.config.get('app.mRPToken')}` },
    };
  }

  async createAccount(accessToken: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/users/by_access_token.json`;
    try {
      const response = await axiosInstance.post(
        endpoint,
        {
          access_token: accessToken,
        },
        this.getRequestHeaderConfigs()
      );
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e.message);
      return null;
    }
  }

  async createAccountByEmail(email: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/users/by_email.json`;
    try {
      const response = await axiosInstance.post(
        endpoint,
        {
          email,
        },
        this.getRequestHeaderConfigs()
      );
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return {};
    } catch (e) {
      console.log(e.message);
      return {};
    }
  }
  async createAccountBySIEmail(payload: SIUserAddUpdateDto) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/users/by_si_email`;
    try {
      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs());
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return {};
    } catch (e) {
      console.log(e.message);
      return {};
    }
  }
  async updateAccountSI(payload: SIUserAddUpdateDto) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/users/update_si_email`;
    try {
      const response = await axiosInstance.post(
        endpoint,
        { ...payload, userId: +payload.userId },
        this.getRequestHeaderConfigs()
      );
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return {};
    } catch (e) {
      console.log(e.message);
      return {};
    }
  }

  async getRecentRoundTMAndUsga(userId: number, query: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/round-usga?${query}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(omit(data, ['links']), { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getRecentRounds(userId: number, query: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/rounds?${query}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(omit(data, ['links']), { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async getAverageScores(userId: number, retry = 5) {
    if (!userId) return null;
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/average_scores`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      console.log(`ERROR getAverageScores : ${error.message}`);
      if (retry > 0) {
        console.log(`---- Retry getAverageScores time ${retry}`);
        retry--;
        console.log(`---- Wait 1.5s to retry`);
        await new Promise((resolve) => setTimeout(resolve, 1500));
        return await this.getAverageScores(userId, retry);
      }
      return null;
    }
  }
  async getAverageScoresClassic(userId: number, retry = 5) {
    if (!userId) return null;
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/average_scores_classic`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      console.log(`ERROR getAverageScores : ${error.message}`);
      if (retry > 0) {
        console.log(`---- Retry getAverageScoresClassic time ${retry}`);
        retry--;
        console.log(`---- Wait 1.5s to retry`);
        await new Promise((resolve) => setTimeout(resolve, 1500));
        return await this.getAverageScoresClassic(userId, retry);
      }
      return null;
    }
  }

  async getCourses(payload: any, request?: any, isCamelCase: any = true) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/igolf/search`;
    const clientName = request?.client?.name || 'MY_TM';
    const mrpId = request?.user?.mrpUID ? Number(request?.user?.mrpUID) : null;

    if (mrpId) {
      payload = {
        ...payload,
        mrpId: mrpId,
      };
    }
    payload = querystring.stringify(payload);
    const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs(clientName));
    if (response.data) {
      if (isCamelCase == false || isCamelCase == 'false') {
        return response.data;
      }
      return camelcaseKeys(response.data, { deep: true });
    }
    return null;
  }
  async getCoursesLogRequest(payload: any, clientName?: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/igolf/log_requests`;
    try {
      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs(clientName));
      if (response.data) {
        return response.data;
      }
      return null;
    } catch (error) {
      throw new BadRequestException(error?.response?.data);
    }
  }

  async getRecentCourses(userId: string, query: string, clientName?: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/igolf/${userId}/course_recent?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs(clientName));
    if (response.data) {
      const data = response.data;
      return camelcaseKeys(data, { deep: true });
    }
    return null;
  }

  async getGHINRecentCourses(userId: string, query: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/ghin/${userId}/course_recent?${query}`;
    try {
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        const data = response.data;
        return camelcaseKeys(data, { deep: true });
      }
      return null;
    } catch (e) {
      console.error(e);
      return null;
    }
  }

  async getCourseDetails(courseId: string, clientName?: string, isCamelCase: any = true) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/igolf/detail`;
    const response = await axiosInstance.post(
      endpoint,
      {
        id_course: courseId,
      },
      this.getRequestHeaderConfigs(clientName)
    );
    if (response.data) {
      const data = response.data;
      if (isCamelCase == false || isCamelCase == 'false') {
        return data;
      }
      return camelcaseKeys(data, { deep: true });
    }
    return null;
  }

  async getCourseInformation(courseId: string, informationType: string, isCamelCase: any, clientName?: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/igolf/course_${informationType}_detail`;
    const response = await axiosInstance.post(
      endpoint,
      {
        id_course: courseId,
      },
      this.getRequestHeaderConfigs(clientName)
    );
    if (response.data) {
      if (isCamelCase == false || isCamelCase == 'false') {
        return response.data;
      }
      return omit(camelcaseKeys(response.data, { deep: true }), ['status', 'errorMessage', 'lastModified']);
    }
    return null;
  }

  async getClubStats(userId: number, clubId: any, query: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/club/${clubId}?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async getDistanceClubStats(userId: number, query: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/club?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async getRoundStats(userId: number, query: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/overall?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async addRound(userId: number, payload: any, clientName?: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/rounds`;
      payload['round']['play_client'] = clientName == CLIENTS.SWING_INDEX_APP ? CLIENTS.SWING_INDEX_APP : 'MyTM+';

      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs(clientName));

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }

  async addRoundUSGA(userId: number, payload: any, clientName?: string) {
    try {
      if (!payload.post_score_ghin) {
        throw new BadRequestException({
          message: 'post_score_ghin is required',
          errorCode: 'POST_SCORE_GHIN_REQUIRED',
        });
      }
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/round-usga`;
      payload['round']['play_client'] = clientName == CLIENTS.SWING_INDEX_APP ? CLIENTS.SWING_INDEX_APP : 'MyTM+';

      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs(clientName));

      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }

  async getRoundDetail(roundId: any, clientName?: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs(clientName));
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
  async getDateCanPostScoreToUSGA() {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/ghin/can_post_score_usga`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async checkRoundStatsCompleted(roundId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}/stats_completed`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
    }
  }

  async getRoundsPlayed(userId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/rounds/played`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
  async getGHINHandicapIndex(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/handicap_index`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      if (error.response.status === 401) {
        throw new UnauthorizedException({
          message: error.response.data.message || error.message,
          ghin_id: error.response.data?.ghin_id || null,
        });
      }
      return null;
    }
  }
  async getGHINGolferProfile(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/golfer_profile`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        errorCode: error.response.data.errorCode || null,
        ghin_id: error.response.data?.ghin_id || null,
        ghinEmail: error.response.data?.ghinEmail || null,
      });
    }
  }
  async requestAccessToGHIN(mrpId: any, payload: RequestAccessGHINDto) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/request_access_ghin`;
      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        ghin_id: error.response.data?.ghin_id,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async revokeAccessToGHIN(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/revoke_access_ghin`;
      const response = await axiosInstance.delete(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }
  async updateGHINId(mrpId: any, ghin_id: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/ghin`;
      const response = await axiosInstance.patch(endpoint, { ghin_id }, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }
  async postGHINScore(roundId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}/post_score`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        ghinCourseId: error.response.data?.ghinCourseId || error.response.data?.ghin_course_id,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async confirmPostGHINScore(roundId: any, postScoreDto: PostScoreGHINDto) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}/post_score`;
      const response = await axiosInstance.post(endpoint, postScoreDto, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        ghinCourseId: error.response.data?.ghinCourseId || error.response.data?.ghin_course_id,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async matchingCourseIGolf(matchCourseDto: MatchingGHINCourseDto) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/ghin/match_igolf_course`;
      const response = await axiosInstance.post(endpoint, matchCourseDto, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        ghinCourseId: error.response.data?.ghinCourseId || error.response.data?.ghin_course_id,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async searchCourseGHINByName(name: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/ghin/search?name=${encodeURIComponent(name)}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
  async searchCourseGHINByGEO(latitude: number, longitude: number) {
    try {
      const endpoint = `${this.config.get(
        'app.myRoundProEndpoint'
      )}/api/ghin/search?latitude=${latitude}&longitude=${longitude}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
  async getGHINCourseDetail(courseId: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/ghin/course_detail/${courseId}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async getGHINCourseHandicap(mrpId: string, courseId: string) {
    try {
      const endpoint = `${this.config.get(
        'app.myRoundProEndpoint'
      )}/api/users/${mrpId}/ghin_course_handicap/${courseId}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        errorCode: error.response.data?.errorCode,
      });
    }
  }

  async getGHINScores(mrpUID: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpUID}/ghin_scores`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        ghinCourseId: error.response.data?.ghinCourseId || error.response.data?.ghin_course_id,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async getGHINAllScores(mrpUID: string, query: GetAllScoreDto) {
    try {
      const queryParams = new URLSearchParams(query as any).toString();
      const endpoint = `${this.config.get(
        'app.myRoundProEndpoint'
      )}/api/users/${mrpUID}/ghin_all_scores?${queryParams}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async postPostScore(userId: number, data: PostScoreDto, clientName?: string) {
    const totalScore = data.front9Score ?? data.back9Score ?? data.eighteenHoleScore;
    const payload = {
      round: {
        igolf_course_id: data.courseID || null,
        map_id: 'iGolf',
        course_name: data.courseName || null,
        tee_name: data.teeName,
        played_on: data.datePlayed,
        round_mode: data.roundMode,
        round_type: data.roundType,
        total_score: totalScore,
        generated_by: data.generatedBy,
        user_timezone: data.userTimezone || 7,
        number_of_holes_played: data.numberOfHolesPlayed,
        front_9_score: data.front9Score || null,
        back_9_score: data.back9Score || null,
        eighteen_holes_score: data.eighteenHoleScore || null,
        source_type: data.sourceType || null,
        inprogress: true,
        play_client: 'MyTM+',
        ghin_course_id: data.ghinCourseId || null,
        ghin_course_name: data.ghinCourseName || null,
        ghin_tee_set_id: data.ghinTeeSetId || null,
        ghin_tee_set_name: data.ghinTeeSetName || null,
        id: data.roundId || null,
      },
      round_submit: true,
      post_score_ghin: data.postScoreGhin || false,
    };
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/rounds`;
    try {
      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs(clientName));
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
    } catch (error) {
      throw new BadRequestException({
        message: error.response?.data?.message || error.message,
        ghinCourseId: error.response.data?.ghinCourseId || error.response.data?.ghin_course_id,
        errorCode: error.response?.data?.errorCode,
      });
    }

    return null;
  }

  async postPostScoreUSGA(userId: number, data: PostScoreDto, clientName?: string) {
    if (!data.postScoreGhin) {
      throw new BadRequestException({
        message: 'postScoreGhin is required',
        errorCode: 'POST_SCORE_GHIN_REQUIRED',
      });
    }

    const totalScore = data.front9Score ?? data.back9Score ?? data.eighteenHoleScore;
    const payload = {
      round: {
        igolf_course_id: data.courseID || null,
        map_id: 'iGolf',
        course_name: data.courseName || null,
        tee_name: data.teeName,
        played_on: data.datePlayed,
        round_mode: data.roundMode,
        round_type: data.roundType,
        total_score: totalScore,
        generated_by: data.generatedBy,
        user_timezone: data.userTimezone || 7,
        number_of_holes_played: data.numberOfHolesPlayed,
        front_9_score: data.front9Score || null,
        back_9_score: data.back9Score || null,
        eighteen_holes_score: data.eighteenHoleScore || null,
        source_type: data.sourceType || null,
        inprogress: true,
        play_client: 'MyTM+',
        ghin_course_id: data.ghinCourseId || null,
        ghin_course_name: data.ghinCourseName || null,
        ghin_tee_set_id: data.ghinTeeSetId || null,
        ghin_tee_set_name: data.ghinTeeSetName || null,
        id: data.roundId || null,
      },
      round_submit: true,
      post_score_ghin: data.postScoreGhin || false,
    };
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/round-usga`;
    try {
      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs(clientName));
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
    } catch (error) {
      throw new BadRequestException({
        message: error.response?.data?.message || error.message,
        ghinCourseId: error.response.data?.ghinCourseId || error.response.data?.ghin_course_id,
        errorCode: error.response?.data?.errorCode,
      });
    }

    return null;
  }

  // GolfNet API
  async updateCanadaCardId(mrpId: any, canadaCardId: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/canada_card_id`;
      const response = await axiosInstance.patch(
        endpoint,
        { canada_card_id: canadaCardId },
        this.getRequestHeaderConfigs()
      );
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }

  async revokeCanadaCardId(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/canada_card_id`;
      const response = await axiosInstance.delete(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }

  async requestAccessWHS(mrpId: any, requestAccessWHSDto: RequestAccessWHSDto) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/request_access_whs`;
      const response = await axiosInstance.post(endpoint, requestAccessWHSDto, this.getRequestHeaderConfigs());
      if (response.data) {
        this.loyaltyService.addPointUsga(mrpId).catch((e) => e);
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      if (error?.response?.data) {
        throw new BadRequestException(error.response.data);
      }
      throw new BadRequestException(error?.message);
    }
  }

  async revokeAccessWHS(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/revoke_access_whs`;
      const response = await axiosInstance.delete(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }

  async postGolfNetScore(roundId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}/post_score_golf_net`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        golfNetCourseId: error.response.data?.golfNetCourseId,
        errorCode: error.response.data?.errorCode,
      });
    }
  }
  async confirmPostGolfNetScore(roundId: any, postScoreDto: PostScoreGolfNetDto) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}/post_score_golf_net`;
      const response = await axiosInstance.post(endpoint, postScoreDto, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        golfNetCourseId: error.response.data?.golfNetCourseId,
        errorCode: error.response.data?.errorCode,
      });
    }
  }

  async getGolfNetGolferProfile(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/canada_handicap_index`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        errorCode: error.response.data.errorCode || null,
        canadaCardId: error.response.data?.canada_card_id || error.response.data?.canadaCardId,
      });
    }
  }

  async getGolfNetScores(mrpId: any, query: string) {
    try {
      let endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}/golf_net_scores`;
      if (query) {
        endpoint += '?' + new URLSearchParams(query);
      }
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        message: error.response.data.message || error.message,
        errorCode: error.response.data.errorCode || null,
        canadaCardId: error.response.data?.canada_card_id || null,
      });
    }
  }

  async searchGolfNetCourseByName(name: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/golf-net/search?name=${encodeURIComponent(
        name
      )}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
  async searchGolfNetCourseByGEO(latitude: number, longitude: number) {
    try {
      const endpoint = `${this.config.get(
        'app.myRoundProEndpoint'
      )}/api/golf-net/search?latitude=${latitude}&longitude=${longitude}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }
  async getGolfNetCourseDetail(courseId: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/golf-net/course_detail/${courseId}`;
      const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async postGolfNetPostScore(userId: number, data: PostScoreDto) {
    const totalScore = data.front9Score ?? data.back9Score ?? data.eighteenHoleScore;
    const payload = {
      round: {
        golf_net_course_id: data?.golfNetCourseId,
        golf_net_course_name: data?.golfNetCourseName,
        golf_net_tee_id: data?.golfNetTeeSetId,
        golf_net_tee_name: data?.golfNetTeeSetName,
        played_on: data.datePlayed,
        played_on_utc: data?.datePlayedUTC,
        round_mode: data.roundMode,
        round_type: data.roundType,
        total_score: totalScore,
        generated_by: data.generatedBy,
        user_timezone: data.userTimezone || 7,
        number_of_holes_played: data.numberOfHolesPlayed,
        front_9_score: data.front9Score || null,
        back_9_score: data.back9Score || null,
        eighteen_holes_score: data.eighteenHoleScore || null,
        source_type: data.sourceType || null,
        inprogress: true,
        id: data?.roundId,
        play_client: 'MyTM+',
      },
      round_submit: true,
      post_score_golf_net: data.postScoreGolfNet || false,
    };
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/rounds`;
    try {
      const response = await axiosInstance.post(endpoint, payload, this.getRequestHeaderConfigs());
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
    } catch (error) {
      throw new BadRequestException({
        message: error.response?.data?.message || error.message,
        golfNetCourseId: error.response.data?.golfNetCourseId,
        errorCode: error.response?.data?.errorCode,
      });
    }

    return null;
  }

  async deleteRound(roundId: number) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}`;
    const response = await axiosInstance.delete(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data, { deep: true });
    }
    return null;
  }

  async mrpUpdateRound(roundId: number, user: any, payload: any, clientName?: string) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}`;
      payload['round']['play_client'] = clientName == CLIENTS.SWING_INDEX_APP ? CLIENTS.SWING_INDEX_APP : 'MyTM+';
      const completed = _.result(payload, 'round.completed', false);
      const userToCourse = _.result(payload, 'round.user_to_course', null);
      if (userToCourse) {
        delete payload['round']['user_to_course'];
      }
      const response = await axiosInstance.put(endpoint, payload, this.getRequestHeaderConfigs());
      if (response.data) {
        if (completed && userToCourse && Number(userToCourse) <= USER_IN_COURSE) {
          // add point to round completed
          await this.loyaltyService.addPointUser(user, FeatureKey.TRACK_ROUND).catch((e) => e);
        }
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (error) {
      console.log('=== error ===', error);
    }
  }
  async updateRound(roundId: number, data: PostScoreDto, clientName?: string) {
    const totalScore = data.front9Score ?? data.back9Score ?? data.eighteenHoleScore;
    const payload = {
      round: {
        igolf_course_id: data.courseID,
        map_id: 'iGolf',
        course_name: data.courseName,
        tee_name: data.teeName,
        played_on: data.datePlayed,
        round_mode: data.roundMode,
        round_type: data.roundType,
        total_score: totalScore,
        generated_by: data.generatedBy,
        user_timezone: data.userTimezone || 7,
        number_of_holes_played: data.numberOfHolesPlayed,
        front_9_score: data.front9Score || null,
        back_9_score: data.back9Score || null,
        eighteen_holes_score: data.eighteenHoleScore || null,
        inprogress: true,
        play_client: 'MyTM+',
      },
    };
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/rounds/${roundId}`;
    const response = await axiosInstance.put(endpoint, payload, this.getRequestHeaderConfigs(clientName));
    if (response.data) {
      return camelcaseKeys(response.data, { deep: true });
    }
    return null;
  }

  async getShowAdvance(userId: number) {
    return { show: true };
    // const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/show_advance`;
    // const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    // if (response.data) {
    //   return camelcaseKeys(response.data, { deep: true });
    // }
    // return null;
  }

  async getClubStatsPutter(userId: number, clubId: any, query: string) {
    const endpoint = `${this.config.get(
      'app.myRoundProEndpoint'
    )}/api/users/${userId}/stats/club/${clubId}/putter?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return response.data.data;
    }
    return null;
  }

  async getStatsDriving(userId: number, query: string) {
    const endpoint = `${this.config.get(
      'app.myRoundProEndpoint'
    )}/api/users/${userId}/stats/driving/dispersion?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async getStatsDrivingOverall(userId: number, query: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/driving?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async getStatsShort(userId: number, query: string, isNotProximity = false) {
    let endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/short/proximity?${query}`;
    if (isNotProximity) {
      endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/short?${query}`;
    }
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async getStatsApproach(userId: number, query: string, isNotProximity = false) {
    let endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/approach/proximity?${query}`;
    if (isNotProximity) {
      endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/approach?${query}`;
    }
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return camelcaseKeys(response.data.data, { deep: true });
    }
    return null;
  }

  async getStatsPutting(userId: number, query: string) {
    const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/stats/putting?${query}`;
    const response = await axiosInstance.get(endpoint, this.getRequestHeaderConfigs());
    if (response.data) {
      return response.data.data;
    }
    return null;
  }

  async getClubsActive(userId: number, take = 10, page = 1) {
    const endPoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${userId}/clubs?page=${page}&per=${take}`;
    const response = await axiosInstance.get(endPoint, this.getRequestHeaderConfigs());
    if (response?.data) {
      const data = response.data;
      return camelcaseKeys({ ...data, per_page: Number(take), page: Number(page) }, { deep: true });
    }
    return null;
  }
  async getCountriesWithCode(take = 400, page = 1) {
    const endPoint = `${this.config.get('app.myRoundProEndpoint')}/api/countries/get/all?page=${page}&per=${take}`;
    const response = await axiosInstance.get(endPoint, this.getRequestHeaderConfigs());
    if (response?.data) {
      const data = response.data;
      return camelcaseKeys({ ...data, per_page: Number(take), page: Number(page) }, { deep: true });
    }
    return null;
  }
  async softDeleteUser(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/${mrpId}`;
      const response = await axiosInstance.delete(endpoint, this.getRequestHeaderConfigs());
      if (response?.data) {
        return true;
      }
      return false;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }

  async forceDeleteUser(mrpId: any) {
    try {
      const endpoint = `${this.config.get('app.myRoundProEndpoint')}/api/users/force-delete/${mrpId}`;
      const response = await axiosInstance.delete(endpoint, this.getRequestHeaderConfigs());
      if (response?.data && response?.data?.success) {
        return true;
      }
      return false;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.response.data.message || error.message);
    }
  }
}

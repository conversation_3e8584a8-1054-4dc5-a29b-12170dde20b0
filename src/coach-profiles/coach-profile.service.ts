import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { EntityManager, Repository, getManager } from 'typeorm';
import { ERROR_CODES } from 'src/utils/errors';
import { isAllCountry, isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { COACH_STATUS, SortCoachProfileDto } from './coach-profile.types';
import { ActiveCoachProfileDto } from './dto/active-coach-profile.dto';
import { CreateCoachProfileDto } from './dto/create-coach-profile.dto';
import { UpdateCoachProfileDto } from './dto/update-coach-profile.dto';
import { CoachProfile } from './entities/coach-profile.entity';

@Injectable()
export class CoachProfileService {
  private readonly logger = new Logger(CoachProfileService.name);
  @InjectRepository(CoachProfile) private readonly coachProfileRepo: Repository<CoachProfile>;
  constructor(private readonly config: ConfigService, private readonly apiVersionsService: ApiVersionsService) {}

  async create(createDTO: CreateCoachProfileDto) {
    if (createDTO.options) {
      createDTO.options = JSON.stringify(createDTO.options);
    }
    if (!createDTO.status) {
      createDTO.status = COACH_STATUS.ACTIVE;
    } else {
      this.validateStatus(createDTO.status);
    }
    createDTO.updatedAt = new Date();
    try {
      const coachProfile = await this.coachProfileRepo.save(this.coachProfileRepo.create(createDTO));
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.CLUB_COACH_PROFILE,
        'USA',
        createDTO.createdBy || null
      );
      return coachProfile;
    } catch (err) {
      console.log(err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }
  validateStatus(status: string) {
    if (![COACH_STATUS.ACTIVE, COACH_STATUS.INACTIVE].includes(status)) {
      throw new BadRequestException({ errorMessage: 'Status must be [ACTIVE, INACTIVE]' });
    }
  }
  async findAll(options: any) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 20;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.coachProfileRepo.createQueryBuilder('coach').where({});
    if (options.type) {
      query.andWhere({ type: options.type });
    }
    if (options.status) {
      query.andWhere({ status: options.status });
    }
    const country = options.country;
    let conditionCountry = ' 1 = 1 ';
    conditionCountry = this.getQueryCountryCondition(country, conditionCountry);
    query.andWhere(conditionCountry);

    if (options.sort) {
      query.orderBy('sortOrder', 'ASC');
      query.addOrderBy('updatedAt', 'DESC');
    } else {
      query.orderBy('createdAt', 'ASC');
    }
    const [coachProfiles, total] = await query
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .select([
        'coach.id',
        'coach.type',
        'coach.title',
        'coach.countries',
        'coach.description',
        'coach.options',
        'coach.imageLink',
        'coach.status',
        'coach.videoLink',
        'coach.sortOrder',
        'coach.tags',
      ])
      .getManyAndCount();
    return { coachProfiles, total };
  }

  getQueryCountryCondition(country: any, conditionCountry: string) {
    if (country) {
      if (isAllCountry(country)) {
        return conditionCountry;
      }
      if (isUSCountry(country)) {
        conditionCountry += ` AND (countries LIKE '%${country}%' OR countries IS NULL) `;
        return conditionCountry;
      }
      conditionCountry += ` AND countries LIKE '%${country}%' `;
      return conditionCountry;
    }
    conditionCountry = `(countries LIKE '%US%'  OR countries IS NULL) `;
    return conditionCountry;
  }

  async findOne(id: string) {
    try {
      return await this.coachProfileRepo.findOne(id);
    } catch (err) {
      console.log(err.message);
      return null;
    }
  }

  async patchUpdateCoachProfile(id: string, updateDTO: UpdateCoachProfileDto, user: any) {
    const coachProfile = await this.findOne(id);
    if (!coachProfile) {
      throw new NotFoundException({ errorMessage: `CoachProfile NotFound!` });
    }
    if (updateDTO.status) {
      this.validateStatus(updateDTO.status);
    }
    if (updateDTO.options) {
      updateDTO.options = JSON.stringify(updateDTO.options);
    }
    updateDTO.updatedAt = new Date();
    updateDTO.createdBy = user.uid;
    delete updateDTO.type;
    try {
      await this.coachProfileRepo.update(id, updateDTO);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_COACH_PROFILE, 'USA', user.uid || null);
      return await this.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }
  async patchActiveCoachProfile(id: string, activeDTO: ActiveCoachProfileDto, user: any) {
    const coachProfile = await this.findOne(id);
    if (!coachProfile) {
      throw new NotFoundException({ errorMessage: `CoachProfile NotFound!` });
    }
    const updateDTO = new UpdateCoachProfileDto();
    updateDTO.updatedAt = new Date();
    updateDTO.createdBy = user.uid;
    updateDTO.status = activeDTO.active ? COACH_STATUS.ACTIVE : COACH_STATUS.INACTIVE;
    delete updateDTO.type;
    try {
      await this.coachProfileRepo.update(id, updateDTO);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_COACH_PROFILE, 'USA', user.uid || null);
      return await this.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async deleteCoachProfile(id: string, user: any) {
    const coachProfile = await this.findOne(id);
    if (!coachProfile) {
      throw new NotFoundException({ errorMessage: `CoachProfile NotFound!` });
    }
    const updateDTO = new UpdateCoachProfileDto();
    updateDTO.deletedAt = new Date();
    updateDTO.deletedBy = user.uid;
    try {
      await this.coachProfileRepo.update(id, updateDTO);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_COACH_PROFILE, 'USA', user.uid);
      return { success: true };
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }
  async postSortCoachProfile(payload: SortCoachProfileDto, userId: string) {
    try {
      const listCoachId = [];
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listCoachProfile = payload.sortOrderList;
          await Promise.all(
            listCoachProfile.map(async (coachProfile) => {
              const checkConstantSortOrder = listCoachProfile.filter(
                (othercoach) => othercoach.sortOrder === coachProfile.sortOrder
              );
              if (checkConstantSortOrder.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }

              const { id } = coachProfile;
              listCoachId.push(id);
              const coachProfileUpdate = await this.findOne(id);
              if (!coachProfileUpdate) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
                  errorMessage: `CoachProfile ${id} not found`,
                });
              }
              await transactionalEntityManager.update(
                CoachProfile,
                { id: coachProfileUpdate.id },
                { sortOrder: coachProfile.sortOrder, updatedBy: userId, updatedAt: new Date() }
              );
            })
          );
        })
        .then(async () => {
          const coachProfiles = await this.coachProfileRepo
            .createQueryBuilder('coachProfile')
            .whereInIds(listCoachId)
            .orderBy({
              'coachProfile.sortOrder': 'ASC',
            })
            .getMany();
          if (coachProfiles && coachProfiles.length > 0) {
            await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CLUB_COACH_PROFILE, 'USA', userId || null);
          }
        });
    } catch (e) {
      return e;
    }
  }
}

import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { CoachProfileService } from './coach-profile.service';
import { SortCoachProfileDto } from './coach-profile.types';
import { ActiveCoachProfileDto } from './dto/active-coach-profile.dto';
import { CreateCoachProfileDto } from './dto/create-coach-profile.dto';
import { UpdateCoachProfileDto } from './dto/update-coach-profile.dto';

@Controller('coach-profile')
export class CoachProfileController {
  constructor(private readonly coachProfileService: CoachProfileService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createCoachProfileDto: CreateCoachProfileDto) {
    return this.coachProfileService.create(createCoachProfileDto);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll(@Req() req: any) {
    return this.coachProfileService.findAll(req.query);
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.coachProfileService.findOne(id);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch(':id')
  patchUpdateCoachProfile(
    @Param('id') id: string,
    @Body() updateCoachProfileDto: UpdateCoachProfileDto,
    @Req() req: any
  ) {
    return this.coachProfileService.patchUpdateCoachProfile(id, updateCoachProfileDto, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch('/active/:id')
  patchActiveCoachProfile(@Param('id') id: string, @Body() activeDTO: ActiveCoachProfileDto, @Req() req: any) {
    return this.coachProfileService.patchActiveCoachProfile(id, activeDTO, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  deleteCoachProfile(@Param('id') id: string, @Req() req: any) {
    return this.coachProfileService.deleteCoachProfile(id, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  async postUpdateSortTilesWidget(@Req() request: BaseRequest, @Body() payload: SortCoachProfileDto) {
    return await this.coachProfileService.postSortCoachProfile(payload, request.user.uid);
  }
}

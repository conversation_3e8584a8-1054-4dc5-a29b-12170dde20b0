import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CmsService } from 'src/cms/cms.service';
import { SharedModule } from 'src/shared/shared.module';
import { CoachProfileController } from './coach-profile.controller';
import { CoachProfileService } from './coach-profile.service';
import { CoachProfile } from './entities/coach-profile.entity';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([CoachProfile])],
  controllers: [CoachProfileController],
  providers: [CoachProfileService, CmsService],
})
export class CoachProfileModule {}

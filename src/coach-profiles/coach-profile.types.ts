import { Is<PERSON>rray, IsNotEmpty, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsUUID } from 'class-validator';

export const COACH_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};

export class SortCoachProfileDto {
  @IsNotEmpty()
  @IsArray()
  sortOrderList: CoachProfileSort[];

  @IsOptional()
  country: string;
}

export class CoachProfileSort {
  @IsUUID()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @IsOptional()
  country: string;
}

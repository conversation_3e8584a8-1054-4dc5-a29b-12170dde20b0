import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateCoachProfileDto {
  @IsNotEmpty()
  title: string;

  @IsOptional()
  type: string;

  @IsOptional()
  countries: string;

  @IsOptional()
  description: string;

  @IsOptional()
  tags: string;

  @IsOptional()
  status: string;

  @IsOptional()
  options: string;

  @IsOptional()
  imageLink: string;

  @IsOptional()
  videoLink: string;

  @IsOptional()
  sortOrder: number;

  @IsOptional()
  createdAt: Date;

  @IsOptional()
  updatedAt: Date;

  @IsOptional()
  deletedAt: Date;

  @IsOptional()
  createdBy: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  deletedBy: string;
}

import { IsNotEmpty, <PERSON>Optional, IsString, MaxLength } from 'class-validator';

export class ViewedProductDto {
  @IsOptional()
  @IsString()
  price: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  productID: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  imageUrl: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  productName: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  url: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  category: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  primaryCategory: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  productUPC: string;
}

export class ViewedCategoryDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  type: string;
}

export class ActiveOnSiteDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  browser: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(100)
  os: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  url: string;
}

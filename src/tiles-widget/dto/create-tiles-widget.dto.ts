import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateTilesWidgetDto {
  @IsNotEmpty()
  type: string;

  @IsNotEmpty()
  title: string;

  @IsOptional()
  countries: string;

  @IsOptional()
  description: string;

  @IsOptional()
  status: string;

  @IsOptional()
  options: string;

  @IsOptional()
  ctaText: string;

  @IsOptional()
  ctaLink: string;

  @IsOptional()
  imageLink: string;

  @IsOptional()
  videoLink: string;

  @IsOptional()
  sortOrder: number;

  @IsOptional()
  createdAt: Date;

  @IsOptional()
  updatedAt: Date;

  @IsOptional()
  deletedAt: Date;

  @IsOptional()
  createdBy: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  deletedBy: string;
}

import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateScheduleTilesWidgetDto {
  @IsNotEmpty()
  widgetId: string;

  @IsNotEmpty()
  widgetType: string;

  @IsOptional()
  activeAt: Date;

  @IsOptional()
  inActiveAt: Date;

  @IsOptional()
  isRunActive: boolean;

  @IsOptional()
  isRunInActive: boolean;

  @IsOptional()
  createdAt: Date;

  @IsOptional()
  updatedAt: Date;

  @IsOptional()
  deletedAt: Date;

  @IsOptional()
  createdBy: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  deletedBy: string;
}

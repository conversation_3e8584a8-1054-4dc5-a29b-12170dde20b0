import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('TilesWidget')
export class TilesWidget {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  type: string;

  @Column()
  countries: string;

  @Column()
  status: string;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  options: string;

  @Column()
  ctaText: string;

  @Column()
  ctaLink: string;

  @Column()
  imageLink: string;

  @Column()
  videoLink: string;

  @Column()
  sortOrder: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

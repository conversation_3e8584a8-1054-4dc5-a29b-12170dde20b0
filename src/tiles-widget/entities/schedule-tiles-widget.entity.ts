import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('ScheduleTilesWidget')
export class ScheduleTilesWidget {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  widgetId: string;

  @Column()
  widgetType: string;

  @Column()
  isRunActive: boolean;

  @Column()
  isRunInActive: boolean;

  @Column()
  activeAt: Date;

  @Column()
  inActiveAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

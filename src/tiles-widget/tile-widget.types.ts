import { Is<PERSON>rray, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsOptional, IsUUID } from 'class-validator';

export const TILE_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};
export const WIDGET_TYPE = {
  MARQUEE: 'MARQUEE',
  PROMOTION: 'PROMOTION',
};
export const TilesCategoryType = {
  WEB_LINK: 'WEB_LINK',
  DEEP_LINK: 'DEEP_LINK',
  CMS: 'CMS',
};
export const TypeWidget = {
  PRODUCT_TILES: 'PRODUCT_TILES',
  MORE_FOR_YOU: 'MORE_FOR_YOU',
  MAINSTAYS: 'SUPPORT_CARD',
  FINAL_TILE: 'FINAL_TILE',
  PROMOTION: 'PROMOTION',
  THE_DAILY: 'THE_DAILY',
  PLAYER_PROFILE: 'PLAYER_PROFILE',
  JUST_IN_CLUBHOUSE: 'JUST_IN_CLUBHOUSE',
};
export class SortTileWidgetsDto {
  @IsNotEmpty()
  @IsArray()
  sortOrderList: TileWidget[];

  @IsOptional()
  country: string;
}

export class TileWidget {
  @IsUUID()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @IsOptional()
  country: string;
}

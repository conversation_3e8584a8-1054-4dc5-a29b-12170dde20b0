import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { ActiveTileDTO } from './dto/active-tile.dto';
import { CreateScheduleTilesWidgetDto } from './dto/create-schedule-tiles-widget.dto';
import { CreateTilesWidgetDto } from './dto/create-tiles-widget.dto';
import { ActiveOnSiteDto, ViewedCategoryDto, ViewedProductDto } from './dto/trigger-shop.dto';
import { UpdateTilesWidgetDto } from './dto/update-tiles-widget.dto';
import { SortTileWidgetsDto } from './tile-widget.types';
import { TilesWidgetService } from './tiles-widget.service';

@Controller('tiles-widget')
export class TilesWidgetController {
  constructor(
    private readonly tilesWidgetService: TilesWidgetService,
    private readonly klaviyoService: KlaviyoService
  ) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createTilesWidgetDto: CreateTilesWidgetDto) {
    return this.tilesWidgetService.create(createTilesWidgetDto);
  }

  @UseGuards(AuthGuard)
  @Get()
  findAll(@Req() req: any) {
    return this.tilesWidgetService.findAll(req.query);
  }

  @UseGuards(AuthGuard)
  @Get('player-profiles')
  getPlayerProfiles(@Query('country') country) {
    return this.tilesWidgetService.getPlayerProfiles(country);
  }

  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.tilesWidgetService.findOne(id);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch(':id')
  patchUpdateTilesWidget(@Param('id') id: string, @Body() updateTilesWidgetDto: UpdateTilesWidgetDto, @Req() req: any) {
    return this.tilesWidgetService.patchUpdateTilesWidget(id, updateTilesWidgetDto, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch('/active/:id')
  patchActiveTilesWidget(@Param('id') id: string, @Body() activeDTO: ActiveTileDTO, @Req() req: any) {
    return this.tilesWidgetService.patchActiveTilesWidget(id, activeDTO, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  deleteTilesWidget(@Param('id') id: string, @Req() req: any) {
    return this.tilesWidgetService.deleteTilesWidget(id, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  async postUpdateSortTilesWidget(@Req() request: BaseRequest, @Body() payload: SortTileWidgetsDto) {
    return await this.tilesWidgetService.postSortWidgets(payload, request.user.uid);
  }
  // Schedule Tiles Widget
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('/schedules/add-update')
  addUpdateScheduleTilesWidget(@Body() createDto: CreateScheduleTilesWidgetDto, @Req() request: BaseRequest) {
    return this.tilesWidgetService.addUpdateScheduleTileWidget(createDto, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('/schedules/all')
  getScheduleTilesWidgets(@Query('type') type: string) {
    return this.tilesWidgetService.getListScheduleTilesWidget(type);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('/schedules/:widgetId')
  deleteScheduleTilesWidget(@Param('widgetId') id: string, @Req() req: any) {
    return this.tilesWidgetService.deleteScheduleTileWidget(id, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('/schedules/:widgetId')
  getScheduleTilesWidget(@Param('widgetId') id: string, @Query('type') type: string) {
    return this.tilesWidgetService.findScheduleTileWidget(id, type);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('trigger/view-product')
  async triggerProduct(@Req() req: BaseRequest, @Body() payload: ViewedProductDto) {
    const event = KlaviyoTrackEvents.MYTM_VIEWED_PRODUCT;
    const email = req?.user?.email;
    const data = {
      Categories: payload?.category || '',
      Price: payload?.price,
      Source: 'TeamTM',
      Email: email,
      'Primary Category': payload?.primaryCategory || '',
      'Product ID': payload?.productID,
      'Product Image URL': payload?.imageUrl,
      'Product Name': payload?.productName,
      'Product Page URL': payload?.url,
      'Product UPC': payload?.productUPC || '',
    };
    return this.klaviyoService.triggerEvent(req?.user?.email, event, data);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('trigger/view-category')
  async triggerCategory(@Req() req: BaseRequest, @Body() payload: ViewedCategoryDto) {
    const event = KlaviyoTrackEvents.MYTM_VIEWED_CATEGORY;
    const email = req?.user?.email;
    const data = {
      'Viewed Category': payload.type,
      Email: email,
      Source: 'TeamTM',
    };
    return this.klaviyoService.triggerEvent(req?.user?.email, event, data);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('trigger/active-on-site')
  async triggerActiveSite(@Req() req: BaseRequest, @Body() payload: ActiveOnSiteDto) {
    const event = KlaviyoTrackEvents.MYTM_ACTIVE_ON_SITE;
    const email = req?.user?.email;
    const data = {
      browser: payload?.browser,
      os: payload?.os,
      page: payload?.url,
      Source: 'TeamTM',
      Email: email,
    };
    return this.klaviyoService.triggerEvent(req?.user?.email, event, data);
  }
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CmsService } from 'src/cms/cms.service';
import { PromotionEntity } from 'src/promotion/entities/promotion.entity';
import { SharedModule } from 'src/shared/shared.module';
import { ScheduleTilesWidget } from './entities/schedule-tiles-widget.entity';
import { TilesWidget } from './entities/tiles-widget.entity';
import { TilesWidgetController } from './tiles-widget.controller';
import { TilesWidgetService } from './tiles-widget.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([TilesWidget, ScheduleTilesWidget, PromotionEntity])],
  controllers: [TilesWidgetController],
  providers: [TilesWidgetService, CmsService],
})
export class TilesWidgetModule {}

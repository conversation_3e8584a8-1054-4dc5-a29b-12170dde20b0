import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { EntityManager, IsNull, Not, Repository, getManager } from 'typeorm';
import { CmsService } from 'src/cms/cms.service';
import { PromotionEntity } from 'src/promotion/entities/promotion.entity';
import { isCronJobHandlersEnabled, isProduction } from 'src/utils/cron';
import { ERROR_CODES } from 'src/utils/errors';
import { isAllCountry, isUSCountry } from 'src/utils/transform';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { ActiveTileDTO } from './dto/active-tile.dto';
import { CreateScheduleTilesWidgetDto } from './dto/create-schedule-tiles-widget.dto';
import { CreateTilesWidgetDto } from './dto/create-tiles-widget.dto';
import { UpdateTilesWidgetDto } from './dto/update-tiles-widget.dto';
import { ScheduleTilesWidget } from './entities/schedule-tiles-widget.entity';
import { TilesWidget } from './entities/tiles-widget.entity';
import { SortTileWidgetsDto, TILE_STATUS, TilesCategoryType, TypeWidget, WIDGET_TYPE } from './tile-widget.types';

@Injectable()
export class TilesWidgetService {
  private readonly logger = new Logger(TilesWidgetService.name);
  @InjectRepository(TilesWidget) private readonly tilesWidgetRepo: Repository<TilesWidget>;
  @InjectRepository(PromotionEntity) private readonly promotionRepo: Repository<PromotionEntity>;
  @InjectRepository(ScheduleTilesWidget) private readonly scheduleTilesWidgetRepo: Repository<ScheduleTilesWidget>;
  constructor(
    private readonly config: ConfigService,
    private readonly cmsService: CmsService,
    private readonly apiVersionsService: ApiVersionsService
  ) {}

  async create(createDTO: CreateTilesWidgetDto) {
    if (createDTO.options) {
      createDTO.options = JSON.stringify(createDTO.options);
    }
    if (!createDTO.status) {
      createDTO.status = TILE_STATUS.ACTIVE;
    } else {
      this.validateStatus(createDTO.status);
    }
    createDTO.updatedAt = new Date();
    try {
      const tileWidget = await this.tilesWidgetRepo.save(this.tilesWidgetRepo.create(createDTO));
      if (tileWidget) {
        await this.apiVersionsService.updateVersionTitleWidget(
          tileWidget.type,
          tileWidget.countries,
          tileWidget.createdBy
        );
      }
      return tileWidget;
    } catch (err) {
      console.log(err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }
  validateStatus(status: string) {
    if (![TILE_STATUS.ACTIVE, TILE_STATUS.INACTIVE].includes(status)) {
      throw new BadRequestException({ errorMessage: 'Status must be [ACTIVE, INACTIVE]' });
    }
  }
  async findAll(options: any) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 20;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.tilesWidgetRepo.createQueryBuilder('widget').where({});
    if (options.type) {
      query.andWhere({ type: options.type });
    }
    if (options.status) {
      query.andWhere({ status: options.status });
    }
    const country = options.country;
    let conditionCountry = ' 1 = 1 ';
    conditionCountry = this.getQueryCountryCondition(country, conditionCountry);
    query.andWhere(conditionCountry);

    if (options.sort) {
      query.orderBy('sortOrder', 'ASC');
      query.addOrderBy('updatedAt', 'DESC');
    } else {
      query.orderBy('createdAt', 'ASC');
    }
    const [widgets, total] = await query
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .select([
        'widget.id',
        'widget.type',
        'widget.title',
        'widget.countries',
        'widget.description',
        'widget.options',
        'widget.ctaText',
        'widget.ctaLink',
        'widget.imageLink',
        'widget.status',
        'widget.videoLink',
        'widget.sortOrder',
      ])
      .getManyAndCount();
    await this.filterCMSContents(options, widgets);
    return { widgets, total };
  }

  async filterCMSContents(options: any, widgets: TilesWidget[]) {
    if (options.type && options.type == TypeWidget.THE_DAILY) {
      const lstTilesTheDaily = widgets.filter((widget) => {
        if (widget.options) {
          const options = JSON.parse(widget.options);
          return options.category == TilesCategoryType.CMS;
        }
      });
      if (!lstTilesTheDaily) {
        return;
      }
      await Promise.all(
        lstTilesTheDaily.map(async (widget) => {
          if (widget.options) {
            try {
              const options = JSON.parse(widget.options);
              const cmsContent = await this.cmsService.getCmsContent(options.ctaCMS, options.cmsType);
              if (cmsContent?.data) {
                widget['extraData'] = { content: cmsContent.data };
              }
            } catch (e) {
              console.log(e);
            }
          }
          return widget;
        })
      );
    }
  }

  getQueryCountryCondition(country: any, conditionCountry: string) {
    if (country) {
      if (isAllCountry(country)) {
        return conditionCountry;
      }
      if (isUSCountry(country)) {
        conditionCountry += ` AND (countries LIKE '%${country}%' OR countries IS NULL) `;
        return conditionCountry;
      }
      conditionCountry += ` AND countries LIKE '%${country}%' `;
      return conditionCountry;
    }
    conditionCountry = `(countries LIKE '%US%'  OR countries IS NULL) `;
    return conditionCountry;
  }

  async findOne(id: string) {
    try {
      return await this.tilesWidgetRepo.findOne(id);
    } catch (err) {
      console.log(err.message);
      return null;
    }
  }

  async patchUpdateTilesWidget(id: string, updateDTO: UpdateTilesWidgetDto, user: any) {
    const widget = await this.findOne(id);
    const widgetType = widget.type;
    if (!widget) {
      throw new NotFoundException({ errorMessage: `Widget NotFound!` });
    }
    if (updateDTO.status) {
      this.validateStatus(updateDTO.status);
    }
    if (updateDTO.options) {
      updateDTO.options = JSON.stringify(updateDTO.options);
    }
    updateDTO.updatedAt = new Date();
    updateDTO.createdBy = user.uid;
    delete updateDTO.type;
    try {
      await this.tilesWidgetRepo.update(id, updateDTO);
      await this.apiVersionsService.updateVersionTitleWidget(widgetType, widget.countries, widget.createdBy);
      return await this.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }
  async patchActiveTilesWidget(id: string, activeDTO: ActiveTileDTO, user: any) {
    const widget = await this.findOne(id);
    const widgetType = widget.type;
    if (!widget) {
      throw new NotFoundException({ errorMessage: `Widget NotFound!` });
    }
    const updateDTO = new UpdateTilesWidgetDto();
    updateDTO.updatedAt = new Date();
    updateDTO.createdBy = user.uid;
    updateDTO.status = activeDTO.active ? TILE_STATUS.ACTIVE : TILE_STATUS.INACTIVE;
    delete updateDTO.type;
    try {
      await this.tilesWidgetRepo.update(id, updateDTO);
      await this.apiVersionsService.updateVersionTitleWidget(widgetType, widget.countries, widget.createdBy);
      return await this.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async deleteTilesWidget(id: string, user: any) {
    const widget = await this.findOne(id);
    const widgetType = widget.type;
    if (!widget) {
      throw new NotFoundException({ errorMessage: `Widget NotFound!` });
    }
    const updateDTO = new UpdateTilesWidgetDto();
    updateDTO.deletedAt = new Date();
    updateDTO.deletedBy = user.uid;
    try {
      await this.tilesWidgetRepo.update(id, updateDTO);
      await this.apiVersionsService.updateVersionTitleWidget(widgetType, widget.countries, widget.createdBy);
      return { success: true };
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }
  async postSortWidgets(payload: SortTileWidgetsDto, userId: string) {
    try {
      const listWidgetId = [];
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listTileWidget = payload.sortOrderList;
          await Promise.all(
            listTileWidget.map(async (widget) => {
              const checkConstantSortOrder = listTileWidget.filter(
                (otherWidget) => otherWidget.sortOrder === widget.sortOrder
              );
              if (checkConstantSortOrder.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }

              const id = widget.id;
              listWidgetId.push(id);
              const widgetNeedUpdate = await this.findOne(id);
              if (!widgetNeedUpdate) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
                  errorMessage: `TilesWidget ${id} not found`,
                });
              }
              await transactionalEntityManager.update(
                TilesWidget,
                { id: widgetNeedUpdate.id },
                { sortOrder: widget.sortOrder, updatedBy: userId, updatedAt: new Date() }
              );
            })
          );
        })
        .then(async () => {
          const tileWidgets = await this.tilesWidgetRepo
            .createQueryBuilder('widget')
            .whereInIds(listWidgetId)
            .orderBy({
              'widget.sortOrder': 'ASC',
            })
            .getMany();
          if (tileWidgets && tileWidgets.length > 0) {
            const tileWidget = tileWidgets[0];
            await this.apiVersionsService.updateVersionTitleWidget(
              tileWidget.type,
              payload.country,
              tileWidget.createdBy
            );
          }
        });
    } catch (e) {
      return e;
    }
  }
  async findScheduleTileWidget(widgetId: string, widgetType: string) {
    if (widgetType.toUpperCase() == WIDGET_TYPE.MARQUEE) {
      const widget = await this.scheduleTilesWidgetRepo
        .createQueryBuilder('schedule')
        .where({ widgetId })
        .leftJoinAndSelect(TilesWidget, 'widget', 'widget.id = schedule.widgetId')
        .select([
          'schedule.*',
          'type',
          'title',
          'countries',
          'description',
          'options',
          'ctaText',
          'ctaLink',
          'imageLink',
          'status',
          'videoLink',
          'sortOrder',
        ])
        .getRawOne();
      return widget;
    }
    if (widgetType.toUpperCase() == WIDGET_TYPE.PROMOTION) {
      const widget = await this.scheduleTilesWidgetRepo
        .createQueryBuilder('schedule')
        .where({ widgetId })
        .leftJoinAndSelect(PromotionEntity, 'pe', 'pe.id = schedule.widgetId')
        .select(['schedule.*', 'title'])
        .getRawOne();
      return widget;
    }
  }
  async addUpdateScheduleTileWidget(payload: CreateScheduleTilesWidgetDto, userId: string) {
    this.validateDateActiveInActiveWidget(payload);
    await this.validateExistWidget(payload);
    const widget = await this.findScheduleTileWidget(payload.widgetId, payload.widgetType);
    if (widget) {
      return await this.updateScheduleTileWidget(widget.widgetId, payload, userId);
    }
    try {
      if (payload.activeAt) {
        payload.isRunActive = false;
        payload.isRunInActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      } else {
        payload.isRunActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      }

      payload.createdAt = new Date();
      payload.createdBy = userId;
      await this.scheduleTilesWidgetRepo.save(this.scheduleTilesWidgetRepo.create(payload));
      return await this.findScheduleTileWidget(payload.widgetId, payload.widgetType);
    } catch (error) {
      throw new BadRequestException({ errorMessage: error.message });
    }
  }
  async validateExistWidget(payload: CreateScheduleTilesWidgetDto) {
    if (payload.widgetType === WIDGET_TYPE.MARQUEE) {
      try {
        const isExistTileWidget = await this.tilesWidgetRepo.findOne(payload.widgetId);
        if (!isExistTileWidget) {
          throw new NotFoundException(`TileWidget not found!`);
        }
      } catch (e) {
        console.log(e);
        throw new NotFoundException(`TileWidget not found!`);
      }
    }
    if (payload.widgetType === WIDGET_TYPE.PROMOTION) {
      try {
        const isExistPromotion = await this.promotionRepo.findOne(payload.widgetId);
        if (!isExistPromotion) {
          throw new NotFoundException(`Promotion not found!`);
        }
      } catch (e) {
        console.log(e);
        throw new NotFoundException(`Promotion not found!`);
      }
    }
  }

  private validateDateActiveInActiveWidget(createDto: CreateScheduleTilesWidgetDto) {
    if (!createDto.inActiveAt && !createDto.activeAt) {
      throw new BadRequestException({ errorMessage: 'Should have InActiveAt or ActiveAt' });
    }
    this.validateCurrentDateWithActiveDate(createDto.activeAt, 'ActiveAt');
    this.validateCurrentDateWithActiveDate(createDto.inActiveAt, 'InActiveAt');
    if (createDto.inActiveAt && createDto.activeAt) {
      const dateInActive = moment(createDto.inActiveAt);
      const dateActive = moment(createDto.activeAt);
      if (dateActive.diff(dateInActive) > 0) {
        throw new BadRequestException({ errorMessage: 'InActiveAt should greater than ActiveAt' });
      }
    }
  }
  private validateCurrentDateWithActiveDate(strDate, labelDate) {
    const currentDate = moment();
    if (strDate) {
      const dateCompare = moment(strDate);
      if (dateCompare.isBefore(currentDate)) {
        throw new BadRequestException({ errorMessage: `${labelDate} should be greater than now!` });
      }
    }
  }

  async updateScheduleTileWidget(widgetId: string, payload: CreateScheduleTilesWidgetDto, userId: string) {
    try {
      payload.updatedAt = new Date();
      payload.updatedBy = userId;
      if (payload?.activeAt?.toString() == '') {
        payload.activeAt = null;
        payload.isRunActive = true;
      }
      if (payload?.inActiveAt?.toString() == '') {
        payload.inActiveAt = null;
        payload.isRunInActive = true;
      }
      if (payload.activeAt) {
        payload.isRunActive = false;
        payload.isRunInActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      } else {
        payload.isRunActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      }
      await this.scheduleTilesWidgetRepo.update({ widgetId }, payload);
      return await this.findScheduleTileWidget(widgetId, payload.widgetType);
    } catch (error) {
      throw new BadRequestException({ errorMessage: error.message });
    }
  }
  async deleteScheduleTileWidget(widgetId: string, userId: string) {
    try {
      const widget = await this.scheduleTilesWidgetRepo.findOne(widgetId);
      if (!widget) {
        throw new NotFoundException({ errorMessage: `Schedule Widget NotFound!` });
      }
      await this.scheduleTilesWidgetRepo.update({ widgetId }, { deletedAt: new Date(), deletedBy: userId });
      return { success: true };
    } catch (error) {
      throw new BadRequestException({ errorMessage: error.message });
    }
  }
  async getListScheduleTilesWidget(widgetType: string) {
    let widgets;
    if (widgetType?.toUpperCase() == WIDGET_TYPE.MARQUEE) {
      widgets = await this.scheduleTilesWidgetRepo
        .createQueryBuilder('sw')
        .innerJoinAndSelect(TilesWidget, 'tw', 'sw.widgetId = tw.id')
        .select(['sw.*', 'type', 'title'])
        .getRawMany();
    } else if (widgetType?.toUpperCase() == WIDGET_TYPE.PROMOTION) {
      widgets = await this.scheduleTilesWidgetRepo
        .createQueryBuilder('sw')
        .innerJoinAndSelect(PromotionEntity, 'pe', 'sw.widgetId = pe.id')
        .select(['sw.*', 'title'])
        .getRawMany();
    }
    return widgets;
  }

  @Cron(isProduction ? CronExpression.EVERY_10_MINUTES : CronExpression.EVERY_10_SECONDS)
  async runActiveWidget() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.log(`START runActiveWidget at ${new Date().toISOString()}`);
    const widgets = await this.scheduleTilesWidgetRepo.find({ where: { isRunActive: false, activeAt: Not(IsNull()) } });
    if (widgets) {
      for (const widget of widgets) {
        try {
          // eslint-disable-next-line @typescript-eslint/no-var-requires
          const momentTz = require('moment-timezone');
          const currentDate = momentTz().utc();
          this.logger.log(`CURRENT TIME PST: ${currentDate.toISOString()}`);
          const convertTime = momentTz(widget.activeAt).format('YYYY-MM-DD HH:mm');
          const activeDate = momentTz.tz(convertTime, 'UTC');

          if (activeDate.diff(currentDate) >= 0) {
            continue;
          }
          if (widget.widgetType == WIDGET_TYPE.MARQUEE) {
            const tileDetail = await this.tilesWidgetRepo.findOne(widget.widgetId);
            if (!tileDetail) {
              console.log(`TileWidget not existed!`);
              continue;
            }
            await this.tilesWidgetRepo.update(widget.widgetId, {
              sortOrder: 1,
              status: TILE_STATUS.ACTIVE,
              updatedAt: new Date(),
            });
            await this.apiVersionsService.updateVersionTitleWidget(
              tileDetail.type,
              tileDetail.countries,
              tileDetail.createdBy
            );
          } else if (widget.widgetType == WIDGET_TYPE.PROMOTION) {
            const promotionDetail = await this.promotionRepo.findOne(widget.widgetId);
            if (!promotionDetail) {
              console.log(`Promotion not existed!`);
              continue;
            }
            await this.promotionRepo.update(widget.widgetId, {
              status: TILE_STATUS.ACTIVE,
              updatedAt: new Date(),
            });
          }

          await this.scheduleTilesWidgetRepo.update(widget.id, { isRunActive: true });
          this.logger.log(`Trigger Active ${widget.id} done!`);
        } catch (error) {
          this.logger.error(`Trigger Active ${widget.id} failed`);
          console.error(error);
        }
      }
    }
    this.logger.log(`FINISH runActiveWidget at ${new Date().toISOString()}`);
  }
  @Cron(isProduction ? CronExpression.EVERY_10_MINUTES : CronExpression.EVERY_10_SECONDS)
  async runInActiveWidget() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.log(`START runInActiveWidget at ${new Date().toISOString()}`);
    const widgets = await this.scheduleTilesWidgetRepo.find({
      where: { isRunActive: true, isRunInActive: false, inActiveAt: Not(IsNull()) },
    });
    if (widgets) {
      for (const widget of widgets) {
        try {
          // eslint-disable-next-line @typescript-eslint/no-var-requires
          const momentTz = require('moment-timezone');
          const currentDate = momentTz().utc();
          this.logger.log(`CURRENT TIME PST: ${currentDate.toISOString()}`);
          const convertTime = momentTz(widget.inActiveAt).format('YYYY-MM-DD HH:mm');
          const inActiveDate = momentTz.tz(convertTime, 'UTC');

          if (currentDate.diff(inActiveDate) <= 0) {
            continue;
          }

          if (widget.widgetType == WIDGET_TYPE.MARQUEE) {
            const tileDetail = await this.tilesWidgetRepo.findOne(widget.widgetId);
            if (!tileDetail) {
              console.log(`TileWidget not existed!`);
              continue;
            }
            await this.tilesWidgetRepo.update(widget.widgetId, {
              status: TILE_STATUS.INACTIVE,
              updatedAt: new Date(),
            });
            await this.apiVersionsService.updateVersionTitleWidget(
              tileDetail.type,
              tileDetail.countries,
              tileDetail.createdBy
            );
          } else if (widget.widgetType == WIDGET_TYPE.PROMOTION) {
            const promotionDetail = await this.promotionRepo.findOne(widget.widgetId);
            if (!promotionDetail) {
              console.log(`Promotion not existed!`);
              continue;
            }
            await this.promotionRepo.update(widget.widgetId, {
              status: TILE_STATUS.INACTIVE,
              updatedAt: new Date(),
            });
          }
          await this.scheduleTilesWidgetRepo.update(widget.id, { isRunInActive: true });
          this.logger.log(`Trigger InActive ${widget.id} done!`);
        } catch (error) {
          this.logger.error(`Trigger InActive ${widget.id} failed!`);
          console.error(error);
        }
      }
    }
    this.logger.log(`FINISH runInActiveWidget at ${new Date().toISOString()}`);
  }

  async getPlayerProfiles(country) {
    try {
      const query = this.tilesWidgetRepo.createQueryBuilder('widget').where({ type: TypeWidget.PLAYER_PROFILE });
      let conditionCountry = ' 1 = 1 ';
      conditionCountry = this.getQueryCountryCondition(country, conditionCountry);
      query.andWhere(conditionCountry);
      const ignorePlayers = ['Sung Hyun Park'];
      const tileWidgets = await query.select(['id', 'countries', 'imageLink', 'options']).getRawMany();
      const cmsTourPlayers = await this.cmsService.getTourPlayers(country);
      const results = [];
      if (cmsTourPlayers && cmsTourPlayers.length) {
        for (const tourPlayer of cmsTourPlayers) {
          if (ignorePlayers.includes(tourPlayer.title)) {
            continue;
          }
          const findData = tileWidgets.find((tileWidget: TilesWidget) => {
            const options = tileWidget.options ? JSON.parse(tileWidget.options) : null;
            return options && options?.tourPlayerId === tourPlayer?.id;
          });
          if (findData) {
            tourPlayer.widgetId = findData.id;
            tourPlayer.imageLink = findData.imageLink;
            tourPlayer.type = TypeWidget.PLAYER_PROFILE;
          }
          results.push(tourPlayer);
        }
      }
      return {
        widgets: results,
        total: results.length,
      };
    } catch (error) {
      this.logger.error(`getAdminPlayerProfiles failed: ${JSON.stringify(error)}`);
      console.error(error);
    }
  }
}

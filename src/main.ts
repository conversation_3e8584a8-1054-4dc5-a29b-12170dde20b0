import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import * as bodyParser from 'body-parser';
import { AppModule } from './app.module';
import initBullBoard from './bullboard';
import AppConfig from './config/app';

// @ts-ignore
process.env.NODE_TLS_REJECT_UNAUTHORIZED = 0;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('v1');
  app.enableCors();
  app.use(bodyParser.json({ limit: '20mb' }));
  app.use(bodyParser.urlencoded({ limit: '20mb', extended: true }));
  app.useGlobalPipes(new ValidationPipe());
  initBullBoard(app);
  const server = await app.listen(AppConfig.port, '0.0.0.0');
  server.setTimeout(300000);
  console.log(`Application is running on: ${await app.getUrl()}`);
}

bootstrap().then((r) => r);

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import delay from 'delay';
import { Repository } from 'typeorm';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { CdmService } from 'src/cdm/cdm.service';
import { KlaviyoService } from 'src/klaviyo/klaviyo.service';
import { DurationName, Plan, PlanName } from './access-code.type';

export enum UsersIdentifyProcessorQueueName {
  IDENTITY = 'users-identify',
}

type ScanJob = Job<{ users: [] }>;

@Processor('users-identify')
export class UsersIdentifyProcessor {
  private readonly logger = new Logger(UsersIdentifyProcessor.name);
  constructor(
    private readonly cdmService: CdmService,
    private readonly klaviyoService: KlaviyoService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>
  ) {}

  @Process({
    name: UsersIdentifyProcessorQueueName.IDENTITY,
    concurrency: 1,
  })
  async UserIdentify(job: ScanJob): Promise<any> {
    this.logger.debug('UserIdentify start');
    const users = job.data.users;

    if (!users) {
      this.logger.debug('users is not found');
      return;
    }

    for (let index = 0; index < users.length; index++) {
      const email = users[index]['email'];
      const monthsOfYear = 12;
      await delay(4000);
      const user = await this.userRepo.findOne({ email });
      const myTMPermission = await this.cdmService.getMyTMPermission(user.id, email);
      const { myTMSubscriptionLevel } = user;
      const subscriptionExpirationDate = myTMPermission?.subscriptionExpirationDate;
      const subscriptionLength = myTMPermission?.subscriptionLength;
      const subscriptionStartDate = myTMPermission?.subscriptionStartDate;
      let subscription = '';
      let subscriptionTier = '';
      // transform myTMSubscriptionLevel number to name
      if (myTMSubscriptionLevel == Plan.CHAMPION) subscription = PlanName.CHAMPION;
      if (myTMSubscriptionLevel == Plan.LEGEND) subscription = PlanName.LEGEND;

      // transform subscriptionLength number to yearly or monthly
      if (subscriptionLength >= monthsOfYear) subscriptionTier = DurationName.YEARLY;
      if (subscriptionLength > 0 && subscriptionLength < monthsOfYear) subscriptionTier = DurationName.MONTHLY;

      this.klaviyoService.identify(email, {
        mytm_subscriber_current_subscription: subscription,
        mytm_subscriber_current_tier: subscriptionTier,
        mytm_subscription_expiration_date: subscriptionExpirationDate,
        mytm_subscription_start_date: subscriptionStartDate,
      });
    }

    this.logger.debug('UserIdentify end');
  }
}

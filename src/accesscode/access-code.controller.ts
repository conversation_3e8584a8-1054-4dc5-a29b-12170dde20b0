import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes } from '@nestjs/swagger';
import { ConfigService } from 'nestjs-config';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { ERROR_CODES } from '../utils/errors';
import { ACCESS_CODE_ERROR } from './access-code.constants';
import { AccessCodeService } from './access-code.service';
import {
  DeactivateAccessCodeDto,
  GenerateAccessCodeDto,
  GenerateMultiAccessCodeDto,
  VerifyAccessCodeDto,
} from './access-code.type';
import { GrantAccessCodeService } from './grant-access-code.service';

@Controller()
export class AccessCodeController {
  constructor(
    private accessCodeService: AccessCodeService,
    private grantAccessCodeService: GrantAccessCodeService,
    private readonly config: ConfigService
  ) {
    this.config = config;
  }

  @Post('admin/access-code/generate')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async generateAccessCode(@Req() request: Request & { user: any }, @Body() data: GenerateAccessCodeDto): Promise<any> {
    return this.accessCodeService.generateAccessCode(data, request.user.uid);
  }

  @Post('admin/access-code/generate-multi')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async generateMultiAccessCode(
    @Req() request: Request & { user: any },
    @Body() payload: GenerateMultiAccessCodeDto
  ): Promise<any> {
    return this.accessCodeService.generateMultiAccessCode(payload, request.user.uid);
  }

  @Post('access-code/grant-exist-users')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    if (!file)
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.FILE_NOT_FOUND,
        errorMessage: ACCESS_CODE_ERROR.FILE_NOT_FOUND,
      });
    if (['application/vnd.ms-excel', 'text/csv'].indexOf(file.mimetype) === -1)
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.FILE_EXTENSION_NOT_VALID,
        errorMessage: ACCESS_CODE_ERROR.FILE_EXTENSION_NOT_VALID,
      });

    await this.grantAccessCodeService.grantAccessCodeToExistUser(file);
    return 'OK';
  }

  @Post('access-code/grant-exist-users-queue')
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFileQueue(@UploadedFile() file: Express.Multer.File) {
    if (!file)
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.FILE_NOT_FOUND,
        errorMessage: ACCESS_CODE_ERROR.FILE_NOT_FOUND,
      });
    if (['application/vnd.ms-excel', 'text/csv'].indexOf(file.mimetype) === -1)
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.FILE_EXTENSION_NOT_VALID,
        errorMessage: ACCESS_CODE_ERROR.FILE_EXTENSION_NOT_VALID,
      });

    await this.grantAccessCodeService.grantAccessCodeQueue(file);
  }

  @Get('access-code/users-identify-queue')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async usersIdentify() {
    await this.grantAccessCodeService.scanUsersIdentifyQueue();
  }

  @Post('access-code/verify')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async verifyAccessCode(@Req() request: Request & { user: any }, @Body() payload: VerifyAccessCodeDto): Promise<any> {
    const result = await this.accessCodeService.verifyAccessCode(payload, request.user.uid, request.user.email);

    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }

  @Get('admin/access-code/all')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getAllAccessCode(
    @Query('take') take,
    @Query('page') page,
    @Query('strSearch') strSearch,
    @Query('duration') duration,
    @Query('plan') plan,
    @Query('target') target,
    @Query('inputDuration') inputDuration,
    @Query('filterEmail') filterEmail,
    @Query('status') status
  ): Promise<any> {
    return this.accessCodeService.getAllAccessCode(take, page, {
      strSearch,
      duration,
      plan,
      target,
      inputDuration,
      filterEmail,
      status,
    });
  }

  @Post('admin/access-code/deactivate')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async deactivateAccessCode(
    @Req() request: Request & { user: any },
    @Body() payload: DeactivateAccessCodeDto
  ): Promise<any> {
    const result = await this.accessCodeService.deactivateAccessCode(payload, request.user.uid);

    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }

  @Get('admin/access-code/get-purpose')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getPurpose(@Req() request: Request & { user: any }): Promise<any> {
    const result = await this.accessCodeService.getPurpose();

    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }
}

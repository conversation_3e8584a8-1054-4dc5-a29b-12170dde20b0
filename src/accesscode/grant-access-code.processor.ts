import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import delay from 'delay';
import { Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { AccessCodeService } from './access-code.service';
import { ExtensionUserEntity } from './entities/extension-user.entity';

type GrantAccessCodeJobDto = Job<{ accessCodeAvailable: [] }>;

export enum GrantAccessCodeProcessorQueueName {
  GRANT_ACCESS_CODE = 'grand-access-code',
}

@Processor('access-code')
export class GrantAccessCodeProcessor {
  private readonly logger = new Logger(GrantAccessCodeProcessor.name);
  constructor(
    private readonly accessCodeService: AccessCodeService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(ExtensionUserEntity) private readonly extUserRepo: Repository<ExtensionUserEntity>
  ) {}

  @Process({
    name: GrantAccessCodeProcessorQueueName.GRANT_ACCESS_CODE,
    concurrency: 1,
  })
  async GrantAccessCode(job: GrantAccessCodeJobDto): Promise<any> {
    const accessCodeAvailable = job.data.accessCodeAvailable;
    const take = job.data.accessCodeAvailable.length;

    const usersExist = await this.userRepo
      .createQueryBuilder('u')
      .innerJoin(ExtensionUserEntity, 'ex', 'u.email = ex.email')
      .where('ex.accessCode IS NULL')
      .skip(0)
      .take(take)
      .getMany();

    if (!usersExist) {
      this.logger.debug('usersExist is not found');
      return;
    }

    for (let i = 0; i < accessCodeAvailable.length; i++) {
      const user = usersExist[i];
      if (!user) break;
      const accessCode = accessCodeAvailable[i];
      this.logger.debug(`Verify access code for ${user.email}`);
      await delay(3000);
      const verifyResult = await this.accessCodeService.verifyAccessCode({ code: accessCode }, user.id, user.email);
      if (!verifyResult?.internalErrorCode) {
        await this.extUserRepo.update({ email: user.email }, { accessCode: accessCode });
      }

      this.logger.debug(verifyResult);
    }
  }
}

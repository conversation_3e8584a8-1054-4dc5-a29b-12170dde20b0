import { IsE<PERSON>, <PERSON>NotEmpty, <PERSON>Number, IsOptional, IsString, IsUUID, NotEquals } from 'class-validator';

export enum AccessTarget {
  VIP = 'VIP',
  MEDIA = 'MEDIA',
  EMPLOYEE = 'EMPLOYEE',
}

export enum Plan {
  CHAMPION = 1,
  LEGEND = 2,
  RANDOM = 3,
}

export enum PlanName {
  CHAMPION = 'Champion',
  LEGEND = 'Legend',
}

export enum Duration {
  RANDOM = 0,
}

export enum CodeUsed {
  USED = 1,
  UNUSED = 0,
}

export enum InputDuration {
  INPUT = 1,
  NOT_INPUT = 0,
}

export enum Duration {
  MONTHLY = 1,
  YEARLY = 2,
}

export enum DurationName {
  MONTHLY = 'Monthly',
  YEARLY = 'Yearly',
}

export enum AccessCodeStatus {
  AVAILABLE = 'AVAILABLE',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  INACTIVE_BY_UPGRADE = 'INACTIVE_BY_UPGRADE',
  EXPIRED = 'EXPIRED',
}

export enum ActionSort {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class VerifyAccessCodeDto {
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class GenerateAccessCodeDto {
  @IsNumber()
  @IsNotEmpty()
  subscriptionDuration: number;

  @IsNumber()
  @IsNotEmpty()
  subscriptionPlan: number;
}

export class GenerateMultiAccessCodeDto {
  @IsNumber()
  @IsNotEmpty()
  @NotEquals(0)
  quantity: number;

  @IsNumber()
  @IsNotEmpty()
  @IsEnum(Plan)
  plan: number;

  @IsNumber()
  @IsNotEmpty()
  duration: number;

  @IsString()
  @IsOptional()
  target: string;
}

export class DeactivateAccessCodeDto {
  @IsString()
  code: string;

  @IsUUID('4')
  userId: string;
}

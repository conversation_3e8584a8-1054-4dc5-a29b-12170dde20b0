export const ACCESS_CODE_ERROR = Object.freeze({
  ACCESS_CODE_NOT_FOUND: 'The access code is not found.',
  FILE_NOT_FOUND: 'File is not found.',
  FILE_EXTENSION_NOT_VALID: 'File extension is not valid.',
  ACCESS_CODE_IS_ALREADY_USED: 'The access code has already been used.',
  ACCESS_CODE_EXPIRED: 'The access code has expired.',
  USER_USING_ACCESS_CODE: 'Another access code is being used',
  ACCESS_CODE_UNUSED: 'The access code is unused',
  ACCESS_CODE_INACTIVE: 'The access code is inactive',
  USER_USING_ANOTHER_PLAN: 'Failed to upgrade plan with the access code.',
  PLAN_NOT_USE_ACCESS_CODE: `The subscription plan can’t be verified with the access code.`,
  ACCESS_CODE_INVALID: `Can't deactivate the access code.`,
});

export const RANDOM_ACCESS_CODE_FROM_STR = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

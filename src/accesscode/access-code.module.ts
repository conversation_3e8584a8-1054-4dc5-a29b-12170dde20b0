import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { SharedModule } from '../shared/shared.module';
import { SYSTEM_STAGE, SYSTEM_TAG } from '../utils/constants';
import { AccessCodeController } from './access-code.controller';
import { AccessCodeService } from './access-code.service';
import { AccessCodeEntity } from './entities/access-code.entity';
import { ExtensionUserEntity } from './entities/extension-user.entity';
import { GrantAccessCodeProcessor } from './grant-access-code.processor';
import { GrantAccessCodeService } from './grant-access-code.service';
import { UsersIdentifyProcessor } from './user-identify.processor';

let processors = [];

if (process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [...processors];
}
if (process.env.STAGE !== SYSTEM_STAGE.development) {
  processors = [...processors, GrantAccessCodeProcessor, UsersIdentifyProcessor];
}

@Module({
  imports: [
    SharedModule,
    TypeOrmModule.forFeature([AccessCodeEntity, ExtensionUserEntity, UserEntity]),
    BullModule.registerQueue({
      name: 'access-code',
    }),
    BullModule.registerQueue({
      name: 'users-identify',
    }),
  ],
  controllers: [AccessCodeController],
  providers: [...processors, AccessCodeService, GrantAccessCodeService],
  exports: [AccessCodeService],
})
export class AccessCodeModule {}

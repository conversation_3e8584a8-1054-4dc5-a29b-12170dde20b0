import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _ from 'lodash';
import chunk from 'lodash/chunk';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { <PERSON><PERSON><PERSON>, <PERSON>tityManager, Is<PERSON>ull, <PERSON><PERSON><PERSON>, Not, Repository, getManager } from 'typeorm';
import { v4 } from 'uuid';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { SUBSCRIPTION_SERVICES } from 'src/payment/payment.constants';
import { CdmService } from '../cdm/cdm.service';
import { SubscriptionEventLog } from '../payment/entities/subscription-event-log.entity';
import { SubscriptionEntity } from '../payment/entities/subscription.entity';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { PLANS } from '../utils/plans';
import { ACCESS_CODE_ERROR, RANDOM_ACCESS_CODE_FROM_STR } from './access-code.constants';
import {
  AccessCodeStatus,
  ActionSort,
  DeactivateAccessCodeDto,
  Duration,
  GenerateMultiAccessCodeDto,
  InputDuration,
  Plan,
  VerifyAccessCodeDto,
} from './access-code.type';
import { AccessCodeEntity } from './entities/access-code.entity';
import { ExtensionUserEntity } from './entities/extension-user.entity';

@Injectable()
export class AccessCodeService {
  private readonly logger = new Logger(AccessCodeService.name);
  constructor(
    private readonly config: ConfigService,
    private readonly cdmService: CdmService,
    @InjectRepository(AccessCodeEntity)
    private readonly accessCodeRepo: Repository<AccessCodeEntity>,
    @InjectRepository(SubscriptionEntity)
    private readonly subscriptionRepo: Repository<SubscriptionEntity>,
    @InjectRepository(ExtensionUserEntity)
    private readonly extUserRepo: Repository<ExtensionUserEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserPermissionsEntity)
    private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(SubscriptionEventLog)
    private readonly subscriptionEventLogRepo: Repository<SubscriptionEventLog>
  ) {
    this.config = config;
  }

  async generateAccessCode(payload: any, userId: string): Promise<any> {
    const accessCodeRecordEntity = this.createAccessCodeEntity(
      {
        code: this.generate(6),
        subscriptionDuration: payload.subscriptionDuration,
        subscriptionPlan: payload.subscriptionPlan,
        target: payload?.target,
        status: AccessCodeStatus.AVAILABLE,
      },
      userId
    );

    return await this.accessCodeRepo.save(accessCodeRecordEntity);
  }

  async generateMultiAccessCode(payload: GenerateMultiAccessCodeDto, userId: string): Promise<any> {
    const subsDurationRandom = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 24, 36, 48, 60];
    const subsPlanRandom = [1, 2];
    const subscriptionPlanList = payload.plan === Plan.RANDOM ? subsPlanRandom : [payload.plan];
    const subscriptionDurationList = payload.duration === Duration.RANDOM ? subsDurationRandom : [payload.duration];
    const listEntityRecord: AccessCodeEntity[] = [];
    for (let i = 0; i < payload.quantity; i++) {
      const accessCodeRecordEntity = this.createAccessCodeEntity(
        {
          code: this.generate(6),
          subscriptionDuration: subscriptionDurationList[Math.floor(Math.random() * subscriptionDurationList.length)],
          subscriptionPlan: subscriptionPlanList[Math.floor(Math.random() * subscriptionPlanList.length)],
          target: payload?.target,
          status: AccessCodeStatus.AVAILABLE,
        },
        userId
      );
      listEntityRecord.push(accessCodeRecordEntity);
    }
    const chunkAccessCodes = chunk(listEntityRecord, 250);
    for (const chunkAccessCodesItem of chunkAccessCodes) {
      await this.accessCodeRepo.insert(chunkAccessCodesItem);
    }
    return listEntityRecord;
  }

  async grantAccessCodeToSignUpUser(user) {
    const accessCodeAvailable = await this.accessCodeRepo.findOne({ status: AccessCodeStatus.AVAILABLE });

    if (!accessCodeAvailable) {
      this.logger.debug('accessCodeAvailable is not found');
      return;
    }

    const extendUser = await this.extUserRepo.findOne({ email: user.email });

    if (!extendUser) {
      this.logger.debug('extendUser is not found');
      return;
    }

    this.logger.debug(`Verify access code for ${user.email}`);
    const verifyResult = await this.verifyAccessCode({ code: accessCodeAvailable.code }, user.id, user.email);
    this.logger.debug(verifyResult);
  }

  async verifyAccessCode(payload: VerifyAccessCodeDto, userId: string, userEmail: string): Promise<any> {
    return await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const recordAC = await this.getRecordByCode(payload.code);
      const userPermission = await this.getPermissionFromMyTM(userId, userEmail);
      const validation = this.validateVerifyAccessCode(
        recordAC,
        userPermission.tmAccessCode,
        userPermission.myTMSubscriptionLevel
      );
      if (validation) {
        return validation;
      }
      const { subscriptionPlan, subscriptionDuration } = recordAC;
      const currentSubsPlan = userPermission.tmAccessCode
        ? await this.getPrevSubscriptionUseAccessCode(userId)
        : await this.getSubscriptionWithPlanFee(userId);
      if (currentSubsPlan) {
        let shouldUseAccessCode = true;
        const isExpired = moment().isAfter(moment(new Date(userPermission.subscriptionExpirationDate)));
        if (!isExpired) {
          shouldUseAccessCode = AccessCodeService.isUpgrade(
            userPermission.myTMSubscriptionLevel,
            userPermission.subscriptionLength,
            subscriptionPlan,
            subscriptionDuration
          );
        }
        if (!shouldUseAccessCode) {
          return {
            internalErrorCode: ERROR_CODES.ACCESS_CODE_INVALID,
            errorMessage: ACCESS_CODE_ERROR.USER_USING_ANOTHER_PLAN,
          };
        }
        await transactionalEntityManager.update(SubscriptionEntity, { id: currentSubsPlan.id }, { isChecked: true });
        if (userPermission.tmAccessCode) {
          await transactionalEntityManager.update(
            AccessCodeEntity,
            {
              userId,
              code: currentSubsPlan?.fromAccessCode,
            },
            { status: AccessCodeStatus.INACTIVE_BY_UPGRADE }
          );
        }
      }

      const expirationDate = moment(moment().add(recordAC?.subscriptionDuration, 'month')).toDate();
      const usedDate = moment().toDate();

      const subscriptionPayload = this.createSubscriptionEntityWithFromAccessCode(
        {
          expirationDate,
          fromAccessCode: payload.code,
          usedDate,
          isChecked: false,
        },
        userId
      );

      await transactionalEntityManager.save(SubscriptionEntity, subscriptionPayload);

      const consumerData = await this.applyAccessCodeToCdm(recordAC, userEmail, userId, expirationDate);
      const accessCodeEntity = this.createAccessCodeEntity(
        {
          id: recordAC.id,
          userId,
          expirationDate,
          usedDate,
          firstName: consumerData.firstName,
          lastName: consumerData.lastName,
          status: AccessCodeStatus.ACTIVE,
        },
        userId,
        recordAC
      );

      const accessCodeSaved = await transactionalEntityManager.save(AccessCodeEntity, accessCodeEntity);
      return this.toRecordAccessCode(accessCodeSaved);
    });
  }

  validateVerifyAccessCode(recordAC: AccessCodeEntity, tmAccessCode: boolean, myTMSubscriptionLevel: any) {
    if (!recordAC) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_NOT_FOUND,
        errorMessage: ACCESS_CODE_ERROR.ACCESS_CODE_NOT_FOUND,
      };
    }
    if (recordAC.userId || recordAC.status !== AccessCodeStatus.AVAILABLE) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_IS_ALREADY_USED,
        errorMessage: ACCESS_CODE_ERROR.ACCESS_CODE_IS_ALREADY_USED,
      };
    }

    if (
      this.config.get('app.replacePlanFromAccessCode') !== 'enabled' &&
      tmAccessCode &&
      myTMSubscriptionLevel != null &&
      myTMSubscriptionLevel != 0
    ) {
      return {
        internalErrorCode: ERROR_CODES.USER_USING_ACCESS_CODE,
        errorMessage: ACCESS_CODE_ERROR.USER_USING_ACCESS_CODE,
      };
    }

    return null;
  }

  async getAllAccessCode(
    take = 10,
    page = 1,
    optionsSearch: {
      strSearch: string;
      duration: number;
      plan: number;
      inputDuration: number;
      target: string;
      filterEmail?: ActionSort;
      status?: AccessCodeStatus;
    }
  ) {
    const nTake = Number(take);
    const nPage = Number(page);

    let query = this.accessCodeRepo.createQueryBuilder('a').leftJoinAndSelect('a.user', 'user');

    const accessCodeDurationMonthly = [];
    const MAX_YEAR = 10;
    for (let i = 1; i <= MAX_YEAR; i++) {
      accessCodeDurationMonthly.push(12 * i); // month * YEAR
    }

    if (optionsSearch?.duration) {
      if (Number(optionsSearch?.inputDuration) === InputDuration.INPUT) {
        query = query.andWhere('a.subscriptionDuration = :duration', { duration: optionsSearch?.duration });
      } else {
        if (Number(optionsSearch?.duration) === Duration.MONTHLY) {
          query = query.andWhere('a.subscriptionDuration NOT IN (:...accessCodeDurationMonthly)', {
            accessCodeDurationMonthly,
          });
        } else if (Number(optionsSearch?.duration) === Duration.YEARLY) {
          query = query.andWhere('a.subscriptionDuration IN (:...accessCodeDurationMonthly)', {
            accessCodeDurationMonthly,
          });
        }
      }
    }

    if (optionsSearch?.plan) {
      query = query.andWhere('subscriptionPlan = :plan', { plan: optionsSearch?.plan });
    }

    if (optionsSearch?.target) {
      query = query.andWhere('target = :target', { target: optionsSearch?.target });
    }

    if (optionsSearch?.status) {
      query = query.andWhere('status IN (:...status)', {
        status: optionsSearch?.status.split(',').map((r) => r.trim()),
      });
    }

    if (optionsSearch?.strSearch) {
      const strSearch = `%${optionsSearch.strSearch || ''}%`;
      query = query.andWhere(
        new Brackets((qb) => {
          qb.where('a.code like :strSearch', { strSearch })
            .orWhere('user.email like :strSearch', { strSearch })
            .orWhere('a.firstName like :strSearch', { strSearch })
            .orWhere('a.lastName like :strSearch', { strSearch })
            .orWhere(`CONCAT(a.firstName,' ',a.lastName) like :strSearch`, { strSearch })
            .orWhere(`CONCAT(a.lastName,' ',a.firstName) like :strSearch`, { strSearch });
        })
      );
    }

    if (
      optionsSearch?.filterEmail &&
      (optionsSearch?.filterEmail === ActionSort.ASC || optionsSearch?.filterEmail === ActionSort.DESC)
    ) {
      query = query
        .addSelect(
          `CASE WHEN user.email = NULL THEN null ELSE SUBSTRING(user.email, CHARINDEX('@', user.email) + 1, LEN(user.email)) END`,
          'extensionEmail'
        )
        .addSelect(`CASE WHEN user.email is null THEN 2 ELSE 1 END`, 'sortEmail')
        .orderBy('sortEmail', 'ASC')
        .addOrderBy('extensionEmail', optionsSearch?.filterEmail);
    }

    const [accessCodes, total] = await query
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .addOrderBy('a.createdAt', 'DESC')
      .getManyAndCount();

    if (accessCodes.length === 0) {
      return {
        total: 0,
        data: [],
      };
    }

    return {
      total,
      take: Number(take),
      page: Number(page),
      data: accessCodes.map((accessCode) => {
        const result = {
          ...accessCode,
          fullName: AccessCodeService.getFullNameFromUser({
            firstName: accessCode.firstName,
            lastName: accessCode.lastName,
          }),
          email: accessCode?.user?.email,
        };
        delete result.user;

        return result;
      }),
    };
  }

  async deactivateAccessCode(payload: DeactivateAccessCodeDto, userId: string): Promise<any> {
    return await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const recordAC = await this.getRecordByCode(payload.code, ['user']);
      const currentSubscription = await this.getPrevSubscriptionUseAccessCode(payload.userId);
      const tmAccessCode = recordAC?.user && (await this.getMyTmAccessCodeFromCdm(recordAC?.user?.email));
      const validation = this.validateDeactivateAccessCode(recordAC, payload.userId, currentSubscription, tmAccessCode);
      if (validation) {
        return validation;
      }
      const preSubsPlanFee = await this.getSubscriptionWithPlanFee(userId);
      if (preSubsPlanFee) {
        const prevPlan = AccessCodeService.getCurrentPlans(preSubsPlanFee);
        if (prevPlan) {
          await this.savePlanToCDM(
            userId,
            recordAC.user.email,
            {
              level: prevPlan.level,
              duration: prevPlan.duration,
            },
            preSubsPlanFee.expirationDate,
            false
          );
          await transactionalEntityManager.update(SubscriptionEntity, { id: preSubsPlanFee.id }, { isChecked: false });
        }
      } else {
        await this.resetToFreePlan(recordAC.user.email);
      }
      await transactionalEntityManager.softDelete(SubscriptionEntity, {
        userId: recordAC.userId,
        fromAccessCode: Not(IsNull()),
      });
      const accessCodeEntity = this.createAccessCodeEntity(
        {
          id: recordAC.id,
          status: AccessCodeStatus.INACTIVE,
        },
        userId,
        recordAC
      );

      const accessCodeSaved = await transactionalEntityManager.save(AccessCodeEntity, accessCodeEntity);

      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: recordAC?.id,
            updatedBy: userId,
          },
          manager: transactionalEntityManager,
        },
        AccessCodeEntity.name,
        recordAC,
        accessCodeSaved
      );

      return this.toRecordAccessCode(accessCodeSaved);
    });
  }

  validateDeactivateAccessCode(
    recordAC: AccessCodeEntity,
    userId: string,
    currentSubscription: SubscriptionEntity,
    tmAccessCode: boolean
  ) {
    if (!recordAC) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_NOT_FOUND,
        errorMessage: ACCESS_CODE_ERROR.ACCESS_CODE_NOT_FOUND,
      };
    }
    if (!tmAccessCode) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_INVALID,
        errorMessage: ACCESS_CODE_ERROR.PLAN_NOT_USE_ACCESS_CODE,
      };
    }
    if (!recordAC.userId) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_UNUSED,
        errorMessage: ACCESS_CODE_ERROR.ACCESS_CODE_UNUSED,
      };
    }
    if (
      recordAC.userId.toUpperCase() !== userId.toUpperCase() ||
      !currentSubscription ||
      recordAC.code.toUpperCase() !== currentSubscription.fromAccessCode.toUpperCase()
    ) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_INVALID,
        errorMessage: ACCESS_CODE_ERROR.ACCESS_CODE_INVALID,
      };
    }
    if (recordAC.status !== AccessCodeStatus.ACTIVE) {
      return {
        internalErrorCode: ERROR_CODES.ACCESS_CODE_INACTIVE,
        errorMessage: ACCESS_CODE_ERROR.ACCESS_CODE_INACTIVE,
      };
    }

    return null;
  }

  createSubscriptionEntityWithFromAccessCode(payload: any, userId: string) {
    const subscriptionEntity = new SubscriptionEntity();
    subscriptionEntity.id = v4();
    subscriptionEntity.userId = userId;
    subscriptionEntity.purchaseDate = new Date(payload?.usedDate).toISOString();
    subscriptionEntity.autoRenewing = false;
    subscriptionEntity.expirationDate = payload?.expirationDate;
    subscriptionEntity.isChecked = payload?.isChecked;
    subscriptionEntity.fromAccessCode = payload?.fromAccessCode;
    subscriptionEntity.createdBy = userId;

    return subscriptionEntity;
  }

  createAccessCodeEntity(payload: any, userId?: string, record?: AccessCodeEntity) {
    const accessCodeEntity = new AccessCodeEntity();
    accessCodeEntity.id = payload?.id || v4();
    accessCodeEntity.code = payload?.code || record?.code;
    accessCodeEntity.subscriptionDuration = payload?.subscriptionDuration || record?.subscriptionDuration;
    accessCodeEntity.subscriptionPlan = payload?.subscriptionPlan || record?.subscriptionPlan;
    accessCodeEntity.userId = payload?.userId || record?.userId;
    accessCodeEntity.expirationDate = payload?.expirationDate || record?.expirationDate;
    accessCodeEntity.target = payload?.target || record?.target;
    accessCodeEntity.usedDate = payload?.usedDate || record?.usedDate;
    accessCodeEntity.firstName = payload?.firstName || record?.firstName;
    accessCodeEntity.lastName = payload?.lastName || record?.lastName;
    accessCodeEntity.status = payload?.status || record?.status;
    accessCodeEntity.createdAt = new Date();
    if (userId) {
      if (payload.id) {
        accessCodeEntity.createdBy = userId;
      } else {
        accessCodeEntity.updatedBy = userId;
      }
    }
    return accessCodeEntity;
  }

  async applyAccessCodeToCdm(accessCode: AccessCodeEntity, email: string, userId: string, expirationDate: any) {
    const { subscriptionPlan, subscriptionDuration } = accessCode;
    try {
      return await this.savePlanToCDM(
        userId,
        email,
        {
          level: subscriptionPlan,
          duration: subscriptionDuration,
        },
        expirationDate,
        true,
        SUBSCRIPTION_SERVICES.MyTaylorMade
      );
    } catch (error) {
      this.logger.error(error);
    }
  }

  async resetToFreePlan(email: string) {
    const user = await this.userRepo.findOne({ email });
    let shouldResetToFreePlan = true;
    const myTMPermission = await this.cdmService.getMyTMPermission(user.id, email);
    const subscriptionExpirationDate = myTMPermission?.subscriptionExpirationDate;
    const tmAccessCode = user.tmAccessCode;
    if (
      user.myTMSubscriptionLevel === 0 ||
      !tmAccessCode ||
      !subscriptionExpirationDate ||
      moment().isAfter(moment(new Date(subscriptionExpirationDate)))
    ) {
      shouldResetToFreePlan = false;
    }
    if (!shouldResetToFreePlan) {
      return false;
    }

    try {
      const permissionList = this.cdmService.getPermissionListByPlan(0);
      await this.userRepo.update({ email }, { myTMSubscriptionLevel: 0, tmAccessCode: false });
      await this.userPermissionRepo.update(
        { userId: user.id },
        {
          subscriptionLength: 0,
          subscriptionExpirationDate: new Date().toISOString(),
          isSyncCDM: false,
          ...permissionList,
        }
      );
      await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
        myTMSubscriptionLevel: 0,
        subscriptionLength: 0,
        tmAccessCode: false,
        subscriptionExpirationDate: new Date().toISOString(),
        ...permissionList,
      });
      return true;
    } catch (error) {
      console.log(error);
    }
  }

  async getRecordByCode(code: string, relations?: string[]) {
    return await this.accessCodeRepo.findOne({
      where: {
        code,
      },
      relations,
    });
  }

  async getCodeByUserId(userId: string) {
    return await this.accessCodeRepo.findOne({ userId }, { order: { createdAt: 'DESC' } });
  }

  generate(nchars: number) {
    const strRandomLength = RANDOM_ACCESS_CODE_FROM_STR.length;
    let code = '';
    for (let i = 0; i < nchars; i++) {
      const ch = RANDOM_ACCESS_CODE_FROM_STR.charAt(Math.floor(Math.random() * strRandomLength));
      code = `${code}${ch}`;
    }

    return code;
  }

  toRecordAccessCode(record: AccessCodeEntity) {
    return {
      id: record?.id,
      code: record?.code,
      subscriptionDuration: record?.subscriptionDuration,
      subscriptionPlan: record?.subscriptionPlan,
      userId: record?.userId,
      expirationDate: record?.expirationDate,
      target: record?.target,
      usedDate: record?.usedDate,
      status: record?.status,
    };
  }

  async savePlanToCDM(
    userId: string,
    email: string,
    plan: any,
    expirationDate: any,
    tmAccessCode: boolean,
    subscriptionService?: string
  ) {
    try {
      const permissionList = this.cdmService.getPermissionListByPlan(plan.level || 0);
      const subscriptionStartDate = new Date().toISOString();
      const payload: any = {
        myTMSubscriptionLevel: plan.level,
        subscriptionLength: plan.duration,
        subscriptionExpirationDate: expirationDate,
        ...permissionList,
        subscriptionStartDate,
        tmAccessCode,
        subscriptionService,
      };
      await this.userRepo.update({ email }, { myTMSubscriptionLevel: plan.level, tmAccessCode, subscriptionService });
      await this.userPermissionRepo.update(
        { userId },
        {
          subscriptionLength: plan.duration,
          subscriptionExpirationDate: expirationDate,
          subscriptionStartDate,
          isSyncCDM: false,
          ...permissionList,
        }
      );
      const account = await this.cdmService.createOrUpdateAccount(
        email,
        this.config.get('app.defaultRegionId'),
        payload
      );
      return this.cdmService.transformConsumerData(account);
    } catch (error) {
      this.logger.error(error);
    }
  }

  private async getSubscriptionWithPlanFee(userId: string): Promise<SubscriptionEntity> {
    const dateNow = moment().toISOString();
    return await this.subscriptionRepo.findOne(
      {
        userId,
        fromAccessCode: IsNull(),
        expirationDate: MoreThan(dateNow),
      },
      { order: { createdAt: 'DESC' } }
    );
  }

  private async getPrevSubscriptionUseAccessCode(userId: string): Promise<SubscriptionEntity> {
    const dateNow = moment().toISOString();
    return await this.subscriptionRepo.findOne(
      {
        userId,
        fromAccessCode: Not(IsNull()),
        expirationDate: MoreThan(dateNow),
        isChecked: false,
      },
      { order: { createdAt: 'DESC' } }
    );
  }

  private async getPermissionFromMyTM(userId: string, userEmail: string) {
    try {
      const myTMPermission = await this.cdmService.getMyTMPermission(userId, userEmail);
      const user = await this.userRepo.findOne({ id: userId });
      const { myTMSubscriptionLevel, subscriptionService, tmAccessCode } = user;
      const subscriptionExpirationDate = myTMPermission?.subscriptionExpirationDate;
      const subscriptionLength = myTMPermission?.subscriptionLength;

      return {
        myTMSubscriptionLevel,
        subscriptionService,
        subscriptionExpirationDate,
        subscriptionLength,
        tmAccessCode,
      };
    } catch (error) {
      console.log(error);
    }
  }

  private async getMyTmAccessCodeFromCdm(userEmail) {
    try {
      const cdmAccount = await this.cdmService.getAccountByEmail(userEmail, this.config.get('app.defaultRegionId'));
      return cdmAccount.tmAccessCode;
    } catch (error) {
      console.log(error);
    }
  }

  private static isUpgrade(
    myTMSubscriptionLevel: number,
    cdmSubscriptionLength: number,
    subscriptionPlan: number,
    subscriptionDuration: number
  ): boolean {
    if (myTMSubscriptionLevel < subscriptionPlan) {
      return true;
    } else return myTMSubscriptionLevel === subscriptionPlan && cdmSubscriptionLength < subscriptionDuration;
  }

  private static getFullNameFromUser(user: any) {
    let fullName = null;
    if (user && (!_.isNil(user?.firstName) || !_.isNil(user?.lastName))) {
      fullName = `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
    }
    return fullName;
  }

  private static getCurrentPlans(subscription: SubscriptionEntity) {
    return PLANS[subscription.productId];
  }

  async getPurpose(): Promise<any> {
    const recordAccessCode = await this.accessCodeRepo
      .createQueryBuilder('ac')
      .distinct(true)
      .select('ac.target')
      .orderBy('ac.target')
      .getRawMany();
    if (recordAccessCode.length > 0) {
      return recordAccessCode.map((r) => ({ target: r.ac_target }));
    }

    return [];
  }
}

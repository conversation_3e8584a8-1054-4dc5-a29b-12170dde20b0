import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../../auth/entities/user.entity';

@Entity('AccessCodes')
export class AccessCodeEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  code: string;

  @Column()
  subscriptionPlan: number;

  @Column()
  subscriptionDuration: number;

  @Column()
  target: string;

  @Column()
  userId: string;

  @Column()
  usedDate: Date;

  @Column()
  expirationDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @OneToOne((type) => UserEntity)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column()
  status: string;
}

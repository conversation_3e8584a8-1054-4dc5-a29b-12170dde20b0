import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import delay from 'delay';
import { Repository } from 'typeorm';
import xlsx from 'xlsx';
import { UserEntity } from '../auth/entities/user.entity';
import { AccessCodeService } from './access-code.service';
import { AccessCodeStatus } from './access-code.type';
import { AccessCodeEntity } from './entities/access-code.entity';
import { ExtensionUserEntity } from './entities/extension-user.entity';
import { GrantAccessCodeProcessorQueueName } from './grant-access-code.processor';
import { UsersIdentifyProcessorQueueName } from './user-identify.processor';

@Injectable()
export class GrantAccessCodeService {
  private readonly logger = new Logger(AccessCodeService.name);
  constructor(
    private readonly accessCodeService: AccessCodeService,
    @InjectQueue('access-code') private accessCodeQueue: Queue,
    @InjectQueue('users-identify') private usersIdentifyQueue: Queue,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(AccessCodeEntity) private readonly accessCodeRepo: Repository<AccessCodeEntity>,
    @InjectRepository(ExtensionUserEntity) private readonly extUserRepo: Repository<ExtensionUserEntity>
  ) {}

  getAccessCodeFromCsv(file: Express.Multer.File) {
    const fileBuffer = file.buffer;
    const readFile = xlsx.read(fileBuffer, { cellDates: true });
    const sheet = readFile.SheetNames;
    const header = ['no', 'code', 'subscriptionPlan', 'subscriptionDuration', 'createdAt'];

    const accessCodeCSV: any = xlsx.utils.sheet_to_json(readFile.Sheets[sheet[0]], { header, raw: true, defval: null });

    if (!accessCodeCSV.length) return;

    accessCodeCSV.shift();
    return accessCodeCSV.map((c) => c.code);
  }

  async grantAccessCodeQueue(file: Express.Multer.File) {
    return;
    // const accessCodeAvailable = this.getAccessCodeFromCsv(file);
    // await this.accessCodeQueue.add(GrantAccessCodeProcessorQueueName.GRANT_ACCESS_CODE, { accessCodeAvailable });
  }

  async scanUsersIdentifyQueue() {
    const countUsers = await this.countUsersNotExpired();
    const count = countUsers[0].count;
    const limit = 100;
    let offset = 1;

    for (let i = 0; i < count; i += limit) {
      const skip = (offset - 1) * limit;
      const users = await this.getUsersNotExpired(limit, skip);
      await this.usersIdentifyQueue.add(UsersIdentifyProcessorQueueName.IDENTITY, { users });
      offset++;
    }
  }

  async countUsersNotExpired() {
    const sql = `SELECT count(1) as count
    FROM Users u inner join Subscriptions s on u.id = s.userId
    where s.expirationDate >= getdate()
    and s.createdAt = 
    (SELECT max(s1.createdAt) FROM Subscriptions s1 
    where s1.userId = s.userId)`;
    return await this.userRepo.query(sql);
  }

  async getUsersNotExpired(limit, skip) {
    const sql = `SELECT u.email
    FROM Users u inner join Subscriptions s on u.id = s.userId
    where s.expirationDate >= getdate()
    and s.createdAt = 
    (SELECT max(s1.createdAt) FROM Subscriptions s1 
    where s1.userId = s.userId) order by s.userId OFFSET ${skip} ROWS FETCH NEXT ${limit} ROWS ONLY`;
    return await this.userRepo.query(sql);
  }

  async grantAccessCodeToExistUser(file: Express.Multer.File) {
    const accessCodeAvailable = this.getAccessCodeFromCsv(file);
    const take = accessCodeAvailable.length;

    const usersExist = await this.userRepo
      .createQueryBuilder('u')
      .innerJoin(ExtensionUserEntity, 'ex', 'u.email = ex.email')
      .where('ex.accessCode IS NULL')
      .skip(0)
      .take(take)
      .getMany();

    if (!usersExist) {
      this.logger.debug('usersExist is not found');
      return;
    }

    for (let i = 0; i < accessCodeAvailable.length; i++) {
      const user = usersExist[i];
      if (!user) break;
      const accessCode = accessCodeAvailable[i];
      this.logger.debug(`Verify access code for ${user.email}`);
      await delay(4000);
      const verifyResult = await this.accessCodeService.verifyAccessCode({ code: accessCode }, user.id, user.email);
      if (!verifyResult?.internalErrorCode) {
        await this.extUserRepo.update({ email: user.email }, { accessCode: accessCode });
      }
      verifyResult['email'] = user.email;
      verifyResult['accessCode'] = accessCode;
      this.logger.debug(verifyResult);
    }
  }

  async grantAccessCodeToSignUpUser(user) {
    const accessCodeAvailable = await this.accessCodeRepo.findOne({ status: AccessCodeStatus.AVAILABLE });

    if (!accessCodeAvailable) {
      this.logger.debug('accessCodeAvailable is not found');
      return;
    }

    const extendUser = await this.extUserRepo.findOne({ email: user.email });

    if (!extendUser) {
      this.logger.debug('extendUser is not found');
      return;
    }

    this.logger.debug(`Verify access code for ${user.email}`);
    const verifyResult = await this.accessCodeService.verifyAccessCode(
      { code: accessCodeAvailable.code },
      user.id,
      user.email
    );
    this.logger.debug(verifyResult);
  }

  async updateAccountPermission() {}
}

import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import jwt from 'jsonwebtoken';
import { ConfigService } from 'nestjs-config';
import { AuthService } from 'src/auth/auth.service';
import { Role } from 'src/auth/roles.decorator';
import { ERROR_CODES } from 'src/utils/errors';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly config: ConfigService,
    private readonly authService: AuthService,
    private reflector: Reflector
  ) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    let accessedRoles = this.reflector.get<number[]>('AccessedRoles', context.getHandler());
    if (!accessedRoles || accessedRoles.length === 0) {
      accessedRoles = [
        Role.USER,
        Role.ADMIN,
        Role.SUPER_ADMIN,
        Role.CUSTOMER_SERVICE,
        Role.TTB_AGENT,
        Role.TTB_MANAGER,
      ];
    }
    const req = context.switchToHttp().getRequest();
    if (!req.headers.authorization) {
      this.throwUnauthorizedError();
    }
    const token = req.headers.authorization.replace('Bearer ', '');
    let decoded: any;
    try {
      decoded = jwt.verify(token, this.config.get('app.jwtSecret'));
    } catch (e) {
      this.throwUnauthorizedError();
    }
    if (decoded.type !== 'AT') {
      this.throwUnauthorizedError();
    }
    const isTokenBlacklisted = await this.authService.isTokenBlacklisted(token, false);
    if (isTokenBlacklisted) {
      this.throwUnauthorizedError();
    }
    const user = await this.authService.getUserById(decoded.uid);
    if (!user) {
      this.throwUserNotFoundError();
    }
    if (!accessedRoles.includes(user.role)) {
      this.throwPermissionDeniedError();
    }
    if (user.deletedAt) {
      this.throwUserActiveError();
    }
    req.user = decoded;
    try {
      Logger.debug(`User Requested: ${decoded?.uid} - ${decoded?.email}`);
    } catch (error) {}

    req.language = req.headers?.language || user.language;
    req.country = req.headers?.country;
    req.token = token;
    return true;
  }
  throwUnauthorizedError() {
    throw new UnauthorizedException({ code: ERROR_CODES.UNAUTHORIZED, errorMessage: 'Unauthorized' });
  }
  throwPermissionDeniedError() {
    throw new ForbiddenException({ code: ERROR_CODES.PERMISSION_DENIED, errorMessage: 'Permission denied!' });
  }
  throwUserNotFoundError() {
    throw new ForbiddenException({ code: ERROR_CODES.USER_NOT_FOUND, errorMessage: 'Unable to authenticate!' });
  }
  throwUserActiveError() {
    throw new UnauthorizedException({
      code: ERROR_CODES.UNAUTHORIZED,
      errorMessage: 'Your account needs to be reactivated!',
    });
  }
}

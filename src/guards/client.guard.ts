import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ClientService } from 'src/client/client.service';
import { ERROR_CODES } from 'src/utils/errors';

export const CLIENTS = {
  MY_TM: 'MY_TM',
  EBS: 'EBS',
  GVC: 'GVC',
  ECOM: 'ECOM',
  SWING_INDEX: 'SWING_INDEX',
  MFE: 'MFE',
  MRP: 'MRP',
  DE: 'DE',
  MRP_APP: 'MRP_APP',
  ADMIN_PORTAL: 'ADMIN_PORTAL',
  SWING_INDEX_APP: 'SWING_INDEX_APP',
};

@Injectable()
export class ClientGuard implements CanActivate {
  constructor(private clientService: ClientService, private reflector: Reflector) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    let accessedClients = this.reflector.get<string[]>('AccessedClients', context.getHandler());
    if (!accessedClients || accessedClients.length === 0) {
      accessedClients = [CLIENTS.MY_TM, CLIENTS.SWING_INDEX, CLIENTS.MRP_APP, CLIENTS.SWING_INDEX_APP];
    }
    const req = context.switchToHttp().getRequest();
    if (!req.headers.clientid) {
      this.throwForbiddenError();
    }
    const clientId = req.headers.clientid;
    const client = await this.clientService.getClient(clientId);
    req.client = client;
    return client && accessedClients.includes(client.name) && client.active;
  }
  throwForbiddenError() {
    throw new ForbiddenException({ code: ERROR_CODES.INVALID_CLIENT, errorMessage: 'Invalid client!' });
  }
}

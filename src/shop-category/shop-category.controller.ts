import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { ActiveShopCategoryDto, SortShopCategoriesDto } from './dto/active-shop-category.dto';
import { CreateShopCategoryDto } from './dto/create-shop-category.dto';
import { UpdateShopCategoryDto } from './dto/update-shop-category.dto';
import { ShopCategoryService } from './shop-category.service';

@Controller('shop-category')
export class ShopCategoryController {
  constructor(private readonly shopCategoryService: ShopCategoryService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('get-list') // paginate
  async getList(@Request() req: any, @Query('country') country?: string) {
    return this.shopCategoryService.getListPaginate(req.query, country);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get()
  async getListAll(@Request() req: any, @Query('country') country?: string) {
    return this.shopCategoryService.getListAll(country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createShopCategoryDto: CreateShopCategoryDto, @Request() req: BaseRequest) {
    return this.shopCategoryService.create(createShopCategoryDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateShopCategoryDto: UpdateShopCategoryDto, @Request() req: BaseRequest) {
    return this.shopCategoryService.updateShopCategory(id, updateShopCategoryDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch('/status/:id')
  patchActiveShopCategory(@Param('id') id: string, @Body() activeDTO: ActiveShopCategoryDto, @Req() req: any) {
    return this.shopCategoryService.patchActiveShopCategory(id, activeDTO, req.user);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  async postUpdateSortShopCategory(@Req() request: BaseRequest, @Body() payload: SortShopCategoriesDto) {
    return await this.shopCategoryService.postSortShopCategory(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  deleteshopCategory(@Param('id') id: string, @Req() req: any) {
    return this.shopCategoryService.deleteshopCategory(id, req.user);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get(':id')
  getshopCategory(@Param('id') shopCategoryId: string) {
    return this.shopCategoryService.findOneShopCategory(shopCategoryId);
  }
}

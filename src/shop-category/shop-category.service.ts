import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { isEmpty } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { Not, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { isCanadaCountry, isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { ERROR_CODES } from '../utils/errors';
import { ActiveShopCategoryDto, SortShopCategoriesDto } from './dto/active-shop-category.dto';
import { CreateShopCategoryDto } from './dto/create-shop-category.dto';
import { UpdateShopCategoryDto } from './dto/update-shop-category.dto';
import { ShopCategoryEntity } from './entities/shop-category.entity';

@Injectable()
export class ShopCategoryService {
  private readonly logger = new Logger(ShopCategoryService.name);
  @InjectRepository(ShopCategoryEntity) private readonly shopCategoryRepo: Repository<ShopCategoryEntity>;
  constructor(private readonly config: ConfigService, private readonly apiVersionsService: ApiVersionsService) {}

  async getListAll(countryCode?: string) {
    const shopCategories = await this.shopCategoryRepo.find({
      where: {
        disabled: false,
        country: isCanadaCountry(countryCode) ? 'CAN' : 'USA',
      },
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
    });
    if (isEmpty(shopCategories)) {
      return { shopCategories: [] };
    }
    return { shopCategories };
  }

  async getListPaginate({ ...options }: any, countryCode?: string) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const skip = (nPage - 1) * nTake;
    const [shopCategories, total] = await this.shopCategoryRepo.findAndCount({
      where: {
        country: isCanadaCountry(countryCode) ? 'CAN' : 'USA',
      },
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
      take: nTake,
      skip,
    });

    if (total === 0) {
      return {
        total: 0,
        shopCategories: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      shopCategories,
    };
  }

  async create(createShopCategoryDto: CreateShopCategoryDto, createdBy: string) {
    const shopCategory = await this.shopCategoryRepo.findOne({
      where: {
        title: createShopCategoryDto.title.trim(),
        country: createShopCategoryDto.country || 'USA',
      },
    });
    if (shopCategory) {
      throw new BadRequestException(`Title must be unique.`);
    }
    const country = isCanadaCountry(createShopCategoryDto?.country) ? 'CAN' : 'USA';
    const data = {
      id: v4(),
      title: createShopCategoryDto.title.trim(),
      ctaLink: createShopCategoryDto.ctaLink,
      country: country,
      disabled: createShopCategoryDto.disabled === false ? false : true,
      sortOrder: createShopCategoryDto.sortOrder || 1,
      createdBy,
    };
    try {
      const shopCategory = this.shopCategoryRepo.save(plainToClass(ShopCategoryEntity, data));
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.SHOP_CATEGORIES, country, createdBy);
      return shopCategory;
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async updateShopCategory(id: string, payload: UpdateShopCategoryDto, userId: string) {
    try {
      const shopCategory = await this.shopCategoryRepo.findOne(id);
      if (isEmpty(shopCategory)) {
        throw new NotFoundException(`Fail to edit. Please try again!`);
      }
      if (payload.title) {
        const isExisted = await this.shopCategoryRepo.findOne({
          where: {
            id: Not(id),
            title: payload.title.trim(),
            country: shopCategory.country || 'USA',
          },
        });
        if (isExisted) {
          throw new BadRequestException(`Title must be unique.`);
        }
        payload.title = payload.title.trim();
      }
      payload.updatedBy = userId;
      payload.updatedAt = new Date();
      await this.shopCategoryRepo.update(id, payload);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.SHOP_CATEGORIES, shopCategory.country, userId);
      return this.shopCategoryRepo.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async patchActiveShopCategory(id, activeDTO: ActiveShopCategoryDto, user) {
    const shopCategory = await this.shopCategoryRepo.findOne(id);
    if (!shopCategory) {
      throw new NotFoundException({ errorMessage: `shopCategory NotFound!` });
    }
    try {
      await this.shopCategoryRepo.update(id, {
        ...activeDTO,
        updatedBy: user?.uid,
        updatedAt: new Date(),
      });
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.SHOP_CATEGORIES,
        shopCategory.country,
        user?.uid || null
      );
      return this.shopCategoryRepo.findOne(id);
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async deleteshopCategory(id: string, user: any) {
    const shopCategory = await this.shopCategoryRepo.findOne(id);
    if (!shopCategory) {
      throw new NotFoundException({ errorMessage: `shopCategory NotFound!` });
    }
    try {
      await this.shopCategoryRepo.update(id, {
        deletedBy: user?.uid,
        deletedAt: new Date(),
      });
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.SHOP_CATEGORIES,
        shopCategory.country,
        user?.uid || null
      );
      return {
        success: true,
      };
    } catch (err) {
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async findOneShopCategory(id) {
    return this.shopCategoryRepo.findOne(id);
  }

  async postSortShopCategory(payload: SortShopCategoriesDto, userId) {
    try {
      const listshopCategoryId = [];
      const listshopCategory = payload.sortOrderList;
      await Promise.all(
        listshopCategory.map(async (shopCategory) => {
          const checkConstantSortOrder = listshopCategory.filter(
            (otherTS) => otherTS.sortOrder === shopCategory.sortOrder
          );
          if (checkConstantSortOrder.length > 1) {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
              errorMessage: `SortOrder exist more than one record`,
            });
          }

          const id = shopCategory.id;
          if (isEmpty(id) || id === 'null') {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
              errorMessage: `shopCategory ${id} not found`,
            });
          }
          listshopCategoryId.push(id);
          const TsNeedUpdate = await this.shopCategoryRepo.findOne(id);
          if (!TsNeedUpdate) {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
              errorMessage: `shopCategory ${id} not found`,
            });
          }
          await this.shopCategoryRepo.update(
            { id: TsNeedUpdate.id },
            { sortOrder: shopCategory.sortOrder, updatedBy: userId, updatedAt: new Date() }
          );
        })
      );

      const shopCategories = await this.shopCategoryRepo
        .createQueryBuilder('shopCategories')
        .whereInIds(listshopCategoryId)
        .orderBy({
          'shopCategories.sortOrder': 'ASC',
        })
        .getMany();
      if (!isEmpty(shopCategories)) {
        const shopCategory = shopCategories[0];
        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.SHOP_CATEGORIES, shopCategory.country, userId);
      }

      return shopCategories;
    } catch (e) {
      return e;
    }
  }
}

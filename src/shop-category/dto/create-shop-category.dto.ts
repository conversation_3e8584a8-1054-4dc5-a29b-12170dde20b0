import { IsNotEmpty, IsOptional } from 'class-validator';

export class CreateShopCategoryDto {
  @IsNotEmpty()
  title: string;

  @IsNotEmpty()
  ctaLink: string;

  @IsOptional()
  country: string;

  @IsOptional()
  disabled: boolean;

  @IsOptional()
  sortOrder: number;

  @IsOptional()
  createdBy: string;

  @IsOptional()
  deletedBy: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  updatedAt: Date;
}

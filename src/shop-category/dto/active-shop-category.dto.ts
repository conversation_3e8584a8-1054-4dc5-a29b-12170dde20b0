import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class ActiveShopCategoryDto {
  @IsNotEmpty()
  @IsBoolean()
  disabled: boolean;
}

export class SortShopCategoriesDto {
  @IsNotEmpty()
  @IsArray()
  sortOrderList: ShopCategory[];

  @IsOptional()
  country: string;
}

export class ShopCategory {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @IsOptional()
  country: string;
}

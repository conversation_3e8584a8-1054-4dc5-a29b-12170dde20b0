import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { ShopCategoryEntity } from './entities/shop-category.entity';
import { ShopCategoryController } from './shop-category.controller';
import { ShopCategoryService } from './shop-category.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([ShopCategoryEntity])],
  controllers: [ShopCategoryController],
  providers: [ShopCategoryService],
})
export class ShopCategoryModule {}

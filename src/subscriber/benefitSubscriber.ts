import { EntitySubscriberInterface, EventSubscriber, InsertEvent } from 'typeorm';
import { BENEFIT_ENTITY } from 'src/benefits/benefits.constants';
import { BenefitEntity } from 'src/benefits/entities/benefit.entity';
import { CommonSubscriber } from './commonSubscriber';

@EventSubscriber()
export class BenefitSubscriber implements EntitySubscriberInterface<BenefitEntity> {
  /**
   * Indicates that this subscriber only listen to BenefitEntity events.
   */
  listenTo() {
    return BenefitEntity;
  }

  async afterInsert(event: InsertEvent<BenefitEntity>) {
    await CommonSubscriber.activityLogCommandInsert(event, BENEFIT_ENTITY);
  }
}

import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { TourTrashEntity, TourTrashStatus } from 'src/tourtrash/entities/tour-trash.entity';
import { CommonSubscriber } from './commonSubscriber';

@EventSubscriber()
export class TourTrashSubscriber implements EntitySubscriberInterface<TourTrashEntity> {
  /**
   * Indicates that this subscriber only listen to TourTrashEntity events.
   */
  listenTo() {
    return TourTrashEntity;
  }

  async afterInsert(event: InsertEvent<TourTrashEntity>) {
    await CommonSubscriber.activityLogCommandInsert(event, TourTrashEntity.name);
  }

  async afterUpdate(event: UpdateEvent<TourTrashEntity>) {
    if (
      event?.updatedColumns?.some((x) => x.propertyName === 'status' || x.propertyName === 'winnerUserId') &&
      event?.entity?.status !== TourTrashStatus.DELETED
    ) {
      await CommonSubscriber.activityLogCommandUpdate(
        event,
        TourTrashEntity.name,
        null,
        null,
        {
          title: event?.databaseEntity?.title || event?.entity?.title,
        },
        {
          title: event?.entity?.title,
        }
      );
    }
  }
}

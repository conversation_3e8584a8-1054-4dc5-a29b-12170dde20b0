import { <PERSON>um<PERSON>, CreateDateColumn, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, PrimaryColumn } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';

export enum CommandSubscriber {
  UPDATE = 'UPDATE',
  INSERT = 'INSERT',
  DELETE = 'DELETE',
}

@Entity('ActivityLogs')
export class ActivityLogEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column('uuid')
  itemId: string;

  @Column()
  module: string;

  @Column()
  command: string;

  @Column()
  columnChanged: string;

  @Column()
  prevColumnValue: string;

  @Column()
  nextColumnValue: string;

  @Column('uuid')
  modifiedBy: string;

  @OneToOne((type) => UserEntity)
  @JoinColumn({ name: 'modifiedBy' })
  user: UserEntity;

  @CreateDateColumn()
  modifiedAt: string;
}

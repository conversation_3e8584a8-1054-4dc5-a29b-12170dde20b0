import { v4 } from 'uuid';
import { ActivityLogEntity, CommandSubscriber } from './entities/activity-logs.entity';

export class CommonSubscriber {
  static async activityLogCommandInsert(
    event: any,
    name: string,
    isReturnedRecord = false
  ): Promise<ActivityLogEntity> {
    const recordActivityLogs = new ActivityLogEntity();
    recordActivityLogs.id = v4();
    recordActivityLogs.itemId = event.entity?.uuid || event.entity?.id;
    recordActivityLogs.command = CommandSubscriber.INSERT;
    recordActivityLogs.module = name;
    recordActivityLogs.modifiedBy = event.entity?.createdBy;

    recordActivityLogs.nextColumnValue = JSON.stringify(event.entity);

    if (isReturnedRecord) {
      return recordActivityLogs;
    } else {
      return await event.manager.save(recordActivityLogs);
    }
  }

  static async activityLogCommandUpdate(
    event: any,
    name: string,
    configDataPrev?: any,
    configDataNext?: any,
    configOtherPrev?: any,
    configOtherNext?: any,
    isReturnedRecord = false
  ): Promise<ActivityLogEntity> {
    const recordActivityLogs = new ActivityLogEntity();
    recordActivityLogs.id = v4();
    recordActivityLogs.itemId = event?.entity?.uuid || event?.entity?.id;
    recordActivityLogs.command = CommandSubscriber.UPDATE;
    recordActivityLogs.module = name;
    recordActivityLogs.columnChanged = event?.updatedColumns
      ? event.updatedColumns.map((r: any) => r.propertyName).join(',')
      : null;
    let dataPrev: any = {};
    let dataNext: any = {};
    if (!configDataPrev) {
      event.updatedColumns.forEach((column) => {
        dataPrev[column.propertyName] = event?.databaseEntity[column.propertyName];
        dataNext[column.propertyName] = event?.entity[column.propertyName];
      });
      // if (name === TourTrashEntity.name) {
      //   dataPrev.title = event?.databaseEntity?.title || event?.entity?.title;
      //   dataNext.title = event?.entity?.title;
      // }
      // if (name === HomeWidgetEntity.name) {
      //   dataPrev.name = event?.databaseEntity?.name;
      //   dataNext.name = event?.entity?.name;
      // }
      dataPrev = {
        ...dataPrev,
        ...configOtherPrev,
      };
      dataNext = {
        ...dataNext,
        ...configOtherNext,
      };
    } else {
      dataPrev = configDataPrev;
      dataNext = configDataNext;
    }
    recordActivityLogs.prevColumnValue = JSON.stringify(dataPrev);
    recordActivityLogs.nextColumnValue = JSON.stringify(dataNext);
    recordActivityLogs.modifiedBy = event?.entity?.updatedBy;

    if (isReturnedRecord) {
      return recordActivityLogs;
    } else {
      return await event.manager.save(recordActivityLogs);
    }
  }

  static async activityLogCommandDelete(
    event: any,
    name: string,
    isReturnedRecord = false
  ): Promise<ActivityLogEntity> {
    const recordActivityLogs = new ActivityLogEntity();
    recordActivityLogs.id = v4();
    recordActivityLogs.itemId = event?.entity?.uuid || event?.entity?.id;
    recordActivityLogs.command = CommandSubscriber.DELETE;
    recordActivityLogs.module = name;
    recordActivityLogs.modifiedBy = event?.entity?.deletedBy;

    recordActivityLogs.prevColumnValue = JSON.stringify(event?.databaseEntity);

    if (isReturnedRecord) {
      return recordActivityLogs;
    } else {
      return await event.manager.save(recordActivityLogs);
    }
  }
}

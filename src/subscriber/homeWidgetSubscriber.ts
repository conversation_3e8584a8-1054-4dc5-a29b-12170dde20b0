import { EntitySubscriberInterface, EventSubscriber, InsertEvent, UpdateEvent } from 'typeorm';
import { HomeWidgetEntity } from 'src/admin/entities/home-widget.entity';
import { CommonSubscriber } from './commonSubscriber';

@EventSubscriber()
export class HomeWidgetSubscriber implements EntitySubscriberInterface<HomeWidgetEntity> {
  /**
   * Indicates that this subscriber only listen to HomeWidgetEntity events.
   */
  listenTo() {
    return HomeWidgetEntity;
  }

  async afterInsert(event: InsertEvent<HomeWidgetEntity>) {
    await CommonSubscriber.activityLogCommandInsert(event, HomeWidgetEntity.name);
  }

  async afterUpdate(event: UpdateEvent<HomeWidgetEntity>) {
    if (event?.updatedColumns?.length > 0) {
      await CommonSubscriber.activityLogCommandUpdate(
        event,
        HomeWidgetEntity.name,
        {
          name: event?.databaseEntity?.name,
        },
        {
          name: event?.entity?.name,
        }
      );
    }
  }
}

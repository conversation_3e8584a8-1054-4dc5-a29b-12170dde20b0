import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { CreateUserAnswerDto } from './dto/create-user-answer.dto';
import { UserAnswerService } from './user-answer.service';

@Controller('user-answers')
export class UserAnswerController {
  constructor(private readonly userAnswerService: UserAnswerService) {}

  @UseGuards(AuthGuard)
  @Post()
  addUpdateAnswer(@Body() createUserAnswerDto: CreateUserAnswerDto, @Req() req: any) {
    return this.userAnswerService.addUpdateAnswer(createUserAnswerDto, req.user.uid);
  }

  @UseGuards(AuthGuard)
  @Get('by-user')
  findOne(@Req() req: any) {
    return this.userAnswerService.findBy({ userId: req.user.uid });
  }

  @UseGuards(AuthGuard)
  @Delete('question/:questionId')
  deleteUserAnswer(@Req() req: any, @Param('questionId') questionId: any) {
    return this.userAnswerService.deleteUserAnswer(req.user.uid, questionId);
  }
}

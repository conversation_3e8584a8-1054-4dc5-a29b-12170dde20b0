import { BadRequestException, Inject, Injectable, Logger, NotFoundException, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { isEmpty, orderBy, result } from 'lodash';
import momentTz from 'moment-timezone';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { PollAnswer } from 'src/answer/entities/answer.entity';
import { PollQuestion } from 'src/question/entities/question.entity';
import { LOYALTY_MESSAGE_ERROR } from '../loyalty/loyalty.constants';
import { QuestionResponseDto } from '../question/dto/question.response.dto';
import { percentRound } from '../utils/transform';
import { CreateUserAnswerDto } from './dto/create-user-answer.dto';
import { UserAnswer } from './entities/user-answer.entity';

@Injectable()
export class UserAnswerService {
  private readonly logger = new Logger(UserAnswerService.name);
  @InjectRepository(PollQuestion) private readonly pollQuestionRepo: Repository<PollQuestion>;
  @InjectRepository(PollAnswer) private readonly pollAnswerRepo: Repository<PollAnswer>;
  @InjectRepository(UserAnswer) private readonly userAnswerRepo: Repository<UserAnswer>;
  constructor(private readonly config: ConfigService) {}

  async addUpdateAnswer(payload: CreateUserAnswerDto, userId: string) {
    const { questionId, answerId } = payload;
    try {
      const question = await this.pollQuestionRepo.findOne({
        select: ['id', 'title', 'description', 'imageUrl', 'disabled', 'createdAt', 'sortOrder', 'options'],
        where: { id: questionId },
        relations: ['answers', 'questionSchedule'],
      });
      const answer = await this.pollAnswerRepo.findOne({
        where: {
          id: answerId,
          questionId,
        },
      });
      if (isEmpty(question)) {
        throw new NotFoundException({
          internalErrorCode: 'QUESTION_NOT_FOUND',
          errorMessage: 'The question not found.',
        });
      } else if (question && question.disabled) {
        throw new BadRequestException({
          internalErrorCode: 'NO_LONGER_AVAILABLE',
          errorMessage: 'The question is no longer available.',
        });
      }
      if (isEmpty(answer)) {
        throw new NotFoundException({
          internalErrorCode: 'ANSWER_NOT_FOUND',
          errorMessage: 'The answer not found.',
        });
      }

      const inActiveAt = question?.questionSchedule?.inActiveAt || null;
      if (inActiveAt) {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const momentTz = require('moment-timezone');
        const currentDate = momentTz().utc();
        const convertTime = momentTz(inActiveAt).format('YYYY-MM-DD HH:mm');
        const inActiveDate = momentTz.tz(convertTime, 'UTC');

        if (currentDate.diff(inActiveDate) > 0) {
          await this.pollQuestionRepo.update({ id: question.id }, { disabled: true });
          throw new BadRequestException({
            internalErrorCode: 'NO_LONGER_AVAILABLE',
            errorMessage: 'The question is no longer available.',
          });
        }
      }

      const handleQuestion = plainToClass(QuestionResponseDto, question, { excludeExtraneousValues: true });
      const userAnswer = await this.userAnswerRepo.findOne({ userId, questionId: payload.questionId });
      payload.userId = userId;
      if (!userAnswer) {
        await this.createUserAnswer(payload);
      } else {
        await this.updateUserAnswer(userAnswer.id, payload);
      }
      const userAnswerCount = await this.pollQuestionRepo.query(
        `SELECT count(1) as userCount FROM PollQuestions WHERE id not in (SELECT questionId FROM UserAnswers WHERE userId = '${userId}') and disabled = 0 and deletedAt is null`
      );
      let isNextQuestion = false;
      if (userAnswerCount && userAnswerCount.length) {
        isNextQuestion = userAnswerCount[0]?.userCount >= 1 ? true : false;
      }
      await this.calculateAnswer(questionId, handleQuestion);
      const answers = result(handleQuestion, 'answers', null);
      if (answers) {
        handleQuestion.answers = orderBy(answers, 'createdAt');
      }
      return {
        ...handleQuestion,
        isNextQuestion,
        userAnswerId: answerId,
      };
    } catch (err) {
      console.log('Error:', err);
      throw new BadRequestException(err);
    }
  }

  async calculateAnswer(id: string, question: QuestionResponseDto) {
    const queryUserAnswer = this.userAnswerRepo.createQueryBuilder('UAnswer');
    queryUserAnswer.where({ questionId: id });
    queryUserAnswer.select(['answerId', 'count(answerId) count']);
    queryUserAnswer.groupBy('answerId');
    const userAnswers = await queryUserAnswer.getRawMany();
    if (userAnswers?.length > 0) {
      let totalAnswer = 0;
      userAnswers.map((answer) => (totalAnswer += answer.count));
      let lstPercentAnswerForQuestions = [];
      lstPercentAnswerForQuestions = userAnswers.map((answer) => {
        return { ...answer, percent: +((+answer.count / +totalAnswer) * 100) };
      });
      const arrQuestionAnswerPercent = [];
      question.answers = question.answers?.map((item) => {
        const percentAnswerForQuestion = lstPercentAnswerForQuestions.find(
          (percentAnswer) => percentAnswer.answerId == item.id
        );
        item['percent'] = percentAnswerForQuestion ? percentAnswerForQuestion.percent : 0;
        item['count'] = percentAnswerForQuestion ? percentAnswerForQuestion.count : 0;
        arrQuestionAnswerPercent.push({ id: item.id, percent: item['percent'] });
        return item;
      });
      const roundPercents = percentRound(
        arrQuestionAnswerPercent.map((item) => item.percent),
        2
      );
      // Remap percent
      question.answers = question.answers.map((item, index) => {
        item['percent'] = roundPercents[index];
        return item;
      });
      question['totalAnswer'] = totalAnswer;
    }
  }

  async createUserAnswer(payload: CreateUserAnswerDto) {
    try {
      return await this.userAnswerRepo.save(this.userAnswerRepo.create(payload));
    } catch (error) {
      console.log(error);

      throw new BadRequestException(`Answer failed`);
    }
  }
  async updateUserAnswer(id: string, payload: CreateUserAnswerDto) {
    payload.updatedAt = new Date();
    try {
      await this.userAnswerRepo.update(id, payload);
      return await this.findOne(id);
    } catch (error) {
      throw new BadRequestException(`Update answer failed`);
    }
  }

  findBy(options: any) {
    return this.userAnswerRepo.find(options);
  }

  async findOne(id: string) {
    try {
      return await this.userAnswerRepo.findOne(id);
    } catch (e) {
      console.log(e);
      throw new BadRequestException(`Answers not found`);
    }
  }

  async deleteUserAnswer(userId: string, questionId: string) {
    if (!questionId) {
      throw new BadRequestException(`Question ID is not null`);
    }
    const userAnswer = await this.userAnswerRepo.findOne({ userId, questionId });
    if (userAnswer) {
      try {
        await this.userAnswerRepo.delete(userAnswer.id);
        return { success: true };
      } catch (error) {
        console.log(error);
        throw new BadRequestException(`Delete Answers failed`);
      }
    } else {
      return { success: true };
    }
  }
}

import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PollAnswer } from 'src/answer/entities/answer.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { PollQuestion } from 'src/question/entities/question.entity';
import { SharedModule } from 'src/shared/shared.module';
import { UserAnswer } from './entities/user-answer.entity';
import { UserAnswerController } from './user-answer.controller';
import { UserAnswerService } from './user-answer.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([UserEntity, PollAnswer, PollQuestion, UserAnswer])],
  controllers: [UserAnswerController],
  providers: [UserAnswerService],
})
export class UserAnswerModule {}

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import { In, Not, Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { EcomService } from 'src/ecom/ecom.service';
import { KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { SeventeentrackService, TRACKING_CARRIERS } from 'src/shared/services/seventeentrack.service';
import { getUpperCaseValue, isContainLetter } from 'src/utils/transform';
import { TTBPushEntity } from './entities/ttb-push.entity';
import { TTBEntity, TTBStatus } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

export type TTBShipmentScanJob = Job<{ orderIds: string[] }>;

export enum TTBShipmentProcessorQueueName {
  SHIPMENT_SCANS = 'shipment-scans',
}

@Processor('ttb-shipment')
export class TTBShipmentProcessor {
  private readonly logger = new Logger(TTBShipmentProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly ttbService: TTBService,
    private readonly seventeentrackService: SeventeentrackService,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private ecomService: EcomService
  ) {}

  @Process({
    name: TTBShipmentProcessorQueueName.SHIPMENT_SCANS,
    concurrency: 1,
  })
  async shipmentScans(job: TTBShipmentScanJob): Promise<any> {
    this.logger.log(`TTB shipment scans for orderIds ${job.data.orderIds.join(',')}`);
    const EBSShipmentFileNames = await this.getLastThreeDaysShipmentFileNames();
    if (!EBSShipmentFileNames || EBSShipmentFileNames.length === 0) {
      for (const orderId of job.data.orderIds) {
        await this.handleCheckShipmentStatusWithClubTracker(orderId);
      }
      return;
    }
    let orders = [];
    let canceledOrders = [];
    let heading = [];
    const unhandledOrderIds = [];
    for (const EBSShipmentFileName of EBSShipmentFileNames) {
      const {
        heading: fileHeading,
        orders: fileOrders,
        canceledOrders: fileCanceledOrders,
      } = await this.ttbService.getDailyEBSShipmentOrders(job.data.orderIds, EBSShipmentFileName);
      if (fileHeading && fileOrders.length > 0) {
        heading = [...fileHeading];
        orders = [...orders, ...fileOrders];
      }
      if (fileCanceledOrders.length > 0) {
        canceledOrders = [...canceledOrders, ...fileCanceledOrders];
      }
    }
    if (canceledOrders.length > 0) {
      await this.handleCanceledOrders(canceledOrders);
    }
    job.data.orderIds.forEach((orderId) => {
      if (orders.length === 0) {
        unhandledOrderIds.push(orderId);
        return;
      }
      if (!orders.find((order) => order[2] === orderId)) {
        unhandledOrderIds.push(orderId);
      }
    });
    this.logger.log(`TTB shipment scans found ${unhandledOrderIds.length} unhandled orders`);
    this.logger.log(`TTB shipment scans found ${orders.length} orders from sFTP scanning`);
    for (const unhandledOrderId of unhandledOrderIds) {
      await new Promise((resolve) => setTimeout(resolve, 4000));
      await this.handleCheckShipmentStatusWithClubTracker(unhandledOrderId);
    }
    if (heading && orders.length > 0) {
      const client = await this.ttbService.getFTPClient();
      for (const order of orders) {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        const orderId: string = order[2];
        const shipmentTrackingNumber: string = order[23];
        const isTrackingNumberFromUPS = isContainLetter(shipmentTrackingNumber);
        let shipmentCarrierName: string = getUpperCaseValue(order[22]);
        if (isTrackingNumberFromUPS && shipmentCarrierName === TRACKING_CARRIERS.FEDEX) {
          shipmentCarrierName = TRACKING_CARRIERS.UPS;
        }
        const filename = `TryThenBuy-${orderId}-Shipped.csv`;
        const hasUploaded = await client.exists(`/${this.config.get('app.ttbFTPRootPath')}/Archived/${filename}`);
        if (!hasUploaded) {
          const buffer = xlsx.build(
            [
              {
                name: orderId,
                data: [heading, order],
              },
            ],
            { bookType: 'csv' }
          );
          this.logger.log(`Started to put ${filename} to GVC...`);
          await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/GVC/${filename}`);
          await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/Archived/${filename}`);
          this.logger.log(`Putted ${filename} to GVC...`);
        }
        const MyTMOrder = await this.ttbRepo.findOne({ orderId });
        if (MyTMOrder.status === TTBStatus.INITIALIZED) {
          const shipmentSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
            shipmentTrackingNumber,
            shipmentCarrierName
          );
          await this.ttbRepo.update(
            { orderId },
            {
              status: TTBStatus.TRIAL_COMING,
              shipmentTrackingNumber,
              shipmentCarrierName,
              shippedAt: new Date(),
              shipmentSeventeentrackCarrierKey,
            }
          );
        }
      }
      return client.end();
    }
  }

  async handleCanceledOrders(orders: Array<string[]>) {
    await Promise.all(
      orders.map(async (order) => {
        const orderId: string = order[2];
        const ttbRecord = await this.ttbRepo.findOne({
          orderId,
          status: Not(In([TTBStatus.CHARGED, TTBStatus.PRODUCT_RETURNED, TTBStatus.BOUGHT])),
        });
        if (ttbRecord) {
          ttbRecord.status = TTBStatus.CANCELLED;
          ttbRecord.canceledAt = new Date();
          ttbRecord.canceledBy = 'EBS';
          await this.ttbRepo.save(ttbRecord);
          const ecomOrder = await this.ecomService.getOrder(orderId, ttbRecord?.country);
          if (ecomOrder) {
            const productInfo = JSON.parse(ttbRecord?.productInfo || JSON.stringify({}));
            if (productInfo.cTmCustomconfiguratorNodes) {
              productInfo.cTmCustomconfiguratorNodes = JSON.parse(productInfo.cTmCustomconfiguratorNodes);
            }
            const user = await this.userRepo.findOne({ id: ttbRecord?.userId });
            await this.ttbService.trackingEventKlaviyoTTB(
              KlaviyoTrackEvents.TTB_CANCELATION,
              user?.email,
              ecomOrder,
              productInfo
            );
          }
        }
      })
    );
  }

  async handleCheckShipmentStatusWithClubTracker(orderId: string) {
    const MyTMOrder = await this.ttbRepo.findOne({ orderId });
    if (MyTMOrder.status === TTBStatus.INITIALIZED) {
      const shipmentInfo = await this.ttbService.getShipmentInfoFromClubTracker(orderId);
      if (shipmentInfo) {
        let shipmentCarrierName = getUpperCaseValue(shipmentInfo.carrierName);
        const isTrackingNumberFromUPS = isContainLetter(shipmentInfo.trackingNumber);
        if (isTrackingNumberFromUPS && shipmentCarrierName === TRACKING_CARRIERS.FEDEX) {
          shipmentCarrierName = TRACKING_CARRIERS.UPS;
        }

        const shipmentSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
          shipmentInfo.trackingNumber,
          shipmentCarrierName
        );
        await this.ttbRepo.update(
          { orderId },
          {
            status: TTBStatus.TRIAL_COMING,
            shipmentTrackingNumber: shipmentInfo.trackingNumber,
            shipmentCarrierName,
            shippedAt: new Date(),
            shipmentSeventeentrackCarrierKey,
          }
        );
      }
    }
  }

  async getLastThreeDaysShipmentFileNames(): Promise<string[]> {
    const formattedDates = [
      moment().format('DDMMYYYY'),
      moment().subtract(1, 'days').format('DDMMYYYY'),
      moment().subtract(2, 'days').format('DDMMYYYY'),
      moment().subtract(3, 'days').format('DDMMYYYY'),
      moment().subtract(4, 'days').format('DDMMYYYY'),
      moment().subtract(5, 'days').format('DDMMYYYY'),
      moment().subtract(6, 'days').format('DDMMYYYY'),
      moment().subtract(7, 'days').format('DDMMYYYY'),
      moment().subtract(8, 'days').format('DDMMYYYY'),
      moment().subtract(9, 'days').format('DDMMYYYY'),
      moment().subtract(10, 'days').format('DDMMYYYY'),
    ];
    return this.ttbService.scanShipmentFileNames(formattedDates);
  }
}

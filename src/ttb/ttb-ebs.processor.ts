import { Process, Processor } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { Cache } from 'cache-manager';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import path from 'path';
import { Repository } from 'typeorm';
import { TTBPushEntity } from './entities/ttb-push.entity';
import { TTBEntity } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

type TTBUploadCSVToEBSJob = Job<{ orderId: string; status: string; statusCode: string; comments: string }>;

export enum TTBEBSProcessorQueueName {
  EBS_CSV_UPLOADS = 'ebs_csv_uploads',
}

const EBSDailyInboundSheetHeading = [
  'DW Number',
  'EBS Order Number',
  'EBS Line Number',
  'SKU',
  'Price',
  'Quantity',
  'Acceptance Status',
  'Acceptance Code',
  'Date of Action (MYTMBE)',
  'Comments',
];

const LATEST_EBS_INBOUND_FILE_TIMESTAMP_CACHE_KEY = 'LATEST_EBS_INBOUND_FILE_TIMESTAMP';

@Processor('ttb-ebs')
export class TTBEBSProcessor {
  private readonly logger = new Logger(TTBEBSProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly ttbService: TTBService,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @Process({
    name: TTBEBSProcessorQueueName.EBS_CSV_UPLOADS,
    concurrency: 1,
  })
  async uploadCSVToEBS(job: TTBUploadCSVToEBSJob): Promise<any> {
    this.logger.log(`TTB upload csv to EBS for orderId ${job.data.orderId}`);
    const order = await this.ttbRepo.findOne({ orderId: job.data.orderId });
    if (!order) {
      return true;
    }
    const orderLine = [
      order.orderId,
      '',
      '1',
      'TM_DRIVER_MODEL',
      order.amountPaid || '',
      '1',
      job.data.status,
      job.data.statusCode,
      moment().toDate(),
      job.data.comments,
    ];
    let latestTimestamp: any = await this.cacheManager.get(LATEST_EBS_INBOUND_FILE_TIMESTAMP_CACHE_KEY);
    if (latestTimestamp) {
      latestTimestamp = parseInt(latestTimestamp);
    }
    if (!latestTimestamp) {
      latestTimestamp = Date.now();
      await this.cacheManager.set(LATEST_EBS_INBOUND_FILE_TIMESTAMP_CACHE_KEY, `${latestTimestamp}`, {
        ttl: 0,
      });
    }
    const client = await this.ttbService.getFTPClient();
    let EBSDailyInboundFileName = `TTB${moment().format('MMDDYYYY')}-${latestTimestamp}.csv`;
    const EBSDailyInboundFilePath = `/${this.config.get(
      'app.ttbFTPRootPath'
    )}/EBS/MyTM/Inbound/TrythenBuy/${EBSDailyInboundFileName}`;
    const existing = await client.exists(EBSDailyInboundFilePath);
    if (!existing) {
      latestTimestamp = Date.now();
      await this.cacheManager.set(LATEST_EBS_INBOUND_FILE_TIMESTAMP_CACHE_KEY, `${latestTimestamp}`, {
        ttl: 0,
      });
      EBSDailyInboundFileName = `TTB${moment().format('MMDDYYYY')}-${latestTimestamp}.csv`;
      const buffer = xlsx.build(
        [
          {
            name: EBSDailyInboundFileName.split('.')[0],
            data: [EBSDailyInboundSheetHeading, orderLine],
          },
        ],
        { bookType: 'csv' }
      );
      return await this.postUpload(client, buffer, EBSDailyInboundFileName);
    }
    const localEBSDailyInboundFilePath = path.join(process.cwd(), `/public/Archived/${EBSDailyInboundFileName}`);
    await client.fastGet(EBSDailyInboundFilePath, localEBSDailyInboundFilePath);
    const workSheetsFromFile = xlsx.parse(localEBSDailyInboundFilePath, {
      cellDates: true,
    });
    const sheet = workSheetsFromFile[0].data;
    const existingOrderLine = sheet.find((item) => item[0] === orderLine[0] && item[7] === orderLine[7]);
    if (existingOrderLine) {
      return;
    }
    const buffer = xlsx.build(
      [
        {
          name: EBSDailyInboundFileName.split('.')[0],
          data: [...sheet, orderLine],
        },
      ],
      { bookType: 'csv', cellDates: true }
    );
    await this.postUpload(client, buffer, EBSDailyInboundFileName);
  }

  async postUpload(client, buffer, EBSDailyInboundFileName) {
    this.logger.log(`Started to put ${EBSDailyInboundFileName} to EBS...`);
    await client.put(
      buffer,
      `/${this.config.get('app.ttbFTPRootPath')}/EBS/MyTM/Inbound/TrythenBuy/${EBSDailyInboundFileName}`
    );
    this.logger.log(`Putted ${EBSDailyInboundFileName} to EBS...`);
    return client.end();
  }
}

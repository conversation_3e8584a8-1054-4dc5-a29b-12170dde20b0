import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { Job } from 'bull';
import * as _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { TTBConfigThresholdEntity, TTBThresholdType } from './entities/ttb-config-threshold.entity';
import { TTBLogThresholdEntity } from './entities/ttb-log-threshold.entity';
import { TTBEntity } from './entities/ttb.entity';

export type TTBThresholdJob = Job<{
  orderId: string;
}>;

export enum TTBThresholdActionQueueName {
  TTB_THRESHOLD = 'ttb-threshold-job',
}

const configNameThresHoldAdmin = {
  HOURLY: 'Hour',
  DAILY: 'Day',
  WEEKLY: 'Week',
  MONTHLY: 'Month',
};

@Processor('ttb-threshold')
export class TTBThresholdProcessor {
  private readonly logger = new Logger(TTBThresholdProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private firebaseMessaging: FirebaseMessagingService,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(TTBConfigThresholdEntity)
    private readonly ttbConfigThresholdRepo: Repository<TTBConfigThresholdEntity>,
    @InjectRepository(TTBLogThresholdEntity)
    private readonly ttbLogThresholdRepo: Repository<TTBLogThresholdEntity>,
    @InjectRepository(UserNotifyAdminEntity) private readonly userNotifyAdminRepo: Repository<UserNotifyAdminEntity>,
    private readonly klaviyoService: KlaviyoService
  ) {}

  @Process({
    name: TTBThresholdActionQueueName.TTB_THRESHOLD,
    concurrency: 1,
  })
  async checkTTBThreshold(job: TTBThresholdJob): Promise<any> {
    console.log(`TTB Threshold Data ${new Date()} ${job.data}`);
    try {
      const orderId: string = _.result(job, 'data.orderId');
      if (!orderId) {
        this.logger.log(`TTB Threshold not exists orderId`);
        return true;
      }
      const ttb = await this.ttbRepo.findOne({ orderId });
      const ttbConfigThreshold = await this.ttbConfigThresholdRepo.find();
      if (!ttb || !(ttbConfigThreshold && ttbConfigThreshold.length)) {
        return true;
      }

      const isHourly = await this.checkHourLyTTB(ttb, ttbConfigThreshold);
      const isDaily = await this.checkDailyTTB(ttb, ttbConfigThreshold);
      const isWeekly = await this.checkWeeklyTTB(ttb, ttbConfigThreshold);
      const isMonthly = await this.checkMonthlyTTB(ttb, ttbConfigThreshold);

      if (isHourly) {
        await this.sentNotifyAdminThreshold(TTBThresholdType.HOURLY, ttbConfigThreshold);
      }

      if (isDaily) {
        await this.sentNotifyAdminThreshold(TTBThresholdType.DAILY, ttbConfigThreshold);
      }

      if (isWeekly) {
        await this.sentNotifyAdminThreshold(TTBThresholdType.WEEKLY, ttbConfigThreshold);
      }

      if (isMonthly) {
        await this.sentNotifyAdminThreshold(TTBThresholdType.MONTHLY, ttbConfigThreshold);
      }

      return true;
    } catch (err) {
      console.log('TB Threshold Error Catch', err);
      this.logger.log(`TTB Threshold Error Catch ${new Date()} ${err}`);
      return false;
    }
  }

  async sentNotifyAdminThreshold(type, ttbConfigThreshold) {
    try {
      const config = ttbConfigThreshold.find((val: TTBLogThresholdEntity) => val.type === type);
      if (!config) return true;
      const { totalOrder } = config;
      const query = await this.ttbLogThresholdRepo
        .createQueryBuilder('ttbLogThreshold')
        .where('type = :type', { type })
        .andWhere('totalOrder = :totalOrder', { totalOrder });
      switch (type) {
        case TTBThresholdType.HOURLY:
          query.andWhere('ttbLogThreshold.createdAt  >= DATEADD(HOUR, -1, GETDATE())');
          break;
        case TTBThresholdType.DAILY:
          query.andWhere('ttbLogThreshold.createdAt  >= DATEADD(day, -1, GETDATE())');
          break;
        case TTBThresholdType.WEEKLY:
          query.andWhere('ttbLogThreshold.createdAt  >= DATEADD(day, -7, GETDATE())');
          break;
        case TTBThresholdType.MONTHLY:
          query.andWhere('ttbLogThreshold.createdAt  >= DATEADD(month, -1, GETDATE())');
          break;
      }
      query.andWhere('ttbLogThreshold.createdAt < GETDATE()');
      const countLogThreshold = await query.getCount();
      console.log('countLogThreshold', countLogThreshold);
      const users = await this.userNotifyAdminRepo.find({
        where: { is_ttb_fraud: true },
        relations: ['user'],
      });
      if (countLogThreshold || !(users && users.length)) return true;
      await this.pushNotification(users, type, totalOrder);
      await this.trackingKlaviyo(users, type, totalOrder);
      await this.saveLogTTBThreshold(type, totalOrder);
    } catch (err) {
      this.logger.log(`TTB Threshold Error Sent Notify ${err}`);
    }
  }

  async saveLogTTBThreshold(type, totalOrder) {
    const dataLogThreshold = new TTBLogThresholdEntity();
    dataLogThreshold.id = v4();
    dataLogThreshold.type = type;
    dataLogThreshold.totalOrder = totalOrder;
    return this.ttbLogThresholdRepo.save(dataLogThreshold);
  }

  async checkHourLyTTB(ttb, ttbConfigThreshold) {
    const configHourly = ttbConfigThreshold.find((val: TTBLogThresholdEntity) => val.type === TTBThresholdType.HOURLY);
    if (!configHourly) {
      return false;
    }
    const { totalOrder } = configHourly;
    const countTTB = await this.ttbRepo
      .createQueryBuilder('ttb')
      .andWhere('ttb.createdAt  >= DATEADD(HOUR,-1, GETDATE())')
      .andWhere('ttb.createdAt < GETDATE()')
      .getCount();
    if (countTTB >= totalOrder) {
      return true;
    }
    return false;
  }

  async checkDailyTTB(ttb, ttbConfigThreshold) {
    const configDaily = ttbConfigThreshold.find((val: TTBLogThresholdEntity) => val.type === TTBThresholdType.DAILY);
    if (!configDaily) {
      return false;
    }
    const { totalOrder } = configDaily;
    const countTTB = await this.ttbRepo
      .createQueryBuilder('ttb')
      .andWhere('ttb.createdAt  >= DATEADD(day, -1, GETDATE())')
      .andWhere('ttb.createdAt < GETDATE()')
      .getCount();
    if (countTTB >= totalOrder) {
      return true;
    }
    return false;
  }

  async checkWeeklyTTB(ttb, ttbConfigThreshold) {
    const configWeekly = ttbConfigThreshold.find((val: TTBLogThresholdEntity) => val.type === TTBThresholdType.WEEKLY);
    if (!configWeekly) {
      return false;
    }
    const { totalOrder } = configWeekly;
    const countTTB = await this.ttbRepo
      .createQueryBuilder('ttb')
      .andWhere('ttb.createdAt  >= DATEADD(day, -7, GETDATE())')
      .andWhere('ttb.createdAt < GETDATE()')
      .getCount();
    if (countTTB >= totalOrder) {
      return true;
    }
    return false;
  }

  async checkMonthlyTTB(ttb, ttbConfigThreshold) {
    const configMonthly = ttbConfigThreshold.find(
      (val: TTBLogThresholdEntity) => val.type === TTBThresholdType.MONTHLY
    );
    if (!configMonthly) {
      return false;
    }
    const { totalOrder } = configMonthly;
    const countTTB = await this.ttbRepo
      .createQueryBuilder('ttb')
      .andWhere('ttb.createdAt  >= DATEADD(month, -1, GETDATE())')
      .andWhere('ttb.createdAt < GETDATE()')
      .getCount();
    if (countTTB >= totalOrder) {
      return true;
    }
    return false;
  }

  async pushNotification(users: UserNotifyAdminEntity[], type, totalOrder) {
    try {
      const name = configNameThresHoldAdmin[type];
      const fcmAdminTokens = _.compact(
        _.map(users, (value) => {
          return _.result(value, 'user.fcmTokenAdmin');
        })
      );
      const fcmTokens = _.compact(
        _.map(users, (value) => {
          return _.result(value, 'user.fcmToken');
        })
      );
      if (!fcmAdminTokens.length && !fcmTokens.length) {
        return true;
      }
      if (fcmAdminTokens && fcmAdminTokens.length) {
        for (const fcmAdminToken of fcmAdminTokens) {
          const message: any = {
            data: {
              type: 'TTB_THRESHOLD',
              link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy`,
            },
            token: fcmAdminToken,
            notification: {
              title: 'TaylorMade',
              body: `${totalOrder} Orders Placed in the Last ${name}`,
            },
            webpush: {
              fcm_options: {
                link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy`,
              },
            },
          };
          this.firebaseMessaging.send(message).then((r) => r);
        }
      }

      if (fcmTokens && fcmTokens.length) {
        for (const fcmToken of fcmTokens) {
          const message: any = {
            data: {
              type: 'TTB_THRESHOLD',
              link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy`,
            },
            token: fcmToken,
            notification: {
              title: 'TaylorMade',
              body: `${totalOrder} Orders Placed in the Last ${name}`,
            },
            webpush: {
              fcm_options: {
                link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy`,
              },
            },
          };
          this.firebaseMessaging.send(message).then((r) => r);
        }
      }
    } catch (e) {
      console.log('ERROR TTB Threshold notify to admin', e);
      return null;
    }
  }

  async trackingKlaviyo(users: UserNotifyAdminEntity[], type, totalOrder) {
    const emails = _.compact(
      _.map(users, (value) => {
        return _.result(value, 'user.email');
      })
    );
    const name = configNameThresHoldAdmin[type];
    if (!emails.length) {
      return true;
    }
    if (emails && emails.length) {
      for (const value of emails) {
        try {
          const payloadOrderInfo = {
            totalOrder,
            name,
            link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy`,
          };
          const email: any = value;
          await this.klaviyoService.track(
            email,
            KlaviyoTrackEvents.MYTM_SUBSCRIPTION_TTBTHRESHOLD_ADMIN,
            payloadOrderInfo
          );
        } catch (error) {
          console.log('ERROR Email Admin TTB Threshold', error);
          return false;
        }
      }
    }
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { Job } from 'bull';
import { messaging } from 'firebase-admin';
import * as _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { In, Repository } from 'typeorm';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { TTBFraudEntity } from './entities/ttb-fraud.entity';
import { TTBEntity } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

type TTBPushJob = Job<{
  orderId: string;
  email: string;
}>;

export enum TTBNotifyAdminProcessorQueueName {
  PUSH = 'push',
}

export enum Role {
  USER = 1,
  ADMIN = 2,
  SUPER_ADMIN = 3,
  CUSTOMER_SERVICE = 4,
  TTB_AGENT = 5,
  TTB_MANAGER = 6,
}

@Processor('ttb-notify-admin')
export class TTBNotifyAdminProcessor {
  private readonly logger = new Logger(TTBNotifyAdminProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private firebaseMessaging: FirebaseMessagingService,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(TTBFraudEntity) private readonly ttbFraudRepo: Repository<TTBFraudEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserNotifyAdminEntity) private readonly userNotifyAdminRepo: Repository<UserNotifyAdminEntity>,
    private readonly klaviyoService: KlaviyoService
  ) {}

  @Process(TTBNotifyAdminProcessorQueueName.PUSH)
  async push(job: TTBPushJob): Promise<any> {
    this.logger.log(`TTB Fraud push orderId ${job.data.orderId}`);
    const orderId = _.result(job, 'data.orderId');
    const email = _.result(job, 'data.email');
    let order = null;
    let orderType = null;
    if (orderId) {
      order = await this.ttbFraudRepo.findOne({ orderId: job.data?.orderId });
      orderType = 'Order ID';
    } else if (email) {
      order = await this.ttbFraudRepo.findOne({ email: job.data?.email });
      orderType = 'Email';
    }

    const users = await this.userNotifyAdminRepo.find({
      where: { is_ttb_fraud: true },
      relations: ['user'],
    });
    if (!order || !users.length) {
      return false;
    }
    try {
      const title = orderId ? orderId : email;
      await this.pushNotification(title, users);
      await this.trackingKlaviyo(order, title, users, orderType);
    } catch (error) {
      this.logger.error(`ERROR TTB push orderId ${job.data.orderId}`);
      this.logger.error(`ERROR trackingKlaviyo: ${error}`);
    }
  }

  async pushNotification(title, users: UserNotifyAdminEntity[]) {
    try {
      const fcmAdminTokens = _.compact(
        _.map(users, (value) => {
          return _.result(value, 'user.fcmTokenAdmin');
        })
      );
      const fcmTokens = _.compact(
        _.map(users, (value) => {
          return _.result(value, 'user.fcmToken');
        })
      );
      if (!fcmAdminTokens.length && !fcmTokens.length) {
        return true;
      }
      if (fcmAdminTokens && fcmAdminTokens.length) {
        for (const fcmAdminToken of fcmAdminTokens) {
          const message: any = {
            data: {
              orderId: title,
              type: 'TTB_FRAUD',
              link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy-fraud?orderId=${title}`,
            },
            token: fcmAdminToken,
            notification: {
              title: 'TaylorMade',
              body: `${title} - TTB Potential Threat`,
            },
            webpush: {
              fcm_options: {
                link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy-fraud?orderId=${title}`,
              },
            },
          };
          this.firebaseMessaging.send(message).then((r) => r);
        }
      }

      if (fcmTokens && fcmTokens.length) {
        for (const fcmToken of fcmTokens) {
          const message: any = {
            data: {
              orderId: title,
              type: 'TTB_FRAUD',
              link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy-fraud?orderId=${title}`,
            },
            token: fcmToken,
            notification: {
              title: 'TaylorMade',
              body: `${title} - TTB Potential Threat`,
            },
            webpush: {
              fcm_options: {
                link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy-fraud?orderId=${title}`,
              },
            },
          };
          try {
            this.firebaseMessaging.send(message).then((r) => r);
          } catch (err) {
            console.log('ERROR TTB FRAUD notify to app', err);
          }
        }
      }
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async trackingKlaviyo(order, title, users: UserNotifyAdminEntity[], orderType) {
    const emails = _.compact(
      _.map(users, (value) => {
        return _.result(value, 'user.email');
      })
    );
    if (!emails.length) {
      return true;
    }
    if (emails && emails.length) {
      for (const value of emails) {
        try {
          const payloadOrderInfo = {
            reason: order.duplicate,
            orderType,
            orderId: title,
            link: `${this.config.get('app.adminPortalEndpoint')}/try-then-buy-fraud?orderId=${title}`,
          };
          const email: any = value;
          await this.klaviyoService.track(email, KlaviyoTrackEvents.MYTM_SUBSCRIPTION_TTBFRAUD_ADMIN, payloadOrderInfo);
        } catch (error) {
          console.log('ERROR Email Admin TTB FRAUD', error);
        }
      }
    }
  }
}

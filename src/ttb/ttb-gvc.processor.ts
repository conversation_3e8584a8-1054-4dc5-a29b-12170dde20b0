import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import path from 'path';
import { Repository } from 'typeorm';
import { TTBPushEntity } from './entities/ttb-push.entity';
import { TTBEntity } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

type TTBUploadCSVToGVCJob = Job<{ orderId: string; status: string; onlyUPS: boolean }>;

export enum TTBGVCProcessorQueueName {
  GVC_CSV_UPLOADS = 'gvc_csv_uploads',
}

@Processor('ttb-gvc')
export class TTBGVCProcessor {
  private readonly logger = new Logger(TTBGVCProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly ttbService: TTBService,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>
  ) {}

  @Process({
    name: TTBGVCProcessorQueueName.GVC_CSV_UPLOADS,
  })
  async uploadCSVToGVC(job: TTBUploadCSVToGVCJob): Promise<any> {
    this.logger.log(`TTB upload csv to GVC for orderId ${job.data.orderId}`);
    const order = await this.ttbRepo.findOne({ orderId: job.data.orderId });
    const client = await this.ttbService.getFTPClient();
    try {
      const ShippedOrderFileName = `TryThenBuy-${order.orderId}-Shipped.csv`;
      const ShippedOrderFilePath = `/${this.config.get('app.ttbFTPRootPath')}/Archived/${ShippedOrderFileName}`;
      const status = await client.exists(ShippedOrderFilePath);
      if (!status) {
        this.logger.log('Can not find shipment file!');
        await client.end();
        return false;
      }
      const localEBSShipmentFilePath = path.join(process.cwd(), `/public/Archived/${ShippedOrderFileName}`);
      await client.fastGet(ShippedOrderFilePath, localEBSShipmentFilePath);
      const isUPSService = this.config.get('app.ttbReturnLabelService') == 'UPS';
      if (isUPSService) {
        this.uploadCSVToUPS(job);
        await client.end();
      } else {
        const workSheetsFromFile = xlsx.parse(localEBSShipmentFilePath);
        const sheet = workSheetsFromFile[0].data;
        const sheetHeading = sheet[0] as string[];
        const sheetOrder = sheet[1] as string[];
        sheetOrder[12] = job.data.status.toUpperCase();
        const buffer = xlsx.build(
          [
            {
              name: order.orderId,
              data: [sheetHeading, sheetOrder],
            },
          ],
          { bookType: 'csv' }
        );
        const filename = `TryThenBuy-${order.orderId}-${job.data.status}.csv`;
        this.logger.log(`Started to put ${filename} to GVC...`);
        await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/GVC/${filename}`);
        await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/Archived/${filename}`);
        this.logger.log(`Putted ${filename} to GVC...`);
        return client.end();
      }
    } catch (e) {
      console.log(e);
      client.end();
      return false;
    }
  }

  async uploadCSVToUPS(job: TTBUploadCSVToGVCJob): Promise<any> {
    this.logger.log(`TTB upload csv to UPS for orderId ${job.data.orderId}`);
    const order = await this.ttbRepo.findOne({ orderId: job.data.orderId });
    const ecomOrder = await this.ttbService.getEcomOrder(job.data.orderId, order?.country);

    const client = await this.ttbService.getUPSFTPClient();
    try {
      const ShippedOrderFileName = `TryThenBuy-${order.orderId}-Shipped.csv`;
      const localEBSShipmentFilePath = path.join(process.cwd(), `/public/Archived/${ShippedOrderFileName}`);
      const workSheetsFromFile = xlsx.parse(localEBSShipmentFilePath);
      const sheet = workSheetsFromFile[0].data;
      const sheetOrder = sheet[1] as string[];
      const orderDateValue = moment(ecomOrder?.creationDate, 'YYYY-MM-DD').format('MM/DD/YY');
      const [fistName, lastName] = ecomOrder?.customerInfo?.customerName.split(' ');
      const email = ecomOrder?.customerInfo?.email;
      const trackingNumber: any = order.GVCReturnTrackingNumber;

      const dataUpload = [
        {
          header: 'ORDER DATE',
          value: orderDateValue,
        },
        {
          header: 'TM ORDER IDENTIFIER',
          value: sheetOrder[2],
        },
        {
          header: 'TRACKING NUMBER',
          value: `${trackingNumber}`,
        },
        {
          header: 'FIRST NAME',
          value: fistName,
        },
        {
          header: 'LAST NAME',
          value: lastName,
        },
        {
          header: 'EMAIL',
          value: email,
        },
        {
          header: 'BRAND',
          value: 'TAYLORMADE',
        },
        {
          header: 'TYPE',
          value: 'DRIVER',
        },
        {
          header: 'HEAD MODEL',
          value: sheetOrder[5],
        },
        {
          header: 'SHAFT MODEL',
          value: sheetOrder[7],
        },
        {
          header: 'GRIP MODEL',
          value: sheetOrder[9],
        },
        {
          header: 'QUOTED PRICE',
          value: ecomOrder?.orderTotal,
        },
      ];
      const dataHeading = [];
      const dataContent = [];
      dataUpload.map((item) => {
        dataHeading.push(item.header);
        dataContent.push(item.value);
      });
      const buffer = xlsx.build(
        [
          {
            name: order.orderId,
            data: [dataHeading, dataContent],
          },
        ],
        { bookType: 'csv' }
      );
      const time = moment().format('MMDDYYYY');
      const filename = `TTB-${order.orderId}-${time}.csv`;
      this.logger.log(`Started to put ${filename} to UPS...`);
      await client.put(buffer, `/${this.config.get('app.ttbUPSFTPRootPath')}/${filename}`);
      this.logger.log(`Putted ${filename} to UPS...`);
      return client.end();
    } catch (e) {
      console.error(e);
      client.end();
      return false;
    }
  }
}

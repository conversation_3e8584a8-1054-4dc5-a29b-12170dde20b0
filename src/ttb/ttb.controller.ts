import { BadRequestException, Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { Request } from 'express';
import moment from 'moment';
import { CancelTTBDto } from 'src/admin/admin.type';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { EcomCronService } from 'src/ecom/ecom.cron.service';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { LoggingService } from 'src/logging/logging.service';
import { BaseRequest } from 'src/types/core';
import { ERROR_CODES } from 'src/utils/errors';
import { EBSStatus, EBSStatusCodes, EBSStatusComments, GVCStatus, TTBStatus } from './entities/ttb.entity';
import { TTBStatusRangeDate } from './ttb.constants';
import { TTBCronService } from './ttb.cron.service';
import { TTBService } from './ttb.service';
import {
  BoughtDto,
  CancelTTBToGatPayloadDto,
  ForceChargeOrdersPayloadDto,
  GVCReceivedDto,
  GVCReceivedSpecsMismatchedDto,
  GVCReturnWrongComponentsDto,
  GVCReturnedDto,
  GVCReturnedToTMDto,
  GVCTrackingNumber,
  NoteTTBFraudPayloadDto,
  PauseOrderPayloadDto,
  ResumeOrderPayloadDto,
  ReturnedDto,
  SeventeentrackWebhookDto,
  ShipmentDto,
  TTBConfigThresholdPayloadDto,
  TTBUpdateUserDto,
  TryDto,
  UpdateOrderDeliveredDto,
  UploadCSVFileToEBS,
} from './ttb.type';

@Controller('ttb')
export class TTBController {
  constructor(
    private ttbService: TTBService,
    private ttbCronService: TTBCronService,
    private ecomCronService: EcomCronService,
    private readonly loggingService: LoggingService
  ) {}

  @Get('orders')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async requests(@Req() request: BaseRequest) {
    return await this.ttbService.getTTB(request.user.uid);
  }

  @Post('send-user-info')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async ttbUpdateUser(@Body() payload: TTBUpdateUserDto, @Req() request: BaseRequest) {
    const ipAddress = request.headers['X-Client-IP'.toLowerCase()];
    return await this.ttbService.updateUserBeforeCreateTTB(payload.data, request.user.uid, ipAddress);
  }

  @Get('report-monthly')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async ttbReportMonthly(@Query('from') from: Date, @Query('to') to: Date) {
    return await this.ttbService.ttbReportMonthly(from, to);
  }

  @Post('webhook/17track')
  async handleWebhookForTrackingNumberUpdates(@Body() payload: SeventeentrackWebhookDto) {
    const { productReturningToTMOrderIds, specsMismatchedReturnReceivedOrderIds } =
      await this.ttbService.handleWebhookForTrackingNumberUpdates(payload);
    try {
      this.loggingService.save({
        event: 'WEBHOOK-17TRACK',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (productReturningToTMOrderIds.length > 0) {
      for (const orderId of productReturningToTMOrderIds) {
        await this.ttbCronService.uploadCSVToEBS(
          orderId,
          EBSStatus.RETURN_TO_TM_RECEIVED,
          EBSStatusCodes.RETURN_TO_TM_RECEIVED,
          EBSStatusComments.RETURN_TO_TM_RECEIVED
        );
      }
    }
    if (specsMismatchedReturnReceivedOrderIds.length > 0) {
      for (const orderId of productReturningToTMOrderIds) {
        await this.ttbCronService.uploadCSVToEBS(
          orderId,
          EBSStatus.MISMATCHED_COMPONENT_RETURN_RECEIVED,
          EBSStatusCodes.MISMATCHED_COMPONENT_RETURN_RECEIVED,
          EBSStatusComments.MISMATCHED_COMPONENT_RETURN_RECEIVED
        );
      }
    }
  }

  @Post('ebs/shipment')
  @AccessedClients(CLIENTS.EBS)
  @UseGuards(ClientGuard)
  async orderShipment(@Body() payload: ShipmentDto) {
    const result = await this.ttbService.updateOrderShipment(payload);
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    return result;
  }

  @Post('gvc/returned')
  @AccessedClients(CLIENTS.GVC, CLIENTS.ADMIN_PORTAL)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_AGENT)
  @UseGuards(ClientGuard)
  async gvcReturned(@Body() payload: GVCReturnedDto) {
    const result = await this.ttbService.updateOrderReturnedFromGVC(payload);
    try {
      this.loggingService.save({
        event: 'GVC-RETURNED',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus.RETURNED,
      EBSStatusCodes.RETURNED,
      EBSStatusComments.RETURNED
    );
    return result;
  }

  @Post('gvc/returned-to-tm')
  @AccessedClients(CLIENTS.GVC)
  @UseGuards(ClientGuard)
  async gvcReturnedToTM(@Body() payload: GVCReturnedToTMDto) {
    const result = await this.ttbService.updateOrderReturnedToTMFromGVC(payload);
    try {
      await this.loggingService.save({
        event: 'GVC-RETURN-TO-TM',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus.RETURNING_TO_TM,
      EBSStatusCodes.RETURNING_TO_TM,
      EBSStatusComments.RETURNING_TO_TM
    );
    return result;
  }

  @Post('gvc/received')
  @AccessedClients(CLIENTS.GVC)
  @UseGuards(ClientGuard)
  async gvcReceived(@Body() payload: GVCReceivedDto) {
    const result = await this.ttbService.updateOrderReceivedFromGVC(payload);
    try {
      await this.loggingService.save({
        event: 'GVC-RECEIVED',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus.RETURN_RECEIVED,
      EBSStatusCodes.RETURN_RECEIVED,
      EBSStatusComments.RETURN_RECEIVED
    );
    return result;
  }

  @Post('gvc/received-specs-mismatched')
  @AccessedClients(CLIENTS.GVC)
  @UseGuards(ClientGuard)
  async gvcReceivedSpecsMismatched(@Body() payload: GVCReceivedSpecsMismatchedDto) {
    const result = await this.ttbService.updateReceivedSpecsMismatched(payload);
    try {
      await this.loggingService.save({
        event: 'GVC-RECEIVED-SPECS-MISMATCHED',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus.RETURN_RECEIVED_SPECS_MISMATCHED,
      EBSStatusCodes.RETURN_RECEIVED_SPECS_MISMATCHED,
      EBSStatusComments.RETURN_RECEIVED_SPECS_MISMATCHED
    );
    return result;
  }

  @Post('gvc/mismatch/tracking-number')
  @AccessedClients(CLIENTS.GVC)
  @UseGuards(ClientGuard)
  async gvcReturnWrongComponents(@Body() payload: GVCReturnWrongComponentsDto) {
    const result = await this.ttbService.gvcReturnWrongComponents(payload);
    try {
      await this.loggingService.save({
        event: 'GVC-MISMATCH-TRACKING-NUMBER',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    return result;
  }

  @Post('gvc/tracking-number')
  @AccessedClients(CLIENTS.GVC)
  @UseGuards(ClientGuard)
  async gvcTrackingNumber(@Body() payload: GVCTrackingNumber) {
    const result = await this.ttbService.updateOrderTrackingNumberFromGVC(payload);
    try {
      await this.loggingService.save({
        event: 'GVC-TRACKING-NUMBER',
        payload,
      });
    } catch (error) {
      console.log(error);
    }
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    return result;
  }

  @Post('try')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async try(@Body() payload: TryDto, @Req() request: BaseRequest) {
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_ID_IS_NOT_VALID,
        errorMessage: 'Order id is not valid!',
      });
    }
    const existed = await this.ttbService.checkTTBExists(request.user.uid, payload.orderId);
    if (existed) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_ALREADY_EXIST,
        errorMessage: 'Your try then buy is already exist!',
      });
    }
    return await this.ttbService.postTry(request.user.uid, payload);
  }

  @Post('return-label')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async getReturnLabel(@Body() payload: TryDto, @Req() request: BaseRequest) {
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_ID_IS_NOT_VALID,
        errorMessage: 'Order id is not valid!',
      });
    }
    const existed = await this.ttbService.checkTTBExists(request.user.uid, payload.orderId);
    if (!existed) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_IS_NOT_EXIST,
        errorMessage: 'Your try then buy is not exist!',
      });
    }
    return await this.ttbService.postReturningLabel(payload.orderId);
  }

  @Post('cancel')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async cancel(@Body() payload: TryDto, @Req() request: BaseRequest) {
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_ID_IS_NOT_VALID,
        errorMessage: 'Order id is not valid!',
      });
    }
    const canCancel = await this.ttbService.canCancel(payload.orderId);
    if (!canCancel) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.DO_ABLE_TO_CANCEL_THIS_TRY_THEN_BUY,
        errorMessage: 'You do not able to cancel!',
      });
    }
    const existed = await this.ttbService.checkTTBExists(request.user.uid, payload.orderId);
    if (!existed) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.TTB_ORDER_IS_NOT_EXIST,
        errorMessage: 'Your try then buy is not exist!',
      });
    }
    return await this.ttbService.postCancel(request.user.uid, payload);
  }

  @Post('admin-cancel')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_MANAGER)
  @UseGuards(AuthGuard)
  async postCancelTTB(@Req() request: BaseRequest, @Body() data: CancelTTBDto) {
    return this.ttbService.postCancelByAdmin(request.user.uid, data.orderId, data.cancelReason);
  }

  @Post('buy')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async bought(@Body() payload: BoughtDto, @Req() request: BaseRequest) {
    await this.canUpdateOrder(request.user.uid, payload.orderId);
    const result = await this.ttbService.postBought(request.user.uid, payload);
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    await this.ttbService.trackingEventKlaviyoTTBByApi(KlaviyoTrackEvents.TTB_CHARGE_INITIATED, payload.orderId);
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus.BOUGHT,
      EBSStatusCodes.BOUGHT,
      EBSStatusComments.BOUGHT
    );
    return result;
  }

  @Post('return')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async return(@Body() payload: ReturnedDto, @Req() request: BaseRequest) {
    await this.canUpdateOrder(request.user.uid, payload.orderId);
    const result = await this.ttbService.postReturned(request.user.uid, payload);
    if (!result) {
      TTBController.throwOrderNotFoundError();
    }
    if (!result.success && result.trialEnded) {
      TTBController.throwTrialEndedError();
    }
    await this.ttbService.trackingEventKlaviyoTTBByApi(KlaviyoTrackEvents.TTB_RETURN_INITIATED, payload.orderId);
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus.RETURN,
      EBSStatusCodes.RETURN,
      EBSStatusComments.RETURN
    );
    await this.ttbCronService.uploadCSVToGVC(payload.orderId, GVCStatus.RETURN);
    return result;
  }

  @Get('orders-admin')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_AGENT, Role.CUSTOMER_SERVICE, Role.TTB_MANAGER)
  @UseGuards(AuthGuard)
  async getOrderAdmin(@Req() req: any) {
    return await this.ttbService.getAllTTB(req.query, req.user);
  }

  @Get('orders-admin-fraud')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getOrderAdminFraud(@Req() req: any) {
    return await this.ttbService.getAllTTBFraud(req.query);
  }

  @Post('orders-admin-fraud/note')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async updateTTBFraudNote(@Req() req: BaseRequest, @Body() payload: NoteTTBFraudPayloadDto) {
    return await this.ttbService.updateTTBFraudNote(payload, req.user.uid);
  }

  @Post('orders-admin-fraud/cancel')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async cancelTTBToGat(@Req() req: BaseRequest, @Body() payload: CancelTTBToGatPayloadDto) {
    return await this.ttbService.cancelTTBToGat(payload, req.user.uid);
  }

  @Post('test-notify-admin')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async testNotifyAdmin(@Body() payload: any) {
    return await this.ttbService.testNotifyAdmin(payload);
  }

  @Get('sync-gat-status')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async syncGatStatus() {
    return await this.ttbCronService.syncGatStatus();
  }

  @Get('config-threshold')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getConfigThreshold() {
    return await this.ttbService.getConfigThreshold();
  }

  @Post('config-threshold')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async saveConfigThreshold(@Req() request: BaseRequest, @Body() data: TTBConfigThresholdPayloadDto) {
    return await this.ttbService.saveConfigThreshold(request.user.uid, data);
  }

  @Post('create-event-klaviyo')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async createEventKlaviyo(@Body() payload: any) {
    return await this.ttbService.createEventKlaviyo(payload);
  }

  @Post('pause')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_AGENT, Role.TTB_MANAGER)
  @UseGuards(AuthGuard)
  async postPauseOrder(@Req() req: BaseRequest, @Body() payload: PauseOrderPayloadDto) {
    return await this.ttbService.postPauseOrder(req.user.uid, payload);
  }

  @Post('resume')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_AGENT, Role.TTB_MANAGER)
  @UseGuards(AuthGuard)
  async postResumeOrder(@Req() req: BaseRequest, @Body() payload: ResumeOrderPayloadDto) {
    return await this.ttbService.postResumeOrder(req.user.uid, payload);
  }

  @Post('upload-csv-to-ebs')
  @UseGuards(AuthGuard)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_MANAGER)
  async uploadCSVToEBS(@Body() payload: UploadCSVFileToEBS, @Req() request: BaseRequest) {
    if (!EBSStatus[payload.status]) {
      return TTBController.throwOrderStatusInvalidError(EBSStatus);
    }
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      return TTBController.throwOrderNotFoundError();
    }
    await this.ttbCronService.uploadCSVToEBS(
      payload.orderId,
      EBSStatus[payload.status],
      EBSStatusCodes[payload.status],
      EBSStatusComments[payload.status]
    );
    return {
      success: true,
    };
  }

  @Post('upload-csv-to-gvc')
  @UseGuards(AuthGuard)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_MANAGER)
  async uploadCSVToGVC(@Body() payload: UploadCSVFileToEBS, @Req() request: BaseRequest) {
    if (!GVCStatus[payload.status]) {
      return TTBController.throwOrderStatusInvalidError(GVCStatus);
    }
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      return TTBController.throwOrderNotFoundError();
    }
    await this.ttbCronService.uploadCSVToGVC(payload.orderId, GVCStatus[payload.status]);
    return {
      success: true,
    };
  }

  @Post('upload-csv-to-ups')
  @UseGuards(AuthGuard)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_MANAGER)
  async uploadCSVToUPS(@Body() payload: UploadCSVFileToEBS, @Req() request: BaseRequest) {
    if (!GVCStatus[payload.status]) {
      return TTBController.throwOrderStatusInvalidError(GVCStatus);
    }
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      return TTBController.throwOrderNotFoundError();
    }
    await this.ttbCronService.uploadCSVToGVC(payload.orderId, GVCStatus[payload.status], true);
    return {
      success: true,
    };
  }

  @Post('force-charge-orders')
  @UseGuards(AuthGuard)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_MANAGER)
  async forceChargeOrders(@Body() payload: ForceChargeOrdersPayloadDto, @Req() request: BaseRequest) {
    const orderIds = payload.orderIds;
    const orderIdsValid = [];
    const orderIdsInvalid = [];
    for (const orderId of orderIds) {
      const orderIdValid = await this.ttbService.checkOrderIdValid(orderId, request?.country);
      if (!orderIdValid) {
        orderIdsInvalid.push(orderId);
      } else {
        orderIdsValid.push(orderId);
      }
      const order = await this.ttbService.getOrder(orderId);
      if (order) {
        await this.ttbCronService.forceChargeOrders(order);
      }
    }
    return {
      addedToQueue: orderIdsValid,
      orderIdsInvalid,
    };
  }

  @Get('script-update-orders')
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async updateAmountTotalOrders(@Req() req: Request) {
    return await this.ttbService.updateAmountTotalOrders();
  }

  @Post('update-delivered-manually')
  @UseGuards(AuthGuard)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.TTB_MANAGER)
  async manuallyUpdateOrderDelivered(@Body() payload: UpdateOrderDeliveredDto, @Req() request: BaseRequest) {
    const orderIdValid = await this.ttbService.checkOrderIdValid(payload.orderId, request?.country);
    if (!orderIdValid) {
      return TTBController.throwOrderNotFoundError();
    }
    const deliveredDateString = payload.deliveredDate || moment().format('YYYY-MM-DD HH:mm');
    const deliveredDate = moment(deliveredDateString, 'YYYY-MM-DD HH:mm');
    const startedAt = moment(deliveredDate.clone().toDate().setHours(17, 0, 0, 0))
      .add(TTBStatusRangeDate[TTBStatus.TRIAL_STARTED], 'days')
      .toDate();
    return this.ttbCronService.updateOrderDelivered(payload.orderId, startedAt, deliveredDate);
  }

  async canUpdateOrder(uid: string, orderId: string) {
    const canUpdateOrder = await this.ttbService.canUpdateOrder(uid, orderId);
    if (!canUpdateOrder) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PERMISSION_DENIED,
        errorMessage: 'Permission denied!',
      });
    }
  }

  static throwOrderNotFoundError() {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.TTB_ORDER_NOT_FOUND,
      errorMessage: 'Order not found!',
    });
  }

  static throwOrderStatusInvalidError(validStatuses) {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.TTB_ORDER_STATUS_INVALID,
      errorMessage: 'Order status invalid!',
      validStatuses,
    });
  }

  static throwTrialEndedError() {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.TTB_TRIAL_ENDED,
      errorMessage: 'Trial ended!',
    });
  }
}

import { Process, Processor } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { TTBEntity, TTBStatus } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

export type TTBDeliveringScanJob = Job<{ orderId: string }>;

export enum TTBDeliveringProcessorQueueName {
  HANDLE = 'handle',
}

@Processor('ttb-delivering')
export class TTBDeliveringProcessor {
  constructor(
    private readonly config: ConfigService,
    private readonly ttbService: TTBService,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>
  ) {}

  @Process({
    name: TTBDeliveringProcessorQueueName.HANDLE,
    concurrency: 1,
  })
  async handle(job: TTBDeliveringScanJob): Promise<any> {
    const orderId = job.data.orderId;
    const order = await this.ttbRepo.findOne({ where: { orderId } });
    if (!order || order.status !== TTBStatus.TRIAL_COMING) {
      return order;
    }
    return this.ttbService.handleCheckOrderDeliveryStatus(order);
  }
}

import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import * as _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { In, Repository } from 'typeorm';
import { TTBPushEntity } from './entities/ttb-push.entity';
import { TTBEntity, TTBStatus } from './entities/ttb.entity';
import { TTBSyncGATStatusProcessorQueueName } from './ttb-sync-gat-status.processor';
import { TTBService } from './ttb.service';

export enum TTBGATStatusProcessorQueueName {
  EBS_GET_STATUS_GAT = 'ebs_get_status_gat',
}

const statusTTB = [TTBStatus.INITIALIZED, TTBStatus.TRIAL_COMING];

@Processor('ttb-gat-status')
export class TTBGATStatusProcessor {
  private readonly logger = new Logger(TTBGATStatusProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly ttbService: TTBService,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectQueue('ttb-sync-gat-status') private ttbSyncGatStatusQueue: Queue,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @Process({
    name: TTBGATStatusProcessorQueueName.EBS_GET_STATUS_GAT,
    concurrency: 1,
  })
  async getStatusGat(): Promise<any> {
    try {
      const ttbOrders = await this.ttbRepo.find({
        where: {
          status: In(statusTTB),
        },
      });
      if (ttbOrders && ttbOrders.length) {
        for (const ttb of ttbOrders) {
          try {
            await this.ttbSyncGatStatusQueue.add(TTBSyncGATStatusProcessorQueueName.EBS_SYNC_STATUS_GAT, {
              orderId: ttb.orderId,
            });
            // await this.syncOrderGatStatus(ttb);
          } catch (error) {
            console.log(`ERROR ADD JOB SYNC GAT STATUS: ${ttb?.orderId}: ${error.message}`);
            console.log(error);
          }
        }
      }
      return true;
    } catch (err) {
      console.log('TTB sync Gat Status from EBS', err);
      this.logger.log(`TTB sync Gat Status from EBS error ${new Date()} ${err}`);
      return false;
    }
  }

  async syncOrderGatStatus(ttb: TTBEntity) {
    const orderId = ttb.orderId;
    const dataEBS = await this.ttbService.getOrderInfoEBSv2(orderId);
    const orderLines = _.result(dataEBS, 'data.orderInfo.orderLines[0].statusCol', null);
    if (orderLines && orderLines.length) {
      const statusGats = [];
      orderLines.map((val: any) => {
        if (val.system === 'GAT') {
          statusGats.push(val?.statusValue);
        }
      });
      const statusGat = _.last(_.compact(statusGats));
      // console.log('statusGat', statusGat);
      this.logger.log(`TTB sync Gat Status EBS statusGat: ${statusGat}`);
      if (statusGat) {
        // ttb.gatStatus = statusGat;
        this.ttbRepo.update({ orderId: ttb.orderId }, { gatStatus: statusGat });
      }
    }
    return true;
  }
}

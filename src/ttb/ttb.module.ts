import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EcomModule } from '../ecom/ecom.module';
import { SharedModule } from '../shared/shared.module';
import { SYSTEM_TAG } from '../utils/constants';
import { TTBPushEntity } from './entities/ttb-push.entity';
import { TTBEntity } from './entities/ttb.entity';
import { TTBDeliveringProcessor } from './ttb-delivering.processor';
import { TTBEBSProcessor } from './ttb-ebs.processor';
import { TTBFraudProcessor } from './ttb-fraud.processor';
import { TTBGATStatusProcessor } from './ttb-gat-status.processor';
import { TTBGVCProcessor } from './ttb-gvc.processor';
import { TTBNotifyAdminProcessor } from './ttb-notify-admin.processor';
import { TTBNotifyProcessor } from './ttb-notify.processor';
import { TTBShipmentProcessor } from './ttb-shipment.processor';
import { TTBSyncGATStatusProcessor } from './ttb-sync-gat-status.processor';
import { TTBThresholdProcessor } from './ttb-threshold.processor';
import { TTBController } from './ttb.controller';
import { TTBCronService } from './ttb.cron.service';
import { TTBService } from './ttb.service';

let processors = [];

if (process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [...processors, TTBShipmentProcessor, TTBGVCProcessor, TTBEBSProcessor, TTBDeliveringProcessor];
}

if (process.env.TAG === SYSTEM_TAG.JOBS || process.env.TAG === SYSTEM_TAG.APIS) {
  processors = [
    ...processors,
    TTBNotifyProcessor,
    TTBFraudProcessor,
    TTBNotifyAdminProcessor,
    TTBGATStatusProcessor,
    TTBSyncGATStatusProcessor,
    TTBThresholdProcessor,
  ];
}

@Module({
  imports: [
    SharedModule,
    TypeOrmModule.forFeature([TTBEntity, TTBPushEntity]),
    BullModule.registerQueue({
      name: 'ttb-notify',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
    BullModule.registerQueue({
      name: 'ttb-shipment',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
    BullModule.registerQueue({
      name: 'ttb-delivering',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
    BullModule.registerQueue({
      name: 'ttb-gvc',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
    BullModule.registerQueue({
      name: 'ttb-ebs',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
    BullModule.registerQueue({
      name: 'ttb-gat-status',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 300000,
      },
    }),
    BullModule.registerQueue({
      name: 'ttb-sync-gat-status',
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 300000,
      },
    }),
    EcomModule,
  ],
  controllers: [TTBController],
  providers: [...processors, TTBService, TTBCronService],
  exports: [TTBService, TTBCronService],
})
export class TTBModule {}

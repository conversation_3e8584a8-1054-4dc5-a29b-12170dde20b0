import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDecimal,
  IsDefined,
  IsEnum,
  IsNotEmpty,
  IsNotEmptyObject,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export enum ShipmentStatus {
  DELIVERED = 'DELIVERED',
  SHIPPED = 'SHIPPED',
}
export enum SeventeentrackWebhookEvent {
  TRACKING_UPDATED = 'TRACKING_UPDATED',
  TRACKING_STOPPED = 'TRACKING_STOPPED',
}

export class TryDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  productInfo: string;
}

export class BoughtDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;
}

export class UploadCSVFileToEBS {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  status: string;
}

export class UpdateOrderDeliveredDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  deliveredDate: string;
}

export class ReturnedDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;
}

export class GVCReturnedToTMDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  carrierTrackingNumber: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  carrier: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  shipmentDate: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  productStatusInfo: string;
}

export class GVCReturnedDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsNotEmpty()
  @IsDecimal()
  amountPaid: number;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  productStatusInfo: string;
}

export class GVCReceivedDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;
}

export class GVCReceivedSpecsMismatchedDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsNotEmpty()
  orderLines: any;
}

export class GVCReturnWrongComponentsDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  carrierTrackingNumber: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  carrier: string;
}

export class GVCTrackingNumber {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  carrierTrackingNumber: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  carrier: string;
}

export class ShipmentDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsEnum(ShipmentStatus)
  status: ShipmentStatus;
}

class SeventeentrackWebhookDataDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  number: string;

  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  track: any;
}

export class SeventeentrackWebhookDto {
  @IsNotEmpty()
  @IsEnum(SeventeentrackWebhookEvent)
  event: SeventeentrackWebhookEvent;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  sign: string;

  @IsDefined()
  @IsNotEmptyObject()
  @IsObject()
  @Type(() => SeventeentrackWebhookDataDto)
  data!: SeventeentrackWebhookDataDto;
}

export class TTBUpdateUserDto {
  @IsNotEmpty()
  @IsString()
  data: string;
}

export class PauseOrderPayloadDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsString()
  @MaxLength(255)
  pauseReason: string;
}

export class TTBConfigThresholdPayloadDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  type: string;

  @IsString()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsNumber()
  totalOrder: number;
}

export class CancelTTBToGatPayloadDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  orderId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  userId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  reasonCancel: string;

  @IsOptional()
  @IsNumber()
  reasonCancelId: number;

  @IsOptional()
  @IsBoolean()
  isBlack: boolean;
}

export class NoteTTBFraudPayloadDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  ttbFraudId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  note: string;
}

export class ResumeOrderPayloadDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  orderId: string;
}
export class ForceChargeOrdersPayloadDto {
  @IsArray()
  orderIds: string[];
}

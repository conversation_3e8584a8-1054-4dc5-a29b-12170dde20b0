import { Column, CreateDateColumn, <PERSON>ti<PERSON>, Join<PERSON><PERSON>umn, <PERSON>To<PERSON><PERSON>, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { UserEntity } from '../../auth/entities/user.entity';

export enum TTBStatus {
  INITIALIZED = 'INITIALIZED',
  TRIAL_COMING = 'TRIAL_COMING',
  TRIAL_STARTED = 'TRIAL_STARTED',
  TRIAL_HALF_DONE = 'TRIAL_HALF_DONE',
  TRIAL_2_DAYS_LEFT = 'TRIAL_2_DAYS_LEFT',
  TRIAL_1_DAY_LEFT = 'TRIAL_1_DAY_LEFT',
  TIME_TO_DECIDE = 'TIME_TO_DECIDE',
  LAST_CHANCE = 'LAST_CHANCE',
  PRODUCT_RETURNING = 'PRODUCT_RETURNING',
  PRODUCT_PICKED_UP = 'PRODUCT_PICKED_UP',
  PRODUCT_RETURNED = 'PRODUCT_RETURNED',
  PRODUCT_RECEIVED = 'PRODUCT_RECEIVED',
  BOUGHT = 'BOUGHT',
  CANCELLED = 'CANCELLED',
  CHARGED = 'CHARGED',
  MISMATCHED_COMPONENT = 'MISMATCHED_COMPONENT',
  MISMATCHED_COMPONENT_RETURN_PICKED_UP = 'MISMATCHED_COMPONENT_RETURN_PICKED_UP',
  MISMATCHED_COMPONENT_RETURN_RECEIVED = 'MISMATCHED_COMPONENT_RETURN_RECEIVED',
  PRODUCT_RETURNING_TO_TM = 'PRODUCT_RETURNING_TO_TM',
  PRODUCT_RECEIVED_TO_TM = 'PRODUCT_RECEIVED_TO_TM',
  ONE_WEEK_LEFT_TO_FORCED_CHARGE = 'ONE_WEEK_LEFT_TO_FORCED_CHARGE',
}

export enum GVCStatus {
  RETURN = 'Return',
  BOUGHT = 'Bought',
  CHARGED = 'Charged',
}

export enum EBSStatus {
  RETURN_RECEIVED = 'Return Received',
  RETURN_RECEIVED_SPECS_MISMATCHED = 'Return Received Specs Mismatched',
  MISMATCHED_COMPONENT_RETURN_RECEIVED = 'Mismatched Components Return Received',
  RETURNED = 'Returned',
  RETURNED_TO_TM = 'Returned To TM',
  RETURNING_TO_TM = 'Returning To TM',
  RETURN_TO_TM_RECEIVED = 'Returning To TM',
  BOUGHT = 'Bought',
  CHARGED = 'Forced Charge',
  RETURN = 'Returned Awaiting Physical Product',
}

export enum EBSStatusCodes {
  RETURN_RECEIVED = 'RC',
  RETURN_RECEIVED_SPECS_MISMATCHED = 'RCSM',
  MISMATCHED_COMPONENT_RETURN_RECEIVED = 'MCRR',
  RETURNED = 'CR',
  RETURNED_TO_TM = 'RDTM',
  RETURNING_TO_TM = 'RITM',
  RETURN_TO_TM_RECEIVED = 'RTMR',
  BOUGHT = 'CA',
  CHARGED = 'CA',
  RETURN = 'RA',
}

export enum EBSStatusComments {
  RETURN_RECEIVED = 'Return Received',
  RETURN_RECEIVED_SPECS_MISMATCHED = 'Return Received Specs Mismatched',
  MISMATCHED_COMPONENT_RETURN_RECEIVED = 'Mismatched Components Return Received',
  RETURNED = 'Returned',
  RETURNING_TO_TM = 'Returning To TM',
  RETURNED_TO_TM = 'Returned To TM',
  RETURN_TO_TM_RECEIVED = 'Return To TM Received',
  BOUGHT = 'Bought the Product',
  CHARGED = 'Bought the Product',
  RETURN = 'Returned Awaiting Physical Product',
}

export enum TTBStatusDescription {
  INITIALIZED = 'Your order is being processed',
  INITIALIZED_WITH_ESTIMATED_ARRIVAL_DATE = 'Your trial club is expected to arrive on [date]',
  TRIAL_COMING = 'Your trial club is on its way',
  TRIAL_2_DAYS_LEFT = 'Your trial is almost over',
  TRIAL_1_DAY_LEFT = 'Your trial is almost over',
}

export enum TTBSpecsMismatchedReturnWrongComponentsStatus {
  RETURNING = 'RETURNING',
  RETURNED = 'RETURNED',
}

@Entity('TryThenBuy')
export class TTBEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  orderId: string;

  @Column()
  status: TTBStatus;

  @Column()
  productInfo: string;

  @Column()
  pushedAt: Date;

  @Column()
  startedAt: Date;

  @Column()
  deliveredAt: Date;

  @Column()
  notifiedGVCAt: Date;

  @Column()
  notifiedEBSAt: Date;

  @Column()
  shippedAt: Date;

  @Column('decimal', { precision: 6, scale: 2 })
  paymentAmount: number;

  @Column('decimal', { precision: 6, scale: 2 })
  amountPaid: number;

  @Column()
  productStatusInfo: string;

  @Column()
  shipmentTrackingNumber: string;

  @Column()
  shipmentCarrierName: string;

  @Column()
  shipmentSeventeentrackCarrierKey: string;

  @Column()
  GVCReturnTrackingNumber: string;

  @Column()
  GVCReturnTrackingCarrier: string;

  @Column()
  GVCReturnToTMTrackingNumber: string;

  @Column()
  GVCReturnToTMTrackingCarrier: string;

  @Column()
  GVCReturnSeventeentrackCarrierKey: string;

  @Column()
  GVCReturnToTMSeventeentrackCarrierKey: string;

  @Column()
  specsMismatchedReturnTrackingNumber: string;

  @Column()
  specsMismatchedReturnTrackingCarrier: string;

  @Column()
  specsMismatchedReturnSeventeentrackCarrierKey: string;

  @Column()
  specsMismatchedReturnReceivedAt: Date;

  @Column()
  specsMismatchedReturnWrongComponentsTrackingNumber: string;

  @Column()
  specsMismatchedReturnWrongComponentsTrackingCarrier: string;

  @Column()
  specsMismatchedReturnWrongComponentsSeventeentrackCarrierKey: string;

  @Column()
  specsMismatchedReturnWrongComponentsStatus: string;

  @Column()
  specsMismatchedReturnWrongComponentsReceivedAt: Date;

  @Column()
  GVCReceivedSpecsMismatchedInfo: string;

  @Column()
  GVCReceivedSpecsMismatchedAt: Date;

  @Column()
  shipmentDate: Date;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  canceledAt: Date;

  @Column()
  GVCReturnProductReceivedAt: Date;

  @Column()
  GVCReturnToTMProductReceivedAt: Date;

  @Column()
  scheduledArrivalDate: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  canceledBy: string;

  @Column()
  cancelReason: string;

  @Column()
  deletedBy: string;

  @Column('decimal', { precision: 6, scale: 2 })
  amountTotal: number;

  @OneToOne((type) => UserEntity)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @Column()
  paused: boolean;

  @Column()
  pauseReason: string;

  @Column()
  pausedBy: string;

  @Column()
  pausedAt: Date;

  @Column()
  returnLabelUrl: string;

  @Column()
  purchaseIPAddress: string;

  @Column()
  createNewReturnLabel: boolean;

  @Column()
  isSimulator: boolean;

  @Column()
  fingerPrint: string;

  @Column({ type: 'float' })
  purchaseLatitude: number;

  @Column({ type: 'float' })
  purchaseLongitude: number;

  @Column()
  gatStatus: string;

  @Column('bit')
  isAdminCancel: boolean;

  @Column()
  country: string;
}

import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('TryThenBuyFraudInfo')
export class TTBFraudInfoEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  tryThenBuyId: string;

  @Column()
  tryThenBuyIdFraudId: string;

  @Column('bit')
  isIPAddress: boolean;

  @Column('bit')
  isGPS: boolean;

  @Column('bit')
  isFingerPrint: boolean;

  @Column('bit')
  isLastName: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;
}

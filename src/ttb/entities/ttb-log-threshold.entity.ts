import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('TryThenBuyLogThreshold')
export class TTBLogThresholdEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  type: string;

  @Column()
  totalOrder: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}

import { <PERSON>umn, CreateDateColumn, Entity, JoinColumn, OneToOne, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { UserBlackListEntity } from '../../auth/entities/user-black-list.entity';
import { UserEntity } from '../../auth/entities/user.entity';
import { TTBEntity } from './ttb.entity';

@Entity('TryThenBuyFraud')
export class TTBFraudEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  tryThenBuyId: string;

  @Column()
  orderId: string;

  @Column()
  userId: string;

  @Column()
  email: string;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @Column()
  purchaseIPAddress: string;

  @Column('bit')
  isSimulator: boolean;

  @Column('bit')
  isUserBlackList: boolean;

  @Column()
  fingerPrint: string;

  @Column({ type: 'float' })
  purchaseLatitude: number;

  @Column({ type: 'float' })
  purchaseLongitude: number;

  @Column({ type: 'float' })
  potentialFraud: number;

  @Column()
  note: string;

  @Column()
  duplicate: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @OneToOne(() => UserEntity)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @OneToOne(() => TTBEntity)
  @JoinColumn({ name: 'tryThenBuyId' })
  trythenbuy: TTBEntity;

  @Column()
  purchaseIPAddressLocation: string;

  @Column()
  purchaseGPSLocation: string;
}

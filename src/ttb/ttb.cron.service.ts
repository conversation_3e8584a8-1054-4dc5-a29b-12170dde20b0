import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { Brackets, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { NotificationService } from 'src/notification/notification.service';
import { UserEntity } from '../auth/entities/user.entity';
import { EcomService } from '../ecom/ecom.service';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { SeventeentrackService } from '../shared/services/seventeentrack.service';
import { isCronJobHandlersEnabled, isProduction } from '../utils/cron';
import { TTBPushEntity } from './entities/ttb-push.entity';
import {
  EBSStatus,
  EBSStatusCodes,
  EBSStatusComments,
  GVCStatus,
  TTBEntity,
  TTBSpecsMismatchedReturnWrongComponentsStatus,
  TTBStatus,
} from './entities/ttb.entity';
import { TTBDeliveringProcessorQueueName } from './ttb-delivering.processor';
import { TTBEBSProcessorQueueName } from './ttb-ebs.processor';
import { TTBGATStatusProcessorQueueName } from './ttb-gat-status.processor';
import { TTBGVCProcessorQueueName } from './ttb-gvc.processor';
import { TTBNotifyProcessorQueueName } from './ttb-notify.processor';
import { TTBShipmentProcessorQueueName } from './ttb-shipment.processor';
import { TTBPushMessage, TTBStatusRangeDate } from './ttb.constants';
import { TTBService } from './ttb.service';

@Injectable()
export class TTBCronService {
  private readonly logger = new Logger(TTBCronService.name);
  constructor(
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectQueue('ttb-notify') private ttbNotifyQueue: Queue,
    @InjectQueue('ttb-shipment') private ttbShipmentQueue: Queue,
    @InjectQueue('ttb-delivering') private ttbDeliveringQueue: Queue,
    @InjectQueue('ttb-gvc') private ttbGVCQueue: Queue,
    @InjectQueue('ttb-ebs') private ttbEBSQueue: Queue,
    @InjectQueue('ttb-gat-status') private ttbGatStatusQueue: Queue,
    private readonly ttbService: TTBService,
    private readonly config: ConfigService,
    private readonly seventeentrackService: SeventeentrackService,
    private ecomService: EcomService,
    private klaviyoService: KlaviyoService,
    private notificationService: NotificationService
  ) {}

  isReachedTimeLeftPush(order: TTBEntity, currentStatus: TTBStatus, nextStatus: TTBStatus) {
    if (!order.startedAt) {
      return false;
    }
    return (
      moment(order.startedAt)
        .add(TTBStatusRangeDate[nextStatus], this.config.get('app.ttbTimeUnit'))
        .isSameOrBefore(moment()) && order.status === currentStatus
    );
  }

  isProductReturnedWithinTrialPeriod(order: TTBEntity) {
    return (
      moment(order.startedAt)
        .add(parseInt(this.config.get('app.ttbReturningAmount'), 10), this.config.get('app.ttbReturningTimeUnit'))
        .isSameOrAfter(moment()) &&
      (order.status === TTBStatus.PRODUCT_RETURNED || order.status === TTBStatus.PRODUCT_RECEIVED_TO_TM)
    );
  }

  static notForcedChargeOrderStatuses() {
    return [
      // TTBStatus.PRODUCT_RETURNING,
      TTBStatus.PRODUCT_RECEIVED,
      TTBStatus.PRODUCT_RETURNED,
      TTBStatus.PRODUCT_PICKED_UP,
      TTBStatus.MISMATCHED_COMPONENT,
      TTBStatus.MISMATCHED_COMPONENT_RETURN_PICKED_UP,
      TTBStatus.MISMATCHED_COMPONENT_RETURN_RECEIVED,
      TTBStatus.PRODUCT_RECEIVED_TO_TM,
    ];
  }

  isProductNotReturnedWithinTrialPeriod(order: TTBEntity) {
    return (
      moment(order.startedAt)
        .add(parseInt(this.config.get('app.ttbReturningAmount'), 10), this.config.get('app.ttbReturningTimeUnit'))
        .isSameOrBefore(moment()) && !TTBCronService.notForcedChargeOrderStatuses().includes(order.status)
    );
  }

  isOrderHasOneWeekLeftToForcedCharge(order: TTBEntity) {
    return (
      moment(order.startedAt)
        .add(parseInt(this.config.get('app.ttbReturningAmount'), 10) - 7, this.config.get('app.ttbReturningTimeUnit'))
        .isSameOrBefore(moment()) && !TTBCronService.notForcedChargeOrderStatuses().includes(order.status)
    );
  }

  isOrderReachTimeToForcedCharge(order: TTBEntity) {
    return (
      moment(order.startedAt)
        .add(parseInt(this.config.get('app.ttbReturningAmount'), 10), this.config.get('app.ttbReturningTimeUnit'))
        .isSameOrBefore(moment()) && !TTBCronService.notForcedChargeOrderStatuses().includes(order.status)
    );
  }

  isSpecsMismatchedNotReturnedWithinValidPeriod(order: TTBEntity) {
    if (!order.GVCReceivedSpecsMismatchedAt) return false;
    return (
      moment(order.GVCReceivedSpecsMismatchedAt)
        .add(
          parseInt(this.config.get('app.ttbSpecsMismatchedAmount'), 10),
          this.config.get('app.ttbSpecsMismatchedUnit')
        )
        .isSameOrBefore(moment()) && order.status === TTBStatus.MISMATCHED_COMPONENT
    );
  }

  async handleTrialTimeLeftPush(order: TTBEntity, user: UserEntity, nextStatus: TTBStatus) {
    await this.ttbRepo.update({ orderId: order.orderId }, { status: nextStatus });
    order.status = nextStatus;
    await this.pushNotification(order, user, order.status);
    return order;
  }

  async uploadCSVToGVC(orderId: string, status: GVCStatus, onlyUPS = false) {
    return;
    // await this.ttbGVCQueue.add(TTBGVCProcessorQueueName.GVC_CSV_UPLOADS, {
    //   orderId,
    //   status,
    //   onlyUPS,
    // });
  }

  async uploadCSVToEBS(orderId: string, status: EBSStatus, statusCode: EBSStatusCodes, comments: EBSStatusComments) {
    return;
    // await this.ttbEBSQueue.add(
    //   TTBEBSProcessorQueueName.EBS_CSV_UPLOADS,
    //   {
    //     orderId,
    //     status,
    //     statusCode,
    //     comments,
    //   },
    //   {
    //     attempts: 10,
    //     backoff: 300000,
    //   }
    // );
  }

  async pushNotification(order: TTBEntity, user: UserEntity, status: TTBStatus) {
    const statusPush = status as any;
    const existedPushQueue = await this.ttbPushRepo.findOne({
      where: {
        userId: order.userId,
        status: statusPush,
        orderId: order.orderId,
      },
    });
    if (existedPushQueue) {
      return;
    }
    const delayed = await this.ttbService.getDelayedTimeForPushAndTriggerNotifications();
    const ttbPush = new TTBPushEntity();
    ttbPush.id = v4();
    ttbPush.userId = order.userId;
    ttbPush.orderId = order.orderId;
    ttbPush.status = status;
    ttbPush.pushedAt = new Date();
    ttbPush.createdBy = order.userId;
    await this.ttbPushRepo.save(ttbPush);
    const variables = {
      message: TTBPushMessage[status].message,
      title: TTBPushMessage[status].title,
    };
    const ctaLink = this.config.get('app.ttbDeepLink');
    const userNotification = await this.notificationService.saveNotificationFromOtherService(
      order.userId,
      ctaLink,
      variables
    );
    const userNotificationId = userNotification ? userNotification.id : '';
    await this.ttbRepo.update({ orderId: order.orderId }, { pushedAt: new Date() });
    // await this.ttbNotifyQueue.add(
    //   TTBNotifyProcessorQueueName.PUSH,
    //   {
    //     userEmail: user.email,
    //     userFCMToken: user.fcmToken,
    //     orderId: order.orderId,
    //     message: TTBPushMessage[status].message,
    //     title: TTBPushMessage[status].title,
    //     status: order.status,
    //     userNotificationId,
    //   },
    //   { delay: delayed }
    // );
    return order;
  }

  // async handleOrderPush(order: TTBEntity) {
  //   try {
  //     this.logger.debug(`Handling ${order.orderId} with status ${order.status}...`);
  //     const user = await this.userRepo.findOne({ id: order.userId });
  //     if (!user) {
  //       return null;
  //     }
  //     if (this.isReachedTimeLeftPush(order, TTBStatus.TRIAL_STARTED, TTBStatus.TRIAL_HALF_DONE)) {
  //       console.log(`Order ${order.orderId}: TRIAL_HALF_DONE`);
  //       return this.handleTrialTimeLeftPush(order, user, TTBStatus.TRIAL_HALF_DONE);
  //     }
  //     if (this.isReachedTimeLeftPush(order, TTBStatus.TRIAL_HALF_DONE, TTBStatus.TRIAL_2_DAYS_LEFT)) {
  //       console.log(`Order ${order.orderId}: TRIAL_2_DAYS_LEFT`);
  //       return this.handleTrialTimeLeftPush(order, user, TTBStatus.TRIAL_2_DAYS_LEFT);
  //     }
  //     if (this.isReachedTimeLeftPush(order, TTBStatus.TRIAL_2_DAYS_LEFT, TTBStatus.TRIAL_1_DAY_LEFT)) {
  //       return this.handleTrialTimeLeftPush(order, user, TTBStatus.TRIAL_1_DAY_LEFT);
  //     }
  //     if (this.isReachedTimeLeftPush(order, TTBStatus.TRIAL_1_DAY_LEFT, TTBStatus.TIME_TO_DECIDE)) {
  //       console.log(`Order ${order.orderId}: TIME_TO_DECIDE`);
  //       return this.handleTrialTimeLeftPush(order, user, TTBStatus.TIME_TO_DECIDE);
  //     }
  //     if (this.isReachedTimeLeftPush(order, TTBStatus.TIME_TO_DECIDE, TTBStatus.LAST_CHANCE)) {
  //       console.log(`Order ${order.orderId}: LAST_CHANCE`);
  //       return this.handleTrialTimeLeftPush(order, user, TTBStatus.LAST_CHANCE);
  //     }
  //
  //     // NOTE: 05-04-2023  Short force charge
  //     // this.logger.debug(`isOrderReachTimeToForcedCharge: ${this.isOrderReachTimeToForcedCharge(order)}`);
  //     // if (this.isOrderReachTimeToForcedCharge(order)) {
  //     //   return await this.handleCheckChargeOrder(order, user);
  //     // }
  //
  //     this.logger.debug(`isOrderHasOneWeekLeftToForcedCharge: ${this.isOrderHasOneWeekLeftToForcedCharge(order)}`);
  //     if (this.isOrderHasOneWeekLeftToForcedCharge(order)) {
  //       this.logger.debug(`isOrderHasOneWeekLeftToForcedCharge: ${order.orderId}`);
  //       order.status = TTBStatus.ONE_WEEK_LEFT_TO_FORCED_CHARGE;
  //       const pushed = (await this.ttbPushRepo.count({ orderId: order.orderId, status: order.status })) > 0;
  //       if (pushed) {
  //         return this.handleCheckChargeOrder(order, user);
  //       }
  //       await this.ttbRepo.update({ orderId: order.orderId }, { status: order.status });
  //       await this.pushNotification(order, user, order.status);
  //       return;
  //     }
  //
  //     const pushed = (await this.ttbPushRepo.count({ orderId: order.orderId, status: order.status })) > 0;
  //     if (pushed) {
  //       return order;
  //     }
  //     if (this.isProductReturnedWithinTrialPeriod(order)) {
  //       await this.pushNotification(order, user, order.status);
  //       return order;
  //     }
  //     switch (order.status) {
  //       case TTBStatus.TRIAL_COMING:
  //         console.log(`Order ${order.orderId}: TRIAL_COMING`);
  //         await this.pushNotification(order, user, order.status);
  //         break;
  //       case TTBStatus.TRIAL_STARTED:
  //         console.log(`Order ${order.orderId}: TRIAL_STARTED`);
  //         await this.pushNotification(order, user, order.status);
  //         break;
  //       case TTBStatus.MISMATCHED_COMPONENT:
  //         console.log(`Order ${order.orderId}: MISMATCHED_COMPONENT`);
  //         await this.pushNotification(order, user, order.status);
  //         break;
  //       default:
  //         break;
  //     }
  //     return order;
  //   } catch (e) {
  //     this.logger.error(e.message);
  //     return order;
  //   }
  // }
  // async handleCheckChargeOrder(order: TTBEntity, user: UserEntity) {
  //   try {
  //     this.logger.debug(`handleCheckChargeOrder... ${order.orderId}`);
  //     if (
  //       this.isProductNotReturnedWithinTrialPeriod(order) ||
  //       this.isSpecsMismatchedNotReturnedWithinValidPeriod(order)
  //     ) {
  //       this.logger.debug(
  //         'handleCheckChargeOrder isProductNotReturnedWithinTrialPeriod, isSpecsMismatchedNotReturnedWithinValidPeriod: true...'
  //       );
  //       await this.ttbRepo.update({ orderId: order.orderId }, { status: TTBStatus.CHARGED });
  //       order.status = TTBStatus.CHARGED;
  //       const pushed = (await this.ttbPushRepo.count({ orderId: order.orderId, status: order.status })) > 0;
  //       if (pushed) {
  //         return;
  //       }
  //       await this.pushNotification(order, user, order.status);
  //       // Don't upload to EBS
  //       // await this.ttbGVCQueue.add(TTBGVCProcessorQueueName.GVC_CSV_UPLOADS, {
  //       //   orderId: order.orderId,
  //       //   status: GVCStatus.CHARGED,
  //       // });
  //       await this.uploadCSVToEBS(order.orderId, EBSStatus.CHARGED, EBSStatusCodes.CHARGED, EBSStatusComments.CHARGED);
  //       return order;
  //     }
  //   } catch (error) {
  //     this.logger.error(error.message);
  //     return null;
  //   }
  // }
  async forceChargeOrders(order: TTBEntity) {
    const user = await this.userRepo.findOne({ id: order.userId });
    if (!user) {
      return null;
    }
    try {
      await this.ttbRepo.update({ orderId: order.orderId }, { status: TTBStatus.CHARGED });
      try {
        await this.pushNotification(order, user, TTBStatus.CHARGED);
      } catch (error) {
        console.log(`Error push notification: ${error}`);
      }
      // Dont upload to GVC and EBS
      // await this.ttbGVCQueue.add(TTBGVCProcessorQueueName.GVC_CSV_UPLOADS, {
      //   orderId: order.orderId,
      //   status: GVCStatus.CHARGED,
      // });
      await this.uploadCSVToEBS(order.orderId, EBSStatus.CHARGED, EBSStatusCodes.CHARGED, EBSStatusComments.CHARGED);
      return order.id;
    } catch (error) {
      this.logger.error(`Error forceCharge: ${order.id}: ${error}`);
      return null;
    }
  }

  async updateOrderDelivered(orderId: string, startedAt, deliveredDate) {
    await this.ttbRepo.update(
      { orderId },
      { status: TTBStatus.TRIAL_STARTED, startedAt, deliveredAt: deliveredDate.toDate() }
    );
  }

  async is17TrackOrderDelivered(trackingNumber: string, carrier: string) {
    const trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(trackingNumber, carrier);
    if (!trackingNumberInfo) {
      return false;
    }
    return this.seventeentrackService.isTrackingNumberDelivered(trackingNumberInfo);
  }

  // @Cron(CronExpression.EVERY_12_HOURS)
  // async handleRunCheckGatStatus() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   // sync Gat Status From EBS
  //   await this.ttbGatStatusQueue.add(TTBGATStatusProcessorQueueName.EBS_GET_STATUS_GAT);
  // }

  // @Cron(isProduction ? CronExpression.EVERY_30_MINUTES : CronExpression.EVERY_MINUTE)
  // async handlePushNotificationCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const beforeDate = moment().subtract(
  //     parseInt(this.config.get('app.ttbReturningAmount'), 10) + 1,
  //     this.config.get('app.ttbReturningTimeUnit')
  //   );
  //   this.logger.debug(`handlePushNotificationCron: Scan orders startedAt >= ${beforeDate.toDate()}`);
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status NOT IN (:...statuses)', {
  //       statuses: [TTBStatus.BOUGHT, TTBStatus.CHARGED, TTBStatus.INITIALIZED, TTBStatus.CANCELLED],
  //     })
  //     // .andWhere('order.paused IS NULL')
  //     .andWhere(
  //       new Brackets((qb) => {
  //         qb.where('order.paused IS NULL').orWhere('order.paused = 0');
  //       })
  //     )
  //     .andWhere(
  //       new Brackets((qb) => {
  //         qb.where('order.startedAt >= :beforeDate', {
  //           beforeDate: beforeDate.toDate(),
  //         }).orWhere('order.startedAt IS NULL');
  //       })
  //     )
  //     .getMany();
  //
  //   this.logger.debug(`Handling ${orders.length} orders...`);
  //
  //   const handleOrderPushPromises = [];
  //   for (const order of orders) {
  //     await this.handleOrderPush(order);
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderShippingStatusCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [TTBStatus.INITIALIZED, TTBStatus.TRIAL_COMING],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking shipping status for ${orders.length} orders...`);
  //   if (orders.length > 0) {
  //     await this.ttbShipmentQueue.add(TTBShipmentProcessorQueueName.SHIPMENT_SCANS, {
  //       orderIds: orders.map((order) => order.orderId),
  //     });
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderEstimatedArrivalDateCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [TTBStatus.INITIALIZED, TTBStatus.TRIAL_COMING],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking estimated arrival date for ${orders.length} orders...`);
  //   if (orders.length > 0) {
  //     for (const order of orders) {
  //       await new Promise((r) => setTimeout(r, 4000));
  //       const scheduledArrivalDate = await this.ttbService.getEstimatedArrivalDateFromClubTracker(order.orderId);
  //       if (scheduledArrivalDate) {
  //         try {
  //           await this.ttbRepo.update({ orderId: order.orderId }, { scheduledArrivalDate });
  //         } catch (e) {
  //           console.log(e.message);
  //         }
  //       }
  //     }
  //   }
  // }

  // @Cron(CronExpression.EVERY_4_HOURS)
  // async handleCheckOrderCanceledCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [
  //         TTBStatus.INITIALIZED,
  //         TTBStatus.TRIAL_COMING,
  //         TTBStatus.TRIAL_STARTED,
  //         TTBStatus.TRIAL_HALF_DONE,
  //         TTBStatus.TRIAL_2_DAYS_LEFT,
  //         TTBStatus.TRIAL_1_DAY_LEFT,
  //         TTBStatus.TIME_TO_DECIDE,
  //         TTBStatus.LAST_CHANCE,
  //       ],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking canceled status for ${orders.length} orders...`);
  //   for (const order of orders) {
  //     await new Promise((resolve) => setTimeout(resolve, 4000));
  //     const cancellationStatus = await this.ttbService.isOrderCanceledFromClubTracker(order.orderId);
  //     if (cancellationStatus && cancellationStatus.canceled) {
  //       await this.ttbRepo.update(
  //         { orderId: order.orderId },
  //         { status: TTBStatus.CANCELLED, canceledAt: cancellationStatus.canceledAt }
  //       );
  //       const ecomOrder = await this.ecomService.getOrder(order.orderId, order?.country);
  //       if (ecomOrder) {
  //         const productInfo = JSON.parse(order?.productInfo || JSON.stringify({}));
  //         if (productInfo.cTmCustomconfiguratorNodes) {
  //           productInfo.cTmCustomconfiguratorNodes = JSON.parse(productInfo.cTmCustomconfiguratorNodes);
  //         }
  //         const user = await this.userRepo.findOne({ id: order?.userId });
  //         await this.ttbService.trackingEventKlaviyoTTB(
  //           KlaviyoTrackEvents.TTB_CANCELATION,
  //           user?.email,
  //           ecomOrder,
  //           productInfo
  //         );
  //       }
  //     }
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderGVCProductReceivedCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [TTBStatus.PRODUCT_RETURNING, TTBStatus.PRODUCT_PICKED_UP],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking GVC returning status for ${orders.length} orders...`);
  //   if (orders.length > 0) {
  //     const handleOrderPromises = [];
  //     orders.forEach(
  //       (order) =>
  //         new Promise<void>(async (resolve) => {
  //           if (
  //             order.GVCReturnTrackingNumber &&
  //             order.GVCReturnTrackingCarrier &&
  //             order.GVCReturnSeventeentrackCarrierKey
  //           ) {
  //             const delivered = await this.is17TrackOrderDelivered(
  //               order.GVCReturnTrackingNumber,
  //               order.GVCReturnTrackingCarrier
  //             );
  //             if (delivered) {
  //               await this.ttbRepo.update(
  //                 { orderId: order.orderId },
  //                 { status: TTBStatus.PRODUCT_RECEIVED, GVCReturnProductReceivedAt: new Date() }
  //               );
  //             }
  //           }
  //           resolve();
  //         })
  //     );
  //     await Promise.all(handleOrderPromises);
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderSpecsMismatchedReturnReceivedCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [TTBStatus.MISMATCHED_COMPONENT, TTBStatus.MISMATCHED_COMPONENT_RETURN_PICKED_UP],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking order specs mismatched returning status for ${orders.length} orders...`);
  //   if (orders.length > 0) {
  //     const handleOrderPromises = [];
  //     orders.forEach(
  //       (order) =>
  //         new Promise<void>(async (resolve) => {
  //           if (
  //             order.specsMismatchedReturnTrackingNumber &&
  //             order.specsMismatchedReturnTrackingCarrier &&
  //             order.specsMismatchedReturnSeventeentrackCarrierKey
  //           ) {
  //             const delivered = await this.is17TrackOrderDelivered(
  //               order.specsMismatchedReturnTrackingNumber,
  //               order.specsMismatchedReturnTrackingCarrier
  //             );
  //             if (delivered) {
  //               await this.ttbRepo.update(
  //                 { orderId: order.orderId },
  //                 {
  //                   status: TTBStatus.MISMATCHED_COMPONENT_RETURN_RECEIVED,
  //                   specsMismatchedReturnReceivedAt: new Date(),
  //                 }
  //               );
  //               await this.uploadCSVToEBS(
  //                 order.orderId,
  //                 EBSStatus.RETURN_RECEIVED_SPECS_MISMATCHED,
  //                 EBSStatusCodes.RETURN_RECEIVED_SPECS_MISMATCHED,
  //                 EBSStatusComments.RETURN_RECEIVED_SPECS_MISMATCHED
  //               );
  //             }
  //           }
  //           resolve();
  //         })
  //     );
  //     await Promise.all(handleOrderPromises);
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderSpecsMismatchedReturnWrongComponentsReceivedCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.specsMismatchedReturnWrongComponentsStatus IN (:...statuses)', {
  //       statuses: [TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNING],
  //     })
  //     .getMany();
  //   this.logger.debug(
  //     `Checking order specs mismatched returning wrong components status for ${orders.length} orders...`
  //   );
  //   if (orders.length > 0) {
  //     const handleOrderPromises = [];
  //     orders.forEach(
  //       (order) =>
  //         new Promise<void>(async (resolve) => {
  //           if (
  //             order.specsMismatchedReturnWrongComponentsTrackingNumber &&
  //             order.specsMismatchedReturnWrongComponentsTrackingCarrier &&
  //             order.specsMismatchedReturnWrongComponentsSeventeentrackCarrierKey
  //           ) {
  //             const delivered = await this.is17TrackOrderDelivered(
  //               order.specsMismatchedReturnWrongComponentsTrackingNumber,
  //               order.specsMismatchedReturnWrongComponentsTrackingCarrier
  //             );
  //             if (delivered) {
  //               await this.ttbRepo.update(
  //                 {
  //                   orderId: order.orderId,
  //                   specsMismatchedReturnWrongComponentsStatus: TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNING,
  //                 },
  //                 {
  //                   specsMismatchedReturnWrongComponentsStatus: TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNED,
  //                   specsMismatchedReturnWrongComponentsReceivedAt: new Date(),
  //                 }
  //               );
  //             }
  //           }
  //           resolve();
  //         })
  //     );
  //     await Promise.all(handleOrderPromises);
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderGVCProductReturnToTMReceivedCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [TTBStatus.PRODUCT_RETURNING_TO_TM],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking GVC returning to TM status for ${orders.length} orders...`);
  //   if (orders.length > 0) {
  //     const handleOrderPromises = [];
  //     orders.forEach(
  //       (order) =>
  //         new Promise<void>(async (resolve) => {
  //           if (
  //             order.GVCReturnToTMTrackingNumber &&
  //             order.GVCReturnToTMTrackingCarrier &&
  //             order.GVCReturnToTMSeventeentrackCarrierKey
  //           ) {
  //             const delivered = await this.is17TrackOrderDelivered(
  //               order.GVCReturnToTMTrackingNumber,
  //               order.GVCReturnToTMTrackingCarrier
  //             );
  //             if (delivered) {
  //               await this.ttbRepo.update(
  //                 { orderId: order.orderId, status: TTBStatus.PRODUCT_RETURNING_TO_TM },
  //                 { status: TTBStatus.PRODUCT_RECEIVED_TO_TM, GVCReturnToTMProductReceivedAt: new Date() }
  //               );
  //             }
  //           }
  //           resolve();
  //         })
  //     );
  //     await Promise.all(handleOrderPromises);
  //   }
  // }

  // @Cron(isProduction ? CronExpression.EVERY_2_HOURS : CronExpression.EVERY_5_MINUTES)
  // async handleCheckOrderDeliveryStatusCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   const orders = await this.ttbRepo
  //     .createQueryBuilder('order')
  //     .where('order.status IN (:...statuses)', {
  //       statuses: [TTBStatus.TRIAL_COMING],
  //     })
  //     .getMany();
  //   this.logger.debug(`Checking delivery status for ${orders.length} orders...`);
  //   for (const order of orders) {
  //     await this.ttbDeliveringQueue.add(TTBDeliveringProcessorQueueName.HANDLE, { orderId: order.orderId });
  //   }
  // }

  // @Cron(CronExpression.EVERY_DAY_AT_4PM)
  // async ttbOrderAbnormalReport() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   this.logger.debug(`ttbOrderAbnormalReport() start`);
  //
  //   const statusHasAbnormal = [
  //     TTBStatus.INITIALIZED,
  //     TTBStatus.TRIAL_COMING,
  //     TTBStatus.PRODUCT_RETURNING,
  //     TTBStatus.PRODUCT_RECEIVED,
  //   ].join(',');
  //
  //   const result = await this.ttbService.getAllTTB({ take: 1000000, abnormalOrder: 1, status: statusHasAbnormal });
  //
  //   if (!result.orders) return;
  //
  //   let orderIds = result.orders.map((order) => order.orderId);
  //   const email = this.config.get('app.userEmail') || this.config.get('app.adminEmail');
  //   await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_ORDER_ABNORMAL_DAILY_REPORT, {
  //     list_order_id: orderIds,
  //   });
  //
  //   this.logger.debug(`ttbOrderAbnormalReport() end`);
  // }

  // @Cron(process.env.TTB_FTP_MONTHLY_SCHEDULE || CronExpression.EVERY_1ST_DAY_OF_MONTH_AT_MIDNIGHT)
  // async ttbReportMonthly() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   await this.ttbService.ttbReportMonthly(null, null);
  // }

  async syncGatStatus() {
    try {
      // await this.ttbGatStatusQueue.add(TTBGATStatusProcessorQueueName.EBS_GET_STATUS_GAT);
      return { success: true };
    } catch (err) {
      return { success: true, err };
    }
  }
}

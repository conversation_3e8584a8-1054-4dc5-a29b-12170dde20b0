import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BlobServiceClient } from '@azure/storage-blob';
import { MailerService } from '@nestjs-modules/mailer';
import axios from 'axios';
import { Queue } from 'bull';
import { plainToClass } from 'class-transformer';
import crypto from 'crypto';
import CryptoJS from 'crypto-js';
import { ApiKeySession, EventCreateQueryV2, EventsApi } from 'klaviyo-api';
import * as _ from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import path from 'path';
import { config } from 'rxjs';
import Client from 'ssh2-sftp-client';
import { Brackets, EntityManager, In, <PERSON><PERSON><PERSON>, Not, Repository, getManager } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { v4 } from 'uuid';
import { LoggingService } from 'src/logging/logging.service';
import { UserBlackListEntity } from '../auth/entities/user-black-list.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { Role } from '../auth/roles.decorator';
import { CdmService } from '../cdm/cdm.service';
import { EcomService } from '../ecom/ecom.service';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { MfeService } from '../mfe/mfe.service';
import { PartnerEntity } from '../partners/entities/partner.entity';
import { SeventeentrackService, TRACKING_CARRIERS } from '../shared/services/seventeentrack.service';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { getUpperCaseValue, isCanadaCountry } from '../utils/transform';
import { TTBConfigThresholdEntity } from './entities/ttb-config-threshold.entity';
import { TTBFraudEntity } from './entities/ttb-fraud.entity';
import { TTBPushEntity } from './entities/ttb-push.entity';
import {
  TTBEntity,
  TTBSpecsMismatchedReturnWrongComponentsStatus,
  TTBStatus,
  TTBStatusDescription,
} from './entities/ttb.entity';
import { TTBFraudProtectionQueueName } from './ttb-fraud.processor';
import { TTBNotifyAdminProcessorQueueName } from './ttb-notify-admin.processor';
import { TTBThresholdActionQueueName } from './ttb-threshold.processor';
import { TTBShippedStatusTexts, TTBStatusRangeDate, TTB_MESSAGE_ERROR } from './ttb.constants';
import {
  BoughtDto,
  CancelTTBToGatPayloadDto,
  GVCReceivedDto,
  GVCReceivedSpecsMismatchedDto,
  GVCReturnWrongComponentsDto,
  GVCReturnedDto,
  GVCReturnedToTMDto,
  GVCTrackingNumber,
  NoteTTBFraudPayloadDto,
  PauseOrderPayloadDto,
  ResumeOrderPayloadDto,
  ReturnedDto,
  SeventeentrackWebhookDto,
  ShipmentDto,
  ShipmentStatus,
  TTBConfigThresholdPayloadDto,
  TryDto,
} from './ttb.type';

const TTBFraudSort = {
  OrderId: 'orderId',
  Email: 'email',
  FirstName: 'firstName',
  LastName: 'lastName',
  FingerPrint: 'fingerPrint',
  IsSimulator: 'isSimulator',
  TtbCreatedAt: 'ttbCreatedAt',
  UserCreatedAt: 'userCreatedAt',
  PurchaseIPAddress: 'purchaseIPAddress',
  PurchaseLatitude: 'purchaseLatitude',
};

@Injectable()
export class TTBService {
  private readonly logger = new Logger(TTBService.name);
  constructor(
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(TTBConfigThresholdEntity)
    private readonly ttbConfigThresholdRepo: Repository<TTBConfigThresholdEntity>,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserBlackListEntity) private readonly userBlackRepo: Repository<UserBlackListEntity>,
    @InjectRepository(TTBFraudEntity) private readonly ttbFraudRepo: Repository<TTBFraudEntity>,
    @InjectQueue('ttb-fraud') private ttbFraudQueue: Queue,
    @InjectQueue('ttb-notify-admin') private ttbFraudNotifyAdminQueue: Queue,
    @InjectQueue('ttb-threshold') private ttbThresholdQueue: Queue,
    private readonly config: ConfigService,
    private readonly mailerService: MailerService,
    private readonly mfeService: MfeService,
    private readonly seventeentrackService: SeventeentrackService,
    private readonly klaviyoService: KlaviyoService,
    private readonly loggingService: LoggingService,
    @Inject(forwardRef(() => EcomService)) private ecomService: EcomService,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService
  ) {}

  async checkTTBExists(uid: string, orderId: string) {
    const countByOrderId = await this.ttbRepo.count({ orderId });
    if (countByOrderId > 0) {
      return true;
    }
    const count = await this.ttbRepo.count({
      userId: uid,
      status: Not(
        In([
          TTBStatus.BOUGHT,
          TTBStatus.CHARGED,
          TTBStatus.PRODUCT_RETURNED,
          TTBStatus.CANCELLED,
          TTBStatus.PRODUCT_RECEIVED_TO_TM,
          TTBStatus.PRODUCT_RETURNING_TO_TM,
        ])
      ),
    });
    return count > 0;
  }

  async canTry(fingerPrint: string, userId) {
    let canTry = false;
    const count = await this.ttbRepo.count({
      fingerPrint: fingerPrint,
      status: Not(
        In([
          TTBStatus.BOUGHT,
          TTBStatus.CHARGED,
          TTBStatus.PRODUCT_RETURNED,
          TTBStatus.CANCELLED,
          TTBStatus.PRODUCT_RECEIVED_TO_TM,
          TTBStatus.PRODUCT_RETURNING_TO_TM,
        ])
      ),
    });

    const countUserBlack = await this.userBlackRepo.count({
      userId,
    });

    if (count === 0 && countUserBlack === 0) {
      canTry = true;
    }

    return canTry;
  }

  async checkFingerPrintInTTBFraud(fingerPrint, userId) {
    const countTTBFraud = await this.ttbFraudRepo
      .createQueryBuilder('ttbf')
      .leftJoinAndSelect('ttbf.trythenbuy', 'ttb')
      .where('ttbf.fingerPrint = :fingerPrint', { fingerPrint: fingerPrint })
      .andWhere('ttbf.userId != :userId', { userId })
      .andWhere(
        new Brackets((qb) => {
          qb.where('ttbf.orderId IS NULL AND ttbf.potentialFraud >= 90 ').orWhere(
            'ttb.status = :status AND ttb.isAdminCancel = :isAdminCancel',
            { status: TTBStatus.CANCELLED, isAdminCancel: true }
          );
        })
      )
      .getCount();

    return countTTBFraud === 0;
  }

  async updateUserBeforeCreateTTB(dataEncrypt: string, userId: string, ipAddress: string) {
    try {
      const datas = this.decryptBody(dataEncrypt);
      console.log(111, datas);

      const updateUserPayload: any = {};
      let isCanTry = false;

      if (_.isNil(datas.fingerPrint)) {
        return { success: false, canTry: false };
      }

      if (!_.isNil(datas?.isSimulator)) {
        updateUserPayload.isSimulator = datas?.isSimulator;
      }

      if (!_.isNil(datas?.fingerPrint)) {
        updateUserPayload.fingerPrint = datas?.fingerPrint;
      }

      if (!_.isNil(datas?.lastLatitude) && !_.isNil(datas?.lastLongitude)) {
        updateUserPayload.lastLatitude = Number(datas.lastLatitude.toFixed(5));
        updateUserPayload.lastLongitude = Number(datas.lastLongitude.toFixed(5));
      }

      if (ipAddress) {
        updateUserPayload.lastIPAddress = ipAddress;
      }

      await this.userRepo.update({ id: userId }, updateUserPayload);
      // this.ttbFraudQueue.add(TTBFraudProtectionQueueName.TTB_FRAUD_PROTECTION, { ...datas, userId, ipAddress });
      const canTry = await this.canTry(datas.fingerPrint, userId);
      const cantTry2 = await this.checkFingerPrintInTTBFraud(datas.fingerPrint, userId);
      if (canTry && datas.isSimulator == false && cantTry2) {
        isCanTry = true;
      }
      return { success: true, canTry: isCanTry };
    } catch (err) {
      console.log('err ===', err);
      return { success: false, canTry: false };
    }
  }

  decryptBody(encrypt: string) {
    const secretKey = this.config.get('app.secretTTBDataKey');
    const bytes = CryptoJS.AES.decrypt(encrypt, secretKey);
    const decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    return decryptedData;
  }

  encryptBody(object: any) {
    const secretKey = this.config.get('app.secretTTBDataKey');
    const ciphertext = CryptoJS.AES.encrypt(JSON.stringify(object), secretKey).toString();
    return ciphertext;
  }

  async checkOrderIdValid(orderId: string, country?: string) {
    const order = await this.ecomService.getOrder(orderId, country);
    return !!order;
  }

  async getEcomOrder(orderId: string, country?: string) {
    return await this.ecomService.getOrder(orderId, country);
  }

  async getOrder(orderId: string) {
    return this.ttbRepo.findOne({ where: { orderId } });
  }

  async canUpdateOrder(uid: string, orderId: string) {
    const count = await this.ttbRepo.count({
      userId: uid,
      orderId,
    });
    return count > 0;
  }

  async postTry(uid: string, order: any) {
    const user = await this.userRepo.findOne({ id: uid });
    const permission = await this.cdmService.getPermission(user.email);
    const productInfo = order?.productItems ? await this.getTTBProductFromOrder(order) : null;
    const ttb = new TTBEntity();
    ttb.id = v4();
    ttb.userId = user.id;
    ttb.orderId = order.orderNo;
    ttb.productInfo = order?.productItems ? JSON.stringify(productInfo) : null;
    ttb.createdBy = user.id;
    ttb.updatedBy = user.id;
    ttb.status = TTBStatus.INITIALIZED;
    ttb.firstName = permission.firstName;
    ttb.lastName = permission.lastName;
    ttb.purchaseIPAddress = user.lastIPAddress;
    ttb.amountTotal = order?.orderTotal ? Number(order?.orderTotal) : null;
    ttb.isSimulator = user.isSimulator;
    ttb.fingerPrint = user.fingerPrint;
    ttb.country = order?.country || 'USA';
    ttb.purchaseLatitude = Number(user.lastLatitude);
    ttb.purchaseLongitude = Number(user.lastLongitude);
    const ttbOrder = await this.ttbRepo.save(ttb);
    const ttbFraud = await this.ttbFraudRepo.findOne({
      where: {
        userId: user.id,
        fingerPrint: user.fingerPrint,
        tryThenBuyId: IsNull(),
      },
      order: { createdAt: 'DESC' },
    });
    if (ttbFraud && ttbFraud.id) {
      ttbFraud.tryThenBuyId = ttbOrder.id;
      ttbFraud.orderId = ttbOrder.orderId;
      await this.ttbFraudRepo.save(ttbFraud);

      // Send FireBase and Email
      // await this.ttbFraudNotifyAdminQueue.add(TTBNotifyAdminProcessorQueueName.PUSH, {
      //   orderId: ttbOrder.orderId,
      // });
    }
    //  check ttb threshold notify to admin
    // await this.ttbThresholdQueue.add(TTBThresholdActionQueueName.TTB_THRESHOLD, {
    //   orderId: ttbOrder.orderId,
    // });
    return this.transformTTBOrder(ttbOrder);
  }

  async postReturningLabel(orderId: string) {
    await this.trackingEventKlaviyoTTBByApi(KlaviyoTrackEvents.TTB_RETURN_INITIATED, orderId);
    return {
      success: true,
    };
  }

  async getTTBProductFromOrder(order: any) {
    const product = order.productItems[0];
    if (!product.cTmCustomconfiguratorNodes) return product;
    const customConfiguratorNodes = JSON.parse(product.cTmCustomconfiguratorNodes || JSON.stringify({}));
    const headModels = await this.mfeService.getHeadModels();
    const headModel = customConfiguratorNodes.selectedNodes?.find((node) => node.name === 'Model');
    const headModelInfo = headModels.find((item) => item.name === headModel?.option?.name);
    const headModelIndex = customConfiguratorNodes.selectedNodes?.indexOf(headModel);
    customConfiguratorNodes.selectedNodes[headModelIndex] = {
      ...headModel,
      option: {
        ...headModel?.option,
        info: headModelInfo,
      },
    };
    customConfiguratorNodes.selectedNodes = customConfiguratorNodes.selectedNodes.map((item) => {
      if (item.option && item.option.imageURL) {
        item.option.imageURL = `${this.config.get('app.ecomEndpoint')}${item.option.imageURL}`;
      }
      return item;
    });
    product.cTmCustomconfiguratorNodes = JSON.stringify(customConfiguratorNodes);
    delete product.cTmCustomconfiguratorResponse;
    return product;
  }

  async canCancel(orderId: string) {
    const ttb = await this.ttbRepo.findOne({ orderId });
    return ttb.status === TTBStatus.INITIALIZED;
  }

  async postCancel(uid: string, payload: TryDto) {
    await this.ttbRepo.update(
      { orderId: payload.orderId, userId: uid },
      { status: TTBStatus.CANCELLED, canceledAt: new Date(), canceledBy: uid }
    );
    return this.ttbRepo.findOne({ orderId: payload.orderId, userId: uid });
  }

  async postCancelByAdmin(uid: string, orderId: string, cancelReason: string, isTTBFraud = false) {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const ttbRecord = await this.ttbRepo.findOne({
        orderId,
      });
      if (!ttbRecord) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.TTB_ORDER_NOT_FOUND,
          errorMessage: TTB_MESSAGE_ERROR.TTB_NOT_FOUND,
        });
      }
      const ttbUpdate = {
        status: TTBStatus.CANCELLED,
        cancelReason: cancelReason,
        canceledAt: new Date(),
        canceledBy: uid,
        orderId: orderId,
        isAdminCancel: true,
      };
      await transactionalEntityManager.update(TTBEntity, { id: ttbRecord.id }, ttbUpdate);
      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: ttbRecord.id,
            updatedBy: uid,
            orderId: ttbRecord.orderId,
          },
          manager: transactionalEntityManager,
        },
        isTTBFraud ? TTBFraudEntity.name : TTBEntity.name,
        ttbRecord,
        ttbUpdate
      );
      const ecomOrder = await this.ecomService.getOrder(orderId, ttbRecord?.country || 'USA');
      if (ecomOrder) {
        const productInfo = JSON.parse(ttbRecord?.productInfo || JSON.stringify({}));
        if (productInfo.cTmCustomconfiguratorNodes) {
          productInfo.cTmCustomconfiguratorNodes = JSON.parse(productInfo.cTmCustomconfiguratorNodes);
        }
        const user = await this.userRepo.findOne({ id: ttbRecord?.userId });
        await this.trackingEventKlaviyoTTB(KlaviyoTrackEvents.TTB_CANCELATION, user?.email, ecomOrder, productInfo);
      }

      return this.ttbRepo.findOne({ orderId });
    });

    return res;
  }

  async updateOrderShipment(payload: ShipmentDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    if (payload.status === ShipmentStatus.SHIPPED && order.status === TTBStatus.TRIAL_STARTED) {
      return {
        success: false,
        trialHasStarted: true,
      };
    }
    const status = payload.status === ShipmentStatus.SHIPPED ? TTBStatus.TRIAL_COMING : TTBStatus.TRIAL_STARTED;
    const updatePayload: any = { status };
    if (status === TTBStatus.TRIAL_STARTED) {
      const nowDate = new Date();
      nowDate.setHours(17, 0, 0, 0);
      updatePayload.startedAt = moment(nowDate)
        // .add(TTBStatusRangeDate[TTBStatus.TRIAL_STARTED], this.config.get('app.ttbTimeUnit'))
        .add(TTBStatusRangeDate[TTBStatus.TRIAL_STARTED], 'days')
        .toDate();
      updatePayload.deliveredAt = new Date();
    }
    await this.ttbRepo.update({ orderId: payload.orderId }, updatePayload);
    return {
      success: true,
    };
  }

  async updateOrderReturnedFromGVC(payload: GVCReturnedDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    const updateData: QueryDeepPartialEntity<TTBEntity> = { ...payload, status: TTBStatus.PRODUCT_RETURNED };
    if (typeof payload.productStatusInfo !== 'undefined') {
      updateData.productStatusInfo = payload.productStatusInfo;
    }
    await this.ttbRepo.update({ orderId: payload.orderId }, updateData);
    return {
      success: true,
    };
  }

  async updateOrderReturnedToTMFromGVC(payload: GVCReturnedToTMDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    const GVCReturnToTMSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
      payload.carrierTrackingNumber,
      payload.carrier
    );
    const updateData: QueryDeepPartialEntity<TTBEntity> = { status: TTBStatus.PRODUCT_RETURNING_TO_TM };
    await this.ttbRepo.update(
      { orderId: payload.orderId },
      {
        ...updateData,
        GVCReturnToTMTrackingCarrier: getUpperCaseValue(payload?.carrier),
        GVCReturnToTMTrackingNumber: payload.carrierTrackingNumber,
        productStatusInfo: payload.productStatusInfo,
        GVCReturnToTMSeventeentrackCarrierKey,
      }
    );
    return {
      success: true,
    };
  }

  async updateOrderReceivedFromGVC(payload: GVCReceivedDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    const updateData: QueryDeepPartialEntity<TTBEntity> = {
      status: TTBStatus.PRODUCT_RECEIVED,
      GVCReturnProductReceivedAt: new Date(),
    };
    await this.ttbRepo.update({ orderId: payload.orderId }, updateData);
    return {
      success: true,
    };
  }

  async gvcReturnWrongComponents(payload: GVCReturnWrongComponentsDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    const SeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
      payload.carrierTrackingNumber,
      payload.carrier
    );
    const updateData: QueryDeepPartialEntity<TTBEntity> = {
      specsMismatchedReturnWrongComponentsTrackingNumber: payload.carrierTrackingNumber,
      specsMismatchedReturnWrongComponentsTrackingCarrier: payload.carrier,
      specsMismatchedReturnWrongComponentsStatus: TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNING,
      specsMismatchedReturnWrongComponentsSeventeentrackCarrierKey: SeventeentrackCarrierKey,
    };
    await this.ttbRepo.update({ orderId: payload.orderId }, updateData);
    return {
      success: true,
    };
  }

  async updateReceivedSpecsMismatched(payload: GVCReceivedSpecsMismatchedDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    const updateData: QueryDeepPartialEntity<TTBEntity> = {
      GVCReceivedSpecsMismatchedInfo: JSON.stringify(payload),
      GVCReceivedSpecsMismatchedAt: new Date(),
      status: TTBStatus.MISMATCHED_COMPONENT,
      createNewReturnLabel: true,
    };
    const user = await this.userRepo.findOne({ where: { id: order.userId } });
    await this.ttbRepo.update({ orderId: payload.orderId }, updateData);

    const ecomOrder = await this.ecomService.getOrder(order.orderId, order?.country);
    if (ecomOrder) {
      const productInfo = JSON.parse(order?.productInfo || JSON.stringify({}));
      if (productInfo.cTmCustomconfiguratorNodes) {
        productInfo.cTmCustomconfiguratorNodes = JSON.parse(productInfo.cTmCustomconfiguratorNodes);
      }
      const returnLabelUrl = order?.returnLabelUrl;
      const transformMyTMOrder = this.transformTTBOrder(order);
      const klaviyoPayload = {
        order_id: order?.orderId,
        order_datetime: order?.createdAt,
        return_label_url: returnLabelUrl,
        failed_reason: payload.orderLines,
        trial_start_date: order?.startedAt,
        trial_end_date: transformMyTMOrder?.endDate,
        ...this.transformTTBTriggerToKlaviyo(ecomOrder, order.productInfo),
      };
      delete klaviyoPayload.products[0].expected_ship_date;
      await this.klaviyoService.track(user.email, KlaviyoTrackEvents.TTB_FAILED_INSPECTION, klaviyoPayload);
    }
    return {
      success: true,
    };
  }

  async updateOrderTrackingNumberFromGVC(payload: GVCTrackingNumber) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    const SeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
      payload.carrierTrackingNumber,
      payload.carrier
    );
    if (order.status === TTBStatus.MISMATCHED_COMPONENT) {
      await this.ttbRepo.update(
        { orderId: payload.orderId },
        {
          specsMismatchedReturnTrackingCarrier: payload.carrier,
          specsMismatchedReturnTrackingNumber: payload.carrierTrackingNumber,
          specsMismatchedReturnSeventeentrackCarrierKey: SeventeentrackCarrierKey,
          // status: TTBStatus.MISMATCHED_COMPONENT_RETURN_PICKED_UP,
        }
      );
      return {
        success: true,
      };
    }
    await this.ttbRepo.update(
      { orderId: payload.orderId },
      {
        GVCReturnTrackingCarrier: getUpperCaseValue(payload?.carrier),
        GVCReturnTrackingNumber: payload.carrierTrackingNumber,
        GVCReturnSeventeentrackCarrierKey: SeventeentrackCarrierKey,
        // status: TTBStatus.PRODUCT_PICKED_UP,
      }
    );
    return {
      success: true,
    };
  }

  async postBought(uid: string, payload: BoughtDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    await this.ttbRepo.update({ orderId: payload.orderId }, { status: TTBStatus.BOUGHT });
    return {
      success: true,
    };
  }

  async postReturned(uid: string, payload: ReturnedDto) {
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order) {
      return null;
    }
    if (this.isTrialEnded(order)) {
      return {
        success: false,
        trialEnded: true,
      };
    }
    await this.ttbRepo.update({ orderId: payload.orderId }, { status: TTBStatus.PRODUCT_RETURNING });
    return {
      success: true,
    };
  }

  async getTTB(uid: string) {
    const orders = await this.ttbRepo.find({ userId: uid });
    return orders.map((order) => {
      return this.transformTTBOrder(order);
    });
  }

  async handleWebhookForTrackingNumberUpdates(payload: SeventeentrackWebhookDto): Promise<{
    productReturningOrderIds: string[];
    productReturningToTMOrderIds: string[];
    specsMismatchedReturnReceivedOrderIds: string[];
    specsMismatchedReturningWrongComponentsOrders: string[];
  }> {
    const productReturningOrders = await this.ttbRepo.find({
      GVCReturnTrackingNumber: payload.data.number,
      status: TTBStatus.PRODUCT_RETURNING,
    });
    if (productReturningOrders.length > 0) {
      const isPickedUp = this.isTrackIncludePickedUpEvent(payload);
      if (isPickedUp) {
        await this.ttbRepo.update(
          { GVCReturnTrackingNumber: payload.data.number, status: TTBStatus.PRODUCT_RETURNING },
          { status: TTBStatus.PRODUCT_PICKED_UP }
        );
      }
    }
    const specsMismatchedReturningOrders = await this.ttbRepo.find({
      specsMismatchedReturnTrackingNumber: payload.data.number,
      status: TTBStatus.MISMATCHED_COMPONENT,
    });

    if (specsMismatchedReturningOrders.length > 0) {
      const isPickedUpMismatched = this.isTrackIncludePickedUpEvent(payload);
      if (isPickedUpMismatched) {
        await this.ttbRepo.update(
          { specsMismatchedReturnTrackingNumber: payload.data.number },
          { status: TTBStatus.MISMATCHED_COMPONENT_RETURN_PICKED_UP }
        );
      }
    }

    if (!this.seventeentrackService.isTrackingNumberDelivered(payload.data)) {
      return {
        productReturningOrderIds: [],
        productReturningToTMOrderIds: [],
        specsMismatchedReturnReceivedOrderIds: [],
        specsMismatchedReturningWrongComponentsOrders: [],
      };
    }
    // const nowDate = new Date();
    // nowDate.setHours(17, 0, 0, 0);
    // const startedAt = moment(nowDate)
    //   // .add(TTBStatusRangeDate[TTBStatus.TRIAL_STARTED], this.config.get('app.ttbTimeUnit'))
    //   .add(TTBStatusRangeDate[TTBStatus.TRIAL_STARTED], 'days')
    //   .toDate();
    // await this.ttbRepo.update(
    //   { shipmentTrackingNumber: payload.data.number, status: TTBStatus.TRIAL_COMING },
    //   { status: TTBStatus.TRIAL_STARTED, startedAt, deliveredAt: new Date() },
    // );
    const productReturningToTMOrders = await this.ttbRepo.find({
      GVCReturnToTMTrackingNumber: payload.data.number,
      status: TTBStatus.PRODUCT_RETURNING_TO_TM,
    });
    if (productReturningToTMOrders.length > 0) {
      await this.ttbRepo.update(
        { GVCReturnToTMTrackingNumber: payload.data.number, status: TTBStatus.PRODUCT_RETURNING_TO_TM },
        { status: TTBStatus.PRODUCT_RECEIVED_TO_TM, GVCReturnToTMProductReceivedAt: new Date() }
      );
    }

    const specsMismatchedReturningWrongComponentsOrders = await this.ttbRepo.find({
      specsMismatchedReturnWrongComponentsTrackingNumber: payload.data.number,
      specsMismatchedReturnWrongComponentsStatus: TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNING,
    });
    if (specsMismatchedReturningWrongComponentsOrders.length > 0) {
      await this.ttbRepo.update(
        {
          specsMismatchedReturnWrongComponentsTrackingNumber: payload.data.number,
          specsMismatchedReturnWrongComponentsStatus: TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNING,
        },
        {
          specsMismatchedReturnWrongComponentsStatus: TTBSpecsMismatchedReturnWrongComponentsStatus.RETURNED,
          specsMismatchedReturnWrongComponentsReceivedAt: new Date(),
        }
      );
    }
    return {
      productReturningOrderIds: productReturningOrders.map((item) => item.id),
      productReturningToTMOrderIds: productReturningToTMOrders.map((item) => item.id),
      specsMismatchedReturnReceivedOrderIds: specsMismatchedReturningOrders.map((item) => item.id),
      specsMismatchedReturningWrongComponentsOrders: specsMismatchedReturningWrongComponentsOrders.map(
        (item) => item.id
      ),
    };
  }

  transformTTBFraud(order: TTBFraudEntity) {
    const ttb = order?.trythenbuy;
    const user = order?.user;
    let ttbFormat = {};
    let userFormat = {};
    if (ttb) {
      const productInfo = JSON.parse(order.trythenbuy.productInfo || JSON.stringify({}));
      if (productInfo.cTmCustomconfiguratorNodes) {
        productInfo.cTmCustomconfiguratorNodes = JSON.parse(productInfo.cTmCustomconfiguratorNodes);
      }
      ttbFormat = {
        productInfo,
        status: ttb.status,
        ttbCreatedAt: ttb.createdAt,
        cancelReason: ttb.cancelReason,
        gatStatus: ttb.gatStatus,
      };
    }

    if (user) {
      userFormat = {
        userCreatedAt: user.createdAt,
        signUpIPAddress: user.signUpIPAddress,
        signUpLatitude: user.signUpLatitude,
        signUpLongitude: user.signUpLongitude,
        userBlackList: user?.userBlackList,
        signUpIPAddressLocation: user?.signUpIPAddressLocation,
        signUpGPSLocation: user?.signUpGPSLocation,
      };
    }

    return {
      ...order,
      ...ttbFormat,
      ...userFormat,
    };
  }

  transformTTBOrder(order: TTBEntity) {
    const productInfo = JSON.parse(order.productInfo || JSON.stringify({}));
    if (productInfo.cTmCustomconfiguratorNodes) {
      productInfo.cTmCustomconfiguratorNodes = JSON.parse(productInfo.cTmCustomconfiguratorNodes);
    }
    return {
      ...order,
      productInfo,
      shipmentTrackingUrl: this.getOrderShipmentTrackingUrl(
        order.shipmentTrackingNumber,
        order.shipmentCarrierName,
        order.shipmentSeventeentrackCarrierKey
      ),
      GVCProductReturningTrackingUrl: this.getOrderShipmentTrackingUrl(
        order.GVCReturnTrackingNumber,
        order.GVCReturnTrackingCarrier,
        order.GVCReturnSeventeentrackCarrierKey
      ),
      specsMismatchedReturnTrackingUrl: this.getOrderShipmentTrackingUrl(
        order.specsMismatchedReturnTrackingNumber,
        order.specsMismatchedReturnTrackingCarrier,
        order.specsMismatchedReturnSeventeentrackCarrierKey
      ),
      specsMismatchedReturnWrongComponentsTrackingUrl: this.getOrderShipmentTrackingUrl(
        order.specsMismatchedReturnWrongComponentsTrackingNumber,
        order.specsMismatchedReturnWrongComponentsTrackingCarrier,
        order.specsMismatchedReturnWrongComponentsSeventeentrackCarrierKey
      ),
      GVCReceivedSpecsMismatchedInfo: order.GVCReceivedSpecsMismatchedInfo
        ? JSON.parse(order.GVCReceivedSpecsMismatchedInfo)
        : null,
      statusDescription: this.getOrderStatusDescription(order),
      endDate: order.startedAt
        ? moment(order.startedAt)
            .add(TTBStatusRangeDate[TTBStatus.TRIAL_HALF_DONE] * 2, this.config.get('app.ttbTimeUnit'))
            .toDate()
        : null,
      cardWillBeChangedAt: order.startedAt
        ? moment(order.startedAt)
            .add(parseInt(this.config.get('app.ttbReturningAmount'), 10), this.config.get('app.ttbReturningTimeUnit'))
            .toDate()
        : null,
    };
  }

  isTrialEnded(order: TTBEntity) {
    return [TTBStatus.BOUGHT, TTBStatus.PRODUCT_RETURNED, TTBStatus.CHARGED].includes(order.status);
  }

  getOrderStatusDescription(order: TTBEntity): string {
    if (order.status === TTBStatus.INITIALIZED && !order.scheduledArrivalDate) {
      return TTBStatusDescription.INITIALIZED;
    }
    if (order.status === TTBStatus.INITIALIZED && order.scheduledArrivalDate) {
      return TTBStatusDescription.INITIALIZED_WITH_ESTIMATED_ARRIVAL_DATE.replace(
        '[date]',
        moment(order.scheduledArrivalDate).format('MM/DD/YYYY')
      );
    }
    if (order.status === TTBStatus.TRIAL_COMING && order.scheduledArrivalDate) {
      return TTBStatusDescription.TRIAL_COMING;
    }
    if (order.status === TTBStatus.TRIAL_1_DAY_LEFT) {
      return TTBStatusDescription.TRIAL_1_DAY_LEFT;
    }
    if (order.status === TTBStatus.TRIAL_2_DAYS_LEFT) {
      return TTBStatusDescription.TRIAL_2_DAYS_LEFT;
    }
    return '';
  }

  async getFTPClient() {
    const client = new Client();
    try {
      await client.connect({
        host: this.config.get('app.ttbFTPHost'),
        user: this.config.get('app.ttbFTPUser'),
        password: this.config.get('app.ttbFTPPassword'),
        port: 22,
        algorithms: {
          cipher: ['3des-cbc'],
        },
      });
    } catch (error) {
      this.logger.error(error.message);
      this.logger.log(`Delay 3s...`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      this.logger.log('Retry connect SFTP...');
      return this.getFTPClient();
    }

    return client;
  }

  async getUPSFTPClient() {
    const client = new Client();
    try {
      await client.connect({
        host: this.config.get('app.ttbUPSFTPHost'),
        user: this.config.get('app.ttbUPSFTPUser'),
        password: this.config.get('app.ttbUPSFTPPassword'),
        port: 22,
        algorithms: {
          cipher: ['3des-cbc'],
        },
      });
    } catch (error) {
      this.logger.error(error.message);
      this.logger.log(`Delay 3s...`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      this.logger.log('Retry connect SFTP...');
      return this.getUPSFTPClient();
    }

    return client;
  }
  async getAllOrderIds() {
    const rows = await this.ttbRepo
      .createQueryBuilder()
      .select('orderId')
      .distinct(true)
      .orderBy('orderId')
      .getRawMany();
    if (rows.length === 0) {
      return [];
    }
    return rows.map((row) => row.orderId);
  }

  async scanShipmentFileNames(formattedDates: string[]) {
    const client = await this.getFTPClient();
    const filenames = [];
    const EBSFileFormatPath = `/${this.config.get('app.ttbFTPRootPath')}/EBS/`;
    if (formattedDates.length === 0) {
      const files = await client.list(EBSFileFormatPath, new RegExp(`TM_TRYTHENBUY_SHIPMENTS_`));
      files.forEach((file) => {
        filenames.push(file.name);
      });
      await client.end();
      return filenames;
    }
    try {
      for (const formattedDate of formattedDates) {
        const files = await client.list(EBSFileFormatPath, new RegExp(`TM_TRYTHENBUY_SHIPMENTS_${formattedDate}`));
        files.forEach((file) => {
          filenames.push(file.name);
        });
      }
      await client.end();
      return filenames;
    } catch (e) {
      console.log(e);
      await client.end();
      return [];
    }
  }

  async getDailyEBSShipmentOrders(
    orderIds: string[],
    EBSFileName: string
  ): Promise<{ heading: string[] | null; orders: Array<string[]>; canceledOrders: Array<string[]> }> {
    const client = await this.getFTPClient();
    this.logger.log(`TTB shipment scans file: ${EBSFileName}`);
    try {
      const EBSFileFormatPath = `/${this.config.get('app.ttbFTPRootPath')}/EBS/${EBSFileName}`;
      const status = await client.exists(EBSFileFormatPath);
      if (!status) {
        this.logger.log('Can not find shipment file!');
        await client.end();
        return {
          heading: null,
          orders: [],
          canceledOrders: [],
        };
      }
      const localEBSShipmentFilePath = path.join(process.cwd(), `/public/EBS/${EBSFileName}`);
      await client.fastGet(EBSFileFormatPath, localEBSShipmentFilePath);
      const workSheetsFromFile = xlsx.parse(localEBSShipmentFilePath);
      const sheet = workSheetsFromFile[0].data;
      if (sheet[0]?.length > 1) {
        sheet.unshift([`TTB${moment().format('MMDDYYYY')}`]);
      }
      const sheetHeading = sheet[1] as string[];
      const orders = sheet.slice(2, sheet.length);
      this.logger.log(`TTB shipment scans file: ${EBSFileName} got ${orders.length} orders`);
      const matchedOrders = [];
      const canceledOrders = [];
      orders.forEach((order: string[]) => {
        this.logger.log(`TTB shipment scans for order: ${order[2]} with status: ${order[12]}`);
        if (orderIds.includes(order[2]) && order[12] === 'SHIPPED') {
          matchedOrders.push(order);
        }
        if (order[12] === 'CANCELED' || order[12] === 'CANCELLED') {
          canceledOrders.push(order);
        }
      });
      await client.end();
      return {
        heading: sheetHeading,
        orders: matchedOrders,
        canceledOrders,
      };
    } catch (e) {
      console.log(e);
      client.end();
      return {
        heading: null,
        orders: [],
        canceledOrders: [],
      };
    }
  }

  getOrderShipmentTrackingUrl(trackingNumber: string, carrier: string, carrierKey: string) {
    let shipmentTrackingUrl = null;
    if (carrierKey && carrier && trackingNumber) {
      const upperCarrier = getUpperCaseValue(carrier);
      shipmentTrackingUrl = `https://t.17track.net/en#nums=${trackingNumber}&fc=${carrierKey}`;
      if (upperCarrier === TRACKING_CARRIERS.FEDEX) {
        shipmentTrackingUrl = `https://www.fedex.com/fedextrack/?trknbr=${trackingNumber}`;
      }
      if (upperCarrier === TRACKING_CARRIERS.UPS) {
        shipmentTrackingUrl = `https://www.ups.com/mobile/track?trackingNumber=${trackingNumber}`;
      }
    }
    return shipmentTrackingUrl;
  }

  async getEstimatedArrivalDateFromClubTracker(orderId: string): Promise<Date | null> {
    try {
      const response = await this.getOrderInfoEBS(orderId);
      if (response?.status === 200) {
        const orderLine = response.data?.orderInfo?.orderLines[0];
        if (orderLine?.latestStatus === 'Cancelled') {
          await this.ttbRepo.update({ orderId }, { status: TTBStatus.CANCELLED, cancelReason: 'EBS Cancelled' });
        }
        if (!orderLine?.scheduledArrivalDate) return null;
        return moment(orderLine.scheduledArrivalDate, 'DD-MMM-YYYY HH:mm:ss').toDate();
      }
      return null;
    } catch (e) {
      console.log(e.message);
      return null;
    }
  }

  async getShipmentInfoFromClubTracker(
    orderId: string
  ): Promise<{ carrierName: string; trackingNumber: string } | null> {
    const response = await this.getOrderInfoEBS(orderId);
    if (response?.status === 200) {
      const orderLine = response.data?.orderInfo?.orderLines[0];
      if (!orderLine) return null;
      if (orderLine.statusCol && orderLine.statusCol.length > 0) {
        let hasShipped = false;
        orderLine.statusCol.forEach((col) => {
          if (
            TTBShippedStatusTexts.includes(col.statusValue) &&
            col.date &&
            orderLine.carrierName &&
            orderLine.trackingNumber
          ) {
            hasShipped = true;
          }
        });
        if (hasShipped) {
          return {
            carrierName: orderLine.carrierName,
            trackingNumber: orderLine.trackingNumber,
          };
        }
      }
      return null;
    }
    return null;
  }

  async getDelayedTimeForPushAndTriggerNotifications() {
    const momentTz = require('moment-timezone');
    const ttbPushNotificationAt = this.config.get('app.ttbPushNotificationAt');
    const [fromDateHour, fromDateTimezone] = ttbPushNotificationAt.split('<->')[0].split(':');
    const [toDateHour] = ttbPushNotificationAt.split('<->')[1].split(':');
    momentTz.tz.setDefault(fromDateTimezone);
    const fromDate = momentTz().set({ hour: fromDateHour, minute: 0, second: 0, millisecond: 0 });
    const toDate = momentTz().set({ hour: toDateHour, minute: 0, second: 0, millisecond: 0 });
    const now = momentTz();
    let delayed = 0;
    if (!now.isBetween(fromDate, toDate)) {
      if (now.isBefore(fromDate)) {
        delayed = fromDate.diff(now);
      }
      if (now.isAfter(toDate)) {
        delayed = fromDate.add(1, 'd').diff(now);
      }
    }
    momentTz.tz.setDefault();
    return delayed;
  }

  async isOrderCanceledFromClubTracker(orderId: string): Promise<{ canceled: boolean; canceledAt: Date } | null> {
    try {
      const response = await this.getOrderInfoEBS(orderId);
      if (response?.status === 200) {
        const orderStatus = response.data?.orderInfo?.status;
        if (!orderStatus) return null;
        if (orderStatus === 'Cancelled') {
          return {
            canceled: true,
            canceledAt: moment(response.data?.orderInfo?.date).toDate(),
          };
        }
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getOrderInfoEBS(orderId: string) {
    const url = `${this.config.get(
      'app.clubTrackerEndpoint'
    )}/api/v1/Main/GetOrderStatus?RequestSource=B2C&OperatingUnit=US&OracleInstance=${this.config.get(
      'app.ecomClubTrackerInstance'
    )}&PONumber=${orderId?.split('-')[0]}`;
    try {
      return await axios.get(url);
    } catch (error) {
      console.log(`${error.message}:${url}`);
      return null;
    }
  }

  async getOrderInfoEBSv2(orderId: string) {
    const url = `${this.config.get(
      'app.clubTrackerEndpoint'
    )}/api/v1/Main/GetOrderStatus?RequestSource=IT&OperatingUnit=US&OracleInstance=${this.config.get(
      'app.ecomClubTrackerInstance'
    )}&PONumber=${orderId?.split('-')[0]}`;
    try {
      return await axios.get(url);
    } catch (error) {
      console.log(`${error.message}:${url}`);
      return null;
    }
  }

  async ttbReportMonthly(from, to) {
    this.logger.debug(`ttbReportMonthly() start`);

    const results = await this.getAllTTBMonthly(from, to);

    if (!results) return;

    const data = results.map((order) => [
      order.orderId,
      this.getProductNameTTB(order.productInfo?.cTmCustomconfiguratorNodes || '{}'),
      '',
      order.amountTotal,
      order.amountPaid,
      order.createdAt.toLocaleString('en-US', {
        timeZone: 'America/Los_Angeles',
      }),
      order.deliveredAt?.toLocaleString('en-US', {
        timeZone: 'America/Los_Angeles',
      }),
      order.status,
    ]);
    const headers = [
      'ORDER ID',
      'PRODUCT NAME',
      'WHOLESALE COST',
      'TOTAL',
      'PAID BY GVC',
      'ORDER DATE',
      'DELIVERED AT',
      'STATUS',
    ];
    data.unshift(headers);
    const yearMonth = from ? moment().format('YYYYMMDDhmmss') : moment().format('YYYYMM');
    const fileName = `TTB${yearMonth}.csv`;
    const filePath = `/${this.config.get('app.ttbFTPRootPath')}/Finance/${fileName}`;
    await this.ttbMonthlyReportUpload(data, filePath, yearMonth);
    this.logger.debug(`ttbReportMonthly() end`);
  }

  async ttbMonthlyReportUpload(data, filePath, sheetName) {
    const client = await this.getFTPClient();
    try {
      const existFile = await client.exists(filePath);
      if (!existFile) {
        const buffer = xlsx.build(
          [
            {
              name: sheetName,
              data: data,
            },
          ],
          { bookType: 'csv' }
        );
        await client.put(buffer, filePath);
        this.logger.log(`Put to Finance...`);
      }
      await client.end();
      return {
        success: true,
      };
    } catch (error) {
      this.logger.log(error);
      await client.end();
    }
  }

  getUTCDate(inputDate, hours, minutes, seconds, ms) {
    return Date.UTC(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate(), hours, minutes, seconds, ms);
  }

  msToDate(date: number) {
    return new Date(date);
  }

  async getAllTTBMonthly(from, to) {
    const fromDate = this.getUTCDate(new Date(from), 0, 0, 0, 0);
    const toDate = this.getUTCDate(new Date(to), 23, 59, 59, 999);

    const monthBefore = moment().subtract(1, 'month');
    const daysInMonthBefore = monthBefore.daysInMonth();
    const monthReport = monthBefore.month();
    const yearReport = monthBefore.year();

    const startOfMonth = from
      ? this.msToDate(fromDate)
      : this.msToDate(Date.UTC(yearReport, monthReport, 1, 0, 0, 0, 0));
    const endOfMonth = to
      ? this.msToDate(toDate)
      : this.msToDate(Date.UTC(yearReport, monthReport, daysInMonthBefore, 23, 59, 59, 999));

    const query = this.ttbRepo
      .createQueryBuilder('ttb')
      .where({})
      .leftJoinAndSelect('ttb.user', 'user')
      .andWhere(' DATEADD(HOUR,-7, ttb.createdAt) >= :startOfMonth', { startOfMonth: startOfMonth })
      .andWhere(' DATEADD(HOUR,-7, ttb.createdAt) <= :endOfMonth', { endOfMonth: endOfMonth });

    const orders = await query.addOrderBy('ttb.createdAt', 'DESC').getMany();
    return orders.map((order) => {
      const result = {
        ...this.transformTTBOrder(order),
        email: order.user?.email,
      };
      delete result.user;
      return result;
    });
  }

  async postPauseOrder(userId: string, payload: PauseOrderPayloadDto) {
    await this.ttbRepo.update(
      { orderId: payload.orderId },
      {
        paused: true,
        pausedAt: new Date(),
        pauseReason: payload.pauseReason,
        pausedBy: userId,
      }
    );
    return { success: true };
  }

  async postResumeOrder(userId: string, payload: ResumeOrderPayloadDto) {
    const updatePayload: QueryDeepPartialEntity<TTBEntity> = {};
    const order = await this.ttbRepo.findOne({ orderId: payload.orderId });
    if (!order?.paused) {
      return {
        success: false,
      };
    }
    const pausedAtDiff = moment().diff(moment(order.pausedAt));
    if (order.startedAt) {
      updatePayload.startedAt = moment(order.startedAt).add(pausedAtDiff, 'milliseconds').toDate();
    }
    if (order.GVCReceivedSpecsMismatchedAt) {
      updatePayload.GVCReceivedSpecsMismatchedAt = moment(order.GVCReceivedSpecsMismatchedAt)
        .add(pausedAtDiff, 'milliseconds')
        .toDate();
    }
    await this.ttbRepo.update(
      { orderId: payload.orderId },
      {
        paused: null,
        pausedAt: null,
        pausedBy: null,
        ...updatePayload,
      }
    );
    return { success: true };
  }

  async testNotifyAdmin(payload) {
    const { orderId, email } = payload || 'DW00068141';
    // await this.ttbFraudNotifyAdminQueue.add(TTBNotifyAdminProcessorQueueName.PUSH, {
    //   orderId: orderId,
    //   email: email,
    // });
    return { success: true };
  }

  async getConfigThreshold() {
    const ttbConfigThreshold = await this.ttbConfigThresholdRepo.find();
    return ttbConfigThreshold;
  }

  async saveConfigThreshold(userId, data: TTBConfigThresholdPayloadDto) {
    const { name, type, totalOrder } = data;
    let ttbConfigThreshold = await this.ttbConfigThresholdRepo.findOne({
      type,
    });

    if (ttbConfigThreshold) {
      ttbConfigThreshold.name = name || null;
      ttbConfigThreshold.type = type;
      ttbConfigThreshold.totalOrder = totalOrder;
      ttbConfigThreshold.updatedBy = userId;
    } else {
      ttbConfigThreshold = plainToClass(TTBConfigThresholdEntity, {
        ...data,
        id: v4(),
        createdBy: userId,
      });
    }
    return this.ttbConfigThresholdRepo.save(ttbConfigThreshold);
  }

  async createEventKlaviyo(payload) {
    try {
      const { event, email } = payload;
      const country = payload?.country || 'USA';
      if (!event || !email) {
        return { success: false, message: 'event, email is required' };
      }
      const configKeyKlaviyo = isCanadaCountry(country)
        ? this.config.get('app.klaviyoCAPrivateToken')
        : this.config.get('app.klaviyoPrivateToken');
      const session = new ApiKeySession(configKeyKlaviyo);
      const eventsApi = new EventsApi(session);
      const body: EventCreateQueryV2 = {
        data: {
          type: 'event',
          attributes: {
            properties: { newKey: event },
            metric: { data: { type: 'metric', attributes: { name: event } } },
            profile: {
              data: {
                type: 'profile',
                attributes: {
                  email: email,
                },
              },
            },
          },
        },
      };
      await eventsApi.createEvent(body);
      return { success: true };
    } catch (err) {
      return err;
    }
  }

  async getAllTTBFraud({ ...options }: any) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 20;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.ttbFraudRepo
      .createQueryBuilder('ttbFraud')
      .where({})
      .leftJoinAndSelect('ttbFraud.user', 'user')
      .leftJoinAndSelect('user.userBlackList', 'userBlackList')
      .leftJoinAndSelect('ttbFraud.trythenbuy', 'ttb');

    let status;
    if (options?.status) {
      status = options.status.split(',').map((r) => r.trim());
      query.andWhere('ttb.status in (:...status)', { status: status });
    }

    if (options.strSearch) {
      const strSearch = `%${options.strSearch || ''}%`;
      query.andWhere(
        new Brackets((qb) => {
          qb.where('ttbFraud.orderId like :strSearch', { strSearch })
            .orWhere('ttbFraud.email like :strSearch', { strSearch })
            .orWhere('ttbFraud.fingerPrint like :strSearch', { strSearch })
            .orWhere('ttbFraud.purchaseIPAddress like :strSearch', { strSearch })
            .orWhere('ttbFraud.firstName like :strSearch', { strSearch })
            .orWhere('ttbFraud.lastName like :strSearch', { strSearch })
            .orWhere(`CONCAT(ttbFraud.firstName,' ',ttbFraud.lastName) like :strSearch`, { strSearch })
            .orWhere(`CONCAT(ttbFraud.lastName,' ',ttbFraud.firstName) like :strSearch`, { strSearch });
        })
      );
    }

    if (options?.sortBy) {
      const sortType = options.sortType === 'asc' ? 'ASC' : 'DESC';
      switch (options.sortBy) {
        case TTBFraudSort.OrderId:
          query.addOrderBy('ttbFraud.orderId', sortType);
          break;
        case TTBFraudSort.Email:
          query.addOrderBy('ttbFraud.email', sortType);
          break;
        case TTBFraudSort.FirstName:
          query.addOrderBy('ttbFraud.firstName', sortType);
          break;
        case TTBFraudSort.LastName:
          query.addOrderBy('ttbFraud.lastName', sortType);
          break;
        case TTBFraudSort.FingerPrint:
          query.addOrderBy('ttbFraud.fingerPrint', sortType);
          break;
        case TTBFraudSort.IsSimulator:
          query.addOrderBy('ttbFraud.isSimulator', sortType);
          break;
        case TTBFraudSort.TtbCreatedAt:
          query.addOrderBy('ttb.createdAt', sortType);
          break;
        case TTBFraudSort.UserCreatedAt:
          query.addOrderBy('user.createdAt', sortType);
          break;
        case TTBFraudSort.PurchaseIPAddress:
          query.addOrderBy('ttbFraud.purchaseIPAddress', sortType);
          break;
        case TTBFraudSort.PurchaseLatitude:
          query.addOrderBy('ttbFraud.purchaseLatitude', sortType);
          query.addOrderBy('ttbFraud.purchaseLongitude', sortType);
          break;
        default:
          query.addOrderBy('ttbFraud.createdAt', 'DESC');
          break;
      }
    } else {
      query.addOrderBy('ttbFraud.createdAt', 'DESC');
    }

    const [orders, total] = await query
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .getManyAndCount();

    if (total === 0) {
      return {
        total: 0,
        orders: [],
      };
    }
    const results = orders.map((order) => {
      const result = {
        ...this.transformTTBFraud(order),
      };
      delete result.user;
      delete result.trythenbuy;
      return result;
    });

    return {
      total,
      take: nTake,
      page: nPage,
      orders: results,
    };
  }

  async updateTTBFraudNote({ ...options }: NoteTTBFraudPayloadDto, updatedBy: string) {
    const { ttbFraudId, note } = options;
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const ttbFraud = await this.ttbFraudRepo.findOne({ id: ttbFraudId });
      if (!ttbFraud) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.TTB_ORDER_NOT_FOUND,
          errorMessage: TTB_MESSAGE_ERROR.TTB_FRAUD_NOT_FOUND,
        });
      }
      await transactionalEntityManager.update(TTBFraudEntity, { id: ttbFraudId }, { note });
      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: ttbFraudId,
            updatedBy: updatedBy,
            orderId: ttbFraud.orderId,
            email: ttbFraud.email,
          },
          manager: transactionalEntityManager,
        },
        TTBFraudEntity.name,
        ttbFraud,
        { note, orderId: ttbFraud.orderId, email: ttbFraud.email }
      );

      return true;
    });
    return { success: true };
  }

  async cancelTTBToGat({ ...options }: CancelTTBToGatPayloadDto, uid: string) {
    const { isBlack, orderId, userId, reasonCancel } = options;
    if (!_.isNil(reasonCancel)) {
      const ttb = await this.ttbRepo.findOne({
        where: { orderId },
        relations: ['user'],
      });
      if (!ttb) return { success: false, message: 'Try Then Buy not exists!' };
      await this.postCancelByAdmin(uid, orderId, reasonCancel, true);
    }

    if (isBlack && userId) {
      const user = await this.userRepo.findOne({ id: userId });
      if (!user) {
        return { success: false, message: 'User not found!' };
      }
      const userBlackList = await this.userBlackRepo.findOne({ userId: userId });
      if (!userBlackList) {
        const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
          const userBlackListData = new UserBlackListEntity();
          userBlackListData.id = v4();
          userBlackListData.userId = userId;
          userBlackListData.email = user.email;
          userBlackListData.createdBy = uid;
          await transactionalEntityManager.save(UserBlackListEntity, userBlackListData);
          await CommonSubscriber.activityLogCommandInsert(
            {
              entity: {
                uuid: userBlackListData.id,
                createdBy: userBlackListData.createdBy,
                email: userBlackListData.email,
              },
              manager: transactionalEntityManager,
            },
            UserBlackListEntity.name
          );

          return true;
        });
      }
    } else if (isBlack && _.isNil(userId)) {
      return { success: false, message: 'UserId is required' };
    }
    return { success: true };
  }

  async getAllTTB({ ...options }: any, user?) {
    let isRoleTTBAgent = false;
    if (user) {
      const userRequest = await this.userRepo.findOne({ id: user.uid });
      isRoleTTBAgent = userRequest.role === Role.TTB_AGENT;
    }
    const nTake = options?.take ? parseInt(options?.take, 10) : 20;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.ttbRepo.createQueryBuilder('ttb').where({}).leftJoinAndSelect('ttb.user', 'user');
    if (isRoleTTBAgent) {
      if (options.strSearch === '') {
        return {
          total: 0,
          take: nTake,
          page: nPage,
          orders: [],
        };
      }
      const strSearch = options.strSearch;
      query.where('ttb.orderId = :strSearch', { strSearch }).andWhere('ttb.status IN (:...statuses)', {
        statuses: [TTBStatus.PRODUCT_PICKED_UP, TTBStatus.PRODUCT_RECEIVED],
      });
      const [orders, total] = await query
        .addOrderBy('ttb.createdAt', 'DESC')
        .take(nTake)
        .skip((nPage - 1) * nTake)
        .getManyAndCount();

      const results = orders.map((order) => {
        const result = {
          ...this.transformTTBOrder(order),
          firstName: '',
          lastName: '',
        };
        delete result.user;
        return result;
      });

      if (total === 0) {
        return {
          total: 0,
          orders: [],
        };
      }

      return {
        total,
        take: nTake,
        page: nPage,
        orders: results,
      };
    }
    let status;
    if (options?.status) {
      status = options.status.split(',').map((r) => r.trim());
    }

    const statusHasAbnormal = [
      TTBStatus.INITIALIZED,
      TTBStatus.TRIAL_COMING,
      TTBStatus.PRODUCT_RETURNING,
      TTBStatus.PRODUCT_RECEIVED,
    ];

    if (options?.abnormalOrder === 1 || options?.abnormalOrder === '1') {
      if (status && status.filter((element) => statusHasAbnormal.includes(element)).length === 0) {
        return {
          total: 0,
          orders: [],
        };
      }
      query.andWhere(
        new Brackets((qb) => {
          if ((status && status.includes(TTBStatus.INITIALIZED)) || !status) {
            qb.where(
              new Brackets((qba) => {
                qba
                  .where('ttb.status = :filterStatusInit', { filterStatusInit: TTBStatus.INITIALIZED })
                  .andWhere('ttb.scheduledArrivalDate IS NULL')
                  .andWhere('ttb.createdAt < :calcDateInit', {
                    calcDateInit: moment(moment().subtract(1, 'days')).toDate(),
                  });
              })
            ).orWhere(
              new Brackets((qba) => {
                qba
                  .where('ttb.status = :filterStatusInitialized', { filterStatusInitialized: TTBStatus.INITIALIZED })
                  .andWhere('ttb.scheduledArrivalDate IS NOT NULL')
                  .andWhere('ttb.shippedAt IS NULL')
                  .andWhere('ttb.scheduledArrivalDate < :calcDateInit', {
                    calcDateInit: moment(moment().subtract(1, 'days')).toDate(),
                  });
              })
            );
          }
          if ((status && status.includes(TTBStatus.TRIAL_COMING)) || !status) {
            qb.orWhere(
              new Brackets((qba) => {
                qba
                  .where('ttb.status = :filterStatusTrial', { filterStatusTrial: TTBStatus.TRIAL_COMING })
                  .andWhere('ttb.shippedAt IS NOT NULL')
                  .andWhere('ttb.deliveredAt IS NULL')
                  .andWhere('ttb.shippedAt < :calcDateTrial', {
                    calcDateTrial: moment(moment().subtract(6, 'days')).toDate(),
                  });
              })
            );
          }
          if ((status && status.includes(TTBStatus.PRODUCT_RETURNING)) || !status) {
            qb.orWhere(
              new Brackets((qba) => {
                qba
                  .where('ttb.status = :filterStatusReturning', {
                    filterStatusReturning: TTBStatus.PRODUCT_RETURNING,
                  })
                  .andWhere('ttb.startedAt < :calcDateReturning', {
                    calcDateReturning: moment(moment().subtract(20, 'days')).toDate(),
                  });
              })
            );
          }
          if ((status && status.includes(TTBStatus.PRODUCT_RECEIVED)) || !status) {
            qb.orWhere(
              new Brackets((qba) => {
                qba
                  .where('ttb.status = :filterStatusReceived', { filterStatusReceived: TTBStatus.PRODUCT_RECEIVED })
                  .andWhere('ttb.GVCReturnProductReceivedAt < :calcDateReceived', {
                    calcDateReceived: moment(moment().subtract(3, 'days')).toDate(),
                  });
              })
            );
          }
        })
      );
    } else if (status && !status.includes('ALL')) {
      query.andWhere('ttb.status IN (:...filterStatus)', { filterStatus: status });
    }

    if (options.strSearch) {
      const strSearch = `%${options.strSearch || ''}%`;
      query.andWhere(
        new Brackets((qb) => {
          qb.where('ttb.orderId like :strSearch', { strSearch })
            .orWhere('user.email like :strSearch', { strSearch })
            .orWhere('ttb.firstName like :strSearch', { strSearch })
            .orWhere('ttb.lastName like :strSearch', { strSearch })
            .orWhere(`CONCAT(ttb.firstName,' ',ttb.lastName) like :strSearch`, { strSearch })
            .orWhere(`CONCAT(ttb.lastName,' ',ttb.firstName) like :strSearch`, { strSearch });
        })
      );
    }

    const [orders, total] = await query
      .addOrderBy('ttb.createdAt', 'DESC')
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .getManyAndCount();

    const results = orders.map((order) => {
      const result = {
        ...this.transformTTBOrder(order),
        email: order.user?.email,
      };
      delete result.user;
      return result;
    });

    if (total === 0) {
      return {
        total: 0,
        orders: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      orders: results,
    };
  }

  getAttribute(data: any) {
    try {
      const exclude = ['Shaft Only?', 'Macro Code', 'TP/NON TP', 'Source Application', 'TTB', 'Operating Unit'];
      return data?.selectedNodes
        ?.filter((item) => !exclude.includes(item.name))
        .map((attr) => ({
          name: attr?.name,
          value: attr?.option?.name,
        }));
    } catch (error) {
      return null;
    }
  }

  getImageUrl(data: any) {
    try {
      const modelInfo = data?.selectedNodes?.find((item) => item.name === 'Model');
      if (!modelInfo) return '';
      return (
        modelInfo?.option?.imageURL ||
        modelInfo?.option?.info?.imageUrl ||
        modelInfo?.option?.info?.imageUrlLarge ||
        modelInfo?.option?.info?.imageUrlMedium ||
        modelInfo?.option?.info?.imageUrlSmall
      );
    } catch (error) {
      return '';
    }
  }

  getProductNameTTB(data: any) {
    try {
      const modelInfo = data?.selectedNodes?.find((item) => item.name === 'Model');
      if (!modelInfo) return '';
      return modelInfo?.option?.name || modelInfo?.option?.info?.name;
    } catch (error) {
      return '';
    }
  }

  transformBindingAndShippingAndPayment(order: any) {
    return {
      billing_address: {
        first_name: order?.billingAddress?.firstName,
        last_name: order?.billingAddress?.lastName,
        address1: order?.billingAddress?.address1,
        address2: order?.billingAddress?.address2,
        city: order?.billingAddress?.city,
        postal_code: order?.billingAddress?.postalCode,
        state_code: order?.billingAddress?.stateCode,
        country_code: order?.billingAddress?.countryCode,
        phone: order?.billingAddress?.phone,
      },
      shipping_address: {
        first_name: order?.shipments[0]?.shippingAddress?.firstName,
        last_name: order?.shipments[0]?.shippingAddress?.lastName,
        address1: order?.shipments[0]?.shippingAddress?.address1,
        address2: order?.shipments[0]?.shippingAddress?.address2,
        city: order?.shipments[0]?.shippingAddress?.city,
        state_code: order?.shipments[0]?.shippingAddress?.stateCode,
        postal_code: order?.shipments[0]?.shippingAddress?.postalCode,
        country_code: order?.shipments[0]?.shippingAddress?.countryCode,
        phone: order?.shipments[0]?.shippingAddress?.phone,
      },
      payments: order?.paymentInstruments?.map((payment) => ({
        payment_id: payment?.paymentMethodId,
        payment_method: 'Credit Card',
        payment_total: payment?.amount,
        payment_card: payment?.paymentCard?.cardType,
        payment_card_last_four: payment?.paymentCard?.numberLastDigits,
      })),
    };
  }

  transformTTBTriggerToKlaviyo(order: any, productInfo: any) {
    return {
      ...this.transformBindingAndShippingAndPayment(order),
      products: [
        {
          name: this.getProductNameTTB(productInfo?.cTmCustomconfiguratorNodes || '{}'),
          image_url: this.getImageUrl(productInfo?.cTmCustomconfiguratorNodes || '{}'),
          price: productInfo?.price,
          tax: productInfo?.tax,
          quantity: productInfo?.quantity,
          attributes: this.getAttribute(productInfo?.cTmCustomconfiguratorNodes || '{}'),
          expected_ship_date: null,
        },
      ],
      subtotal: order?.productSubTotal,
      shipping_cost: order?.shippingTotal,
      sales_tax: order?.taxTotal,
      order_total: order?.orderTotal,
    };
  }

  async trackingEventKlaviyoTTB(event: string, email: string, order: any, productInfo: any = {}) {
    if (!order?.shipments || !Array.isArray(order?.shipments)) {
      order.shipments = [];
    }
    let payloadOrderInfo: any = {
      order_id: order?.orderNo,
      order_datetime: order?.creationDate,
    };
    try {
      payloadOrderInfo = {
        ...payloadOrderInfo,
        shipping_method: order?.shipments[0]?.shippingMethod?.name,
        ...this.transformTTBTriggerToKlaviyo(order, productInfo),
      };
    } catch (error) {
      this.logger.error(`ERROR trackingEventKlaviyoTTB ${error}`);
    }

    let orderInfoHasChargeDate: any = {
      order_id: order?.orderNo,
      order_datetime: order?.creationDate,
      trial_start_date: order?.trialStartDate,
      trial_end_date: order?.trialEndDate,
      charge_date: order?.chargeDate,
    };
    try {
      orderInfoHasChargeDate = {
        ...orderInfoHasChargeDate,
        ...this.transformTTBTriggerToKlaviyo(order, productInfo),
      };
    } catch (error) {
      this.logger.error(`ERROR transformTTBTriggerToKlaviyo ${error}`);
    }
    const isUPSService = this.config.get('app.ttbReturnLabelService') == 'UPS';
    switch (event) {
      case KlaviyoTrackEvents.TTB_ORDER_CONFIRMATION:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_ORDER_CONFIRMATION, payloadOrderInfo);
        break;
      case KlaviyoTrackEvents.TTB_SHIP_CONFIRMATION:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_SHIP_CONFIRMATION, {
          ...payloadOrderInfo,
          tracking_url: order?.shipmentTrackingUrl,
          tracking_number: order?.shipmentTrackingNumber,
          estimated_delivery_date: this.getEstimatedArrivalDateFromClubTracker(order?.orderNo),
        });
        break;
      case KlaviyoTrackEvents.TTB_DELIVERY_CONFIRMATION:
        let payloadOrderDelivery: any = {
          order_id: order?.orderNo,
          order_datetime: order?.creationDate,
          tracking_url: order?.shipmentTrackingUrl,
          trial_start_date: order?.trialStartDate,
          trial_end_date: order?.trialEndDate,
        };
        try {
          payloadOrderDelivery = {
            ...payloadOrderDelivery,
            ...this.transformTTBTriggerToKlaviyo(order, productInfo),
          };
          delete payloadOrderDelivery.payments;
          delete payloadOrderDelivery.products[0]?.expected_ship_date;
          delete payloadOrderDelivery.products[0]?.price;
          delete payloadOrderDelivery.products[0]?.tax;
        } catch (error) {
          this.logger.error(`ERROR TTB_DELIVERY_CONFIRMATION transformTTBTriggerToKlaviyo ${error}`);
        }

        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_DELIVERY_CONFIRMATION, payloadOrderDelivery);
        break;
      case KlaviyoTrackEvents.TTB_HALFWAY_OVER:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_HALFWAY_OVER, {
          ...orderInfoHasChargeDate,
        });
        break;
      case KlaviyoTrackEvents.TTB_TWO_DAYS_LEFT:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_TWO_DAYS_LEFT, {
          ...orderInfoHasChargeDate,
        });
        break;
      case KlaviyoTrackEvents.TTB_TRIAL_ENDED:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_TRIAL_ENDED, {
          ...orderInfoHasChargeDate,
        });
        break;
      case KlaviyoTrackEvents.TTB_DECISION_TIME:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_DECISION_TIME, {
          ...orderInfoHasChargeDate,
        });
        break;
      case KlaviyoTrackEvents.TTB_LAST_CHANCE:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_LAST_CHANCE, {
          ...orderInfoHasChargeDate,
        });
        break;
      case KlaviyoTrackEvents.TTB_RETURN_CONFIRMATION:
        const ttbOrder = await this.ttbRepo.findOne({ orderId: order?.orderNo });
        let payloadOrderReturn: any = {
          order_id: order?.orderNo,
          order_datetime: order?.creationDate,
          return_label_url: isUPSService
            ? ttbOrder.returnLabelUrl
            : `${this.config.get('app.ttbReturningLabelUrl')}${order?.orderNo}`,
        };
        try {
          payloadOrderReturn = {
            ...payloadOrderReturn,
            ...this.transformTTBTriggerToKlaviyo(order, productInfo),
          };
          delete payloadOrderReturn.products[0]?.expected_ship_date;
        } catch (error) {
          this.logger.error(`ERROR TTB_RETURN_CONFIRMATION transformTTBTriggerToKlaviyo ${error}`);
        }

        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_RETURN_CONFIRMATION, payloadOrderReturn);
        break;
      case KlaviyoTrackEvents.TTB_CHARGE_CONFIRMATION:
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_CHARGE_CONFIRMATION, {
          ...orderInfoHasChargeDate,
        });
        break;
      case KlaviyoTrackEvents.TTB_RETURN_INITIATED:
        const returnLabelUrl = isUPSService
          ? await this.generateReturnLabel(order)
          : `${this.config.get('app.ttbReturningLabelUrl')}${order?.orderNo}`;
        console.log({ returnLabelUrl });
        let payloadOrderReturnInitiated: any = {
          order_id: order?.orderNo,
          order_datetime: order?.creationDate,
          trial_start_date: order?.trialStartDate,
          trial_end_date: order?.trialEndDate,
          return_label_url: returnLabelUrl,
          charge_date: order?.chargeDate,
        };
        try {
          payloadOrderReturnInitiated = {
            ...payloadOrderReturnInitiated,
            ...this.transformTTBTriggerToKlaviyo(order, productInfo),
          };
          delete payloadOrderReturnInitiated.products[0]?.expected_ship_date;
        } catch (error) {
          this.logger.error(`ERROR TTB_RETURN_INITIATED transformTTBTriggerToKlaviyo ${error}`);
        }
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_RETURN_INITIATED, payloadOrderReturnInitiated);
        break;
      case KlaviyoTrackEvents.TTB_CHARGE_INITIATED:
        const payloadChargeInitiated = { ...payloadOrderInfo };
        delete payloadChargeInitiated.products[0]?.expected_ship_date;
        delete payloadChargeInitiated.shipping_method;
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_CHARGE_INITIATED, {
          ...payloadChargeInitiated,
        });
        break;
      case KlaviyoTrackEvents.TTB_RETURN_REMINDER:
        const payloadReturnReminder = {
          ...payloadOrderInfo,
          image_url: this.getImageUrl(productInfo?.cTmCustomconfiguratorNodes || '{}'),
        };
        delete payloadReturnReminder.products[0]?.expected_ship_date;
        delete payloadReturnReminder.shipping_method;
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_RETURN_REMINDER, {
          ...payloadReturnReminder,
        });
        break;
      case KlaviyoTrackEvents.TTB_CANCELATION:
        const payloadCancelation = {
          event: KlaviyoTrackEvents.TTB_CANCELATION,
          order_id: order?.orderNo,
          order_datetime: order?.creationDate,
          customer_number: order?.customerInfo?.customerId,
          customer_name: order?.customerName,
          shipping_method: order?.shipments[0]?.shippingMethod?.name,
          ...this.transformBindingAndShippingAndPayment(order),
          item_count: order?.productItems ? order?.productItems?.length : '',
          subtotal: order?.productSubTotal,
          shipping_cost: order?.shippingTotal,
          ship_discount: '',
          merchandise_tax: order?.merchandizeTotalTax,
          sales_tax: order?.taxTotal,
          ship_tax: order?.shippingTotalTax,
          total_tax: order?.taxTotal,
          order_total: order?.orderTotal,
          products: [
            {
              name: this.getProductNameTTB(productInfo?.cTmCustomconfiguratorNodes || '{}'),
              image_url: this.getImageUrl(productInfo?.cTmCustomconfiguratorNodes || '{}'),
              article_no: '',
              price: productInfo?.price,
              base_price: productInfo?.basePrice,
              gross_price: productInfo?.basePrice,
              net_price: productInfo?.price,
              tax: productInfo?.tax,
              quantity: productInfo?.quantity,
              attributes: this.getAttribute(productInfo?.cTmCustomconfiguratorNodes || '{}'),
            },
          ],
        };
        await this.klaviyoService.track(email, KlaviyoTrackEvents.TTB_CANCELATION, payloadCancelation);
        break;
      default:
        break;
    }
  }

  async trackingEventKlaviyoTTBByApi(event: string, orderId: string) {
    const order = await this.ttbRepo.findOne({ orderId });
    if (!order) {
      return;
    }

    const user = await this.userRepo.findOne({ id: order.userId });
    if (!user) {
      return;
    }

    const ecomOrder = await this.ecomService.getOrder(order.orderId, order?.country);

    const mytmOrder = await this.transformTTBOrder(order);
    const productReturningData = {
      ...ecomOrder,
      shipmentTrackingUrl: mytmOrder.shipmentTrackingUrl,
      shipmentTrackingNumber: mytmOrder.shipmentTrackingNumber,
      trialStartDate: mytmOrder.startedAt,
      trialEndDate: mytmOrder.endDate,
      chargeDate: mytmOrder.cardWillBeChangedAt,
      GVCProductReturningTrackingUrl: mytmOrder.GVCProductReturningTrackingUrl,
    };

    await this.trackingEventKlaviyoTTB(event, user.email, productReturningData, mytmOrder.productInfo);
  }

  async updateAmountTotalOrders() {
    try {
      const orders = await this.ttbRepo.createQueryBuilder('ttb').where('ttb.amountTotal IS NULL').getMany();

      await Promise.all(
        orders.map(async (order) => {
          const orderInfo = await this.ecomService.getOrder(order.orderId, order?.country);
          order.amountTotal = orderInfo?.orderTotal ? Number(orderInfo?.orderTotal) : null;
          await this.ttbRepo.save(order);
        })
      );

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
      };
    }
  }

  async updateProductInfo(orderId: any, productInfo: any) {
    await this.ttbRepo.update({ orderId }, { productInfo });
  }

  isTrackIncludePickedUpEvent(payload: SeventeentrackWebhookDto) {
    const trackEvents = payload?.data?.track?.z1;
    const zex = payload?.data?.track?.zex;
    if (zex) {
      const isPickedUp = zex?.pickup == true;
      if (isPickedUp) {
        return true;
      }
    }
    if (trackEvents) {
      return trackEvents.some(
        (event: any) =>
          event['z']?.toLowerCase() === 'picked up' ||
          event['z']?.toLowerCase() === 'pickup scan' ||
          event['z']?.toLowerCase() === 'origin scan'
      );
    }

    return false;
  }

  async generateReturnLabel(order: any) {
    const ttbDetail = await this.ttbRepo.findOne({ orderId: order.orderNo });
    if (!ttbDetail) {
      return '';
    }

    if (this.shouldNotCreateReturnLabel(ttbDetail)) {
      return ttbDetail.returnLabelUrl;
    }
    console.log(`Create new return label....`);

    const uploadResult = await this.uploadPrintLabelToAzureBlob(order);
    if (!uploadResult) {
      return '';
    }
    const { returnLabelUrl, trackingNumber } = uploadResult;
    const payloadTracking: GVCTrackingNumber = {
      orderId: order.orderNo,
      carrierTrackingNumber: trackingNumber,
      carrier: TRACKING_CARRIERS.UPS,
    };
    const updateTrackingNumber = await this.updateOrderTrackingNumberFromGVC(payloadTracking);

    let updateData: any = {
      returnLabelUrl,
    };
    if (ttbDetail.createNewReturnLabel) {
      updateData = { returnLabelUrl, createNewReturnLabel: false };
    }
    await this.ttbRepo.update({ orderId: order.orderNo }, updateData);
    return returnLabelUrl;
  }

  shouldNotCreateReturnLabel(ttbOrder: TTBEntity) {
    if (ttbOrder.status !== TTBStatus.MISMATCHED_COMPONENT) {
      return !!ttbOrder.returnLabelUrl;
    } else {
      return !ttbOrder.createNewReturnLabel;
    }
  }

  async handleCheckOrderDeliveryStatus(order: TTBEntity) {
    this.logger.log(`START CHECK ORDER DELIVER: ${order?.orderId}`);
    if (!order.shippedAt) {
      return order;
    }
    let trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
      order.shipmentTrackingNumber,
      order.shipmentCarrierName,
      false
    );
    if (!trackingNumberInfo) {
      this.logger.log(`NOT FOUND trackingNumberInfo: ${order?.orderId}`);
      const shipmentSeventeentrackCarrierKey = await this.seventeentrackService.registerTrackingNumber(
        order.shipmentTrackingNumber,
        order.shipmentCarrierName,
        true
      );
      if (!shipmentSeventeentrackCarrierKey) {
        return order;
      }
      trackingNumberInfo = await this.seventeentrackService.getTrackingNumberInfo(
        order.shipmentTrackingNumber,
        order.shipmentCarrierName,
        false
      );
      if (!trackingNumberInfo) {
        return order;
      }
    }
    if (!this.seventeentrackService.isTrackingNumberDelivered(trackingNumberInfo)) {
      return order;
    }
    if (!trackingNumberInfo.track) {
      this.logger.log(`NOT FOUND track: ${order?.orderId}`);
      return order;
    }
    const deliveredDateString = trackingNumberInfo.track['z0']?.a;
    if (!deliveredDateString) {
      this.logger.log(`INVALID deliveredDateString: ${order?.orderId}`);
      return order;
    }
    const deliveredDate = moment(deliveredDateString, 'YYYY-MM-DD HH:mm');
    if (!deliveredDate.isValid()) {
      this.logger.log(`INVALID deliveredDate: ${order?.orderId}`);
      return order;
    }
    const startedAt = moment(deliveredDate.clone().toDate().setHours(17, 0, 0, 0))
      .add(TTBStatusRangeDate[TTBStatus.TRIAL_STARTED], 'days')
      .toDate();
    try {
      await this.ttbRepo.update(
        { orderId: order.orderId },
        { status: TTBStatus.TRIAL_STARTED, startedAt, deliveredAt: deliveredDate.toDate() }
      );
      const orderUpdate = await this.ttbRepo.findOne({ orderId: order.orderId });
      if (orderUpdate) {
        console.log(`ORDER UPDATED: ${order?.orderId} STATUS: ${orderUpdate?.status}`);
        console.log(`ORDER UPDATED: ${order?.orderId}, startedAt: ${startedAt}, deliveredAt: ${deliveredDate}`);
      }
    } catch (error) {
      console.log(`ERROR UPDATE DELIVERED: ${order?.orderId}, ERROR: ${error?.message}`);
      console.log(error);
    }
    this.logger.log(
      `UPDATE ORDER DELIVERED: ${order?.orderId}, startedAt: ${startedAt}, deliveredAt: ${deliveredDate} `
    );
    return order;
  }

  async uploadPrintLabelToAzureBlob(order: any) {
    const currentDate = moment();
    const storageAccountName = this.config.get('app.azureBlobAccountName');
    const sasToken = this.config.get('app.azureBlobSASToken');
    const containerName = this.config.get('app.azureBlobContainer');
    const azureCDN = this.config.get('app.azureCDN');
    const blobService = new BlobServiceClient(`https://${storageAccountName}.blob.core.windows.net/?${sasToken}`);

    const containerClient = blobService.getContainerClient(containerName);

    const fileName = crypto.createHash('md5').update(currentDate.unix().toString()).digest('hex');
    const blobPath = `${currentDate.format('YYYY')}/${currentDate.format('MM')}/${fileName}.html`;

    const resultShipConfirm = await this.shipConfirmUPS(order);
    if (!resultShipConfirm) {
      return null;
    }
    const { printLabelBase64, trackingNumber, packageResults, htmlBase64 } = resultShipConfirm;
    const blobClient = containerClient.getBlockBlobClient(blobPath);
    const htmlPrintLabel = this.generateHTMLPrintLabel(printLabelBase64, htmlBase64, trackingNumber);
    const file = Buffer.from(htmlPrintLabel);
    const options = { blobHTTPHeaders: { blobContentType: 'text/html' } };
    await blobClient.upload(file, file.byteLength, options);
    await blobClient.setMetadata({ UserName: 'anonymous' });
    return { returnLabelUrl: `https://${azureCDN}/${containerName}/${blobPath}`, trackingNumber, packageResults };
  }

  async shipConfirmUPS(order: any) {
    const upsURL = this.config.get('app.upsServiceURL');
    const payload = {
      ...this.transformShipmentRequestUPS(order),
    };
    try {
      const responseShipment = await axios.post(`${upsURL}/ship/v1/shipments`, payload, {
        headers: {
          Username: this.config.get('app.upsUserName'),
          Password: this.config.get('app.upsPassword'),
          AccessLicenseNumber: this.config.get('app.upsToken'),
        },
      });
      if (responseShipment.data?.ShipmentResponse?.Response?.ResponseStatus?.Code != 1) {
        this.logger.error(
          `UPS POST SHIP CONFIRM ERROR: ${responseShipment.data?.ShipmentResponse?.Response?.ResponseStatus?.Description}`
        );
        await this.loggingService.save({
          event: `UPS-SHIP-CONFIRM`,
          data: responseShipment.data,
          orderId: order?.orderNo,
        });
        return null;
      }
      const packageResults = responseShipment.data?.ShipmentResponse?.ShipmentResults?.PackageResults;
      return {
        printLabelBase64: packageResults?.ShippingLabel?.GraphicImage,
        htmlBase64: packageResults?.ShippingLabel?.HTMLImage,
        trackingNumber: packageResults.TrackingNumber,
        packageResults,
      };
    } catch (error) {
      this.logger.error(`UPS SHIP CONFIRM ERROR: ${JSON.stringify(error?.response?.data || error.message)}`);
      await this.loggingService.save({
        event: `UPS-SHIP-CONFIRM`,
        data: error?.response?.data || error.message,
        payload: payload.ShipmentRequest.Shipment.ShipFrom,
        orderId: order?.orderNo,
      });
      return null;
    }
  }

  generateHTMLPrintLabel(graphicImage: string, htmlImage: string, trackingNumber: any) {
    let returnLabelHTML = Buffer.from(htmlImage, 'base64').toString('utf8');
    const regImg = RegExp(/.\/(.*).gif/gm);
    const matchImg = returnLabelHTML.match(regImg);
    if (matchImg && matchImg.length > 0) {
      returnLabelHTML = returnLabelHTML.replace(matchImg[0], `data:image/png;base64, ${graphicImage}`);
    }
    returnLabelHTML = returnLabelHTML.replace(/\u00ef/g, '');
    returnLabelHTML = returnLabelHTML.replace(/\u00bf/g, '');
    returnLabelHTML = returnLabelHTML.replace(/\u00bd/g, '');
    return returnLabelHTML;
  }

  transformSecurityUPS() {
    return {
      UPSSecurity: {
        UsernameToken: {
          Username: this.config.get('app.upsUserName'),
          Password: this.config.get('app.upsPassword'),
        },
        ServiceAccessToken: {
          AccessLicenseNumber: this.config.get('app.upsToken'),
        },
      },
    };
  }

  transformShipmentRequestUPS(order: any) {
    const orderShipment = order?.shipments[0] ?? {};
    return {
      ShipmentRequest: {
        LabelSpecification: {
          LabelImageFormat: {
            Code: 'GIF',
            Description: 'GIF',
          },
          HTTPUserAgent: 'Mozilla/4.5',
        },
        Shipment: {
          ReturnService: {
            Code: `${this.config.get('app.upsShipmentReturnService')}`,
          },
          ...this.transformShipmentRequestShipperUPS(),
          ...this.transformShipmentRequestShipToUPS(),
          ...this.transformShipmentRequestShipFromUPS(orderShipment),
          ...this.transformShipmentRequestPaymentUPS(),
          Service: {
            Code: `${this.config.get('app.upsShipmentService')}`,
          },
          Description: 'Tradein/Purchase Program',
          Package: {
            Description: 'Tradein/Purchase Program',
            Packaging: {
              Code: `${this.config.get('app.upsShipmentPackingCode')}`,
            },
            ReferenceNumber: [
              {
                Code: 'PO',
                Value: order?.orderNo,
              },
            ],
            PackageWeight: {
              UnitOfMeasurement: {
                Code: 'LBS',
                Description: 'Pounds',
              },
              Weight: '6.0',
            },
            Dimensions: {
              UnitOfMeasurement: {
                Code: 'IN',
              },
              Length: '48',
              Width: '6',
              Height: '6',
            },
          },
          ShipmentRatingOptions: {
            NegotiatedRatesIndicator: '0',
          },
        },
      },
    };
  }

  transformShipmentRequestShipperUPS() {
    return {
      Shipper: {
        Name: `${this.config.get('app.upsShipperName')}`,
        AttentionName: `${this.config.get('app.upsShipperAttentionName')}`,
        Phone: {
          Number: `${this.config.get('app.upsShipperPhone')}`,
        },
        ShipperNumber: `${this.config.get('app.upsShipperNumber')}`,
        Address: {
          AddressLine: `${this.config.get('app.upsShipperAddressLine')}`,
          City: `${this.config.get('app.upsShipperAddressCity')}`,
          StateProvinceCode: `${this.config.get('app.upsShipperAddressState')}`,
          PostalCode: `${this.config.get('app.upsShipperAddressPostalCode')}`,
          CountryCode: `${this.config.get('app.upsShipperAddressCountryCode')}`,
        },
      },
    };
  }

  transformShipmentRequestShipToUPS() {
    return {
      ShipTo: {
        Name: `${this.config.get('app.upsShipToName')}`,
        AttentionName: `${this.config.get('app.upsShipToAttentionName')}`,
        Phone: {
          Number: `${this.config.get('app.upsShipToPhone')}`,
        },
        Address: {
          AddressLine: `${this.config.get('app.upsShipToAddressLine')}`,
          City: `${this.config.get('app.upsShipToAddressCity')}`,
          StateProvinceCode: `${this.config.get('app.upsShipToAddressState')}`,
          PostalCode: `${this.config.get('app.upsShipToAddressPostalCode')}`,
          CountryCode: `${this.config.get('app.upsShipToAddressCountryCode')}`,
        },
      },
    };
  }

  transformShipmentRequestShipFromUPS(orderShipment: any) {
    return {
      ShipFrom: {
        Name: orderShipment.shippingAddress?.fullName,
        AttentionName: `${this.config.get('app.upsShipToAttentionName')}`,
        Phone: {
          Number: orderShipment.shippingAddress?.phone,
        },
        Address: {
          AddressLine: orderShipment?.shippingAddress?.address1,
          City: orderShipment?.shippingAddress?.city,
          StateProvinceCode: orderShipment?.shippingAddress?.stateCode,
          PostalCode: orderShipment?.shippingAddress?.postalCode,
          CountryCode: orderShipment?.shippingAddress?.countryCode,
        },
      },
    };
  }

  transformShipmentRequestPaymentUPS() {
    return {
      PaymentInformation: {
        ShipmentCharge: {
          Type: `${this.config.get('app.upsPaymentType')}`,
          BillShipper: {
            AccountNumber: `${this.config.get('app.upsPaymentAccountNumber')}`,
          },
        },
      },
    };
  }
}

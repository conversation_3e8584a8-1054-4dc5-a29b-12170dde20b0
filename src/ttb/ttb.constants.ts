import { TRACKING_CARRIERS } from 'src/shared/services/seventeentrack.service';
import { TTBStatus } from './entities/ttb.entity';

export const TTB_MESSAGE_ERROR = {
  TTB_NOT_FOUND: 'The Try Then Buy is not found.',
  TTB_FRAUD_NOT_FOUND: 'The Try Then Buy Fraud is not found.',
};

export const TTBPushMessage = {
  [TTBStatus.INITIALIZED]: null,
  [TTBStatus.TRIAL_COMING]: {
    title: 'Your Try Then Buy Club Is on the Way',
    message: 'Tracking info sent via email or follow your club in the Try Then Buy section of MyTaylorMade+.',
  },
  [TTBStatus.TRIAL_STARTED]: {
    title: 'Ready to Tee it Up?',
    message: 'Your club has arrived and is ready for the course. Time to start testing!',
  },
  [TTBStatus.TRIAL_HALF_DONE]: {
    title: 'Making the Turn',
    message: 'Your Try Then Buy trial is halfway through. Keep testing, keep the club, or send it back at no cost.',
  },
  [TTBStatus.TRIAL_2_DAYS_LEFT]: {
    title: 'Your Free Trial is Almost Up',
    message: 'You have 2 days remaining! Tell us if you plan to keep your club or send it back.',
  },
  [TTBStatus.TRIAL_1_DAY_LEFT]: {
    title: 'Last Day of Your Try Then Buy Trial',
    message: `Let us know if you're keeping the club or sending it back.`,
  },
  [TTBStatus.TIME_TO_DECIDE]: {
    title: 'Decision Time!',
    message: 'Let us know if you plan to keep your trial club.',
  },
  [TTBStatus.LAST_CHANCE]: {
    title: 'Last Chance!',
    message: `You'll be charged the full price of the club if you don't return today.`,
  },
  [TTBStatus.PRODUCT_RETURNING]: null,
  [TTBStatus.PRODUCT_RETURNED]: {
    title: `We've Received Your Return`,
    message: 'Thank you for using Try Then Buy! We received your returned club and you can now start another trial!',
  },
  [TTBStatus.CHARGED]: {
    title: 'The Clock Has Hit Zero',
    message: 'Your trial club was not returned in time and the credit card you provided has been charged.',
  },
  [TTBStatus.MISMATCHED_COMPONENT]: {
    title: 'Your Returned Club Failed Inspection',
    message:
      'Please return the correct components ASAP to avoid being charged the full price of the club. Tap to get your return label.',
  },
  [TTBStatus.ONE_WEEK_LEFT_TO_FORCED_CHARGE]: {
    title: `Don't Forget to Return Your Club!`,
    message: `You will be charged if you don't return to ${TRACKING_CARRIERS.UPS} within a week.`,
  },
};

export const TTBStatusRangeDate = {
  [TTBStatus.TRIAL_STARTED]: 1,
  [TTBStatus.TRIAL_HALF_DONE]: 7,
  [TTBStatus.TRIAL_2_DAYS_LEFT]: 12,
  [TTBStatus.TRIAL_1_DAY_LEFT]: 13,
  [TTBStatus.TIME_TO_DECIDE]: 14,
  [TTBStatus.LAST_CHANCE]: 15,
};

export const TTBShippedStatusTexts = ['We have shipped your product', 'We have shipped your custom product'];

import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Inject, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Job, Queue } from 'bull';
import * as _ from 'lodash';
import { subtract } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { Brackets, In, IsNull, Not, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserEntity } from 'src/auth/entities/user.entity';
import { UserBlackListEntity } from '../auth/entities/user-black-list.entity';
import { CdmService } from '../cdm/cdm.service';
import { TTBFraudInfoEntity } from './entities/ttb-fraud-info.entity';
import { TTBFraudEntity } from './entities/ttb-fraud.entity';
import { TTBEntity, TTBStatus } from './entities/ttb.entity';
import { TTBNotifyAdminProcessorQueueName } from './ttb-notify-admin.processor';

export type TTBShipmentScanJob = Job<{
  userId: string;
  isSimulator: boolean;
  fingerPrint: string;
  ipAddress: string;
  lastLatitude: number;
  lastLongitude: number;
}>;

const TTBFraudName = {
  ISSIMULATOR: 'Is Simulator',
  USERBLACKLIST: 'User Black List',
  LASTNAME: 'Last Name',
  FINGERPRINT: 'Fingerprint',
  GPS: 'GPS',
  IPADDRESS: 'IP Address',
};

const TTBFraudPercent = {
  ISSIMULATOR: 100,
  USERBLACKLIST: 100,
  LASTNAME: 10,
  FINGERPRINT: 90,
  GPS: 30,
  IPADDRESS: 65,
};

const statusTTBIgnore = [
  TTBStatus.BOUGHT,
  TTBStatus.CHARGED,
  TTBStatus.PRODUCT_RETURNED,
  TTBStatus.CANCELLED,
  TTBStatus.PRODUCT_RECEIVED_TO_TM,
  TTBStatus.PRODUCT_RETURNING_TO_TM,
];

export enum TTBFraudProtectionQueueName {
  TTB_FRAUD_PROTECTION = 'fraud-protection-job',
}

@Processor('ttb-fraud')
export class TTBFraudProcessor {
  private readonly logger = new Logger(TTBFraudProcessor.name);
  constructor(
    private readonly config: ConfigService,
    @InjectQueue('ttb-notify-admin') private ttbFraudNotifyAdminQueue: Queue,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(TTBFraudEntity) private readonly ttbFraudRepo: Repository<TTBFraudEntity>,
    @InjectRepository(TTBFraudInfoEntity) private readonly ttbFraudInfoRepo: Repository<TTBFraudInfoEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserBlackListEntity) private readonly userBlackListRepo: Repository<UserBlackListEntity>,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService
  ) {}

  @Process({
    name: TTBFraudProtectionQueueName.TTB_FRAUD_PROTECTION,
    concurrency: 1,
  })
  async checkTTBFraud(job: TTBShipmentScanJob): Promise<any> {
    console.log(`TTB Fraud Data ${new Date()} ${job.data}`);
    const reason = [];
    const percentFraudArr = [];
    const dataTTBFraudInfo = [];
    let isFingerprint = false;
    let percentTTBFraud = 0;
    let duplicateReason = '';
    let finishCheck = false;
    this.logger.log(`TTB Fraud Data ${new Date()} ${job.data}`);
    try {
      const { data } = job;
      const { isSimulator, fingerPrint, lastLatitude, lastLongitude, userId, ipAddress } = data;
      const countTtbFraud = await this.ttbFraudRepo.count({
        userId,
        tryThenBuyId: IsNull(),
        fingerPrint,
      });
      const user = await this.userRepo.findOne(userId);
      const permission = await this.cdmService.getPermission(user.email);
      if (countTtbFraud || !user || !permission) {
        return true;
      }

      const ttbFraudData = new TTBFraudEntity();
      ttbFraudData.id = v4();
      ttbFraudData.userId = userId;
      ttbFraudData.email = user.email;
      ttbFraudData.firstName = permission.firstName;
      ttbFraudData.lastName = permission.lastName;
      ttbFraudData.purchaseIPAddress = ipAddress;
      ttbFraudData.isSimulator = false;
      ttbFraudData.isUserBlackList = false;
      ttbFraudData.fingerPrint = fingerPrint;
      if (lastLatitude && lastLongitude) {
        const lat = Number(lastLatitude.toFixed(5));
        const long = Number(lastLongitude.toFixed(5));
        ttbFraudData.purchaseLatitude = lat;
        ttbFraudData.purchaseLongitude = long;
        const locationGPS = await this.getLocationFromGPS(lat, long);
        ttbFraudData.purchaseGPSLocation = locationGPS || null;
      }

      if (ipAddress) {
        const locationIp = await this.getLocationFromIp(ipAddress);
        ttbFraudData.purchaseIPAddressLocation = locationIp || null;
      }

      if (isSimulator && isSimulator === true) {
        ttbFraudData.isSimulator = true;
        reason.push(TTBFraudName.ISSIMULATOR);
        percentFraudArr.push(TTBFraudPercent.ISSIMULATOR);
        finishCheck = true;
      }

      const countUserBlack = await this.userBlackListRepo.count({
        userId,
      });
      if (countUserBlack && countUserBlack > 0) {
        ttbFraudData.isUserBlackList = true;
        reason.push(TTBFraudName.USERBLACKLIST);
        percentFraudArr.push(TTBFraudPercent.USERBLACKLIST);
        finishCheck = true;
      }

      if (finishCheck) {
        duplicateReason = reason.join(', ');
        percentTTBFraud = _.max(percentFraudArr);
        ttbFraudData.potentialFraud = percentTTBFraud;
        ttbFraudData.duplicate = duplicateReason;
        await this.ttbFraudRepo.save(ttbFraudData);
        await this.ttbFraudNotifyAdminQueue.add(TTBNotifyAdminProcessorQueueName.PUSH, {
          email: user.email,
        });
        return true;
      }

      let ttbDupLastName = [];
      if (permission.lastName) {
        ttbDupLastName = await this.checkDuplicateLastName(permission.lastName);
        if (ttbDupLastName && ttbDupLastName.length) {
          ttbDupLastName.map((id: string) => {
            const indexF = dataTTBFraudInfo.findIndex((val: any) => val.ttbId === id);
            if (indexF != -1) {
              const obj = dataTTBFraudInfo[indexF];
              obj.persentArr.push(TTBFraudPercent.LASTNAME);
              dataTTBFraudInfo[indexF] = { ...obj, isLastName: true };
            } else {
              dataTTBFraudInfo.push({
                ttbId: id,
                isLastName: true,
                persentArr: [TTBFraudPercent.LASTNAME],
              });
            }
          });
          finishCheck = true;
          reason.push(TTBFraudName.LASTNAME);
          percentFraudArr.push(TTBFraudPercent.LASTNAME);
        }
      }

      let ttbDupIpAddress = [];
      if (ipAddress) {
        ttbDupIpAddress = await this.checkDuplicateIpAddress(ipAddress);
        if (ttbDupIpAddress && ttbDupIpAddress.length) {
          ttbDupIpAddress.map((id: string) => {
            const indexF = dataTTBFraudInfo.findIndex((val: any) => val.ttbId === id);
            if (indexF != -1) {
              const obj = dataTTBFraudInfo[indexF];
              obj.persentArr.push(TTBFraudPercent.IPADDRESS);
              dataTTBFraudInfo[indexF] = { ...obj, isIPAddress: true };
            } else {
              dataTTBFraudInfo.push({
                ttbId: id,
                isIPAddress: true,
                persentArr: [TTBFraudPercent.IPADDRESS],
              });
            }
          });
          finishCheck = true;
          reason.push(TTBFraudName.IPADDRESS);
          percentFraudArr.push(TTBFraudPercent.IPADDRESS);
        } else {
          const ttbFraudIps = await this.checkDuplicateIpAddressTTBFraud(ipAddress);
          if (ttbFraudIps && ttbFraudIps.length) {
            finishCheck = true;
            reason.push(TTBFraudName.IPADDRESS);
            percentFraudArr.push(TTBFraudPercent.IPADDRESS);
          }
        }
      }

      let ttbDupFingerPrint = [];
      if (fingerPrint) {
        ttbDupFingerPrint = await this.checkDuplicateFingerPrint(fingerPrint);
        if (ttbDupFingerPrint && ttbDupFingerPrint.length) {
          ttbDupFingerPrint.map((id: string) => {
            const indexF = dataTTBFraudInfo.findIndex((val: any) => val.ttbId === id);
            if (indexF != -1) {
              const obj = dataTTBFraudInfo[indexF];
              obj.persentArr.push(TTBFraudPercent.FINGERPRINT);
              dataTTBFraudInfo[indexF] = { ...obj, isFingerPrint: true };
            } else {
              dataTTBFraudInfo.push({
                ttbId: id,
                isFingerPrint: true,
                persentArr: [TTBFraudPercent.FINGERPRINT],
              });
            }
          });
          finishCheck = true;
          reason.push(TTBFraudName.FINGERPRINT);
          percentFraudArr.push(TTBFraudPercent.FINGERPRINT);
          isFingerprint = true;
        } else {
          const ttbFraudFingerprints = await this.checkDuplicateFingerPrintTTBFraud(fingerPrint, userId);
          if (ttbFraudFingerprints && ttbFraudFingerprints.length) {
            finishCheck = true;
            reason.push(TTBFraudName.FINGERPRINT);
            percentFraudArr.push(TTBFraudPercent.FINGERPRINT);
            isFingerprint = true;
          }
        }
      }

      let ttbDupGPS = [];
      if (lastLongitude && lastLatitude) {
        ttbDupGPS = await this.checkDuplicateFingerGPS(lastLatitude, lastLongitude);
        if (ttbDupGPS && ttbDupGPS.length) {
          ttbDupGPS.map((id: string) => {
            const indexF = dataTTBFraudInfo.findIndex((val: any) => val.ttbId === id);
            if (indexF != -1) {
              const obj = dataTTBFraudInfo[indexF];
              obj.persentArr.push(TTBFraudPercent.GPS);
              dataTTBFraudInfo[indexF] = { ...obj, isGPS: true };
            } else {
              dataTTBFraudInfo.push({
                ttbId: id,
                isGPS: true,
                persentArr: [TTBFraudPercent.GPS],
              });
            }
          });
          finishCheck = true;
          reason.push(TTBFraudName.GPS);
          percentFraudArr.push(TTBFraudPercent.GPS);
        }
      }

      if (!finishCheck || !percentFraudArr.length) {
        return true;
      }

      duplicateReason = reason.join(', ');
      percentTTBFraud = this.caculatePotentialFraud(percentFraudArr);
      ttbFraudData.potentialFraud = percentTTBFraud;
      ttbFraudData.duplicate = duplicateReason;
      const duplicateIdTTBs = _.uniq(_.concat(ttbDupLastName, ttbDupGPS, ttbDupIpAddress, ttbDupFingerPrint));
      const ttbFraud = await this.ttbFraudRepo.save(ttbFraudData);
      console.log('dataTTBFraudInfo', dataTTBFraudInfo);
      await this.saveTTBFraudPartner(duplicateIdTTBs, dataTTBFraudInfo);
      await this.saveTTBFraudInfo(ttbFraud.id, dataTTBFraudInfo);
      if (isFingerprint) {
        await this.ttbFraudNotifyAdminQueue.add(TTBNotifyAdminProcessorQueueName.PUSH, {
          email: user.email,
        });
      }
      return true;
    } catch (err) {
      console.log('TB Fraud Error Catch', err);
      this.logger.log(`TTB Fraud Error Catch ${new Date()} ${err}`);
      return false;
    }
  }
  caculatePotentialFraud(percentFraudArr) {
    let result = 0;
    const number = Number((percentFraudArr.length - 1) * 10);
    const max = Number(_.max(percentFraudArr));
    result = max + number;
    if (result >= 100) {
      return 100;
    }
    return result;
  }

  getReasonTTBFraud(ttbFrauInfo) {
    const { isFingerPrint, isLastName, isIPAddress, isGPS } = ttbFrauInfo;
    const reason = [];
    if (isFingerPrint) {
      reason.push(TTBFraudName.FINGERPRINT);
    }

    if (isLastName) {
      reason.push(TTBFraudName.LASTNAME);
    }

    if (isIPAddress) {
      reason.push(TTBFraudName.IPADDRESS);
    }

    if (isGPS) {
      reason.push(TTBFraudName.GPS);
    }
    return reason.join(', ');
  }
  async saveTTBFraudInfo(ttbFrauId, datas) {
    try {
      await Promise.all(
        datas.map((value: any) => {
          const ttbFraudInfoData = new TTBFraudInfoEntity();
          ttbFraudInfoData.id = v4();
          ttbFraudInfoData.tryThenBuyId = value.ttbId;
          ttbFraudInfoData.tryThenBuyIdFraudId = ttbFrauId;
          ttbFraudInfoData.isLastName = !!value.isLastName || null;
          ttbFraudInfoData.isFingerPrint = !!value.isFingerPrint || null;
          ttbFraudInfoData.isIPAddress = !!value.isIPAddress || null;
          ttbFraudInfoData.isGPS = !!value.isGPS || null;
          this.ttbFraudInfoRepo.save(ttbFraudInfoData);
        })
      );
      return true;
    } catch (err) {
      this.logger.log(`TTB Fraud INFO ERR ${new Date()} ${err}`);
      return false;
    }
  }

  async saveTTBFraudPartner(ttbIds, dataTTBFraudInfo) {
    try {
      if (!ttbIds || !ttbIds.length) {
        return true;
      }
      const ttbs = await this.ttbRepo.find({
        where: {
          id: In(ttbIds),
        },
        select: [
          'id',
          'userId',
          'orderId',
          'lastName',
          'firstName',
          'purchaseIPAddress',
          'isSimulator',
          'fingerPrint',
          'purchaseLatitude',
          'purchaseLongitude',
        ],
        relations: ['user'],
      });

      await Promise.all(
        ttbs.map(async (ttb: TTBEntity) => {
          const ttbInfo = dataTTBFraudInfo.find((val) => val.ttbId === ttb.id);
          const ttbFraud = await this.ttbFraudRepo.findOne({ tryThenBuyId: ttb.id });
          if (ttbFraud) {
            const newPotentialFraud = this.caculatePotentialFraud(ttbInfo.persentArr);
            const oldPotentialFraud = ttbFraud?.potentialFraud;
            if (newPotentialFraud > oldPotentialFraud) {
              ttbFraud.potentialFraud = this.caculatePotentialFraud(ttbInfo.persentArr);
              ttbFraud.duplicate = this.getReasonTTBFraud(ttbInfo);
              this.ttbFraudRepo.save(ttbFraud);
            }
          } else {
            const ttbFraudData = new TTBFraudEntity();
            ttbFraudData.id = v4();
            ttbFraudData.tryThenBuyId = ttb.id;
            ttbFraudData.orderId = ttb.orderId;
            ttbFraudData.userId = ttb.userId;
            ttbFraudData.email = ttb?.user?.email || null;
            ttbFraudData.lastName = ttb.lastName;
            ttbFraudData.firstName = ttb.firstName;
            ttbFraudData.purchaseIPAddress = ttb.purchaseIPAddress || null;
            ttbFraudData.isSimulator = ttb.isSimulator || null;
            ttbFraudData.fingerPrint = ttb.fingerPrint || null;
            ttbFraudData.purchaseLatitude = ttb.purchaseLatitude || null;
            ttbFraudData.purchaseLongitude = ttb.purchaseLongitude || null;
            ttbFraudData.potentialFraud = this.caculatePotentialFraud(ttbInfo.persentArr);
            ttbFraudData.duplicate = this.getReasonTTBFraud(ttbInfo);
            if (ttb.purchaseIPAddress) {
              const locationIp = await this.getLocationFromIp(ttb.purchaseIPAddress);
              ttbFraudData.purchaseIPAddressLocation = locationIp || null;
            }

            if (ttb.purchaseLatitude && ttb.purchaseLongitude) {
              const locationGPS = await this.getLocationFromGPS(ttb.purchaseLatitude, ttb.purchaseLongitude);
              ttbFraudData.purchaseGPSLocation = locationGPS || null;
            }

            this.ttbFraudRepo.save(ttbFraudData);
          }
        })
      );
      return true;
    } catch (err) {
      console.log('TTB Fraud Partner', err);
      this.logger.log(`TTB Fraud Partner ERR ${new Date()} ${err}`);
      return false;
    }
  }

  async checkDuplicateLastName(lastName: string) {
    const datas = await this.ttbRepo.find({
      where: {
        lastName: lastName,
        status: Not(In(statusTTBIgnore)),
      },
      select: ['id'],
    });

    if (datas && datas.length) {
      return _.map(datas, 'id');
    }
    return [];
  }

  async checkDuplicateIpAddress(ipAdress: string) {
    const datas = await this.ttbRepo.find({
      where: {
        purchaseIPAddress: ipAdress,
        status: Not(In(statusTTBIgnore)),
      },
      select: ['id'],
    });

    if (datas && datas.length) {
      return _.map(datas, 'id');
    }
    return [];
  }

  async checkDuplicateFingerPrint(fingerPrint: string) {
    const datas = await this.ttbRepo.find({
      where: {
        fingerPrint: fingerPrint,
        status: Not(In(statusTTBIgnore)),
      },
      select: ['id'],
    });

    if (datas && datas.length) {
      return _.map(datas, 'id');
    }
    return [];
  }

  async checkDuplicateIpAddressTTBFraud(ipAddress: string) {
    const datas = await this.ttbFraudRepo
      .createQueryBuilder('ttbf')
      .leftJoinAndSelect('ttbf.trythenbuy', 'ttb')
      .select('ttbf.id')
      .where('ttbf.purchaseIPAddress = :ipAddress', { ipAddress: ipAddress })
      .andWhere(
        new Brackets((qb) => {
          qb.where('ttbf.orderId is NULL AND ttbf.potentialFraud >= 90').orWhere(
            'ttb.status = :status AND ttb.isAdminCancel = :isAdminCancel',
            { status: TTBStatus.CANCELLED, isAdminCancel: true }
          );
        })
      )
      .getRawMany();

    if (datas && datas.length) {
      return _.map(datas, 'ttbf_id');
    }
    return [];
  }

  async checkDuplicateFingerPrintTTBFraud(fingerPrint: string, userId) {
    const datas = await this.ttbFraudRepo
      .createQueryBuilder('ttbf')
      .leftJoinAndSelect('ttbf.trythenbuy', 'ttb')
      .select('ttbf.id')
      .where('ttbf.fingerPrint = :fingerPrint', { fingerPrint: fingerPrint })
      .andWhere('ttbf.userId != :userId', { userId })
      .andWhere(
        new Brackets((qb) => {
          qb.where('ttbf.orderId IS NULL AND ttbf.potentialFraud >= 90').orWhere(
            'ttb.status = :status AND ttb.isAdminCancel = :isAdminCancel',
            { status: TTBStatus.CANCELLED, isAdminCancel: true }
          );
        })
      )
      .getRawMany();

    if (datas && datas.length) {
      return _.map(datas, 'ttbf_id');
    }
    return [];
  }

  async checkDuplicateFingerGPS(lastLatitude, lastLongitude) {
    // get 50 meters radius
    const coeLat = 0.00051;
    const coeLong = 0.00099;
    const newLat = lastLatitude + coeLat;
    const newLat2 = lastLatitude - coeLat;
    const newLong = lastLongitude + coeLong;
    const newLong2 = lastLongitude - coeLong;

    const datas = await this.ttbRepo
      .createQueryBuilder('ttb')
      .where('ttb.status not in (:...status)', { status: statusTTBIgnore })
      .andWhere('ttb.purchaseLatitude < :newLat', { newLat: newLat })
      .andWhere('ttb.purchaseLatitude > :lat', { lat: newLat2 })
      .andWhere('ttb.purchaseLongitude < :newLong', { newLong: newLong })
      .andWhere('ttb.purchaseLongitude > :long', { long: newLong2 })
      .select(['id'])
      .getRawMany();

    if (datas && datas.length) {
      return _.map(datas, 'id');
    }
    return [];
  }

  async getLocationFromIp(ipAddress: string) {
    const url = `${this.config.get('app.ip2LocationEndpoint')}/?key=${this.config.get(
      'app.ip2LocationKey'
    )}&ip=${ipAddress}`;
    try {
      const result = await axios.get(url);
      const { data } = result;
      if (data && data.country_code && data.region_name) {
        return `${data?.city_name} - ${data?.region_name} - ${data?.country_code}`;
      }
      return null;
    } catch (error) {
      console.log(`${error.message}:${url}`);
      return null;
    }
  }

  async getLocationFromGPS(lat: number, long: number) {
    const url = `${this.config.get(
      'app.googleMapApiEndpoint'
    )}/maps/api/geocode/json?latlng=${lat}, ${long}&sensor=true&key=${this.config.get('app.googleMapApiKey')}`;
    try {
      const result = await axios.get(url);
      const { data } = result;
      const address_components = _.result(data, 'results[0].address_components', null);
      if (address_components && address_components.length) {
        let addressArr = [];
        let city = null;
        address_components.map((val: any) => {
          if (val.types && val.types.find((value) => value === 'locality')) {
            city = val?.long_name;
            addressArr.push(val?.long_name);
          }
          if (val.types && val.types.find((value) => value === 'country')) {
            addressArr.push(val?.short_name);
          }
          if (val.types && val.types.find((value) => value === 'administrative_area_level_1')) {
            addressArr.push(val?.long_name);
          }
          if (val.types && !city && val.types.find((value) => value === 'administrative_area_level_2')) {
            addressArr.push(val?.long_name);
          }
        });
        addressArr = _.compact(addressArr);
        return addressArr.join(' - ');
      }
      return null;
    } catch (error) {
      console.log(`${error.message}:${url}`);
      return null;
    }
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import * as _ from 'lodash';
import { Repository } from 'typeorm';
import { TTBEntity, TTBStatus } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

export enum TTBSyncGATStatusProcessorQueueName {
  EBS_SYNC_STATUS_GAT = 'ebs_sync_status_gat',
}
export type TTBSyncGATStatusJob = Job<{ orderId: string }>;
const statusTTB = [TTBStatus.INITIALIZED, TTBStatus.TRIAL_COMING];

@Processor('ttb-sync-gat-status')
export class TTBSyncGATStatusProcessor {
  private readonly logger = new Logger(TTBSyncGATStatusProcessor.name);
  constructor(
    private readonly ttbService: TTBService,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>
  ) {}

  @Process({
    name: TTBSyncGATStatusProcessorQueueName.EBS_SYNC_STATUS_GAT,
    concurrency: 1,
  })
  async process(job: TTBSyncGATStatusJob): Promise<any> {
    const ttb = job.data;
    const ttbStatus = await this.ttbRepo.findOne({ where: { orderId: ttb.orderId }, select: ['status'] });
    if (!ttbStatus) {
      return { success: false, msg: `Not found with TTB Order ${ttb.orderId}` };
    }
    if (ttbStatus && !statusTTB.includes(ttbStatus.status)) {
      return { success: true, msg: `TTB Status ${ttbStatus.status} not in ${statusTTB}` };
    }
    try {
      const statusGat = await this.processSyncOrderGatStatus(ttb);
      return { success: true, statusGat };
    } catch (error) {
      console.log(`ERROR SYNC GAT STATUS: ${ttb?.orderId}: ${error.message}`);
      console.log(error);
      return { success: false, msg: error.message };
    }
  }
  async processSyncOrderGatStatus(ttb: any) {
    const orderId = ttb.orderId;
    const dataEBS = await this.ttbService.getOrderInfoEBSv2(orderId);
    const orderLines = _.result(dataEBS, 'data.orderInfo.orderLines[0].statusCol', null);
    if (orderLines && orderLines.length) {
      const statusGats = [];
      orderLines.map((val: any) => {
        if (val.system === 'GAT') {
          statusGats.push(val?.statusValue);
        }
      });
      const statusGat = _.last(_.compact(statusGats));
      // console.log('statusGat', statusGat);
      this.logger.log(`TTB sync Gat Status EBS statusGat: ${statusGat}`);
      if (statusGat) {
        // ttb.gatStatus = statusGat;
        await this.ttbRepo.update({ orderId: ttb.orderId }, { gatStatus: statusGat });
        return statusGat;
      }
    }
    return '';
  }
}

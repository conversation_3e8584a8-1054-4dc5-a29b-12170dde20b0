import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { Job } from 'bull';
import { messaging } from 'firebase-admin';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { EcomService } from '../ecom/ecom.service';
import { KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { TTBPushEntity } from './entities/ttb-push.entity';
import { TTBEntity, TTBStatus } from './entities/ttb.entity';
import { TTBService } from './ttb.service';

type TTBPushJob = Job<{
  userEmail: string;
  userFCMToken: string;
  orderId: string;
  title: string;
  message: string;
  userNotificationId: string;
  status?: string;
}>;

export enum TTBNotifyProcessorQueueName {
  PUSH = 'push',
}

@Processor('ttb-notify')
export class TTBNotifyProcessor {
  private readonly logger = new Logger(TTBNotifyProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private firebaseMessaging: FirebaseMessagingService,
    @InjectRepository(TTBPushEntity) private readonly ttbPushRepo: Repository<TTBPushEntity>,
    @InjectRepository(TTBEntity) private readonly ttbRepo: Repository<TTBEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private readonly ttbService: TTBService,
    private ecomService: EcomService
  ) {}

  @Process(TTBNotifyProcessorQueueName.PUSH)
  async push(job: TTBPushJob): Promise<any> {
    this.logger.log(`TTB push orderId ${job.data.orderId}`);
    const order = await this.ttbRepo.findOne({ orderId: job.data.orderId });
    const currentOrderStatus = job.data.status || order.status;
    if (job.data.userFCMToken) {
      this.pushNotification(job).catch((e) => e);
    }
    try {
      await this.trackingKlaviyo(order, currentOrderStatus);
    } catch (error) {
      this.logger.error(`ERROR TTB push orderId ${job.data.orderId}`);
      this.logger.error(`ERROR trackingKlaviyo: ${error}`);
    }
  }

  async pushNotification(job: TTBPushJob) {
    try {
      const message: messaging.Message = {
        data: {
          orderId: job.data.orderId,
          deepLink: this.config.get('app.ttbDeepLink'),
          userNotificationId: job.data.userNotificationId,
        },
        token: job.data.userFCMToken,
        notification: {
          title: job.data.title || 'TaylorMade',
          body: job.data.message,
        },
      };
      this.firebaseMessaging.send(message).then((r) => r);
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async trackingKlaviyo(order: TTBEntity, currentOrderStatus: any) {
    const user = await this.userRepo.findOne({ id: order.userId });
    if (!user) {
      return;
    }
    const ecomOrder = await this.ecomService.getOrder(order.orderId, order?.country);
    const mytmOrder = this.ttbService.transformTTBOrder(order);
    switch (currentOrderStatus) {
      case TTBStatus.TRIAL_COMING:
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_SHIP_CONFIRMATION,
          user.email,
          {
            ...ecomOrder,
            shipmentTrackingUrl: mytmOrder.shipmentTrackingUrl,
            shipmentTrackingNumber: mytmOrder.shipmentTrackingNumber,
          },
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.TRIAL_STARTED:
        const orderDelivery = {
          ...ecomOrder,
          shipmentTrackingUrl: mytmOrder.shipmentTrackingUrl,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_DELIVERY_CONFIRMATION,
          user.email,
          orderDelivery,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.TRIAL_HALF_DONE:
        const halfWayOver = {
          ...ecomOrder,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
          chargeDate: mytmOrder.cardWillBeChangedAt,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_HALFWAY_OVER,
          user.email,
          halfWayOver,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.TRIAL_1_DAY_LEFT:
        const trialEndedData = {
          ...ecomOrder,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
          chargeDate: mytmOrder.cardWillBeChangedAt,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_TRIAL_ENDED,
          user.email,
          trialEndedData,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.TRIAL_2_DAYS_LEFT:
        const trialTwoDaysLeft = {
          ...ecomOrder,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
          chargeDate: mytmOrder.cardWillBeChangedAt,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_TWO_DAYS_LEFT,
          user.email,
          trialTwoDaysLeft,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.CHARGED:
        const chargeConfirmation = {
          ...ecomOrder,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
          chargeDate: mytmOrder.cardWillBeChangedAt,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_CHARGE_CONFIRMATION,
          user.email,
          chargeConfirmation,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.PRODUCT_RETURNED:
        const returnData = {
          ...ecomOrder,
          GVCProductReturningTrackingUrl: mytmOrder.GVCProductReturningTrackingUrl,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_RETURN_CONFIRMATION,
          user.email,
          returnData,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.TIME_TO_DECIDE:
        const decisionTimeData = {
          ...ecomOrder,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
          chargeDate: mytmOrder.cardWillBeChangedAt,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_DECISION_TIME,
          user.email,
          decisionTimeData,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.LAST_CHANCE:
        const lastChanceData = {
          ...ecomOrder,
          trialStartDate: mytmOrder.startedAt,
          trialEndDate: mytmOrder.endDate,
          chargeDate: mytmOrder.cardWillBeChangedAt,
        };
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_LAST_CHANCE,
          user.email,
          lastChanceData,
          mytmOrder?.productInfo
        );
        break;
      case TTBStatus.ONE_WEEK_LEFT_TO_FORCED_CHARGE:
        await this.ttbService.trackingEventKlaviyoTTB(
          KlaviyoTrackEvents.TTB_RETURN_REMINDER,
          user.email,
          ecomOrder,
          mytmOrder?.productInfo
        );
        break;
      default:
        break;
    }
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as semver from 'semver';
import { Repository } from 'typeorm';
import { CreateVersionDto } from './dto/create-version.dto';
import { VersionsEntity } from './entities/versions.entity';

@Injectable()
export class VersionService {
  constructor(
    @InjectRepository(VersionsEntity)
    private versionRepository: Repository<VersionsEntity>
  ) {}

  async createVersion(commitInfo: CreateVersionDto) {
    let currentVersion = '1.0.0';
    const oldVersion = await this.versionRepository.findOne({
      order: { createdAt: 'DESC' },
    });
    if (oldVersion) {
      currentVersion = oldVersion?.version;
    }
    const newVersion = semver.inc(currentVersion, 'patch');

    const version = new CreateVersionDto();
    version.version = newVersion;
    version.commitHash = commitInfo.commitHash;
    version.commitMessage = commitInfo.commitMessage;
    version.branch = commitInfo.branch;
    version.author = commitInfo.author;

    await this.versionRepository.save(version);

    return {
      version: newVersion,
      commitInfo,
    };
  }

  async getLatestVersion() {
    return this.versionRepository.findOne({
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async getAllVersion() {
    return this.versionRepository.find({
      order: {
        createdAt: 'DESC',
      },
    });
  }
}

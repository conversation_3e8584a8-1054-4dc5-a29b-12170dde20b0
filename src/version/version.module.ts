import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '../shared/shared.module';
import { VersionsEntity } from './entities/versions.entity';
import { VersionController } from './version.controller';
import { VersionService } from './version.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([VersionsEntity])],
  controllers: [VersionController],
  providers: [VersionService],
})
export class VersionModule {}

import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { CreateTilesWidgetDto } from '../tiles-widget/dto/create-tiles-widget.dto';
import { CreateVersionDto } from './dto/create-version.dto';
import { VersionService } from './version.service';

@Controller('version')
export class VersionController {
  constructor(private readonly versionService: VersionService) {}

  @Get('/list')
  async getListVersion() {
    return this.versionService.getAllVersion();
  }

  @Get('/latest')
  async getLatestVersion() {
    return this.versionService.getLatestVersion();
  }

  @Post('')
  async createVersion(@Body() createVersionDto: CreateVersionDto) {
    return this.versionService.createVersion(createVersionDto);
  }
}

import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('Versions')
export class VersionsEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  version: string;

  @Column()
  commitHash: string;

  @Column()
  commitMessage: string;

  @Column()
  branch: string;

  @Column()
  author: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}

import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '../shared/shared.module';
import { ApiVersionsController } from './api-version.controller';
import { ApiVersionsService } from './api-version.service';
import { ApiVersionsEntity } from './entities/api-versions.entity';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([ApiVersionsEntity])],
  controllers: [ApiVersionsController],
  providers: [ApiVersionsService],
})
export class ApiVersionsModule {}

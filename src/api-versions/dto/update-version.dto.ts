import { IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateVersionDto {
  @IsString()
  @IsIn([
    'HOME_THE_DAILY',
    'HOME_PRODUCT',
    'HOME_CARTNER_CONVOS',
    'SHOP_MARQUEE',
    'SHOP_PROMOTION',
    'SHOP_PRODUCT_CAROUSEL',
    'SHOP_CATEGORIES',
    'SHOP_MAINSTAYS',
    'SHOP_PRODUCT_CATALOG',
    'SHOP_FINAL_TILE',
    'CLUB_JUST_IN',
    'CLUB_TOUR_STORIES',
    'CLUB_COACH_PROFILE',
    'CLUB_PLAYER_PROFILE',
    'REWARD_PERK',
    'REWARD_LOYALTY_ACTIONS',
    'RE<PERSON>RD_TOUR_TRASH',
    'COUNTRY_FEATURES',
    'CONTENT_IMAGE',
    'CONTENT_TEXT',
  ])
  featureKey: string;

  @IsOptional()
  @IsIn(['USA', 'CAN'])
  country?: string;

  @IsOptional()
  @IsString()
  updatedBy: string;
}

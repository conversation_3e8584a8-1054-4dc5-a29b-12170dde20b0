import { Column, CreateDateColumn, DeleteDateColumn, Entity, Index, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('ApiVersions')
@Index(['featureKey', 'country'])
export class ApiVersionsEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column({ type: 'nvarchar', length: 100 })
  featureKey: string;

  @Column({ type: 'nvarchar', length: 50, nullable: true })
  country: string;

  @Column({ type: 'bigint' })
  version: number;

  @Column({ type: 'nvarchar', length: 255, nullable: true })
  updatedBy: string;

  @CreateDateColumn({ type: 'datetime', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'datetime', nullable: true })
  updatedAt: Date;

  @DeleteDateColumn({ type: 'datetime', nullable: true })
  deletedAt: Date;
}

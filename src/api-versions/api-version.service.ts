import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { union } from 'lodash';
import { In, Repository } from 'typeorm';
import { isCanadaCountry } from '../utils/transform';
import { FEATURE_KEY_VERSION, FEATURE_KEY_VERSION_NO_COUNTRY } from './api-version.constants';
import { ApiVersionsEntity } from './entities/api-versions.entity';

export const TypeWidget = {
  PRODUCT_TILES: FEATURE_KEY_VERSION.SHOP_MARQUEE,
  MORE_FOR_YOU: FEATURE_KEY_VERSION.SHOP_PRODUCT_CAROUSEL,
  SUPPORT_CARD: FEATURE_KEY_VERSION.SHOP_MAINSTAYS,
  FINAL_TILE: FEATURE_KEY_VERSION.SHOP_FINAL_TILE,
  PROMOTION: FEATURE_KEY_VERSION.SHOP_PROMOTION,
  THE_DAILY: FEATURE_KEY_VERSION.HOME_THE_DAILY,
  PLAYER_PROFILE: FEATURE_KEY_VERSION.CLUB_PLAYER_PROFILE,
  JUST_IN_CLUBHOUSE: FEATURE_KEY_VERSION.CLUB_JUST_IN,
};

@Injectable()
export class ApiVersionsService {
  constructor(
    @InjectRepository(ApiVersionsEntity)
    private readonly apiVersionRepository: Repository<ApiVersionsEntity>
  ) {}

  async checkVersions(country: string): Promise<{ country: string; features: { key: string; version: number }[] }> {
    const countryCode = isCanadaCountry(country) ? 'CAN' : 'USA';
    let noCountryFeatures = [];
    const dbVersions = await this.apiVersionRepository.find({
      where: { country: countryCode },
    });

    if (!dbVersions.length) {
      throw new NotFoundException(`No versions found for country ${countryCode || 'USA'}`);
    }

    const features = dbVersions.map((dbVersion) => ({
      key: dbVersion.featureKey,
      version: dbVersion.version,
    }));
    if (countryCode === 'CAN') {
      const noCountryVersions = await this.apiVersionRepository.find({
        where: { featureKey: In(FEATURE_KEY_VERSION_NO_COUNTRY) },
      });
      if (noCountryVersions.length) {
        noCountryFeatures = noCountryVersions.map((dbVersion) => ({
          key: dbVersion.featureKey,
          version: dbVersion.version,
        }));
      }
    }

    return {
      country: countryCode,
      features: union(features, noCountryFeatures),
    };
  }

  async updateVersion(
    featureKey: string,
    country: string,
    updatedBy: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    try {
      const countryCode = isCanadaCountry(country) ? 'CAN' : 'USA';
      const result = await this.apiVersionRepository.update(
        { featureKey, country: countryCode, deletedAt: null },
        {
          version: Date.now(),
          updatedAt: new Date(),
          updatedBy,
        }
      );

      if (result.affected === 0) {
        throw new NotFoundException(`Api version: Feature ${featureKey} not found for country ${country || 'USA'}`);
      }
      return { success: true };
    } catch (error) {
      return { success: false, errorMessage: error.message };
    }
  }

  async updateVersionTitleWidget(
    featureKey: string,
    country: string,
    updatedBy: string
  ): Promise<{ success: boolean; errorMessage?: string }> {
    const key = TypeWidget[featureKey];
    if (!key) {
      console.error(`Feature key ${featureKey} not found in TypeWidget`);
      return null;
    }
    return this.updateVersion(key, country, updatedBy);
  }
}

import { Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { ApiVersionsService } from './api-version.service';
import { UpdateVersionDto } from './dto/update-version.dto';

@Controller('api-versions')
export class ApiVersionsController {
  constructor(private readonly apiVersionsService: ApiVersionsService) {}

  @UseGuards(AuthGuard)
  @Get('check')
  async checkVersions(@Req() req: any, @Query('country') country?: string) {
    const countryCode = country || req.user?.userCountry;
    return this.apiVersionsService.checkVersions(countryCode);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('update')
  async updateVersion(@Body() body: UpdateVersionDto, @Req() req: any) {
    return this.apiVersionsService.updateVersion(body.featureKey, body.country, req.user.uid);
  }
}

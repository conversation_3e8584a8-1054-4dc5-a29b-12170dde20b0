import { Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { SwingIndexService } from './si.service';
import { RegisterPlayerDTO } from './si.type';

@Controller('si')
@UseGuards(ClientGuard)
export class SwingIndexController {
  constructor(private siService: SwingIndexService) {}

  @Get('player/swing-potential-index')
  @UseGuards(AuthGuard)
  async getSwingPotentialIndex(@Req() request: BaseRequest): Promise<any> {
    return await this.siService.getSwingPotentialIndex(request.user.email);
  }

  @Get('player/instructor')
  @UseGuards(AuthGuard)
  async getPlayerInstructor(@Req() request: BaseRequest): Promise<any> {
    return await this.siService.getPlayerInstructor(request.user.email);
  }

  @Post('player/register')
  @UseGuards(AuthGuard)
  async playerRegister(@Req() request: BaseRequest, @Body() body: RegisterPlayerDTO): Promise<any> {
    return await this.siService.playerRegister(request.user.email, body.auth0AccessToken);
  }

  @Get('player/roadmap')
  @UseGuards(AuthGuard)
  async getPlayerRoadmap(@Req() request: BaseRequest): Promise<any> {
    return await this.siService.getPlayerRoadmap(request.user.email);
  }

  @Get('player/analysis/:swingSubElementId')
  @UseGuards(AuthGuard)
  async getPlayerAnalysis(@Req() request: BaseRequest, @Param() params): Promise<any> {
    return await this.siService.getPlayerAnalysis(request.user.email, params.swingSubElementId);
  }

  @Get('content/strings')
  @UseGuards(AuthGuard)
  async getContentStrings(@Req() request: BaseRequest): Promise<any> {
    return await this.siService.getContentStrings(request.user.email);
  }

  @Get('content/batch')
  @UseGuards(AuthGuard)
  async getContentBatch(@Req() request: BaseRequest): Promise<any> {
    return await this.siService.getContentBatch(request.user.email);
  }

  @Get('upload/credentials')
  @UseGuards(AuthGuard)
  async getUploadCredentials(@Req() request: BaseRequest): Promise<any> {
    return await this.siService.getUploadCredentials(request.user.email);
  }
}

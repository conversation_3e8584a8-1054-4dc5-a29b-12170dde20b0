import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ConfigService } from 'nestjs-config';
import { DeService } from 'src/content/de.service';

@Injectable()
export class SwingIndexService {
  constructor(private readonly config: ConfigService, private deService: DeService) {
    this.config = config;
  }

  async getSwingPotentialIndex(email: string) {
    return this.deService.getPlayerSwingPotentialIndex(email);
  }

  async getPlayerInstructor(email: string) {
    return this.deService.getPlayerInstructor(email);
  }

  async playerRegister(email: string, auth0AccessToken: string) {
    return this.deService.postPlayerRegister(email, auth0AccessToken);
  }

  async getPlayerRoadmap(email: string) {
    return this.deService.getPlayerRoadmap(email);
  }

  async getPlayerAnalysis(email: string, swingSubElementId: string) {
    return this.deService.getPlayerAnalysis(email, swingSubElementId);
  }

  async getContentStrings(email: string) {
    return this.deService.getContentStrings(email);
  }

  async getContentBatch(email: string) {
    return this.deService.getContentBatch(email);
  }

  async getUploadCredentials(email: string) {
    return this.deService.getUploadCredentials(email);
  }

  async getRecentSwingShotVideos(email: string) {
    try {
      const credentials = await this.deService.getUploadCredentials(email);
      const { data } = await axios.get(`${credentials.endpoint}/api/v1/player/video/recent`, {
        headers: {
          Authorization: `${credentials.token}`,
          'Content-Type': 'application/json',
        },
      });
      if (data?.length > 0) {
        const detailedVideos = [];
        for (const video of data) {
          const { data: videoDetail } = await axios.get(
            `${credentials.endpoint}/api/v1/player/video/${video.playerSwingShotVideoId}`,
            {
              headers: {
                Authorization: `${credentials.token}`,
                'Content-Type': 'application/json',
              },
            }
          );
          detailedVideos.push(videoDetail);
        }
        return detailedVideos;
      }
      return data;
    } catch (e) {
      console.log(e.message);
      return [];
    }
  }
}

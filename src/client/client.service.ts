import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { ClientEntity } from './entities/client.entity';

@Injectable()
export class ClientService {
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(ClientEntity) private readonly clientRepo: Repository<ClientEntity>
  ) {
    this.config = config;
  }

  async getClient(clientId: string) {
    return await this.clientRepo.findOne({ apiKey: clientId });
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { ConfigService } from 'nestjs-config';
import { IsNull, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { CreatePartnerDto } from './dto/create-partner.dto';
import { UpdatePartnerDto } from './dto/update-partner.dto';
import { PartnerEntity } from './entities/partner.entity';

@Injectable()
export class PartnersService {
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(PartnerEntity)
    private readonly partnerEntityRepository: Repository<PartnerEntity>
  ) {}

  create(createPartnerDto: CreatePartnerDto, createdBy: string) {
    const partner = plainToClass(PartnerEntity, {
      ...createPartnerDto,
      id: v4(),
      createdBy,
      updatedBy: createdBy,
    });
    return this.partnerEntityRepository.save(partner);
  }

  async getAll({ ...options }: any) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.partnerEntityRepository.createQueryBuilder('p').where({ deletedAt: IsNull() });
    const [partners, total] = await query
      .orderBy('p.createdAt', 'DESC')
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .getManyAndCount();
    if (total === 0) {
      return {
        total: 0,
        partners: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      partners,
    };
  }

  findAll() {
    return this.partnerEntityRepository.find({ where: { deletedAt: IsNull() } });
  }

  findOne(id: string) {
    return this.partnerEntityRepository.findOne({ where: { id } });
  }

  async update(id: string, updatePartnerDto: UpdatePartnerDto, updatedBy: string) {
    await this.partnerEntityRepository.update({ id }, { ...updatePartnerDto, updatedBy });
    return this.findOne(id);
  }

  async remove(id: string) {
    const partner = await this.partnerEntityRepository.findOne({
      where: { id },
      relations: ['benefits'],
    });
    if (partner && partner.benefits && partner.benefits.length) {
      return { success: false, message: 'Please remove the partner from current Perk(s) first.' };
    }
    if (partner) {
      await this.partnerEntityRepository.softDelete({ id });
      return { success: true, message: 'Partner successfully deleted.' };
    }
    return { success: false, message: 'Partner is not exists.' };
  }
}

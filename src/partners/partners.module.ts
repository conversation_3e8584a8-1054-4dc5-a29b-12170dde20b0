import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '../shared/shared.module';
import { PartnerEntity } from './entities/partner.entity';
import { PartnersController } from './partners.controller';
import { PartnersService } from './partners.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([PartnerEntity])],
  providers: [PartnersService],
  controllers: [PartnersController],
})
export class PartnersModule {}

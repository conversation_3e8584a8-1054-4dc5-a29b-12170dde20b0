import { BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { concat } from 'lodash';
import { EntityManager, Not, Repository, getManager } from 'typeorm';
import * as uuid from 'uuid';
import { MrpService } from 'src/mrp/mrp.service';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { LoggingService } from '../logging/logging.service';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { isCanOrUsaCountry, isCanadaCountry, isUSCountry } from '../utils/transform';
import { AnnexService } from './annex.service';
import { AddPointsLoyaltyDto } from './dto/addPointsLoyalty.dto';
import { CreateLoyaltyActionDto } from './dto/createLoyaltyAction.dto';
import { CreateUserLoyaltyDto } from './dto/createUserLoyalty.dto';
import { UpdateLoyaltyActionDto } from './dto/updateLoyaltyAction.dto';
import { ACTIVITY, FeatureKey, LoyaltyActionEntity } from './entities/loyalty-action.entity';
import { LOYALTY_MESSAGE_ERROR } from './loyalty.constants';

export class LoyaltyService {
  constructor(
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(LoyaltyActionEntity) private readonly loyaltyActionsRepo: Repository<LoyaltyActionEntity>,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    @Inject(forwardRef(() => MrpService)) private mrpService: MrpService,
    @Inject(forwardRef(() => LoggingService)) private loggingService: LoggingService,
    private annexService: AnnexService,
    private readonly apiVersionsService: ApiVersionsService
  ) {}

  async getCountryUser(userId) {
    const user = await this.userRepo.findOne(userId);
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
      });
    }
    return user?.userCountry || null;
  }

  async createUserLoyalty({ userId }: CreateUserLoyaltyDto) {
    try {
      const user = await this.userRepo.findOne(userId);
      if (!isCanOrUsaCountry(user?.userCountry)) {
        return;
      }
      const userCdm = await this.cdmService.getPermission(user?.email);
      if (!user || !userCdm) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
          errorMessage: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
        });
      }
      const userAnnexExist = await this.annexService.getUser(user?.email, user?.userCountry);
      if (userAnnexExist && userAnnexExist.id) {
        if (user.onboardingComplete) {
          this.addPointUser(user, FeatureKey.COMPLETE_QUIZ);
        }
        await this.userRepo.update({ id: user?.id }, { is_user_annex: true });
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.USER_ANNEX_EXISTS,
          errorMessage: LOYALTY_MESSAGE_ERROR.USER_ANNEX_EXISTS,
        });
      }

      const countryUser = user?.userCountry || 'USA';
      const payload = {
        id: user?.email,
        email: user?.email,
        firstName: userCdm?.firstName,
        lastName: userCdm?.lastName,
        birthDate: userCdm?.dob,
        anniversaryDate: new Date().toISOString(),
        source: 'App',
      };
      const userAnnex = await this.annexService.createUser(payload, countryUser);
      if (userAnnex && userAnnex.id) {
        await this.userRepo.update({ id: user?.id }, { is_user_annex: true });
      }
      if (user.onboardingComplete) {
        this.addPointUser(user, FeatureKey.COMPLETE_QUIZ);
      }
      return userAnnex;
    } catch (err) {
      throw new BadRequestException({
        internalErrorCode: err?.errorCode || err?.response?.internalErrorCode,
        errorMessage: err?.errorMessage || err?.response?.errorMessage,
      });
    }
  }

  async getUserLoyalty(userEmail, country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const userAnnex = await this.annexService.getUser(userEmail, userCountry);
      return userAnnex;
    } catch (err) {
      throw new BadRequestException({
        internalErrorCode: err?.errorCode,
        errorMessage: err?.errorMessage,
      });
    }
  }

  async getUserTier(userEmail, country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const userAnnex = await this.annexService.getTiers(userEmail, userCountry);
      return userAnnex;
    } catch (err) {
      throw new BadRequestException({
        internalErrorCode: err?.errorCode,
        errorMessage: err?.errorMessage,
      });
    }
  }

  async getUserPoints(userEmail, country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const userAnnex = await this.annexService.getPoints(userEmail, userCountry);
      return userAnnex;
    } catch (err) {
      throw new BadRequestException({
        internalErrorCode: err?.errorCode,
        errorMessage: err?.errorMessage,
      });
    }
  }

  async getUserActivity(userEmail, country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const userAnnex = await this.annexService.getActivityUser(userEmail, userCountry);
      return userAnnex;
    } catch (err) {
      console.log('err getUserActivity', err);
      return null;
    }
  }

  findAll(country = null) {
    try {
      if (!isCanOrUsaCountry(country)) {
        return;
      }
      const conditionCountry = this.getConditionCountryQuery(country);
      return this.loyaltyActionsRepo.find({
        where: conditionCountry,
        order: { createdAt: 'DESC' },
      });
    } catch (err) {
      console.log('err findALL', err);
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
      });
    }
  }

  async create(createLoyaltyPointDto: CreateLoyaltyActionDto, createdBy: string) {
    const { annexActionId, countries, feature } = createLoyaltyPointDto;
    const actionDetail = await this.validateCreateLoyaltyAction(annexActionId, countries, feature);
    const loyaltyPoint = plainToClass(LoyaltyActionEntity, {
      ...createLoyaltyPointDto,
      actionName: actionDetail?.actionName,
      actionPoints: actionDetail?.actionPoints,
      maxPoints: actionDetail?.maxPoints,
      id: uuid.v4(),
      createdBy,
    });
    const result = await this.loyaltyActionsRepo.save(loyaltyPoint);
    await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.REWARD_LOYALTY_ACTIONS, countries, createdBy);
    return result;
  }

  async validateCreateLoyaltyAction(annexActionId, countries, feature) {
    const loyaltyAction = await this.loyaltyActionsRepo.count({
      where: [
        {
          annexActionId: annexActionId,
          countries,
        },
        {
          feature: feature,
          countries,
        },
      ],
    });
    if (loyaltyAction) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DUPLICATE,
        errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DUPLICATE,
      });
    }

    const actionAnnex = await this.annexService.getAction(annexActionId, countries);
    if (!actionAnnex || !actionAnnex.actionId) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
      });
    }
    return actionAnnex;
  }

  async validateUpdateLoyaltyAction(id, updateLoyaltyActionDto: UpdateLoyaltyActionDto) {
    const loyaltyAction = await this.loyaltyActionsRepo.findOne({ id });
    if (!loyaltyAction) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
      });
    }

    if (updateLoyaltyActionDto.feature && updateLoyaltyActionDto.feature !== loyaltyAction.feature) {
      const { feature } = updateLoyaltyActionDto;
      const checkDuplicate = await this.loyaltyActionsRepo.count({
        where: {
          feature,
          countries: updateLoyaltyActionDto.countries,
          id: Not(id),
        },
      });
      if (checkDuplicate) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DUPLICATE,
          errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DUPLICATE,
        });
      }
    }

    if (updateLoyaltyActionDto.annexActionId && updateLoyaltyActionDto.annexActionId !== loyaltyAction.annexActionId) {
      const { annexActionId } = updateLoyaltyActionDto;
      const checkDuplicate = await this.loyaltyActionsRepo.count({
        where: {
          annexActionId,
          countries: updateLoyaltyActionDto.countries,
          id: Not(id),
        },
      });
      if (checkDuplicate) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DUPLICATE,
          errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DUPLICATE,
        });
      }

      const actionAnnex = await this.annexService.getAction(annexActionId, loyaltyAction?.countries);
      if (!actionAnnex || !actionAnnex.actionId) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
          errorMessage: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
        });
      }
    }

    return loyaltyAction;
  }

  async update(id: string, updateLoyaltyActionDto: UpdateLoyaltyActionDto, updatedBy) {
    await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const loyaltyAction = await this.validateUpdateLoyaltyAction(id, updateLoyaltyActionDto);
      const payloadUpdate = {};
      if (updateLoyaltyActionDto.annexActionId) {
        try {
          const actionAnnex = await this.annexService.getAction(
            updateLoyaltyActionDto.annexActionId,
            loyaltyAction?.countries
          );
          payloadUpdate['actionName'] = actionAnnex.actionName;
          payloadUpdate['actionPoints'] = actionAnnex.actionPoints;
          payloadUpdate['maxPoints'] = actionAnnex.maxPoints;
        } catch (e) {
          console.log(e);
        }
      }

      await transactionalEntityManager.update(
        LoyaltyActionEntity,
        { id: loyaltyAction.id },
        { ...updateLoyaltyActionDto, ...payloadUpdate }
      );
      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: id,
            updatedBy: updatedBy,
          },
          manager: transactionalEntityManager,
        },
        LoyaltyActionEntity.name,
        loyaltyAction,
        updateLoyaltyActionDto
      );
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.REWARD_LOYALTY_ACTIONS,
        loyaltyAction.countries,
        updatedBy
      );

      return true;
    });
    return this.loyaltyActionsRepo.findOne({ id });
  }

  private getConditionCountryQuery(countryCode: string) {
    let conditionCountry = ' LoyaltyActionEntity.deletedAt IS NULL ';
    if (countryCode) {
      if (isUSCountry(countryCode)) {
        conditionCountry += ` AND (LoyaltyActionEntity.countries LIKE '%${countryCode}%' OR LoyaltyActionEntity.countries IS NULL) `;
      } else {
        conditionCountry += ` AND LoyaltyActionEntity.countries LIKE '%${countryCode}%' `;
      }
    } else {
      conditionCountry += ` AND (LoyaltyActionEntity.countries LIKE '%US%' OR LoyaltyActionEntity.countries IS NULL) `;
    }
    return conditionCountry;
  }

  async remove(id: string, updatedBy: string) {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const loyaltyAction = await this.loyaltyActionsRepo.findOne({ id });
      if (!loyaltyAction) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
          errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
        });
      }
      await transactionalEntityManager.softDelete(LoyaltyActionEntity, { id });
      await CommonSubscriber.activityLogCommandDelete(
        {
          entity: {
            uuid: id,
            deletedBy: updatedBy,
          },
          manager: transactionalEntityManager,
          databaseEntity: loyaltyAction,
        },
        LoyaltyActionEntity.name
      );
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.REWARD_LOYALTY_ACTIONS,
        loyaltyAction.countries,
        updatedBy
      );

      return { success: true, message: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_DELETED };
    });

    return res;
  }

  async addPointAdmin(addPointsLoyaltyDto: AddPointsLoyaltyDto) {
    const { id, actionId } = addPointsLoyaltyDto;
    try {
      const user = await this.userRepo.findOne({
        where: {
          email: id,
        },
      });
      if (!user) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
          errorMessage: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
        });
      }
      const { userCountry } = user;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const actionAnnex = await this.annexService.getAction(actionId, userCountry);
      if (!actionAnnex) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
          errorMessage: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
        });
      }
      return this.annexService.addPointForUser(addPointsLoyaltyDto, userCountry);
    } catch (err) {
      await this.loggingService.save({
        event: 'ERR_LOYALTY_ADD_POINT_ADMIN',
        data: {
          error: err,
          payload: addPointsLoyaltyDto,
        },
      });
      throw new BadRequestException({
        internalErrorCode: err?.errorCode || err?.response?.internalErrorCode,
        errorMessage: err?.errorMessage || err?.response?.errorMessage,
      });
    }
  }

  async addPointUser(user, actionKey) {
    try {
      const { email, userCountry } = user;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const annexActionId = await this.validateAddPoint(email, userCountry, actionKey);
      const payload: AddPointsLoyaltyDto = {
        id: email,
        actionId: String(annexActionId),
        activity: ACTIVITY.CREDIT,
      };
      return this.annexService.addPointForUser(payload, userCountry);
    } catch (err) {
      await this.loggingService.save({
        event: 'ERR_LOYALTY_ADD_POINT',
        data: {
          error: err,
          user,
          actionKey,
        },
      });
      throw new BadRequestException({
        internalErrorCode: err?.errorCode || err?.response?.internalErrorCode,
        errorMessage: err?.errorMessage || err?.response?.errorMessage,
      });
    }
  }

  async validateAddPoint(email, userCountry, actionKey) {
    const country = isCanadaCountry(userCountry) ? 'CAN' : 'USA';
    const userAnnex = await this.annexService.getUser(email, userCountry);
    if (!userAnnex || userAnnex.errorCode) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.USER_ANNEX_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.USER_ANNEX_NOT_FOUND,
      });
    }
    const actionAnnexDB = await this.loyaltyActionsRepo.findOne({
      where: {
        feature: actionKey,
        countries: country,
      },
    });
    if (!actionAnnexDB) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.LOYALTY_ACTION_NOT_FOUND,
      });
    }
    const { annexActionId } = actionAnnexDB;
    const actionAnnex = await this.annexService.getAction(annexActionId, userCountry);
    if (!actionAnnex) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.ANNEX_ACTION_NOT_FOUND,
      });
    }
    return annexActionId;
  }

  async addPointUsga(mrbId) {
    const user = await this.userRepo.findOne({
      where: {
        mrpUID: mrbId,
      },
    });
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
        errorMessage: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
      });
    }
    if (!isCanOrUsaCountry(user?.userCountry)) {
      return;
    }

    return this.addPointUser(user, FeatureKey.SYNC_USGA);
  }

  async addPointUsgaForOldUser(user) {
    try {
      const usgaGolfer = await this.mrpService.getGHINGolferProfile(user.mrpUID);
      if (!usgaGolfer) {
        throw new BadRequestException({
          internalErrorCode: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
          errorMessage: LOYALTY_MESSAGE_ERROR.USER_NOT_FOUND,
        });
      }

      return this.addPointUser(user, FeatureKey.SYNC_USGA);
    } catch (error) {
      console.log(`ERROR: addPointUsgaForOldUser: ${error.message}`);
      console.log(error.message);
    }
  }

  async getActionPlay(country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      const actionAnnexDB = await this.loyaltyActionsRepo.findOne({
        where: {
          feature: FeatureKey.TRACK_ROUND,
          countries: userCountry,
        },
      });
      const { annexActionId } = actionAnnexDB;
      if (annexActionId) {
        return await this.annexService.getAction(annexActionId, userCountry);
      }

      return null;
    } catch (err) {
      console.log('err getActionPlay', err);
      return null;
    }
  }
  async getTierListAll(country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      return await this.annexService.getTierListAll(userCountry);

      return null;
    } catch (err) {
      console.log('err getActionPlay', err);
      return null;
    }
  }
  async getActionsAll(country, page) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      return await this.annexService.getActions(userCountry, page);
    } catch (err) {
      console.log('err getActionsAll', err);
      return null;
    }
  }
  async getUserActionsAll(email, country) {
    try {
      const userCountry = country || null;
      if (!isCanOrUsaCountry(userCountry)) {
        return;
      }
      let result = [];
      const data = await this.annexService.getUserActions(email, userCountry, 1);
      const pages = data?.pages;
      if (pages && Number(pages) > 1 && data?.allActionDetails.length) {
        result = concat(result, data.allActionDetails);
        for (let i = 1; i < pages; i++) {
          const page = i + 1;
          const rs = await this.annexService.getUserActions(email, userCountry, page);
          if (rs && rs?.allActionDetails.length) {
            result = concat(result, rs?.allActionDetails);
          }
        }
        result = result.filter((val) => Number(val.actionPerformStatus) === 1);
      } else if (pages && Number(pages) === 1 && data?.allActionDetails.length) {
        result = concat(result, data.allActionDetails);
        result = result.filter((val) => Number(val.actionPerformStatus) === 1);
      }
      if (result.length) {
        for (let j = 0; j < result.length; j++) {
          const loyalty = await this.loyaltyActionsRepo.findOne({
            where: {
              annexActionId: result[j].actionId,
            },
          });
          if (loyalty && loyalty.feature) {
            result[j]['feature'] = loyalty.feature;
          }
        }
      }
      return result;
    } catch (err) {
      console.log('err getActionsAll', err);
      return null;
    }
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { AddPointsLoyaltyDto } from './dto/addPointsLoyalty.dto';
import { CreateLoyaltyActionDto } from './dto/createLoyaltyAction.dto';
import { CreateUserLoyaltyDto } from './dto/createUserLoyalty.dto';
import { UpdateLoyaltyActionDto } from './dto/updateLoyaltyAction.dto';
import { FeatureKey } from './entities/loyalty-action.entity';
import { LoyaltyService } from './loyalty.service';

@Controller('loyalty')
export class LoyaltyController {
  constructor(private loyaltyService: LoyaltyService) {}

  @UseGuards(AuthGuard)
  @Get('actions')
  async getAll(@Request() req: any, @Query('country') country?: string) {
    return this.loyaltyService.findAll(country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('action')
  create(@Body() createLoyaltyActionDto: CreateLoyaltyActionDto, @Request() req: BaseRequest) {
    return this.loyaltyService.create(createLoyaltyActionDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Put('action/:id')
  update(@Param('id') id: string, @Body() updateLoyaltyActionDto: UpdateLoyaltyActionDto, @Request() req: BaseRequest) {
    return this.loyaltyService.update(id, updateLoyaltyActionDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('action/:id')
  remove(@Param('id') id: string, @Request() req: BaseRequest) {
    return this.loyaltyService.remove(id, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('create/user')
  async adminCreateUserLoyalty(@Body() createUserLoyaltyDto: CreateUserLoyaltyDto) {
    return this.loyaltyService.createUserLoyalty(createUserLoyaltyDto);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('add-point')
  async addPointAdmin(@Body() addPointsLoyaltyDto: AddPointsLoyaltyDto) {
    return this.loyaltyService.addPointAdmin(addPointsLoyaltyDto);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user')
  async getUserLoyalty(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return this.loyaltyService.getUserLoyalty(req.user?.email, userCountry);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('user')
  async createUserLoyalty(@Request() req: BaseRequest) {
    return this.loyaltyService.createUserLoyalty({ userId: req?.user?.uid });
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user/tier')
  async getUserTier(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return this.loyaltyService.getUserTier(req.user?.email, userCountry);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user/points')
  async getUserPoints(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return this.loyaltyService.getUserPoints(req.user?.email, userCountry);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user/activity')
  async getUserActivity(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return this.loyaltyService.getUserActivity(req.user?.email, userCountry);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('point/notification')
  addPointNotification(@Request() req: BaseRequest) {
    return this.loyaltyService.addPointUser(req.user, FeatureKey.ENABLE_NOTIFICATIONS);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('action/play')
  async getActionPlay(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return this.loyaltyService.getActionPlay(userCountry);
  }

  @AccessedClients(CLIENTS.MRP)
  @Get('point/usga/:id')
  @UseGuards(ClientGuard)
  async addPointUsga(@Param('id') mrpId: string) {
    return await this.loyaltyService.addPointUsga(mrpId);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('tierlist/all')
  async getTierListAll(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return await this.loyaltyService.getTierListAll(userCountry);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('actions/all')
  async getActions(@Request() req: BaseRequest, @Query('page') page: number) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return await this.loyaltyService.getActionsAll(userCountry, page);
  }
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user/actions')
  async getUserActions(@Request() req: BaseRequest) {
    const userCountry = await this.loyaltyService.getCountryUser(req.user?.uid);
    return await this.loyaltyService.getUserActionsAll(req.user?.email, userCountry);
  }
}

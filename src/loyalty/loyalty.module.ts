import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { AnnexService } from './annex.service';
import { LoyaltyActionEntity } from './entities/loyalty-action.entity';
import { LoyaltyController } from './loyalty.controller';
import { LoyaltyService } from './loyalty.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([LoyaltyActionEntity])],
  controllers: [LoyaltyController],
  providers: [LoyaltyService, AnnexService],
})
export class LoyaltyModule {}

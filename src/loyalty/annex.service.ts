import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import CryptoJS from 'crypto-js';
import jwt from 'jsonwebtoken';
import { ConfigService } from 'nestjs-config';
import { LoggingService } from '../logging/logging.service';
import { isCanadaCountry } from '../utils/transform';

const ANNEX_SERVICE = {
  CREATE_USER_API: '/users',
  GET_USER_API: '/users/:id',
  GET_ACTION_API: '/actions/:id',
  GET_ACTION_API_BY_USER: '/actions',
  GET_ACTIONS_API: '/actions/all',
  GET_USER_POINTS_API: '/points/:id',
  PATCH_USER_API: '/users/:id',
  USER_ACTIVITY_API: '/users/:id/activity',
  USER_TIERS_API: '/users/:id/tiers',
  USER_CALC_POINT_API: '/users/:id/tiers/calculate',
  USER_REWARD_API: '/users/:id/reward',
  USER_USED_REWARD_API: '/users/:id/usedreward',
  ADD_POINTS_API: '/points',
  TIER_LIST_ALL: '/tierlist/all',
};

const b64EncodeUnicode = (str) => {
  const strEncode = encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (t, e) => String.fromCharCode(Number('0x' + e)));
  return Buffer.from(strEncode, 'binary').toString('base64');
};

@Injectable()
export class AnnexService {
  requestService;
  constructor(private readonly config: ConfigService, private readonly loggingService: LoggingService) {
    this.requestService = axios.create({
      baseURL: this.config.get('app.annexEndpoint'),
    });
  }

  async initHeaderRequest(userCountry = 'USA', payload) {
    try {
      const secret = isCanadaCountry(userCountry)
        ? this.config.get('app.annexCASecret')
        : this.config.get('app.annexUSSecret');
      const sideId = isCanadaCountry(userCountry)
        ? this.config.get('app.annexCAClientId')
        : this.config.get('app.annexUSClientId');
      const timeExpire = Math.floor(Date.now() / 1000) + 5 * 60; // expire in 5 minutes
      const dataRequest = b64EncodeUnicode(JSON.stringify(payload));
      const hmac = CryptoJS.HmacSHA256(dataRequest, secret).toString(CryptoJS.enc.Base64);

      const payloadHash = {
        sub: sideId,
        site_id: sideId,
        exp: timeExpire,
        hmac: hmac,
      };

      const token = jwt.sign(payloadHash, secret);

      return {
        headers: {
          Authorization: `Bearer ${token}`,
          'X-AnnexCloud-Site': `${sideId}`,
          'Content-Type': 'application/json',
        },
      };
    } catch (err) {
      console.log('err header', err);
      await this.loggingService.save({
        event: 'ERR_ANNEX_HEADER',
        data: {
          error: err,
        },
      });
      throw new BadRequestException(err);
    }
  }

  async createUser(payload, countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, payload);
      const result = await this.requestService.post(ANNEX_SERVICE.CREATE_USER_API, payload, headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_CREATE_USER',
        data: {
          error: error,
          email: payload?.email,
        },
      });
      return error?.response?.data;
    }
  }

  async addPointForUser(payload, countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, payload);
      const result = await this.requestService.post(ANNEX_SERVICE.ADD_POINTS_API, payload, headers);
      const { data } = result;
      await this.loggingService.save({
        event: 'SUCCESS_ANNEX_ADD_POINTS_USER',
        data: {
          response: data,
          payload: payload,
        },
      });
      return data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_ADD_POINTS_USER',
        data: {
          error: error,
          payload: payload,
        },
      });
      return error?.response?.data;
    }
  }

  async getUser(userId, countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, userId);
      const result = await this.requestService.get(ANNEX_SERVICE.GET_USER_API.replace(':id', userId), headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_USER',
        data: {
          error: error,
          email: userId,
        },
      });
      console.log(error?.response?.data);
      return error?.response?.data;
    }
  }

  async getActivityUser(userId, countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, userId);
      const result = await this.requestService.get(ANNEX_SERVICE.USER_ACTIVITY_API.replace(':id', userId), headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_ACTIVITY_USER',
        data: {
          error: error,
          email: userId,
        },
      });
      return error?.response?.data;
    }
  }

  async getPoints(userId, countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, userId);
      const result = await this.requestService.get(ANNEX_SERVICE.GET_USER_POINTS_API.replace(':id', userId), headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_POINT_USER',
        data: {
          error: error,
          email: userId,
        },
      });
      return error?.response?.data;
    }
  }

  async getTiers(userId, countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, userId);
      const result = await this.requestService.get(ANNEX_SERVICE.USER_TIERS_API.replace(':id', userId), headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_TIER_USER',
        data: {
          error: error,
          email: userId,
        },
      });
      return error?.response?.data;
    }
  }

  async getAction(actionId, countryUser) {
    try {
      console.log('actionId, countryUser', actionId, countryUser);
      const headers = await this.initHeaderRequest(countryUser, String(actionId));
      const result = await this.requestService.get(ANNEX_SERVICE.GET_ACTION_API.replace(':id', actionId), headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_getAction',
        data: {
          error: error,
        },
      });
      return error?.response?.data;
    }
  }

  async getActions(countryUser, page = 1) {
    try {
      const headers = await this.initHeaderRequest(countryUser, 'all');
      const result = await this.requestService.get(ANNEX_SERVICE.GET_ACTIONS_API + `?page=${page}`, headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_getActions',
        data: {
          error: error,
        },
      });
      throw new BadRequestException({
        internalErrorCode: error?.response?.data?.errorCode,
        errorMessage: error?.response?.data?.errorMessage,
      });
    }
  }
  async getUserActions(email, countryUser, page = 1) {
    try {
      const payload = { id: email };
      const headers = await this.initHeaderRequest(countryUser, payload);
      const result = await this.requestService.post(
        ANNEX_SERVICE.GET_ACTION_API_BY_USER + `?page=${page}`,
        payload,
        headers
      );
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_getUserActions',
        data: {
          error: error,
        },
      });
      throw new BadRequestException({
        internalErrorCode: error?.response?.data?.errorCode,
        errorMessage: error?.response?.data?.errorMessage,
      });
    }
  }
  async getTierListAll(countryUser) {
    try {
      const headers = await this.initHeaderRequest(countryUser, 'all');
      const result = await this.requestService.get(ANNEX_SERVICE.TIER_LIST_ALL, headers);
      return result?.data;
    } catch (error) {
      await this.loggingService.save({
        event: 'ERR_ANNEX_GET_getTierListAll',
        data: {
          error: error,
        },
      });
      throw new BadRequestException({
        internalErrorCode: error?.response?.data?.errorCode,
        errorMessage: error?.response?.data?.errorMessage,
      });
    }
  }
}

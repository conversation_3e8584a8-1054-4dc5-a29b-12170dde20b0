import { Column, CreateDateColumn, DeleteDateColumn, <PERSON>tity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('LoyaltyActions')
export class LoyaltyActionEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  actionName: string;

  @Column()
  ctaLink: string;

  @Column()
  actionPoints: number;

  @Column()
  maxPoints: number;

  @Column()
  feature: FeatureKey;

  @Column()
  annexActionId: number;

  @Column()
  countries: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;
}

export enum FeatureKey {
  ENABLE_NOTIFICATIONS = 'ENABLE_NOTIFICATIONS',
  COMPLETE_WITB = 'COMPLETE_WITB',
  SYNC_USGA = 'SYNC_USGA',
  TRACK_ROUND = 'TRACK_ROUND',
  COMPLETE_QUIZ = 'COMPLETE_QUIZ',
  SIGNUP_NEWSLETTER = 'SIGNUP_NEWSLETTER',
}

export enum ACTIVITY {
  CREDIT = 'CREDIT',
  DEBIT = 'DEBIT',
}

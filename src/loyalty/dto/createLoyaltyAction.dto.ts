import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>ber, Is<PERSON><PERSON>al, IsString, <PERSON>Leng<PERSON> } from 'class-validator';
import { FeatureKey } from '../entities/loyalty-action.entity';

export class CreateLoyaltyActionDto {
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  ctaLink: string;

  @IsNotEmpty()
  @IsNumber()
  annexActionId: number;

  @IsNotEmpty()
  @IsString()
  countries: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  feature: FeatureKey;
}

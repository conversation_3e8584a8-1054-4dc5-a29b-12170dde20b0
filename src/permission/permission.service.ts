import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isEmpty } from 'lodash';
import { Repository } from 'typeorm';
import { ERROR_CODES } from 'src/utils/errors';
import { PermissionDto } from './dto/update-permission.dto';
import { PermissionEntity } from './entities/permission.entity';

@Injectable()
export class PermissionService {
  private readonly logger = new Logger(PermissionService.name);
  constructor(@InjectRepository(PermissionEntity) private readonly permissionRepo: Repository<PermissionEntity>) {}

  async find(planId: number) {
    return await this.permissionRepo.findOne({ planId });
  }
  async findAll() {
    return await this.permissionRepo.find({ order: { planId: 'ASC' } });
  }
  async update(updatePermissionDto: PermissionDto, planId: number) {
    try {
      const permission = await this.permissionRepo.findOne({ planId });

      if (updatePermissionDto.hasOwnProperty('planId')) {
        delete updatePermissionDto['planId'];
      }

      await this.permissionRepo.update({ id: permission.id }, updatePermissionDto);
      return await this.permissionRepo.findOne({ planId });
    } catch (error) {
      this.logger.error(`UPDATE PERMISSION ERROR: ${error}`);
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.INVALID_PAYLOAD,
        errorMessage: error.message,
      });
    }
  }
  async overrideMyPermission(myTMPermission: any, myTMSubscriptionLevel?: any) {
    if (!myTMSubscriptionLevel || myTMSubscriptionLevel == 0) {
      myTMSubscriptionLevel = 0;
    }
    const permission = await this.permissionRepo.findOne({ planId: myTMSubscriptionLevel });

    if (!permission) {
      return myTMPermission;
    }

    myTMPermission['canCommunity'] = permission.community;
    myTMPermission['canMFE'] = permission.mfe;
    myTMPermission['canMRP'] = permission.mrp;
    myTMPermission['canExclProductDrops'] = permission.productDrops;
    myTMPermission['canPerfInsights'] = permission.insights;
    myTMPermission['canTryThenBuy'] = permission.tryThenBuy;
    myTMPermission['canCalculatedHandicap'] = permission.calculateHandicap;
    myTMPermission['canWinTourTrash'] = permission.winTourTrash;
    myTMPermission['canFreeShipping'] = permission.freeShipping;
    myTMPermission['canFree2DaysShipping'] = permission.freeTwoDaysShipping;
    myTMPermission['canVirtualCoaching'] = permission.virtualCoaching;
    myTMPermission['canVideoInstruction'] = permission.videoInstruction;
    myTMPermission['canPlayAdvancedRound'] = permission.playAdvancedRound;
    return myTMPermission;
  }
}

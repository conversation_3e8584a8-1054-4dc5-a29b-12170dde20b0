import { BadRequestException, Body, Controller, Get, Param, Put, UseGuards } from '@nestjs/common';
import { isEmpty, isNumber } from 'lodash';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { ERROR_CODES } from 'src/utils/errors';
import { PermissionDto } from './dto/update-permission.dto';
import { PermissionService } from './permission.service';

@Controller('permission')
export class PermissionController {
  constructor(private readonly permissionService: PermissionService) {}

  @Get()
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.CUSTOMER_SERVICE)
  @UseGuards(AuthGuard)
  async findAll() {
    return await this.permissionService.findAll();
  }

  @Get('plan/:planId')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.CUSTOMER_SERVICE)
  @UseGuards(AuthGuard)
  async findByPlanId(@Param() { planId }) {
    if (isEmpty(planId) || !isNumber(+planId)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.INVALID_PAYLOAD,
        errorMessage: 'Invalid PlanId!',
      });
    }
    return await this.permissionService.find(+planId);
  }

  @Put('plan/:planId')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN, Role.CUSTOMER_SERVICE)
  @UseGuards(AuthGuard)
  async update(@Body() updatePermissionDto: PermissionDto, @Param() { planId }) {
    if (isEmpty(updatePermissionDto) || isEmpty(planId) || !isNumber(+planId)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.INVALID_PAYLOAD,
        errorMessage: 'Invalid Payload!',
      });
    }
    return await this.permissionService.update(updatePermissionDto, +planId);
  }
}

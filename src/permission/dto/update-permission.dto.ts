import { IsBoolean, IsOptional } from 'class-validator';

export class PermissionDto {
  @IsOptional()
  @IsBoolean()
  community: boolean;
  @IsOptional()
  @IsBoolean()
  mfe: boolean;
  @IsOptional()
  @IsBoolean()
  mrp: boolean;
  @IsOptional()
  @IsBoolean()
  productDrops: boolean;
  @IsOptional()
  @IsBoolean()
  insights: boolean;
  @IsOptional()
  @IsBoolean()
  tryThenBuy: boolean;
  @IsOptional()
  @IsBoolean()
  calculateHandicap: boolean;
  @IsOptional()
  @IsBoolean()
  winTourTrash: boolean;
  @IsOptional()
  @IsBoolean()
  freeShipping: boolean;
  @IsOptional()
  @IsBoolean()
  freeTwoDaysShipping: boolean;
  @IsOptional()
  @IsBoolean()
  virtualCoaching: boolean;
  @IsOptional()
  @IsBoolean()
  videoInstruction: boolean;
}

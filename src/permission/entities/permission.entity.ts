import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('Permission')
export class PermissionEntity {
  @PrimaryColumn('uuid')
  id: string;
  @Column()
  planId: number;
  @Column()
  community: boolean;
  @Column()
  mfe: boolean;
  @Column()
  mrp: boolean;
  @Column()
  productDrops: boolean;
  @Column()
  insights: boolean;
  @Column()
  tryThenBuy: boolean;
  @Column()
  calculateHandicap: boolean;
  @Column()
  winTourTrash: boolean;
  @Column()
  freeShipping: boolean;
  @Column()
  freeTwoDaysShipping: boolean;
  @Column()
  virtualCoaching: boolean;
  @Column()
  videoInstruction: boolean;
  @Column()
  playAdvancedRound: boolean;
}

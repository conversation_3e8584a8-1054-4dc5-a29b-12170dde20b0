import { isCanadaCountry } from 'src/utils/transform';
import faqCA from './faq.ca.json';
import faqUS from './faq.us.json';
import tiersBirdieCA from './tiers.birdie.ca.json';
import tiersBirdieUS from './tiers.birdie.us.json';
import tiersEagleCA from './tiers.eagle.ca.json';
import tiersEagleUS from './tiers.eagle.us.json';
import tiersParCA from './tiers.par.ca.json';
import tiersParUS from './tiers.par.us.json';

const TIER_LEVEL = {
  PAR: 'PAR',
  EAGLE: 'EAGLE',
  BIRDIE: 'BIRDIE',
};
const TIER_DETAIL_US = {
  PAR: tiersParUS,
  BIRDIE: tiersBirdieUS,
  EAGLE: tiersEagleUS,
};
const TIER_DETAIL_CA = {
  PAR: tiersParCA,
  BIRDIE: tiersBirdieCA,
  EAGLE: tiersEagleCA,
};
export const getTierFile = (country, level) => {
  level = getLevel(level);
  if (isCanadaCountry(country)) {
    return TIER_DETAIL_CA[level];
  }
  return TIER_DETAIL_US[level];
};
export const getLevel = (level) => {
  if (!level) {
    return TIER_LEVEL.PAR;
  }
  if (level.trim().toUpperCase() === TIER_LEVEL.PAR) {
    return TIER_LEVEL.PAR;
  }
  if (level.trim().toUpperCase() === TIER_LEVEL.BIRDIE) {
    return TIER_LEVEL.BIRDIE;
  }
  if (level.trim().toUpperCase() === TIER_LEVEL.EAGLE) {
    return TIER_LEVEL.EAGLE;
  }
};
export const getFaqFile = (country) => {
  if (isCanadaCountry(country)) {
    return faqCA;
  }
  return faqUS;
};

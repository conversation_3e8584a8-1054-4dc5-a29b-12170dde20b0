export const PREFETCH_CONTENTS_QUEUE_NAME = 'prefetch-contents';
export const PREFETCH_CONTENT_USER_QUEUE_NAME = 'prefetch-content-user';
export enum ContentProcessorQueueName {
  PREFETCH = 'prefetch',
  PREFETCH_USER = 'prefetch-user',
}
export const HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES = ['DRILL_OF_THE_WEEK'];
export enum DE_EVENT {
  FITTING_COMPLETED = 'fitting-completed',
  ROUND_STARTED = 'round-started',
  SIGN_UP = 'sign-up',
  USER_UPDATE = 'user-update',
}

export enum DE_MODULE {
  HomeExplorer = 'HomeExplorer',
  HomeTile = 'HomeTile',
  HomeTiles = 'HomeTiles',
  HomeStats = 'HomeStats',
  Drills = 'Drills',
  Insights = 'Insights',
}

export const VIDEO_CMS_IDS = [
  235614, 202687, 235628, 235638, 229143, 202656, 235622, 235644, 235615, 229119, 229126, 235648, 202729, 202718,
  235631, 197184, 197155, 195412, 197166, 197159, 197185,
];

export const PREFETCH_HOME_EXPLORER_COMMON_PREFIX_CACHE_KEY = 'CONTENTS:HOME_EXPLORER:FIXED_WIDGETS';
export const PREFETCH_HOME_EXPLORER_PREFIX_CACHE_KEY = 'CONTENTS:HOME_EXPLORER';
export const SORTED_HOME_EXPLORER_WIDGETS_CACHE_KEY = 'CONTENTS:SORTED_HOME_EXPLORER_WIDGETS';
export const PREFETCH_HOME_EXPLORER_WIDGETS_STATUS_CACHE_KEY = 'CONTENTS:PREFETCH_HOME_EXPLORER_WIDGETS_STATUS';
export const RANDOM_DRILL_VIDEOS_CACHE_PREFIX_KEY = 'CONTENTS:RANDOM_DRILL_VIDEOS';
export const LIST_TOUR_STORY = 'LIST_TOUR_STORY';

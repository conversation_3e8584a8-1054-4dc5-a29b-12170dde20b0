import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { ContentCacheService } from './content.cache.service';
import {
  ContentProcessorQueueName,
  DE_MODULE,
  HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES,
  PREFETCH_CONTENT_USER_QUEUE_NAME,
} from './content.constants';
import { ContentService } from './content.service';
import { DeService } from './de.service';

type PrefetchUserContentsJob = Job<{ userId: string }>;
const isStaging = process.env.STAGE === 'staging';

@Processor(PREFETCH_CONTENT_USER_QUEUE_NAME)
export class ContentUserProcessor {
  private readonly logger = new Logger(ContentUserProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly deService: DeService,
    private readonly cdmService: CdmService,
    private readonly contentService: ContentService,
    private readonly contentCacheService: ContentCacheService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}

  @Process({
    name: ContentProcessorQueueName.PREFETCH_USER,
    concurrency: 1,
  })
  async prefetchContentUser(job: PrefetchUserContentsJob): Promise<any> {
    this.logger.log(`Prefetch content for user Start`);
    const userId = job.data.userId;
    if (isStaging) {
      return true;
    }
    let homeWidgets = await this.deService.getHomeWidgets(DE_MODULE.HomeExplorer);
    homeWidgets = homeWidgets.filter((widget) => HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES.includes(widget.type));
    try {
      await new Promise((resolve) =>
        setTimeout(resolve, this.config.get('app.prefetchContentProcessorTimeout') || 2000)
      );
      this.logger.log(`Fetching contents for: ${userId}`);
      const user = await this.userRepo.findOne({ cdmUID: userId });
      const subscriptionLevel = await this.cdmService.getSubscriptionLevel(user.email);
      const isServicePreferenceArccos = await this.cdmService.isArccosEmailAndIsServicePreferenceArccos(
        user.email,
        user?.regionId || this.config.get('app.defaultRegionId'),
        true
      );
      const res = await this.deService.requestDecisionEngineContents(
        userId,
        DE_MODULE.HomeExplorer,
        15,
        homeWidgets,
        subscriptionLevel,
        user.isArccosEmail && isServicePreferenceArccos ? 'ARCCOS' : 'MyTMOC'
      );
      await this.contentCacheService.cacheHomeExplorerContents(userId, res.data);
    } catch (e) {
      this.logger.log(e);
      this.logger.log(`Prefetch contents failed for user id: ${userId}`);
    }
    this.logger.log(`Prefetch content for user END`);
    return {
      success: true,
    };
  }
}

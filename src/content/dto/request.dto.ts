import { IsNotEmpty, <PERSON>Optional, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class ContentDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  contentId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  contentFormat: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  contentTitle: string;
}

export class DismissContentDto {
  @IsString()
  @IsOptional()
  @MaxLength(255)
  contentId: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  contentFormat: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  widgetId: string;
}

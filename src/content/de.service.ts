import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import omit from 'lodash/omit';
import { ConfigService } from 'nestjs-config';
import { <PERSON>Null, <PERSON>Than, Not, Repository } from 'typeorm';
import { HomeTypeStatus } from 'src/admin/admin.type';
import { HomeWidgetEntity } from 'src/admin/entities/home-widget.entity';
import { ArccosService } from 'src/arccos/arccos.service';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { CdmService } from 'src/cdm/cdm.service';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { LoggingService } from 'src/logging/logging.service';
import { MrpService } from 'src/mrp/mrp.service';
import { convertPstDate } from 'src/utils/datetime';
import { isUSCountry } from 'src/utils/transform';
import { Role } from '../auth/roles.decorator';
import { ContentCacheService } from './content.cache.service';
import { DE_EVENT, DE_MODULE, HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES, VIDEO_CMS_IDS } from './content.constants';

@Injectable()
export class DeService {
  constructor(
    private readonly config: ConfigService,
    private readonly loggingService: LoggingService,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    @Inject(forwardRef(() => MrpService)) private mrpService: MrpService,
    @Inject(forwardRef(() => ArccosService)) private arccosService: ArccosService,
    @InjectRepository(HomeWidgetEntity) private readonly homeWidgetRepo: Repository<HomeWidgetEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private readonly klaviyoService: KlaviyoService,
    private readonly contentCacheService: ContentCacheService
  ) {}

  async requestContentDefault(userId: string, section: string, limit: number, servicePreference: string) {
    if (
      section.toLowerCase() === DE_MODULE.Drills.toLowerCase() ||
      section.toLowerCase() === DE_MODULE.Insights.toLowerCase()
    ) {
      const userDefault = await this.userRepo.findOne(
        {
          role: Role.SUPER_ADMIN,
          cdmUID: Not(IsNull()),
        },
        { order: { updatedAt: 'DESC' } }
      );

      if (!userDefault) return null;

      const datas = await this.requestContents(
        userDefault?.cdmUID,
        userDefault?.email,
        section,
        limit,
        servicePreference,
        userDefault?.id
      );

      return {
        ...datas,
        isDataDefault: true,
        userId,
      };
    }

    return null;
  }

  async requestContents(
    cdmUID: string,
    email: string,
    section: string,
    limit: number,
    servicePreference?: string,
    userId?: string,
    country?: string
  ) {
    let moduleContentLimit = 3;
    if (section.toLowerCase() === DE_MODULE.Drills.toLowerCase()) {
      moduleContentLimit = 15;
    }
    if (limit) {
      moduleContentLimit = limit;
    }
    const subscriptionLevel = await this.cdmService.getSubscriptionLevel(email);
    if (section === DE_MODULE.HomeExplorer) {
      let [cacheSortedHomeExplorerWidgetIds, cacheHomeExplorerFixedContents, cacheHomeExplorerContents] =
        await Promise.all([
          this.contentCacheService.getSortedHomeExplorerWidgetIds(country),
          this.contentCacheService.getHomeExplorerFixedContents(),
          this.contentCacheService.getHomeExplorerContents(cdmUID),
        ]);
      if (
        cacheSortedHomeExplorerWidgetIds.length > 0 &&
        cacheHomeExplorerFixedContents.length > 0 &&
        cacheHomeExplorerContents.length > 0 &&
        cacheHomeExplorerFixedContents.length + cacheHomeExplorerContents.length ===
          cacheSortedHomeExplorerWidgetIds.length
      ) {
        return cacheSortedHomeExplorerWidgetIds.map((widgetId) => {
          const matchWidget = cacheHomeExplorerFixedContents.find((item) => item.id === widgetId);
          if (matchWidget) return matchWidget;
          return cacheHomeExplorerContents.find((item) => item.id === widgetId);
        });
      }
      const widgets = await this.getHomeWidgets(section, country);
      if (cacheSortedHomeExplorerWidgetIds.length === 0) {
        const sorted = widgets.map((item) => item.id);
        await this.contentCacheService.cacheSortedHomeExplorerWidgetIds(sorted);
      }

      if (cacheHomeExplorerFixedContents.length > 0 && cacheSortedHomeExplorerWidgetIds.length > 0) {
        const res = await this.requestDecisionEngineContents(
          cdmUID,
          section,
          moduleContentLimit,
          widgets.filter((widget) => HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES.includes(widget.type)),
          subscriptionLevel,
          servicePreference
        );
        cacheHomeExplorerContents = res.data;
        await this.contentCacheService.cacheHomeExplorerContents(cdmUID, cacheHomeExplorerContents);
        return cacheSortedHomeExplorerWidgetIds
          .map((widgetId) => {
            const matchWidget = cacheHomeExplorerFixedContents.find((item) => item.id === widgetId);
            if (matchWidget) return matchWidget;
            return cacheHomeExplorerContents.find((item) => item.id === widgetId);
          })
          .filter((item) => !!item);
      }
      const res = await this.requestDecisionEngineContents(
        cdmUID,
        section,
        moduleContentLimit,
        widgets,
        subscriptionLevel,
        servicePreference
      );
      return res.data;
    }

    const queries: any = {
      limit: moduleContentLimit,
      clientId: this.config.get('app.deClientId'),
      servicePreference,
    };
    if (subscriptionLevel === 0) {
      queries.freeTier = true;
    }
    const baseUrl = `${this.config.get('app.deEndpoint')}/api/v1/content/users/${cdmUID}`;

    if (section === DE_MODULE.HomeTile || section === DE_MODULE.HomeTiles || section === DE_MODULE.HomeStats) {
      section = DE_MODULE.HomeTile;
      const [user, targetScore] = await Promise.all([
        this.userRepo.findOne({ email }),
        this.cdmService.getTargetScore(cdmUID),
      ]);

      let widgetsAndVariables: any;
      const isServicePreferenceArccos = await this.cdmService.isArccosEmailAndIsServicePreferenceArccos(
        user.email,
        user?.regionId || this.config.get('app.defaultRegionId'),
        true
      );
      if (user.isArccosEmail && isServicePreferenceArccos) {
        const arccosAllTime = await this.arccosService.getArccosAllTime(email);
        if (arccosAllTime.userStatus.isMember) {
          const widgets = await this.getHomeWidgets(section, country);
          const widgetsStr = Buffer.from(
            JSON.stringify(
              widgets.map((widget) => ({
                ...widget,
                heading: widget.type === 'HANDICAP_INDEX' ? 'Arccos Handicap' : widget.heading,
              }))
            )
          ).toString('base64');
          const variableStr = JSON.stringify({
            targetScore,
            handicapHistories: [
              {
                handicap: arccosAllTime?.allTimeStats?.handicap,
              },
            ],
            averageScores: {
              averageScore: arccosAllTime?.allTimeStats?.averageScore,
              lowestScore: arccosAllTime?.allTimeStats?.lowScore,
              highestScore: arccosAllTime?.allTimeStats?.highScore,
              totalRounds: arccosAllTime?.allTimeStats?.roundCount,
            },
          });
          widgetsAndVariables = {
            widgets: widgetsStr,
            variables: variableStr,
          };
        } else {
          widgetsAndVariables = await this.getWidgetsAndVariables(section, targetScore, user, country);
        }
      } else {
        widgetsAndVariables = await this.getWidgetsAndVariables(section, targetScore, user, country);
      }
      const res = await axios.get(`${baseUrl}/recommended/${section}?${new URLSearchParams(queries).toString()}`, {
        headers: widgetsAndVariables,
      });
      return res.data?.map((item) => ({
        ...item,
        extraData: JSON.parse(item.extraData),
      }));
    }
    if (section === DE_MODULE.Insights) {
      this.klaviyoService
        .track(
          email,
          KlaviyoTrackEvents.VIEWED_INSIGHTS,
          {},
          {
            mytm_subscriber_last_view_insight: convertPstDate(),
          }
        )
        .then((r) => r);
    }

    if (section.toLowerCase() === DE_MODULE.Drills.toLowerCase()) {
      const userPermission = await this.cdmService.getMyTMPermission(userId, email);
      const { data } = await axios.get(`${baseUrl}/recommended/${section}?${new URLSearchParams(queries).toString()}`);
      const videos = !userPermission.canVideoInstruction
        ? data.content.videos.map((video) => ({ ...video, playable: false }))
        : data.content.videos.map((video) => ({ ...video, playable: true }));

      if (!userPermission.canVideoInstruction) {
        return {
          userId: data.userId,
          recommended: data.recommended,
          module: data.module,
          limit: data.limit,
          content: {
            videos: videos.map((video) => {
              return {
                ...video,
                playable: false,
                video_url: '',
              };
            }),
          },
        };
      }

      return {
        userId: data.userId,
        recommended: data.recommended,
        module: data.module,
        limit: data.limit,
        content: {
          videos,
        },
      };
    }
    const response = await axios.get(`${baseUrl}/recommended/${section}?${new URLSearchParams(queries).toString()}`);
    return response.data;
  }

  async getFixedHomeExplorerContents(cdmUID) {
    let widgets = await this.getHomeWidgets(DE_MODULE.HomeExplorer);
    widgets = widgets.filter((widget) => !HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES.includes(widget.type));
    const res = await this.requestDecisionEngineContents(cdmUID, DE_MODULE.HomeExplorer, 15, widgets);
    return res.data;
  }

  async requestDecisionEngineContents(
    cdmUID,
    section,
    moduleContentLimit,
    widgets,
    subscriptionLevel?: number,
    servicePreference?: string
  ) {
    const queries: any = {
      limit: moduleContentLimit,
      clientId: this.config.get('app.deClientId'),
      servicePreference,
    };
    if (subscriptionLevel === 0) {
      queries.freeTier = true;
    }
    const baseUrl = `${this.config.get('app.deEndpoint')}/api/v1/content/users/${cdmUID}`;
    return axios.get(`${baseUrl}/recommended/${section}?${new URLSearchParams(queries).toString()}`, {
      headers: { widgets: Buffer.from(JSON.stringify(widgets)).toString('base64') },
    });
  }

  async getWidgetsAndVariables(section, targetScore, user, country?: string) {
    try {
      const [widgets, handicapHistories, newHandicaps, averageScores] = await Promise.all([
        this.getHomeWidgets(section, country),
        this.cdmService.getHandicapHistories(targetScore.golferProfileId),
        this.cdmService.getNewHandicaps(targetScore.golferProfileId),
        this.mrpService.getAverageScores(parseInt(user.mrpUID, 10)),
      ]);
      const variableStr = JSON.stringify({
        targetScore,
        handicapHistories,
        newHandicaps,
        averageScores,
      });
      const widgetsStr = Buffer.from(JSON.stringify(widgets)).toString('base64');
      return { widgets: widgetsStr, variables: variableStr };
    } catch (err) {
      console.error(err);
      return null;
    }
  }

  async getHomeWidgets(section, countryCode?: string) {
    let conditionCountry = '1=1';
    if (countryCode) {
      if (isUSCountry(countryCode)) {
        conditionCountry += ` AND (countries LIKE '%${countryCode}%' OR countries IS NULL) `;
      } else {
        conditionCountry += ` AND countries LIKE '%${countryCode}%' `;
      }
    } else {
      conditionCountry += ` AND (countries LIKE '%US%' OR countries IS NULL) `;
    }
    const widgets = await this.homeWidgetRepo
      .createQueryBuilder('hw')
      .where('hw.groupKey = :section', { section })
      .andWhere(`hw.status = :status`, { status: HomeTypeStatus.ACTIVE })
      .andWhere('hw.show = 1')
      .andWhere(conditionCountry)
      .orderBy({
        'hw.sortOrder': 'ASC',
      })
      .getMany();
    return widgets.map((widget) =>
      omit(widget, [
        'groupKey',
        'createdAt',
        'updatedAt',
        'deletedAt',
        'createdBy',
        'updatedBy',
        'deletedBy',
        'sortOrder',
        'status',
        'show',
      ])
    );
  }

  async triggerNewEvent(eventName: string, userId: string, data: any) {
    try {
      return true;
      // const response = await axios.post(`${this.config.get('app.deEndpoint')}/api/v1/events/${eventName}`, {
      //   userId,
      //   data,
      // });
      // await this.loggingService.save({
      //   event: `DE_TRIGGER:${eventName}`,
      //   userId,
      //   data,
      //   triggeredEvent: eventName,
      //   responseStatusCode: response.status,
      // });
      // return response.data;
    } catch (error) {
      console.log('triggerNewEvent error: ', error);
      return false;
    }
  }

  async getContentList(query: string) {
    try {
      const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/cms/contents?${query}`);
      return response.data;
    } catch (error) {
      return [];
    }
  }

  async getVideosList(query: string) {
    try {
      const { data } = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/cms/videos?${query}`);
      return data;
    } catch (error) {
      return [];
    }
  }

  async getContentTags(query: string) {
    try {
      const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/cms/tags?${query}`);
      return response.data;
    } catch (error) {
      return [];
    }
  }

  async getSingleContent(contentType: string, contentId: string) {
    try {
      const response = await axios.get(
        `${this.config.get('app.deEndpoint')}/api/v1/cms/contents/${contentType}/${contentId}`
      );
      return response.data;
    } catch (error) {
      return [];
    }
  }

  async triggerFittingCompleteEvent(email: string, fittingId: number | string) {
    const data = { fittingId: `${fittingId}` };
    return await this.triggerNewEvent(DE_EVENT.FITTING_COMPLETED, email, data);
  }

  async triggerRoundStartedEvent(userId: string, roundId: number | string, iGolfCourseId: number | string) {
    const data = { roundId, iGolfCourseId };
    return await this.triggerNewEvent(DE_EVENT.ROUND_STARTED, userId, data);
  }

  async triggerNewAccountEvent(userId: string, data) {
    return await this.triggerNewEvent(DE_EVENT.SIGN_UP, userId, data);
  }

  async triggerUserUpdateEvent(userId: string, data) {
    return await this.triggerNewEvent(DE_EVENT.USER_UPDATE, userId, data);
  }

  getSwingIndexHeaders(email) {
    return {
      Authorization: Buffer.from(email).toString('base64'),
    };
  }

  async healthy() {
    const { data } = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/health`);
    return {
      data: {
        healthy: data.status === 'healthy',
      },
    };
  }

  async getPlayerSwingPotentialIndex(email) {
    try {
      const response = await axios.get(
        `${this.config.get('app.deEndpoint')}/api/v1/swing-index/player/swing-potential-index`,
        { headers: this.getSwingIndexHeaders(email) }
      );
      return response.data;
    } catch (e) {
      return {};
    }
  }

  async getPlayerInstructor(email) {
    try {
      const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/swing-index/player/instructor`, {
        headers: this.getSwingIndexHeaders(email),
      });
      return response.data;
    } catch (e) {
      return {};
    }
  }

  async postPlayerRegister(email, auth0AccessToken: string) {
    try {
      const response = await axios.post(
        `${this.config.get('app.deEndpoint')}/api/v1/swing-index/player/register`,
        { accessToken: auth0AccessToken },
        {
          headers: this.getSwingIndexHeaders(email),
        }
      );
      return response.data;
    } catch (e) {
      return {};
    }
  }

  async getPlayerRoadmap(email) {
    try {
      const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/swing-index/player/roadmap`, {
        headers: this.getSwingIndexHeaders(email),
      });
      return response.data;
    } catch (e) {
      return {};
    }
  }

  async getPlayerAnalysis(email, swingSubElementId) {
    try {
      const response = await axios.get(
        `${this.config.get('app.deEndpoint')}/api/v1/swing-index/player/analysis/${swingSubElementId}`,
        {
          headers: this.getSwingIndexHeaders(email),
        }
      );
      return response.data;
    } catch (e) {
      return {};
    }
  }

  async getContentStrings(email) {
    const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/swing-index/content/strings`, {
      headers: this.getSwingIndexHeaders(email),
    });
    return response.data;
  }

  async getContentBatch(email) {
    const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/swing-index/content/batch`, {
      headers: this.getSwingIndexHeaders(email),
    });
    return response.data;
  }

  async getUploadCredentials(email) {
    const response = await axios.get(`${this.config.get('app.deEndpoint')}/api/v1/swing-index/upload/credentials`, {
      headers: this.getSwingIndexHeaders(email),
    });
    return response.data;
  }
}

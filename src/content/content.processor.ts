import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job, Queue } from 'bull';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { ContentCacheService } from './content.cache.service';
import {
  ContentProcessorQueueName,
  DE_MODULE,
  HOME_EXPLORER_PERSONALIZED_WIDGET_TYPES,
  PREFETCH_CONTENTS_QUEUE_NAME,
  PREFETCH_CONTENT_USER_QUEUE_NAME,
} from './content.constants';
import { ContentService } from './content.service';
import { DeService } from './de.service';

type PrefetchUserContentsJob = Job<{ userIds: [] }>;
const isStaging = process.env.STAGE === 'staging';

@Processor(PREFETCH_CONTENTS_QUEUE_NAME)
export class ContentProcessor {
  private readonly logger = new Logger(ContentProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly deService: DeService,
    private readonly cdmService: CdmService,
    private readonly contentService: ContentService,
    private readonly contentCacheService: ContentCacheService,
    @InjectQueue(PREFETCH_CONTENT_USER_QUEUE_NAME) private prefetchContentUserQueue: Queue,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}

  @Process({
    name: ContentProcessorQueueName.PREFETCH,
    concurrency: isStaging ? 1 : 4,
  })
  async prefetchContents(job: PrefetchUserContentsJob): Promise<any> {
    this.logger.log(`Prefetch contents for ${job.data.userIds.length} users`);
    if (isStaging) {
      return true;
    }
    for (const userId of job.data.userIds) {
      await this.prefetchContentUserQueue.add(ContentProcessorQueueName.PREFETCH_USER, { userId });
    }

    return {
      success: true,
    };
  }
}

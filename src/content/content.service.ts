import { BadRequestException, CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cache } from 'cache-manager';
import { isEmpty, result } from 'lodash';
import startCase from 'lodash/startCase';
import toLower from 'lodash/toLower';
import { ConfigService } from 'nestjs-config';
import { FindConditions, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { CmsService } from '../cms/cms.service';
import { LoggingService } from '../logging/logging.service';
import { isProduction } from '../utils/cron';
import { ERROR_CODES } from '../utils/errors';
import { VIDEO_CMS_IDS } from './content.constants';
import { DeService } from './de.service';
import { ContentDto } from './dto/request.dto';
import { ContentTagThumbnailEntity } from './entities/content-tag-thumbnail.entity';
import { DismissedContentsEntity } from './entities/dismissed-content.entity';
import { FavoriteContentEntity } from './entities/favorite-content.entity';
import { ViewedContentEntity } from './entities/viewed-content.entity';

@Injectable()
export class ContentService {
  private readonly categoryType = ['off-the-tee', 'approach', 'around-the-green', 'putting'];
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(FavoriteContentEntity)
    private readonly favoriteContentRepo: Repository<FavoriteContentEntity>,
    @InjectRepository(ViewedContentEntity)
    private readonly viewedContentRepo: Repository<ViewedContentEntity>,
    @InjectRepository(DismissedContentsEntity)
    private readonly dismissedContentRepo: Repository<DismissedContentsEntity>,
    @InjectRepository(ContentTagThumbnailEntity)
    private readonly contentTagThumbnailRepo: Repository<ContentTagThumbnailEntity>,
    private readonly deService: DeService,
    private readonly cdmService: CdmService,
    private readonly loggingService: LoggingService,
    private readonly cmsService: CmsService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async favoriteContent(userId: string, contentId: string, contentFormat: string) {
    const content = await this.favoriteContentRepo.findOne({
      where: {
        userId,
        contentId,
        contentFormat,
      },
    });
    if (content) {
      return { ...content, userId, contentId };
    }

    let newContent = await this.favoriteContentRepo.create({
      userId,
      contentId,
      contentFormat,
      createdBy: userId,
    });
    newContent = await this.favoriteContentRepo.save(newContent);
    return newContent;
  }

  async unfavoriteContent(userId: string, contentId: string, contentFormat: string) {
    await this.favoriteContentRepo.delete({
      userId,
      contentId,
      contentFormat,
    });
    return { success: true };
  }

  async dismissContent(userId: string, contentId: string, contentFormat: string, widgetId: string) {
    const conditions: FindConditions<DismissedContentsEntity> = {
      userId,
    };
    if (widgetId) {
      conditions.widgetId = widgetId;
    }
    if (contentId && contentFormat) {
      conditions.contentId = contentId;
      conditions.contentFormat = contentFormat;
    }
    if (Object.keys(conditions).length === 0) {
      return { success: false };
    }
    const content = await this.dismissedContentRepo.findOne({
      where: conditions,
    });
    if (content) {
      return { success: true };
    }
    await this.dismissedContentRepo.save({
      id: v4(),
      userId,
      contentId,
      contentFormat,
      widgetId,
    });
    return { success: true };
  }

  async viewContent(userId: string, data: ContentDto) {
    const contentId = data.contentId;
    const contentTitle = data.contentTitle;
    const content = await this.viewedContentRepo.findOne({
      where: {
        userId,
        contentId,
      },
    });
    if (content) {
      await this.viewedContentRepo.update(
        { id: content.id },
        { views: content.views + 1, updatedBy: userId, contentTitle }
      );
      return { ...content, userId, contentId, contentTitle, views: content.views + 1 };
    }

    let newContent = await this.viewedContentRepo.create({
      userId,
      contentId,
      contentTitle,
      views: 1,
      createdBy: userId,
    });
    newContent = await this.viewedContentRepo.save(newContent);
    return newContent;
  }

  async getHomeContents(
    userId: string,
    cdmUID: string,
    email: string,
    section: string,
    limit = 5,
    servicePreference?: string,
    country?: string
  ) {
    try {
      const dismissedContents = await this.dismissedContentRepo.find({
        userId,
      });
      let contents = await this.deService.requestContents(
        cdmUID,
        email,
        section,
        limit,
        servicePreference,
        userId,
        country
      );
      contents = contents
        .filter((content) => !!content)
        .filter((content) => !dismissedContents.find((dismissedContent) => dismissedContent.widgetId === content.id));
      const newDatas = [];
      if (contents && contents.length) {
        for (const value of contents) {
          let isCheckProduct = true;
          const { productId } = value;
          if (isEmpty(value.title)) {
            isCheckProduct = false;
          }

          if (productId) {
            const productEcom = await this.cdmService.ecomService.getProduct(productId, country);
            if (!productEcom) {
              isCheckProduct = false;
            }
          }
          if (isCheckProduct) {
            newDatas.push(value);
          }
        }
      }

      return newDatas;
    } catch (error) {
      await this.loggingService.save({
        event: 'Err_Content_Func_getHomeContents',
        data: {
          error,
        },
      });
      return new BadRequestException({
        internalErrorCode: ERROR_CODES.CONTENT_ERROR,
        errorMessage: error?.message,
      });
    }
  }

  async getContentList(query: string) {
    return this.deService.getContentList(query);
  }

  async getVideosList(query: string) {
    return this.deService.getVideosList(query);
  }

  async getContentTags(query: string) {
    const cacheKey = `DRILL:CONTENT:TAGS`;
    const cachedData = await this.cacheManager.get(cacheKey);

    if (cachedData) {
      return cachedData;
    }

    const availableTags = [
      {
        slug: 'overall',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'off-the-tee',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'approach',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'around-the-green',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'putting',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'mental-game',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'fitness',
        group: 'mytmGenericGameArea',
      },
      {
        slug: 'sftw',
        group: 'mytmGenericGameArea',
        hideTitle: true,
      },
    ];

    const tagThumbnails = await this.contentTagThumbnailRepo.find();
    let tags = await this.cmsService.getCmsTags(query, 'USA');
    tags = result(tags, 'data', []);
    const sortedTags = availableTags.map((item) => {
      const tag = tags.find((tag) => item.group === tag.group && item.slug === tag.slug);
      if (item.hideTitle) {
        if (tag && tag.hasOwnProperty('title')) {
          tag.title = '';
        } else {
          item['title'] = '';
        }
      }
      if (!tag) {
        return item;
      }
      return tag;
    });
    const finalTags = sortedTags?.map((tag) => {
      const tagThumbnail = tagThumbnails.find((thumb) => thumb.tagSlug === tag.slug && thumb.tagGroup === tag.group);
      return {
        ...tag,
        title: startCase(toLower(tag.title)),
        imageReg: tagThumbnail?.imageReg || '',
        imageLarge: tagThumbnail?.imageLarge || '',
        imageSmall: tagThumbnail?.imageSmall || '',
        imageThumb: tagThumbnail?.imageThumb || '',
      };
    });

    // Cache the result with a TTL of 7 days (604800 seconds)
    await this.cacheManager.set(cacheKey, finalTags, { ttl: 604800 });

    return finalTags;
  }

  async getSingleContent(contentType: string, contentId: string) {
    return this.deService.getSingleContent(contentType, contentId);
  }

  async getContents(
    cdmUID: string,
    email: string,
    section: string,
    limit: number,
    servicePreference?: string,
    userId?: string,
    country?: string
  ) {
    return this.deService.requestContents(cdmUID, email, section, limit, servicePreference, userId, country);
  }

  async getContentDefaults(userId: string, section: string, limit: number, servicePreference?: string) {
    return this.deService.requestContentDefault(userId, section, limit, servicePreference);
  }

  async getFavoritesContents(userId: string, email: string, take = 5, page = 1) {
    const [favoriteContents, total] = await this.favoriteContentRepo
      .createQueryBuilder('c')
      .where('c.userId = :userId', { userId })
      .orderBy({
        'c.createdAt': 'DESC',
      })
      .limit(take)
      .skip((page - 1) * take)
      .getManyAndCount();

    if (total === 0) {
      return {
        total: 0,
        contents: [],
      };
    }
    const myTMPermission = await this.cdmService.getMyTMPermission(userId, email);
    const getSingleContentPromises = favoriteContents.map((favoriteContent) => {
      return new Promise(async (resolve) => {
        const content = await this.deService
          .getSingleContent(favoriteContent.contentFormat, favoriteContent.contentId)
          .catch(() => resolve(null));
        resolve(content);
      });
    });
    const contents = await Promise.all(getSingleContentPromises);
    return {
      total,
      take,
      page,
      contents: contents
        .filter((content) => !!content)
        .map((item: any) => ({
          ...item,
          playable: VIDEO_CMS_IDS.includes(item?.data?.id) || !!myTMPermission.canVideoInstruction,
        })),
    };
  }

  async getDrillVideosFromCMS(userId, servicePreference) {
    const user = await this.userRepo.findOne(userId);
    if (!user) return {};

    const userCountry = user?.userCountry || 'USA';
    const cacheKey = `DRILL:CMS_VIDEO_META_DATA`;
    const cachedData: any = await this.cacheManager.get(cacheKey);
    const slugs = ['off-the-tee', 'approach', 'around-the-green', 'putting', 'mental-game', 'fitness'];

    const now = new Date().getTime();
    let lastUpdated = null;
    const timeLimit = isProduction ? 7 * 24 * 60 * 60 * 1000 : 10 * 60 * 1000; // 7 days or 10 minutes
    const timeCacheVideos = isProduction ? 24 * 60 * 60 : 10 * 60;

    let currentSlugs = [];
    let slugPageMap = {};
    let startIndex = 0;

    if (!cachedData || now - cachedData.lastUpdated > timeLimit) {
      startIndex = cachedData ? (cachedData.startIndex + 3) % slugs.length : 0;
      currentSlugs = slugs.slice(startIndex, startIndex + 3);
      if (currentSlugs.length < 3) {
        currentSlugs = currentSlugs.concat(slugs.slice(0, 3 - currentSlugs.length));
      }

      slugPageMap = cachedData?.slugPageMap || {};
      for (const slug of currentSlugs) {
        slugPageMap[slug] = (slugPageMap[slug] || 0) + 1;
      }

      lastUpdated = now;
      await this.cacheManager.set(cacheKey, {
        slugs: currentSlugs,
        slugPageMap,
        startIndex,
        lastUpdated,
      });
    } else {
      startIndex = cachedData.startIndex;
      currentSlugs = cachedData.slugs;
      lastUpdated = cachedData.lastUpdated;
      slugPageMap = cachedData.slugPageMap;
    }

    const videos = [];
    let isNeedUpdateCache = false;
    for (const slug of currentSlugs) {
      const page = slugPageMap[slug] || 1;
      const videoCacheKey = `DRILL:CMS_VIDEO:${page}_${slug}_${servicePreference}`;
      let cmsVideo = await this.cacheManager.get(videoCacheKey);

      if (!cmsVideo) {
        const query = `page=${page}&count=3&mytmContentType=drill&mytmGenericGameArea=${slug}&servicePreference=${servicePreference}`;
        cmsVideo = await this.cmsService.getCmsDrillVideo(query, userCountry);
        const cmsData = result(cmsVideo, 'data', []);
        if (!isEmpty(cmsData)) {
          await this.cacheManager.set(videoCacheKey, cmsVideo, { ttl: timeCacheVideos });
        }
      }

      const cmsData = result(cmsVideo, 'data', []);
      const pagination: any = result(cmsVideo, 'meta.pagination', {});

      if (!isEmpty(cmsData)) {
        const formatData: any = this.formatDrillVideoDE(cmsData);
        videos.push(...formatData);
      }

      if (pagination.current_page === pagination.total_pages) {
        slugPageMap[slug] = 1;
        isNeedUpdateCache = true;
      }
    }

    if (isNeedUpdateCache) {
      await this.cacheManager.set(cacheKey, {
        slugs: currentSlugs,
        slugPageMap,
        startIndex,
        lastUpdated,
      });
    }

    return {
      userId: userId,
      recommended: 'recommended',
      module: 'Drills',
      limit: 9,
      content: {
        videos,
      },
    };
  }

  async getInsightTypes(userId) {
    const cacheKey = 'DRILL:INSIGHT_TYPES_LOCAL';
    const now = new Date().getTime();
    const cachedData: any = await this.cacheManager.get(cacheKey);
    let timeLimit = 10 * 60 * 1000;
    if (isProduction) timeLimit = 7 * 24 * 60 * 60 * 1000;

    if (!cachedData || now - cachedData?.lastUpdated > timeLimit) {
      const newCategory = this.getNextCategory(cachedData?.category);
      await this.cacheManager.set(cacheKey, { category: newCategory, lastUpdated: now });
      return this.formatInsightTypes(newCategory, userId);
    }

    return this.formatInsightTypes(cachedData.category, userId);
  }

  private getNextCategory(currentCategory: string) {
    const currentIndex = this.categoryType.indexOf(currentCategory);
    const nextIndex = (currentIndex + 1) % this.categoryType.length;
    return this.categoryType[nextIndex];
  }

  private formatInsightTypes(category: string, userId: string) {
    const insights = [{ drills_type: [category] }];
    return {
      userId: userId,
      recommended: 'recommended',
      module: 'Insights',
      limit: 3,
      content: {
        insights: insights,
      },
    };
  }

  private formatDrillVideoDE(cmsData: any) {
    // Group videos by category
    const groupedVideos = cmsData.reduce((acc, data) => {
      const category = data.tags.mytmGenericGameArea?.[0]?.title || '';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(data);
      return acc;
    }, {});

    // Format videos and set priority
    const videos = [];
    for (const group of Object.values(groupedVideos)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      for (let index = 0; index < group?.length; index++) {
        const data = group[index];
        videos.push({
          id: data.id,
          title: data.title,
          postDate: data.postDate,
          primaryImage: data.primaryImage,
          primaryImageSecondary: data.primaryImageSecondary,
          synopsis: data.synopsis,
          video_type: data.video_type,
          video_id: data.video_id,
          duration: data.duration,
          premium: data.premium,
          jsonUrl: data.jsonUrl,
          year: data.year,
          dataSource: data.dataSource,
          doNotPromote: data.doNotPromote,
          freeAccess: data.freeAccess,
          exclusive: data.exclusive,
          related: data.related,
          tags: data.tags,
          video_url: data.video_url,
          modules: ['Drills'],
          category: data.tags.mytmGenericGameArea?.[0]?.title || '',
          priority: index + 1,
          playable: false,
        });
      }
    }

    return videos;
  }
}

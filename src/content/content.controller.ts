import { BadRequestException, Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isEmpty, result } from 'lodash';
import sampleSize from 'lodash/sampleSize';
import uniqBy from 'lodash/uniqBy';
import querystring from 'querystring';
import { Repository } from 'typeorm';
import { ImageService } from 'src/admin/image.service';
import { IMAGE_TYPE } from 'src/admin/image.types';
import { TextService } from 'src/admin/text.service';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { CdmService } from '../cdm/cdm.service';
import { AccessedClients } from '../client/clients.decorator';
import { CmsService } from '../cms/cms.service';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { ERROR_CODES } from '../utils/errors';
import { ContentCacheService } from './content.cache.service';
import { DE_MODULE, VIDEO_CMS_IDS } from './content.constants';
import { ContentCronService } from './content.cron.service';
import { ContentService } from './content.service';
import { ContentDto, DismissContentDto } from './dto/request.dto';
import { getFaqFile, getTierFile } from './files';

@Controller('content')
@UseGuards(ClientGuard)
export class ContentController {
  constructor(
    private readonly contentService: ContentService,
    private readonly cdmService: CdmService,
    private readonly contentCronService: ContentCronService,
    private readonly contentCacheService: ContentCacheService,
    private readonly cmsService: CmsService,
    private readonly imageService: ImageService,
    private readonly textService: TextService,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>
  ) {}

  @Get('home/widgets/:section')
  @UseGuards(AuthGuard)
  async getHomeContents(
    @Req() request: BaseRequest,
    @Param() params,
    @Query('limit') limit,
    @Query('servicePreference') servicePreference
  ): Promise<any> {
    return {};
    // return await this.contentService.getHomeContents(
    //   request.user.uid,
    //   request.user.cdmUID,
    //   request.user.email,
    //   params.section,
    //   limit,
    //   servicePreference,
    //   request?.country
    // );
  }

  @Get('list')
  async getContentList(@Query() query): Promise<any> {
    return {};
    // return await this.contentService.getContentList(querystring.stringify(query));
  }

  @Get('videos')
  @UseGuards(AuthGuard)
  async getVideosList(@Req() request: BaseRequest, @Query() query): Promise<any> {
    let mergedVideos: any[];
    const userCountry = request?.user?.userCountry || 'USA';
    const listOfContents = await this.cmsService.getCmsDrillVideo(
      querystring.stringify({ ...query, page: 1, count: 75 }),
      userCountry
    );
    mergedVideos = !isEmpty(listOfContents.data) ? listOfContents.data : [];
    const myTMPermission = await this.cdmService.getMyTMPermission(request.user.uid, request.user.email);
    if (!myTMPermission.canVideoInstruction) {
      mergedVideos = mergedVideos.map((video) => {
        if (VIDEO_CMS_IDS.includes(video.id)) {
          return {
            ...video,
            playable: true,
          };
        }

        return {
          ...video,
          playable: false,
          video_url: '',
        };
      });
    } else {
      mergedVideos = mergedVideos.map((video) => ({ ...video, playable: true }));
    }
    // @ts-ignore
    return [...mergedVideos.filter((item) => item.playable), ...mergedVideos.filter((item) => !item.playable)];
  }

  @Get('tags')
  async getContentTags(@Query() query): Promise<any> {
    return await this.contentService.getContentTags(querystring.stringify(query));
  }

  @Get('single/:contentType/:contentId')
  async getSingleContent(@Param() params): Promise<any> {
    return {};
    // return await this.contentService.getSingleContent(params.contentType, params.contentId);
  }

  @Get('favorites')
  @UseGuards(AuthGuard)
  async getFavoritesContents(@Req() request: BaseRequest, @Query('take') take, @Query('page') page): Promise<any> {
    return {};
    // return await this.contentService.getFavoritesContents(
    //   request.user.uid,
    //   request.user.email,
    //   parseInt(take, 10),
    //   parseInt(page, 10)
    // );
  }
  @Get('images')
  @UseGuards(AuthGuard)
  async getImages(): Promise<any> {
    return await this.imageService.getImages();
  }
  @Get('play-bg-images')
  @UseGuards(AuthGuard)
  async getPlayBackgroundImages(): Promise<any> {
    return await this.imageService.getImages(IMAGE_TYPE.PLAY_BACKGROUND);
  }

  @Get('texts')
  @UseGuards(AuthGuard)
  async getTexts(): Promise<any> {
    return await this.textService.getTexts();
  }

  @Get('tiers')
  @UseGuards(AuthGuard)
  getTiers(@Query('country') country, @Query('level') level) {
    return getTierFile(country, level);
  }

  @Get('faqs')
  @UseGuards(AuthGuard)
  getFAQs(@Query('country') country) {
    return getFaqFile(country);
  }

  @Get('/tour-story')
  @UseGuards(AuthGuard)
  async getTourStory() {
    return this.contentCacheService.getTourStory();
  }

  @Post('/tour-story')
  @UseGuards(AuthGuard)
  async postTourStory(@Body() body: any) {
    return this.contentCacheService.postTourStory(body);
  }

  @Get(':module')
  @UseGuards(AuthGuard)
  async getContents(
    @Req() request: BaseRequest,
    @Param() params,
    @Query('limit') limit,
    @Query('servicePreference') servicePreference
  ): Promise<any> {
    const userId = request.user.uid;
    let datas = null;
    if (params.module.toLowerCase() === DE_MODULE.Drills.toLowerCase()) {
      datas = await this.contentService.getDrillVideosFromCMS(userId, servicePreference);
    } else if (params.module.toLowerCase() === DE_MODULE.Insights.toLowerCase()) {
      datas = await this.contentService.getInsightTypes(userId);
    }
    return datas;

    // datas = await this.contentService.getContents(
    //   request.user.cdmUID,
    //   request.user.email,
    //   params.module,
    //   limit,
    //   servicePreference,
    //   request.user.uid,
    //   request.country
    // );
    // let dataDefaults = null;
    // let content = null;
    // let isDrillDefault = false;
    // if (params.module.toLowerCase() === DE_MODULE.Drills.toLowerCase()) {
    //   content = result(datas, `content.videos`, null);
    //   isDrillDefault = true;
    // } else if (params.module.toLowerCase() === DE_MODULE.Insights.toLowerCase()) {
    //   content = result(datas, `content.${params.module.toLowerCase()}`, null);
    //   isDrillDefault = true;
    // }
    // if ((!content || !content.length) && isDrillDefault) {
    //   dataDefaults = await this.contentService.getContentDefaults(
    //     request.user.uid,
    //     params.module,
    //     limit,
    //     servicePreference
    //   );
    // }
  }

  @Post('favorite')
  @UseGuards(AuthGuard)
  async favoriteContent(@Req() request: BaseRequest, @Body() data: ContentDto): Promise<any> {
    if (!(await this.cmsService.isValidContent(parseInt(data.contentId, 10), data.contentFormat))) {
      return ContentController.throwContentInvalidError();
    }
    return await this.contentService.favoriteContent(request.user.uid, data.contentId, data.contentFormat);
  }

  @Post('unfavorite')
  @UseGuards(AuthGuard)
  async unfavoriteContent(@Req() request: BaseRequest, @Body() data: ContentDto): Promise<any> {
    if (!(await this.cmsService.isValidContent(parseInt(data.contentId, 10), data.contentFormat))) {
      return ContentController.throwContentInvalidError();
    }
    return await this.contentService.unfavoriteContent(request.user.uid, data.contentId, data.contentFormat);
  }

  @Post('view')
  @UseGuards(AuthGuard)
  async viewContent(@Req() request: BaseRequest, @Body() data: ContentDto): Promise<any> {
    return await this.contentService.viewContent(request.user.uid, data);
  }

  @Post('prefetch')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async prefetchUserContents(): Promise<any> {
    return await this.contentCronService.postPrefetchUserContents();
  }

  @Post('clear-random-drill-videos')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async clearRandomDrillVideos(): Promise<any> {
    return await this.contentCacheService.clearRandomDrillVideos();
  }

  @Post('dismiss')
  @UseGuards(AuthGuard)
  async dismissContent(@Req() request: BaseRequest, @Body() data: DismissContentDto): Promise<any> {
    if (!data.widgetId && !(await this.cmsService.isValidContent(parseInt(data.contentId, 10), data.contentFormat))) {
      return ContentController.throwContentInvalidError();
    }
    return await this.contentService.dismissContent(
      request.user.uid,
      data.contentId,
      data.contentFormat,
      data.widgetId
    );
  }

  static throwContentInvalidError() {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.CMS_CONTENT_INVALID,
      errorMessage: 'Content is not valid, please check your content id or content format!',
    });
  }
}

import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('ContentTagThumbnails')
export class ContentTagThumbnailEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  tagSlug: string;

  @Column()
  tagGroup: string;

  @Column()
  imageUrl: string;

  @Column()
  imageReg: string;

  @Column()
  imageLarge: string;

  @Column()
  imageSmall: string;

  @Column()
  imageThumb: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

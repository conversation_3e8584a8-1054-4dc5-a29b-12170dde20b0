import { Column, CreateDateColumn, En<PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('FavoriteContents')
export class FavoriteContentEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string;

  @Column()
  contentId: string;

  @Column()
  contentFormat: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

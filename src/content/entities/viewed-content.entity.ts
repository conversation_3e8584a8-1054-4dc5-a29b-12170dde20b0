import { Column, CreateDateColumn, En<PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

@Entity('ViewedContents')
export class ViewedContentEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string;

  @Column()
  contentId: string;

  @Column()
  contentFormat: string;

  @Column()
  contentTitle: string;

  @Column()
  views: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

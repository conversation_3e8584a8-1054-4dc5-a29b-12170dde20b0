import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { Cache } from 'cache-manager';
import ms from 'ms';
import { ConfigService } from 'nestjs-config';
import hash from 'object-hash';
import {
  LIST_TOUR_STORY,
  PREFETCH_HOME_EXPLORER_COMMON_PREFIX_CACHE_KEY,
  PREFETCH_HOME_EXPLORER_PREFIX_CACHE_KEY,
  PREFETCH_HOME_EXPLORER_WIDGETS_STATUS_CACHE_KEY,
  RANDOM_DRILL_VIDEOS_CACHE_PREFIX_KEY,
  SORTED_HOME_EXPLORER_WIDGETS_CACHE_KEY,
} from './content.constants';

@Injectable()
export class ContentCacheService {
  constructor(private readonly config: ConfigService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async cachePrefetchHomeExplorerContentsStatus(status = 'PROCESSING') {
    return this.cacheManager.set(PREFETCH_HOME_EXPLORER_WIDGETS_STATUS_CACHE_KEY, status, {
      ttl: ms('1 days') / 1000,
    });
  }

  async removePrefetchHomeExplorerContentsStatus() {
    return this.cacheManager.del(PREFETCH_HOME_EXPLORER_WIDGETS_STATUS_CACHE_KEY);
  }

  async getPrefetchHomeExplorerContentsStatus() {
    return this.cacheManager.get(PREFETCH_HOME_EXPLORER_WIDGETS_STATUS_CACHE_KEY);
  }

  async cacheHomeExplorerContents(userId, contents) {
    if (contents) {
      await this.cacheManager.set(`${PREFETCH_HOME_EXPLORER_PREFIX_CACHE_KEY}:${userId}`, JSON.stringify(contents), {
        ttl: ms('1 days') / 1000,
      });
      return true;
    }
    return false;
  }

  async getHomeExplorerContents(userId) {
    const results: string | null = await this.cacheManager.get(`${PREFETCH_HOME_EXPLORER_PREFIX_CACHE_KEY}:${userId}`);
    if (results) {
      return JSON.parse(results);
    }
    return [];
  }

  async cacheHomeExplorerFixedContents(contents) {
    if (contents) {
      await this.cacheManager.set(PREFETCH_HOME_EXPLORER_COMMON_PREFIX_CACHE_KEY, JSON.stringify(contents), {
        ttl: ms('1 days') / 1000,
      });
      return true;
    }
    return false;
  }

  async getHomeExplorerFixedContents() {
    const results: string | null = await this.cacheManager.get(PREFETCH_HOME_EXPLORER_COMMON_PREFIX_CACHE_KEY);
    if (results) {
      return JSON.parse(results);
    }
    return [];
  }

  async removeSortedHomeExplorerWidgetIds() {
    return this.cacheManager.del(SORTED_HOME_EXPLORER_WIDGETS_CACHE_KEY);
  }

  async cacheSortedHomeExplorerWidgetIds(widgetIds: string[]) {
    await this.cacheManager.set(SORTED_HOME_EXPLORER_WIDGETS_CACHE_KEY, JSON.stringify(widgetIds), {
      ttl: ms('1 days') / 1000,
    });
  }

  async getSortedHomeExplorerWidgetIds(country?: string) {
    const keySort = country
      ? SORTED_HOME_EXPLORER_WIDGETS_CACHE_KEY + ':' + country
      : SORTED_HOME_EXPLORER_WIDGETS_CACHE_KEY;
    const results: string | null = await this.cacheManager.get(keySort);
    if (results) {
      return JSON.parse(results);
    }
    return [];
  }

  async getRandomDrillVideos(query) {
    const results: string | null = await this.cacheManager.get(
      `${RANDOM_DRILL_VIDEOS_CACHE_PREFIX_KEY}:${hash(query)}`
    );
    if (results) {
      return JSON.parse(results);
    }
    return [];
  }

  async cacheRandomDrillVideos(query, videos: any[]) {
    const ttl = ms(this.config.get('app.randomDrillVideosTTL'));
    await this.cacheManager.set(`${RANDOM_DRILL_VIDEOS_CACHE_PREFIX_KEY}:${hash(query)}`, JSON.stringify(videos), {
      ttl: parseInt(ttl, 10) / 1000,
    });
  }

  async clearRandomDrillVideos() {
    const keys = await this.cacheManager.store.keys(`*${RANDOM_DRILL_VIDEOS_CACHE_PREFIX_KEY}*`);
    for (const key of keys) {
      await this.cacheManager.del(key);
    }
    return {
      success: true,
    };
  }

  async getTourStory() {
    return [];
    const exams = [
      {
        id: 1,
        type: 'video',
        link: 'https://mytmassets.azureedge.net/uploads/2023/12/1701415844_0_TaylorMade Golf Limited Edition with Oracle Red Bull Racing.mp4',
        duration: 34.59770965576172,
      },
      {
        id: 2,
        type: 'image',
        link: 'https://golfdigest.sports.sndimg.com/content/dam/images/golfdigest/fullset/2017/05/09/5911d4b0f71c5577e02767fe_Rory-Taylor-Made.jpg.rend.hgtvcom.966.1288.suffix/1573313848089.jpeg',
      },
      {
        id: 3,
        type: 'video',
        link: 'https://mytmassets.azureedge.net/uploads/2023/12/1701415844_0_TaylorMade Golf Limited Edition with Oracle Red Bull Racing.mp4',
        duration: 34.59770965576172,
      },
      {
        id: 4,
        type: 'image',
        link: 'https://images.pexels.com/photos/6572979/pexels-photo-6572979.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
      },
    ];
    try {
      const dataCache = await this.cacheManager.get(LIST_TOUR_STORY);
      if (dataCache) {
        return dataCache;
      } else {
        await this.cacheManager.del(LIST_TOUR_STORY);
        await this.cacheManager.set(LIST_TOUR_STORY, exams, {
          ttl: 1000 * 60 * 60 * 24,
        });
      }

      return exams;
    } catch (err) {
      return [];
    }
  }

  async postTourStory(body) {
    const { data } = body;
    await this.cacheManager.del(LIST_TOUR_STORY);
    await this.cacheManager.set(LIST_TOUR_STORY, data, {
      ttl: 1000 * 60 * 60 * 24,
    });
    return true;
  }
}

import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { IsNull, Not, Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { isCronJobHandlersEnabled, isProduction } from '../utils/cron';
import { ContentCacheService } from './content.cache.service';
import { ContentProcessorQueueName, PREFETCH_CONTENTS_QUEUE_NAME } from './content.constants';
import { ContentService } from './content.service';
import { DeService } from './de.service';

@Injectable()
export class ContentCronService {
  private readonly logger = new Logger(ContentCronService.name);
  constructor(
    private readonly config: ConfigService,
    private readonly deService: DeService,
    private readonly contentService: ContentService,
    private readonly contentCacheService: ContentCacheService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectQueue(PREFETCH_CONTENTS_QUEUE_NAME) private prefetchContentsQueue: Queue
  ) {}

  async postPrefetchUserContents(onlyFixedContents = true) {
    await this.contentCacheService.cachePrefetchHomeExplorerContentsStatus();
    const rows = await this.userRepo
      .createQueryBuilder()
      .where({ onboardingComplete: true, cdmUID: Not(IsNull()) })
      .select('cdmUID')
      .distinct(true)
      .orderBy('cdmUID')
      .getRawMany();
    if (rows.length === 0) {
      return true;
    }
    const firstCdmUID = rows[0].cdmUID;
    const homeExplorerFixedContents = await this.deService.getFixedHomeExplorerContents(firstCdmUID);
    await this.contentCacheService.cacheHomeExplorerFixedContents(homeExplorerFixedContents);
    if (!onlyFixedContents) {
      const chunkRows = _.chunk(rows, this.config.get('app.prefetchContentQueueLimits') || 100);
      for (const chunkRow of chunkRows) {
        await this.prefetchContentsQueue.add(ContentProcessorQueueName.PREFETCH, {
          userIds: chunkRow.map((item) => item.cdmUID),
        });
      }
      return {
        success: true,
      };
    }
  }
}

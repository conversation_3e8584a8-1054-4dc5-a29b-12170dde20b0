import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ImageService } from 'src/admin/image.service';
import { TextService } from 'src/admin/text.service';
import { CmsService } from '../cms/cms.service';
import { SharedModule } from '../shared/shared.module';
import { SYSTEM_TAG } from '../utils/constants';
import { ContentUserProcessor } from './content-user.processor';
import { ContentCacheService } from './content.cache.service';
import { PREFETCH_CONTENTS_QUEUE_NAME, PREFETCH_CONTENT_USER_QUEUE_NAME } from './content.constants';
import { ContentController } from './content.controller';
import { ContentCronService } from './content.cron.service';
import { ContentProcessor } from './content.processor';
import { ContentService } from './content.service';

let processors = [];

if (process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [...processors, ContentProcessor, ContentUserProcessor];
}

@Module({
  imports: [
    SharedModule,
    BullModule.registerQueue({
      name: PREFETCH_CONTENTS_QUEUE_NAME,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 300000,
      },
    }),
    BullModule.registerQueue({
      name: PREFETCH_CONTENT_USER_QUEUE_NAME,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 300000,
      },
    }),
  ],
  controllers: [ContentController],
  providers: [
    ...processors,
    ContentService,
    CmsService,
    ContentCronService,
    ContentCacheService,
    ImageService,
    TextService,
  ],
})
export class ContentModule {}

import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import camelcaseKeys from 'camelcase-keys';
import crypto from 'crypto';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';

@Injectable()
export class ArccosService {
  private readonly logger = new Logger(ArccosService.name);
  constructor(private readonly config: ConfigService) {
    this.config = config;
  }

  getRequestHeaderConfigs() {
    try {
      const unix = moment().unix();
      const secretKey = this.config.get('app.arccosSecretKey');
      const appId = this.config.get('app.arccosAppId');
      const stringHash = `${appId}GET${unix}`;
      const hash = crypto.createHmac('sha256', secretKey).update(stringHash).digest('hex');
      this.logger.log('hash: ' + hash);
      return {
        headers: { Authorization: `${appId}:${hash}:${unix}` },
      };
    } catch (err) {
      console.log(err);
      return null;
    }
  }

  async getArccosRoundsList(email: string, query: string): Promise<any> {
    try {
      const endpoint = `${this.config.get('app.arccosEndpoint')}/user/${encodeURIComponent(email)}/rounds?${query}`;
      this.logger.log(endpoint);
      const header = this.getRequestHeaderConfigs();
      const response = await axios.get(endpoint, header);
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getArccosRoundStats(email: string, roundId: number): Promise<any> {
    try {
      const endpoint = `${this.config.get('app.arccosEndpoint')}/user/${encodeURIComponent(email)}/round/${roundId}`;
      this.logger.log(endpoint);
      const header = this.getRequestHeaderConfigs();
      const response = await axios.get(endpoint, header);
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getArccosUserStatus(email: string): Promise<any> {
    try {
      const endpoint = `${this.config.get('app.arccosEndpoint')}/user/${encodeURIComponent(email)}/status`;
      this.logger.log(endpoint);
      const header = this.getRequestHeaderConfigs();
      const response = await axios.get(endpoint, header);
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getArccosUser(email: string, query: string): Promise<any> {
    try {
      const endpoint = `${this.config.get('app.arccosEndpoint')}/user/${encodeURIComponent(email)}?${query}`;
      this.logger.log(endpoint);
      const header = this.getRequestHeaderConfigs();
      const response = await axios.get(endpoint, header);
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getArccosClubs(email: string): Promise<any> {
    try {
      const endpoint = `${this.config.get('app.arccosEndpoint')}/user/${encodeURIComponent(email)}/clubs`;
      this.logger.log(endpoint);
      const header = this.getRequestHeaderConfigs();
      const response = await axios.get(endpoint, header);
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getArccosAllTime(email: string): Promise<any> {
    try {
      const endpoint = `${this.config.get('app.arccosEndpoint')}/user/${encodeURIComponent(email)}/allTime`;
      this.logger.log(endpoint);
      const header = this.getRequestHeaderConfigs();
      const response = await axios.get(endpoint, header);
      if (response.data) {
        return camelcaseKeys(response.data, { deep: true });
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }
}

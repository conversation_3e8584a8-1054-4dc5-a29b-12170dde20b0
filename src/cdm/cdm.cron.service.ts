import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { CronJob } from 'cron';
import _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, Repository } from 'typeorm';
import { AdminForceSyncCDMCacheService } from 'src/admin/admin.cache.service';
import { FORCE_SYNC_CDM_CRON_JOB } from 'src/admin/admin.constants';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { isCronJobHandlersEnabled, isProduction } from 'src/utils/cron';
import { CDMProcessorQueueName } from './cdm.processor';

@Injectable()
export class UserPermissionCronService {
  private readonly logger = new Logger(UserPermissionCronService.name);
  constructor(
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectQueue('cdm') private syncCDMQueue: Queue,
    private readonly adminForceSyncCDMCacheService: AdminForceSyncCDMCacheService,
    private schedulerRegistry: SchedulerRegistry,
    private readonly entityManager: EntityManager,
    private readonly config: ConfigService
  ) {}

  @Cron(isProduction ? CronExpression.EVERY_4_HOURS : CronExpression.EVERY_MINUTE)
  async handleCheckUserPermissionCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.debug('Job scans check user permission run...');

    const userPermissions = await this.userPermissionRepo
      .createQueryBuilder('user')
      .select('user.userId')
      .where({ isSyncCDM: false })
      .limit(200)
      .getMany();
    const chunkTargetUsers = _.chunk(userPermissions, 10);
    for (const chunkUsers of chunkTargetUsers) {
      this.syncCDMQueue.add(
        CDMProcessorQueueName.SYNC_PERMISSION_TO_CDM,
        {
          userIds: chunkUsers.map((user) => user.userId),
        },
        { delay: 10000 }
      );
    }
  }
  // NO NEED ANYMORE
  // @Cron(isProduction ? CronExpression.EVERY_HOUR : CronExpression.EVERY_MINUTE)
  // async handleCheckMyTMPermissionCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   this.logger.debug('Job scans check MyTM permission run...');

  //   const userPermissions = await this.userRepo
  //     .createQueryBuilder('user')
  //     .leftJoinAndSelect(UserPermissionsEntity, 'up', 'user.id = up.userId')
  //     .where('userId IS NULL')
  //     .limit(500)
  //     .getMany();
  //   const chunkTargetUsers = _.chunk(userPermissions, 100);

  //   for (const chunkUsers of chunkTargetUsers) {
  //     this.syncCDMQueue.add(
  //       CDMProcessorQueueName.SYNC_CDM_TO_MYTM,
  //       {
  //         userIds: chunkUsers.map((user) => user.id),
  //       },
  //       { delay: 10000 },
  //     );
  //   }
  // }
  async startForceSyncToCDMCronJob(numberUserPerJob = 200) {
    try {
      this.stopForceSyncToCDMCronJob();
      this.logger.debug('Job force sync user permission run...');
      const forceSyncJob = new CronJob(
        isProduction ? CronExpression.EVERY_10_MINUTES : CronExpression.EVERY_MINUTE,
        async () => {
          const currentUserSynced = (await this.adminForceSyncCDMCacheService.getCurrentUserWhenForceSync()) || 0;
          const total = (await this.adminForceSyncCDMCacheService.getTotalUserWhenForceSync()) || 0;
          this.logger.debug(`currentUserSynced: ${currentUserSynced} on Total User Permissions: ${total}`);
          if (currentUserSynced >= total) {
            this.stopForceSyncToCDMCronJob();
            return;
          }
          const userPermissions = await this.userPermissionRepo
            .createQueryBuilder('up')
            .where({ isSyncCDM: true })
            .andWhere('up.subscriptionLength IS NOT NULL')
            .leftJoinAndSelect(UserEntity, 'user', 'up.userId = user.id')
            .select('up.userId, up.subscriptionLength, up.updatedAt, user.myTMSubscriptionLevel')
            .where('myTMSubscriptionLevel > 0')
            .orderBy('up.updatedAt', 'ASC')
            .limit(numberUserPerJob)
            .getRawMany();
          await this.adminForceSyncCDMCacheService.cacheCurrentUserWhenForceSync(currentUserSynced + numberUserPerJob);
          const chunkTargetUsers = _.chunk(userPermissions, 10);
          for (const chunkUsers of chunkTargetUsers) {
            this.syncCDMQueue.add(
              CDMProcessorQueueName.FORCE_SYNC_PERMISSION_TO_CDM,
              {
                userIds: chunkUsers.map((user) => user?.userId),
              },
              { delay: 5000 }
            );
          }
        }
      );

      this.schedulerRegistry.addCronJob(FORCE_SYNC_CDM_CRON_JOB, forceSyncJob);
      forceSyncJob.start();
    } catch (error) {
      this.logger.error(error);
    }
  }

  stopForceSyncToCDMCronJob() {
    try {
      if (this.isExistForceSyncToCDMCronJob()) {
        const job = this.schedulerRegistry.getCronJob(FORCE_SYNC_CDM_CRON_JOB);
        job?.stop();
        this.logger.debug(`Job force sync user permission stop at ${job.lastDate()}`);
      }
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
  isExistForceSyncToCDMCronJob() {
    return this.schedulerRegistry.doesExists('cron', FORCE_SYNC_CDM_CRON_JOB);
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { CdmService } from './cdm.service';
import { SyncCDMJob } from './types/consumer.type';

export enum CDMProcessorQueueName {
  SYNC_PERMISSION_TO_CDM = 'sync-permission-to-cdm',
  FORCE_SYNC_PERMISSION_TO_CDM = 'force-sync-permission-to-cdm',
  // SYNC_CDM_TO_MYTM = 'sync-cdm-to-mytm',
}

@Processor('cdm')
export class CDMProcessor {
  private readonly logger = new Logger(CDMProcessor.name);
  constructor(
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,

    private readonly config: ConfigService,
    private readonly cdmService: CdmService
  ) {}

  @Process(CDMProcessorQueueName.SYNC_PERMISSION_TO_CDM)
  async syncPermissionToCDM(job: SyncCDMJob): Promise<any> {
    for (const userId of job.data.userIds) {
      try {
        const user = await this.userRepo.findOne({ id: userId });
        const payload = await this.userPermissionRepo.findOne({ userId });
        if (payload && !payload.isSyncCDM) {
          if (user.cdmUID === 'NULL') {
            return;
          }
          await this.cdmService.createOrUpdateAccount(user.email, this.config.get('app.defaultRegionId'), {
            ...payload,
            myTMSubscriptionLevel: user.myTMSubscriptionLevel,
            subscriptionService: user.subscriptionService,
          });
        }
      } catch (error) {
        this.logger.error(error);
      }
    }
  }
  // NO NEED ANYMORE
  // @Process(CDMProcessorQueueName.SYNC_CDM_TO_MYTM)
  // async syncCDMtoMyTM(job: SyncCDMJob): Promise<any> {
  //   for (const userId of job.data.userIds) {
  //     try {
  //       const user = await this.userRepo.findOne({ id: userId });
  //       const userPermission = await this.userPermissionRepo.findOne({ userId });
  //       if (!userPermission && user?.email && user?.cdmUID && user?.cdmUID.toUpperCase() !== 'NULL') {
  //         await this.cdmService.getPermission(user.email);
  //       }
  //     } catch (error) {
  //       this.logger.error(error);
  //     }
  //   }
  // }
  @Process(CDMProcessorQueueName.FORCE_SYNC_PERMISSION_TO_CDM)
  async forceSyncPermissionToCDM(job: SyncCDMJob): Promise<any> {
    for (const userId of job.data.userIds) {
      try {
        const user = await this.userRepo.findOne({ id: userId });
        const payload = await this.userPermissionRepo.findOne({ userId });
        if (payload) {
          if (user.cdmUID === 'NULL') {
            return;
          }
          await this.cdmService.createOrUpdateAccount(user.email, this.config.get('app.defaultRegionId'), {
            ...payload,
            myTMSubscriptionLevel: user.myTMSubscriptionLevel,
            subscriptionService: user.subscriptionService,
          });
        }
      } catch (error) {
        this.logger.error(error);
      }
    }
  }
}

import { BadRequestException, CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Cache } from 'cache-manager';
import _, { isEmpty } from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { LaunchFeatureService } from 'src/admin/launch-feature.service';
import { AgreementCheckDto } from 'src/auth/auth.type';
import { UserOnboardingStep } from 'src/auth/entities/user-onboarding-step.entity';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { WITBDto, WITBUpdateDto } from 'src/bag/bag.type';
import { DeService } from 'src/content/de.service';
import { EcomService } from 'src/ecom/ecom.service';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { SubscriptionEntity } from 'src/payment/entities/subscription.entity';
import { COMMON_SUBSCRIPTION_EVENT_TEXT, SUBSCRIPTION_SERVICES } from 'src/payment/payment.constants';
import { PermissionService } from 'src/permission/permission.service';
import { PLAY_SERVICE } from 'src/play/play.type';
import axiosInstance from 'src/utils/axios.instance';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import { convertPstDate } from 'src/utils/datetime';
import { getBoolValue } from 'src/utils/transform';
import { LoggingService } from '../logging/logging.service';
import { FeatureKey } from '../loyalty/entities/loyalty-action.entity';
import { LoyaltyService } from '../loyalty/loyalty.service';
import { UserReferralEntity } from '../user-referral/entity/user-referral.entity';
import { AVERAGE_SCORE_RANGE, DeleteConsumerOptInDto, SaveConsumerOptInDto } from './types/consumer.type';

const CDM_AUTH_TOKEN_CACHE_KEY = 'CDM_AUTH_TOKEN';
const USER_PERMISSIONS_CACHE_PREFIX = 'USER_PERMISSIONS';

@Injectable()
export class CdmService {
  private readonly logger = new Logger(CdmService.name);
  constructor(
    private readonly config: ConfigService,
    private readonly deService: DeService,
    readonly ecomService: EcomService,
    private readonly klaviyoService: KlaviyoService,
    private readonly launchFeatureService: LaunchFeatureService,
    private readonly permissionService: PermissionService,
    private readonly loggingService: LoggingService,
    private readonly loyaltyService: LoyaltyService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserReferralEntity) private readonly userReferralRepo: Repository<UserReferralEntity>,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepo: Repository<SubscriptionEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(UserOnboardingStep) private readonly userOnboardingStepRepo: Repository<UserOnboardingStep>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {
    this.config = config;
  }

  async getRequestHeaderConfigs() {
    let token = await this.cacheManager.get(CDM_AUTH_TOKEN_CACHE_KEY);
    if (!token) {
      token = await this.handleRefreshCdmAuthToken(true);
    }
    return {
      headers: { Authorization: `bearer ${token}` },
    };
  }

  async getConsumer(email: string, regionId: string, isCalledBySwingIndex = false, tracking = true) {
    try {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
          email
        )}&regionId=${regionId}&omitWITB=true`,
        await this.getRequestHeaderConfigs()
      );

      const data = response.data;
      if (data.tmAccessCode !== undefined) {
        await this.userRepo.update(
          { email },
          {
            tmAccessCode: data.tmAccessCode,
          }
        );
      }
      if (data.emailVerified !== undefined && getBoolValue(data.emailVerified)) {
        await this.userRepo.update(
          { email },
          {
            emailVerified: true,
          }
        );
      }

      if (data.myTMSubscriptionLevel !== undefined) {
        try {
          const userSubs = await this.userRepo.findOne({ email });
          if (userSubs && userSubs.myTMSubscriptionLevel == null && data.myTMSubscriptionLevel != null) {
            console.log(`Sync ${email} myTMSubscriptionLevel: ${data.myTMSubscriptionLevel}...`);
            await this.userRepo.update(
              { email },
              {
                myTMSubscriptionLevel: data.myTMSubscriptionLevel,
              }
            );
          }
        } catch (error) {
          console.log(`ERROR update myTMSubscriptionLevel: ${data.myTMSubscriptionLevel}`);
        }
      }
      const consumerData = await this.transformConsumerData(response.data, null, isCalledBySwingIndex);
      if (tracking) {
        this.klaviyoService.identify(email, { mytm_subscriber_last_app_open: convertPstDate() });
      }

      if (isCalledBySwingIndex) {
        return consumerData;
      } else {
        const responseTospp = await axiosInstance.get(
          `${this.config.get('app.cdmEndpoint')}/api/tospp/getTOSPP`,
          await this.getRequestHeaderConfigs()
        );
        return {
          ...consumerData,
          ...CdmService.compareTermsAndPrivacyDateFromTosppCdm(response.data, responseTospp.data),
        };
      }
    } catch (e) {
      this.logger.error(e);
      return null;
    }
  }

  async getConsumerWithoutRegionId(email: string) {
    try {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(email)}&omitWITB=true`,
        await this.getRequestHeaderConfigs()
      );
      const data = response.data;
      if (data.myTMSubscriptionLevel !== undefined) {
        try {
          const userSubs = await this.userRepo.findOne({ email });
          if (userSubs && userSubs.myTMSubscriptionLevel == null && data.myTMSubscriptionLevel != null) {
            console.log(`Sync ${email} myTMSubscriptionLevel: ${data.myTMSubscriptionLevel}...`);
            await this.userRepo.update(
              { email },
              {
                myTMSubscriptionLevel: data.myTMSubscriptionLevel,
              }
            );
          }
        } catch (error) {
          console.log(`ERROR update myTMSubscriptionLevel: ${data.myTMSubscriptionLevel}`);
        }
      }
      const consumerData = await this.transformConsumerData(response.data, null, false);
      const responseTospp = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/tospp/getTOSPP`,
        await this.getRequestHeaderConfigs()
      );
      return {
        ...consumerData,
        ...CdmService.compareTermsAndPrivacyDateFromTosppCdm(response.data, responseTospp.data),
      };
    } catch (e) {
      this.logger.error(e);
      return null;
    }
  }

  async getMyTMUserPermission(userId: string, permissions: any, forceUserToProPlusPlan = false) {
    let userPermission = await this.userPermissionRepo.findOne({ userId });
    if (!userPermission) {
      userPermission = await this.createMyTMUserPermission(userId, permissions, forceUserToProPlusPlan);
    }
    return userPermission;
  }

  async createMyTMUserPermission(
    userId: string,
    permissions: any,
    forceUserToProPlusPlan = false
  ): Promise<UserPermissionsEntity> {
    const dataPermissions = CdmService.transformConsumerMyTMPermission(permissions, forceUserToProPlusPlan);
    dataPermissions.id = v4();
    dataPermissions['userId'] = userId;
    return await this.userPermissionRepo.save(dataPermissions);
  }

  async addUpdateMyTMUserPermission(userId: string, permissions: any) {
    const userPermissions = await this.userPermissionRepo.findOne({ userId });
    if (userPermissions) {
      await this.userPermissionRepo.update({ id: userPermissions.id }, permissions);
    } else {
      await this.createMyTMUserPermission(userId, permissions);
    }
  }

  async getConsumerFromAdminPortal(email: string, regionId: string) {
    try {
      const [customerCDM, customMyTM] = await Promise.all([
        axiosInstance.get(
          `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
            email
          )}&regionId=${regionId}&omitWITB=true`,
          await this.getRequestHeaderConfigs()
        ),
        this.userRepo.findOne({ email }),
      ]);

      return {
        firstName: customerCDM.data?.firstName,
        lastName: customerCDM?.data?.lastName,
        avatar: customerCDM?.data?.avatar,
        email: customMyTM?.email,
        role: customMyTM?.role,
      };
    } catch (e) {
      console.error(e);
      return null;
    }
  }

  async isArccosEmailAndIsServicePreferenceArccos(
    email: string,
    regionId: string,
    onlyCallCDM = false
  ): Promise<boolean> {
    try {
      if (onlyCallCDM) {
        const responseCDM = await axiosInstance.get(
          `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
            email
          )}&regionId=${regionId}&omitWITB=true`,
          await this.getRequestHeaderConfigs()
        );
        return responseCDM?.data?.tmUserIds?.playServicePreference === PLAY_SERVICE.ARCCOS;
      }
      const [customerCDM, customMyTM] = await Promise.all([
        axiosInstance.get(
          `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
            email
          )}&regionId=${regionId}&omitWITB=true`,
          await this.getRequestHeaderConfigs()
        ),
        this.userRepo.findOne({ email }),
      ]);
      return !!customMyTM?.isArccosEmail && customerCDM?.data?.tmUserIds?.playServicePreference === PLAY_SERVICE.ARCCOS;
    } catch (e) {
      console.error(e);
      return false;
    }
  }

  async failToSycnCDM(email: string) {
    const user = await this.userRepo.findOne({ email });
    if (user) {
      await this.userPermissionRepo.update({ userId: user.id }, { isSyncCDM: false });
    }
  }

  async getPermission(email: string, isCalledBySwingIndex = false, isCallFromController = false) {
    try {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/permission/query?email=${encodeURIComponent(email)}`,
        await this.getRequestHeaderConfigs()
      );
      const account = response.data;

      const forceUserToProPlusPlan = this.forceUserToProPlusPlan(email);

      const user = await this.userRepo.findOne({ email });
      const subscriptionService = account.subscriptionService;
      if (subscriptionService) {
        await this.userRepo.update(
          { email },
          {
            subscriptionService,
          }
        );
      }
      if (user.myTMSubscriptionLevel === null) {
        await this.userRepo.update(
          { email },
          {
            myTMSubscriptionLevel: account.myTMSubscriptionLevel,
            emailVerified: account.emailVerified,
          }
        );
      }
      const userPermission = await this.getMyTMUserPermission(user.id, account.myTmPermission, forceUserToProPlusPlan);

      const cachedPermissions = await this.getCacheUserPermissionsWhenPurchase(user.id);

      const myTMSubscriptionLevel = forceUserToProPlusPlan
        ? 2
        : cachedPermissions
        ? cachedPermissions.myTMSubscriptionLevel
        : user.myTMSubscriptionLevel;

      let myTMPermission = null;
      if (cachedPermissions && cachedPermissions.myTMPermission) {
        myTMPermission = { ...userPermission, ...cachedPermissions.myTMPermission };
        myTMPermission = CdmService.transformConsumerMyTMPermission(
          myTMPermission,
          forceUserToProPlusPlan,
          isCalledBySwingIndex
        );
      } else {
        myTMPermission = { ...userPermission };
        myTMPermission = CdmService.transformConsumerMyTMPermission(
          myTMPermission,
          forceUserToProPlusPlan,
          isCalledBySwingIndex
        );
      }
      if (isCallFromController && forceUserToProPlusPlan) {
        myTMPermission = CdmService.transformConsumerMyTMPermission(
          myTMPermission,
          forceUserToProPlusPlan,
          isCalledBySwingIndex
        );
      }
      if (isCalledBySwingIndex) {
        return {
          myTMSubscriptionLevel,
          subscriptionService,
          myTMPermission,
        };
      }

      return {
        myTMSubscriptionLevel,
        subscriptionService,
        dob: this.convertDobWithLocalTz(account.dob, user?.dobOffset),
        firstName: account.firstName,
        lastName: account.lastName,
        userCountry: account.userCountry,
        gender: account.gender,
        acceptedTermsOn: account.acceptedTermsOn,
        acceptedPrivacyOn: account.acceptedPrivacyOn,
        avatar: account.avatar,
        emailVerified: getBoolValue(account.emailVerified),
        myTMPermission,
        acceptTermsRequired: account.acceptedTermRequired,
        acceptPrivacyRequired: account.acceptedPrivacyRequired,
      };
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getSubscriptionLevel(email: string) {
    const forceUserToProPlusPlan = this.forceUserToProPlusPlan(email);
    if (forceUserToProPlusPlan) {
      return 2;
    }
    const user = await this.userRepo.findOne({ email });
    const cachedPermissions = await this.getCacheUserPermissionsWhenPurchase(user.id);
    if (cachedPermissions) {
      return cachedPermissions.myTMSubscriptionLevel || 0;
    }

    if (user.myTMSubscriptionLevel == null) {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/permission/query?email=${encodeURIComponent(email)}`,
        await this.getRequestHeaderConfigs()
      );
      const account = response.data;
      return account.myTMSubscriptionLevel || 0;
    }

    return user.myTMSubscriptionLevel || 0;
  }

  async transformConsumerData(account: any, initialUser?: UserEntity, isCalledBySwingIndex = false) {
    const forceUserToProPlusPlan = this.forceUserToProPlusPlan(account.primaryEmail);
    if (isCalledBySwingIndex) {
      const transformedAccount = CdmService.transformAccount(null, account, forceUserToProPlusPlan);
      return {
        firstName: transformedAccount.firstName || initialUser?.firstNameTM,
        lastName: transformedAccount.lastName || initialUser?.lastNameTM,
        gender: transformedAccount.gender || initialUser?.genderTM,
        email: transformedAccount.email,
        golferProfile: transformedAccount?.golferProfile,
        myTMPermission: {
          canVirtualCoaching: transformedAccount?.myTMPermission?.canVirtualCoaching,
        },
      };
    }
    let user: UserEntity = initialUser;
    if (!user) {
      user = await this.userRepo.findOne({ email: account.primaryEmail });
    }
    const cachedPermissions: any = (await this.getCacheUserPermissionsWhenPurchase(user.id)) || {};
    const launchFeatures = await this.launchFeatureService.mappingLaunchFeatures();
    const onboardingCompleteSteps = await this.getUserOnboardingSteps(user.id);
    const isTrialSubscription: boolean = await this.checkUserTrialSubscription(user.id);
    const consumerData = {
      ...CdmService.transformAccount(
        user.id,
        {
          ...account,
          myTmPermission: cachedPermissions?.myTMPermission || account.myTmPermission,
          myTMSubscriptionLevel: cachedPermissions?.myTMSubscriptionLevel || user.myTMSubscriptionLevel,
          tmAccessCode: user.tmAccessCode,
          emailVerified: user.emailVerified,
          firstNameTM: user?.firstNameTM,
          lastNameTM: user?.lastNameTM,
          genderTM: user?.genderTM,
          measurementUnits: user?.measurementUnits,
        },
        forceUserToProPlusPlan
      ),
      dob: this.convertDobWithLocalTz(account.dob, user.dobOffset),
      newMyTMAccount: typeof user.isNewAccount === 'undefined' ? false : user.isNewAccount,
      onboardingComplete: getBoolValue(user?.onboardingComplete),
      features: launchFeatures,
      signUpByDevice: user.signUpByDevice,
      createdAt: user.createdAt,
      onboardingCompleteSteps,
      isTrialSubscription,
      isManualInputAverageScore: user.isManualInputAverageScore,
      signUpLatitude: user.signUpLatitude,
      signUpLongitude: user.signUpLongitude,
      shouldCollectSalesforceData: user.shouldCollectSalesforceData,
      userCountry: user.userCountry,
      language: user.language,
      address: user.address,
      isUserAnnex: user.is_user_annex,
      siEmail: user?.siEmail,
      siUserId: user?.siUserId,
    };
    // await this.handleCheckResetPermissionFail(consumerData, account);

    consumerData.myTMPermission = await this.permissionService.overrideMyPermission(
      consumerData.myTMPermission,
      // Force myTMSubscriptionLevel to 2
      2
      //consumerData.myTMSubscriptionLevel
    );
    return consumerData;
  }

  async handleCheckResetPermissionFail(consumerData: any, account: any) {
    try {
      const user = await this.userRepo.findOne({
        where: {
          email: account?.primaryEmail,
        },
      });
      if (!user) {
        return;
      }
      const { firstNameTM, lastNameTM, genderTM } = user;
      const userUpdate = { firstNameTM, lastNameTM, genderTM };
      let isUpdate = false;
      if (consumerData.firstName && consumerData.firstName.toLowerCase() !== firstNameTM?.toLowerCase()) {
        userUpdate.firstNameTM = consumerData.firstName;
        isUpdate = true;
      }
      if (consumerData.lastName && consumerData.lastName.toLowerCase() !== lastNameTM?.toLowerCase()) {
        userUpdate.lastNameTM = consumerData.lastName;
        isUpdate = true;
      }
      if (consumerData.gender && consumerData.gender.toLowerCase() !== genderTM?.toLowerCase()) {
        userUpdate.genderTM = consumerData.gender;
        isUpdate = true;
      }

      if (isUpdate) {
        await this.userRepo.update({ id: user.id }, userUpdate);
      }

      const userPermission = await this.userPermissionRepo.findOne({ userId: consumerData?.id });
      if (!userPermission) {
        return;
      }
      const isExpired = new Date(userPermission?.subscriptionExpirationDate).getTime() < new Date().getTime();
      if (isExpired && consumerData?.myTMSubscriptionLevel > 0) {
        await this.resetPermissionsToTheFreePlan(account.primaryEmail);
      }
    } catch (error) {
      console.error(`ERROR RESET PERMISSION: ${error.message}`);
      console.error(error);
    }
  }

  async resetPermissionsToTheFreePlan(email: string) {
    const permissionList = this.getPermissionListByPlan(0);
    const user = await this.userRepo.findOne({ where: { email } });
    await this.userRepo.update({ email }, { myTMSubscriptionLevel: 0, tmAccessCode: false });
    await this.userPermissionRepo.update(
      { userId: user.id },
      {
        subscriptionLength: 0,
        isSyncCDM: false,
        ...permissionList,
      }
    );
    await this.clearCacheUserPermissionsWhenPurchase(user.id);

    const subcriptionUser = await this.subscriptionRepo.query(
      `SELECT * FROM Subscriptions WHERE userId = '${user.id}' AND isChecked = 0 AND expirationDate < DATEADD( mi,- 4, GETDATE( ) ) AND ( notificationType IS NULL OR notificationType <> 'EXPIRED' ) ORDER BY createdAt DESC OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY`
    );

    if (subcriptionUser && subcriptionUser.length) {
      const sub = _.result(subcriptionUser, '[0]', null);
      await this.subscriptionRepo.update({ id: sub?.id }, { isChecked: true });
      const expiredSub = {
        ...sub,
        notificationType: COMMON_SUBSCRIPTION_EVENT_TEXT.EXPIRED,
        refSubscriptionId: sub?.id,
        id: v4(),
        createdAt: new Date(),
      };
      await this.subscriptionRepo.save(expiredSub);
    }
    return this.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
      myTMSubscriptionLevel: 0,
      subscriptionLength: 0,
      tmAccessCode: false,
      ...permissionList,
    });
  }
  private async checkUserTrialSubscription(userId: string) {
    let isTrialPeriod = false;
    const subscription = await this.subscriptionRepo
      .createQueryBuilder('s')
      .where('s.userId = :userId', { userId: userId })
      .andWhere('s.expirationDate >= getdate()')
      .andWhere("s.isTrialPeriod = 'true'")
      .getCount();

    if (subscription) {
      isTrialPeriod = true;
    }
    return isTrialPeriod;
  }

  async getMyTMPermission(userId: string, email: string, isCalledBySwingIndex = false, isCallFromController = false) {
    let myTMPermission = await this.userPermissionRepo.findOne({ userId });
    if (!myTMPermission) {
      const cdmPermission = await this.getPermission(email, isCalledBySwingIndex, isCallFromController);
      myTMPermission = cdmPermission.myTMPermission;
    }
    return myTMPermission;
  }

  async triggerSubscriberKlaviyo(email: string, data: any) {
    this.klaviyoService.identify(email, { mytm_subscriber_my_game_profile: data });
  }

  async transformAdminData(account: any, user: UserEntity) {
    return {
      dob: this.convertDobWithLocalTz(account.dob, user.dobOffset),
      firstName: account.firstName,
      lastName: account.lastName,
      userCountry: account.userCountry,
      gender: account.gender,
      avatar: account.avatar,
    };
  }

  async updateAccount(email: string, regionId: string, data) {
    const payload = {
      ...data,
      email,
      regionId,
      systemId: this.config.get('app.systemId'),
    };
    return axiosInstance.post(
      `${this.config.get('app.cdmEndpoint')}/api/consumer/addUpdate?omitWITB=true`,
      payload,
      await this.getRequestHeaderConfigs()
    );
  }

  async createAccount(email: string, regionId: string, data) {
    const payload = {
      ...data,
      email,
      regionId,
      systemId: this.config.get('app.systemId'),
    };
    return axiosInstance.post(
      `${this.config.get('app.cdmEndpoint')}/api/consumer/addUpdate?omitWITB=true`,
      payload,
      await this.getRequestHeaderConfigs()
    );
  }

  async createOrUpdateAccount(email: string, regionId: string, data: any, omitWITB = true, fromSignUp = false) {
    try {
      const user = await this.userRepo.findOne({ email });
      regionId = user?.regionId || this.config.get('app.defaultRegionId');

      const payload = {
        ...data,
        email,
        regionId,
        systemId: this.config.get('app.systemId'),
      };
      if (data.favoriteTeamMembers) {
        payload.favoriteTeamMember = data.favoriteTeamMembers;
      }
      if (!isEmpty(data.measurementUnits)) {
        payload.measurementUnits =
          data.measurementUnits && data.measurementUnits.toLowerCase() === 'meters' ? 'meters' : 'yards';
      }

      if (data?.averageScoreRange && _.isNil(data?.userInputHandicap)) {
        const handicap = this.transformUserInputHandicap(data?.averageScoreRange);
        if (!_.isNil(handicap)) {
          payload.userInputHandicap = handicap;
        }
      }

      this.logger.debug('Syncing to cdm:', JSON.stringify(payload));
      let consumer;
      const fromSetting = !!user?.onboardingComplete;
      const onboardingCompleted = !!data.onboardingComplete;
      const response = await axiosInstance.post(
        `${this.config.get(
          'app.cdmEndpoint'
        )}/api/consumer/addUpdate?omitWITB=${omitWITB}&onboardingCompleted=${onboardingCompleted}&fromSetting=${fromSetting}`,
        payload,
        await this.getRequestHeaderConfigs()
      );
      await this.userPermissionRepo.update({ userId: user.id }, { isSyncCDM: true });
      consumer = response.data;
      const updateUserPayload: any = {};
      if (onboardingCompleted) {
        console.log(`CALL loyaltyService COMPLETE_QUIZ`);
        this.loyaltyService.addPointUsgaForOldUser(user).then((r) => r);
        try {
          const resCompleted = await this.loyaltyService.addPointUser(user, FeatureKey.COMPLETE_QUIZ);
          console.log(resCompleted);
        } catch (error) {
          console.log(error.message);
        }
      }
      if (data?.userCountry) {
        updateUserPayload.userCountry = data.userCountry;

        // Set default measurement units based on country if not provided
        if (_.isNil(user.measurementUnits)) {
          updateUserPayload.measurementUnits = this.getMeasurementUnitFromUserCountry(data.userCountry);
        }
      }
      if (data?.emailVerified) {
        updateUserPayload.emailVerified = data.emailVerified;
      }
      if (data?.dob) {
        updateUserPayload.dobOffset = this.getDobOffset(data?.dob);
      }
      if (!!user?.onboardingComplete) {
        await this.postUserUpdateEventToDE(user, response);
      }
      if (typeof data.onboardingComplete !== 'undefined' && !user.onboardingComplete) {
        updateUserPayload.onboardingComplete = data.onboardingComplete;
        consumer = response.data;
        if (data.onboardingComplete) {
          console.log(` -------------------------------- START onboardingComplete --------------------------------`);
          try {
            await this.klaviyoService.track(
              consumer.primaryEmail,
              KlaviyoTrackEvents.COMPLETE_ONBOARDING,
              {},
              {
                mytm_subscriber_id: 'True',
                mytm_subscription_date: convertPstDate(),
                mytm_subscriber_current_tier: 'Free',
              }
            );
            await this.identifyMyTMOptIn(email, user);
            await this.postSignUpEventToDE(user, response);
          } catch (e) {
            console.log(e);
          }
        }
      }
      if (typeof data.signUpByDevice !== 'undefined') {
        updateUserPayload.signUpByDevice = data.signUpByDevice;
      }

      if (typeof data.myTMSubscriptionLevel !== 'undefined') {
        updateUserPayload.myTMSubscriptionLevel = data.myTMSubscriptionLevel;
      }

      if (typeof data.tmAccessCode !== 'undefined') {
        updateUserPayload.tmAccessCode = data.tmAccessCode;
      }

      if (!fromSignUp && onboardingCompleted && user?.isNewAccount) {
        updateUserPayload.isNewAccount = false;
      }

      if (!_.isNil(data?.signUpLatitude) && !_.isNil(data?.signUpLongitude)) {
        const lat = Number(data.signUpLatitude.toFixed(5));
        const long = Number(data.signUpLongitude.toFixed(5));
        updateUserPayload.signUpLatitude = lat;
        updateUserPayload.signUpLongitude = long;
        const locationGps = await this.getLocationFromGPS(lat, long);
        if (locationGps) {
          updateUserPayload.signUpGPSLocation = locationGps;
        }
      }

      if (!_.isNil(data?.isManualInputAverageScore)) {
        updateUserPayload.isManualInputAverageScore = data?.isManualInputAverageScore || false;
      }

      if (!_.isNil(data?.address)) {
        updateUserPayload.address = data?.address;
        const checkUserReferral = await this.checkEmailCanReceiveGift(email);
        if (checkUserReferral) {
          await this.klaviyoService.track(email, KlaviyoTrackEvents.MYTM_REFERRAL_REWARD_SENT);
        }
      }

      if (!isEmpty(data.firstName)) {
        updateUserPayload.firstNameTM = data.firstName.trim();
      }

      if (!isEmpty(data.lastName)) {
        updateUserPayload.lastNameTM = data.lastName.trim();
      }

      if (!isEmpty(data.gender)) {
        updateUserPayload.genderTM = data.gender;
      }

      if (!isEmpty(data.measurementUnits)) {
        updateUserPayload.measurementUnits =
          data.measurementUnits && data.measurementUnits.toLowerCase() === 'meters' ? 'meters' : 'yards';
      }

      await this.userRepo.update({ email }, updateUserPayload);
      const onboardingCompleteSteps = data?.onboardingCompleteSteps;
      if (onboardingCompleteSteps && Object.keys(onboardingCompleteSteps).length > 0) {
        await this.userOnboardingStepRepo.save({
          id: user.id,
          userId: user.id,
          ...onboardingCompleteSteps,
          createdBy: user.id,
          updatedBy: user.id,
        });
        if (
          onboardingCompleted &&
          onboardingCompleteSteps?.handed &&
          onboardingCompleteSteps?.maximumDriverDistance &&
          onboardingCompleteSteps?.strongestArea &&
          onboardingCompleteSteps?.weakestArea &&
          onboardingCompleteSteps?.mostScaredShot
        ) {
          await this.klaviyoService.track(email, KlaviyoTrackEvents.DEEPER_INSIGHT_COMPLETED_ONBOARDING);
        }
      }
      return consumer;
    } catch (error) {
      this.logger.error(`ERR SYNC CDM Func createOrUpdateAccount:`);
      this.logger.log(error);
      await this.loggingService.save({
        event: 'ERR_SYNC_CDM_FUNC_createOrUpdateAccount',
        data: {
          error: error,
          email,
        },
      });
      await this.failToSycnCDM(email);
      return null;
    }
  }

  async createWITB(user, data: WITBDto) {
    const { cdmUID, email } = user;
    const payload: any = { ...data };
    if (typeof data.categoryTypeId !== 'undefined') {
      payload.clubCategoryTypeId = data.categoryTypeId;
    }
    await axiosInstance.post(
      `${this.config.get('app.cdmEndpoint')}/api/whatsInTheBag/create`,
      {
        ...payload,
        consumerId: cdmUID,
      },
      await this.getRequestHeaderConfigs()
    );
    const witbs = await this.getWITB(email);
    if (witbs && witbs.length && witbs.length >= 10) {
      await this.loyaltyService.addPointUser(user, FeatureKey.COMPLETE_WITB).catch((e) => e);
    }
    return witbs;
  }

  async agreementCheck(email: string, data: AgreementCheckDto) {
    const payload: { acceptedTermsOn?: Date; acceptedPrivacyOn?: Date } = {};
    if (data.TOS) {
      payload.acceptedTermsOn = new Date();
    }
    if (data.PrivacyPolicy) {
      payload.acceptedPrivacyOn = new Date();
    }
    return this.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), payload);
  }

  async getAgreements(email: string) {
    try {
      const [consumerResponse, agreements] = await Promise.all([
        axiosInstance.get(
          `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
            email
          )}&regionId=${this.config.get('app.defaultRegionId')}`,
          await this.getRequestHeaderConfigs()
        ),
        axios
          .get(`${this.config.get('app.cdmEndpoint')}/api/tospp/getTOSPP`, await this.getRequestHeaderConfigs())
          .then((r) => r.data),
      ]);
      const neededToAcceptTOS =
        new Date(agreements?.lastUpdatedTermsDate).getTime() >
        new Date(consumerResponse?.data?.acceptedTermsOn).getTime();
      const neededToAcceptPrivacyPolicy =
        new Date(agreements?.lastUpdatedPrivacyDate).getTime() >
        new Date(consumerResponse?.data?.acceptedPrivacyOn).getTime();
      return {
        ...agreements,
        neededToAcceptTOS,
        neededToAcceptPrivacyPolicy,
      };
    } catch (e) {
      return null;
    }
  }

  async getTargetScore(consumerId: string): Promise<{ golferProfileId: string; targetScore: number }> {
    const data = await axios
      .get(
        `${this.config.get('app.cdmEndpoint')}/api/GolferProfile/GetTargetScores?consumerId=${consumerId}`,
        await this.getRequestHeaderConfigs()
      )
      .then((r) => r.data);
    return {
      golferProfileId: data[0]?.golferProfileId,
      targetScore: data[0]?.targetScore,
    };
  }

  async getHandicapHistories(golferProfileId: string) {
    return axios
      .get(
        `${this.config.get(
          'app.cdmEndpoint'
        )}/api/HistoryHandicap/GetHistoryHandicaps?golferProfileId=${golferProfileId}`,
        await this.getRequestHeaderConfigs()
      )
      .then((r) => r.data);
  }

  async getNewHandicaps(golferProfileId: string) {
    return axios
      .get(
        `${this.config.get('app.cdmEndpoint')}/api/NewHandicap/GetNewHandicaps?golferProfileId=${golferProfileId}`,
        await this.getRequestHeaderConfigs()
      )
      .then((r) => r.data);
  }
  async getRegions() {
    try {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/region?take=99&skip=0`,
        await this.getRequestHeaderConfigs()
      );
      return _.uniqBy(response.data, 'code')?.map((region: any) => {
        return {
          id: region?.id,
          code: region?.code,
          description: region?.description,
        };
      });
    } catch (error) {
      return null;
    }
  }

  async updateWITB(cdmUID: string, email: string, data: WITBUpdateDto) {
    const payload: any = {
      ...data,
      consumerId: cdmUID,
    };
    if (typeof data.deleted !== 'undefined') {
      payload.deleted = data.deleted ? new Date().toISOString() : null;
    }
    if (typeof data.disable !== 'undefined') {
      payload.deleted = data.disable ? new Date().toISOString() : null;
    }
    if (typeof data.categoryTypeId !== 'undefined') {
      payload.clubCategoryTypeId = data.categoryTypeId;
    }
    await axiosInstance.post(
      `${this.config.get('app.cdmEndpoint')}/api/whatsInTheBag/update`,
      payload,
      await this.getRequestHeaderConfigs()
    );
    return this.getWITB(email);
  }

  async getAccountByEmail(email: string, regionId: string) {
    try {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(
          email
        )}&regionId=${regionId}&omitWITB=true`,
        await this.getRequestHeaderConfigs()
      );
      const data = response.data;
      if (data.tmAccessCode !== undefined) {
        await this.userRepo.update(
          { email },
          {
            tmAccessCode: data.tmAccessCode,
          }
        );
      }
      return data;
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getAccountByEmailWithNoRegion(email: string) {
    try {
      const response = await axiosInstance.get(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(email)}&omitWITB=true`,
        await this.getRequestHeaderConfigs()
      );
      const data = response.data;
      if (data.tmAccessCode !== undefined) {
        await this.userRepo.update(
          { email },
          {
            tmAccessCode: data.tmAccessCode,
          }
        );
      }
      return data;
    } catch (error) {
      this.logger.error(error);
    }
  }

  async getClubCategoriesTypes(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubCategoryType?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      clubCategoryId: item.clubCategoryId,
      type: item.type,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubCategoriesTypesQuery(query: string): Promise<any> {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubCategoryType/query?${query}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item: any) => ({
      id: item?.id,
      type: item?.type,
      name: item?.name,
      clubCategoryId: item?.clubCategoryId,
      clubCategoryName: item?.clubCategoryName,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubCategoriesTypesByCategory(categoryId: string) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubCategoryType/byCategoryId/${categoryId}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      clubCategoryId: item.clubCategoryId,
      type: item.type?.replace('Rescue', 'Hybrid'),
      sortOrder: item.sortOrder,
    }));
  }

  async getClubCategories(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubCategory?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      name: item.name,
    }));
  }

  async getModels(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/model?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      name: item.name,
      imageThumb: item.imageThumb,
      imageSmall: item.imageSmall,
      imageReg: item.imageReg,
      imageLarge: item.imageLarge,
      brandId: item.brandId,
      thirdPartyId: item.thirdPartyId,
    }));
  }

  async getClubHands(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubHand?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      description: item.description,
    }));
  }

  async getClubLofts(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubLoft?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      clubCategoryId: item.clubCategoryId,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubLoftsByCategory(categoryId: string) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubLoft/byCategoryId/${categoryId}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      clubCategoryId: item.clubCategoryId,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubShaftFlex(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubShaftFlex/query?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubShaftFlexByCategory(categoryId: string) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubShaftFlex/byCategoryId/${categoryId}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubLoftAdjustment(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/clubLoftAdjustment/query?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubShaftLength(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubShaftLength?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      isMinorAdjustment: item.isMinorAdjustment,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubShaftLengthByClubCategoryId(clubCategoryId: string, take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get(
        'app.cdmEndpoint'
      )}/api/ClubShaftLength/byCategoryId/${clubCategoryId}?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      isMinorAdjustment: item.isMinorAdjustment,
      sortOrder: item.sortOrder,
    }));
  }

  async getClubLies(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/ClubLie/query?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      value: item.value,
      sortOrder: item.sortOrder,
    }));
  }

  async getModelsByBrand(brandId: string) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/model/byBrandId/${brandId}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      name: item.name,
      imageThumb: item.imageThumb,
      imageSmall: item.imageSmall,
      imageReg: item.imageReg,
      imageLarge: item.imageLarge,
      brandId: item.brandId,
      thirdPartyId: item.thirdPartyId,
    }));
  }

  async getBrands(take: number, skip: number) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/brand?take=${take}&skip=${skip}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      clubCategoryId: item.clubCategoryId,
      name: item.name,
    }));
  }

  async getBrandsByClubCategory(categoryId: string) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/brand/byCategoryId/${categoryId}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data.map((item) => ({
      id: item.id,
      clubCategoryId: item.clubCategoryId,
      name: item.name,
    }));
  }

  async getWITB(email: string) {
    const response = await axiosInstance.get(
      `${this.config.get('app.cdmEndpoint')}/api/consumer/WITBs?email=${encodeURIComponent(email)}`,
      await this.getRequestHeaderConfigs()
    );
    return response.data;
  }

  async getConsumerOptIn(consumerId: string, regionId: string, key: string) {
    if (typeof key !== 'string') {
      throw new BadRequestException('key must be string');
    }
    const response = await axiosInstance.get(
      `${this.config.get(
        'app.cdmEndpoint'
      )}/api/consumerOptIn/get?consumerId=${consumerId}&regionId=${regionId}&key=${key}`,
      await this.getRequestHeaderConfigs()
    );

    return response.data;
  }

  async createOrUpdateConsumerOptIn(email: string, consumerId: string, regionId: string, data: SaveConsumerOptInDto) {
    const payload = {
      ...data,
      consumerId,
      regionId,
    };
    const isUserSubEmail = data.value || false;

    const response = await axiosInstance.post(
      `${this.config.get('app.cdmEndpoint')}/api/consumerOptIn/addUpdate`,
      payload,
      await this.getRequestHeaderConfigs()
    );

    this.klaviyoService.identify(email, { mytm_email_optin: data.value ? 'True' : 'False' });

    if (isUserSubEmail) {
      const user = await this.userRepo.findOne({
        where: { email },
      });
      await this.loyaltyService.addPointUser(user, FeatureKey.SIGNUP_NEWSLETTER).catch((e) => e);
    }

    return response.data;
  }

  async deleteConsumerOptIn(data: DeleteConsumerOptInDto) {
    try {
      await axiosInstance.post(
        `${this.config.get('app.cdmEndpoint')}/api/consumerOptIn/Delete`,
        data,
        await this.getRequestHeaderConfigs()
      );
      return { success: true };
    } catch (error) {
      throw new BadRequestException(error.response.data);
    }
  }

  async getConsumerByEmails(emails: string[]) {
    try {
      const response = await axiosInstance.post(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/listByEmails`,
        {
          emails,
        },
        await this.getRequestHeaderConfigs()
      );
      return response.data;
    } catch (error) {
      return null;
    }
  }

  async updatePlayService(email: string, regionId: string, playServicePreference: string) {
    const user = await this.userRepo.findOne({ where: { email } });
    try {
      const payload: any = {
        email,
        regionId: regionId || this.config.get('app.defaultRegionId'),
        systemId: this.config.get('app.systemId'),
        playServicePreference,
      };
      if (playServicePreference === PLAY_SERVICE.ARCCOS && user?.isArccosEmail) {
        payload.arccosId = email;
      }
      const res = await axiosInstance.post(
        `${this.config.get('app.cdmEndpoint')}/api/consumer/addUpdate`,
        payload,
        await this.getRequestHeaderConfigs()
      );
      return res.data;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  async postSignUpEventToDE(user, response) {
    const dePayload = { ...response.data, ...response.data.golferProfile };
    delete dePayload.consumerProfiles;
    delete dePayload.myTmPermission;
    delete dePayload.tmUserIds;
    delete dePayload.deleted;
    delete dePayload.created;
    delete dePayload.createdBy;
    delete dePayload.modified;
    delete dePayload.modifiedBy;
    delete dePayload.id;
    delete dePayload.consumerId;
    await this.deService.triggerNewAccountEvent(user.cdmUID, dePayload);
  }

  async postUserUpdateEventToDE(user, response) {
    const dePayload = { ...response.data, ...response.data.golferProfile };
    delete dePayload.consumerProfiles;
    delete dePayload.myTmPermission;
    delete dePayload.tmUserIds;
    delete dePayload.deleted;
    delete dePayload.created;
    delete dePayload.createdBy;
    delete dePayload.modified;
    delete dePayload.modifiedBy;
    delete dePayload.id;
    delete dePayload.consumerId;
    await this.deService.triggerUserUpdateEvent(user.cdmUID, dePayload);
  }

  async getEmailsBySubLevel(level: number | string, take = 1000, skip = 0) {
    try {
      const response = await axiosInstance.get(
        `${this.config.get(
          'app.cdmEndpoint'
        )}/api/consumer/get-emails-by-sublevel?level=${level}&take=${take}&skip=${skip}`,
        await this.getRequestHeaderConfigs()
      );
      return response.data;
    } catch (error) {
      return null;
    }
  }

  static transformAccount(uid, account: any, forceUserToProPlusPlan = false) {
    let userCountryData = {};
    if (account.userCountry) {
      userCountryData = { userCountry: account.userCountry };
    }
    return {
      ...{
        ...userCountryData,
        id: uid,
        cdmUID: account.id,
        email: account.primaryEmail,
        loyaltyTier: account.loyaltyTier,
        myTMSubscriptionLevel: forceUserToProPlusPlan ? 2 : account.myTMSubscriptionLevel,
        subscriptionService: account.subscriptionService,
        dob: account.dob,
        firstName: account.firstName || account?.firstNameTM,
        lastName: account.lastName || account?.lastNameTM,
        userCountry: account.userCountry,
        gender: account.gender || account?.genderTM,
        measurementUnits: account.measurementUnits || null,
        acceptedTermsOn: account.acceptedTermsOn,
        acceptedPrivacyOn: account.acceptedPrivacyOn,
        tmAccessCode: account.tmAccessCode,
        avatar: account.avatar,
        emailVerified: getBoolValue(account.emailVerified),
        myTMPermission: CdmService.transformConsumerMyTMPermission(account.myTmPermission, forceUserToProPlusPlan),
        consumerProfiles: CdmService.transformConsumerProfiles(account.consumerProfiles),
        golferProfile: CdmService.transformGolferData(account?.golferProfile),
        tmUserIds: CdmService.transformTmUserIds(account.tmUserIds),
      },
    };
  }

  static transformConsumerProfiles(consumerProfiles: any[]) {
    return consumerProfiles.map((item) => {
      return {
        id: item.id,
        regionId: item.regionId,
        phoneHome: item.phoneHome,
        phoneCell: item.phoneCell,
        notes: item.notes,
        memberRank: item.memberRank,
        address: CdmService.transformConsumerProfileAddress(item.address),
        consumerOptIns: (item.consumerOptIns || [])?.map((consumerOptIn) => ({
          id: consumerOptIn.id,
          key: consumerOptIn.key,
          value: consumerOptIn.value,
        })),
      };
    });
  }

  static transformConsumerProfileAddress(address: any) {
    if (!address) {
      return null;
    }
    return {
      id: address.id,
      addressLine1: address.addressLine1,
      addressLine2: address.addressLine2,
      addressLine3: address.addressLine3,
      city: address.city,
      state: address.state,
      postalCode: address.zipCode,
      country: address.country,
    };
  }

  static transformConsumerMyTMPermission(
    myTmPermission: any,
    forceUserToProPlusPlan = false,
    isCalledBySwingIndexAndMyPermission = false
  ) {
    if (!myTmPermission) {
      return null;
    }

    if (isCalledBySwingIndexAndMyPermission) {
      return {
        id: myTmPermission.id,
        subscriptionExpirationDate: forceUserToProPlusPlan
          ? moment().add(1, 'years').toISOString()
          : myTmPermission.subscriptionExpirationDate,
        subscriptionLength: forceUserToProPlusPlan ? 12 : myTmPermission.subscriptionLength,
        canVirtualCoaching: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canVirtualCoaching),
      };
    }
    return {
      id: myTmPermission.id,
      subscriptionExpirationDate: forceUserToProPlusPlan
        ? moment().add(1, 'years').toISOString()
        : myTmPermission.subscriptionExpirationDate,
      subscriptionLength: forceUserToProPlusPlan ? 12 : myTmPermission.subscriptionLength,
      canCommunity: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canCommunity),
      canMFE: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canMFE),
      canMRP: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canMRP),
      canExclProductDrops: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canExclProductDrops),
      canPerfInsights: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canPerfInsights),
      canTryThenBuy: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canTryThenBuy),
      canCalculatedHandicap: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canCalculatedHandicap),
      canWinTourTrash: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canWinTourTrash),
      canFreeShipping: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canFreeShipping),
      canFree2DaysShipping: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canFree2DaysShipping),
      canVirtualCoaching: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canVirtualCoaching),
      canVideoInstruction: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canVideoInstruction),
      canPlayAdvancedRound: forceUserToProPlusPlan ? true : getBoolValue(myTmPermission.canPlayAdvancedRound),
    };
  }

  static transformGolferData(golferProfile: any) {
    return {
      id: golferProfile.id,
      targetScore: golferProfile.targetScore,
      timePlayingGolf: golferProfile.timePlayingGolf
        ? {
            id: golferProfile.timePlayingGolf?.id,
            value: golferProfile.timePlayingGolf?.value,
            created: golferProfile.timePlayingGolf?.created,
            modified: golferProfile.timePlayingGolf?.modified,
          }
        : null,
      roundsPerMonth: golferProfile.roundsPerMonth,
      maximumDriverDistance: golferProfile.maximumDriverDistance,
      strongestArea: golferProfile.strongestArea,
      weakestArea: golferProfile.weakestArea,
      mostScaredShot: golferProfile.mostScaredShot,
      shotShape: golferProfile.shotShape,
      ballStyle: golferProfile.ballStyle,
      ballStrike: golferProfile.ballStrike,
      averageScore: 72,
      newHandicap: CdmService.transformGolferNewHandicap(golferProfile.newHandicap),
      measurementUnits: golferProfile.measurementUnits,
      height: golferProfile.height,
      handicap: golferProfile.handicap,
      handed: golferProfile.handed,
      avoidShot: golferProfile.avoidShot,
      averageScoreRange: golferProfile.averageScoreRange,
      strokesGainedBaseline: golferProfile.strokesGainedBaseline,
      homeCourse: golferProfile.homeCourse,
      iGolfCourseId: golferProfile.iGolfCourseId,
      favoriteTeamMembers: golferProfile.favoriteTeamMember,
      ghinHandicap: golferProfile.ghinHandicap,
      ghinNumber: golferProfile.ghinNumber,
      misHit: golferProfile.misHit,
      driverLoftId: golferProfile.driverLoftId,
      currentDriverFlexId: golferProfile.currentDriverFlexId,
      currentDriverBallFlight: golferProfile.currentDriverBallFlight,
      desiredDriverBallFlight: golferProfile.desiredDriverBallFlight,
      typicalDriverDistance: golferProfile.typicalDriverDistance,
      currentIronLengthId: golferProfile.currentIronLengthId,
      current7IronBallFlight: golferProfile.current7IronBallFlight,
      desired7IronBallFlight: golferProfile.desired7IronBallFlight,
      typical7IronDistance: golferProfile.typical7IronDistance,
    };
  }

  static transformGolferNewHandicap(handicap) {
    if (!handicap) {
      return null;
    }
    return {
      handicapPreference: handicap.handicapPreference,
      userInputHandicap: handicap.userInputHandicap,
      tmCalculatedHandicap: handicap.tmCalculatedHandicap,
    };
  }

  async getUserOnboardingSteps(userId: string) {
    const onboardingSteps = await this.userOnboardingStepRepo.findOne({ userId });
    return {
      firstName: !!onboardingSteps?.firstName,
      lastName: !!onboardingSteps?.lastName,
      gender: !!onboardingSteps?.gender,
      dob: !!onboardingSteps?.dob,
      height: !!onboardingSteps?.height,
      handed: !!onboardingSteps?.handed,
      typical7IronDistance: !!onboardingSteps?.typical7IronDistance,
      measurement: !!onboardingSteps?.measurement,
      timePlayingGolf: !!onboardingSteps?.timePlayingGolf,
      rpmComplete: !!onboardingSteps?.rpmComplete,
      targetScoreComplete: !!onboardingSteps?.targetScoreComplete,
      maximumDriverDistance: !!onboardingSteps?.maximumDriverDistance,
      strongestArea: !!onboardingSteps?.strongestArea,
      weakestArea: !!onboardingSteps?.weakestArea,
      shotShape: !!onboardingSteps?.shotShape,
      ballStrike: !!onboardingSteps?.ballStrike,
      avoidShot: !!onboardingSteps?.avoidShot,
      averageScoreRange: !!onboardingSteps?.averageScoreRange,
      mostScaredShot: !!onboardingSteps?.mostScaredShot,
      misHit: !!onboardingSteps?.misHit,
      homeCourse: !!onboardingSteps?.homeCourse,
      handicapPreference: !!onboardingSteps?.handicapPreference,
      userInputHandicap: !!onboardingSteps?.userInputHandicap,
      favoriteTeamMembers: !!onboardingSteps?.favoriteTeamMembers,
    };
  }

  public getPermissionListByPlan(planLevel: number) {
    switch (planLevel) {
      case 2:
        return {
          canCalculatedHandicap: true,
          canMFE: true,
          canMRP: true,
          canVideoInstruction: true,
          canFree2DaysShipping: true,
          canFreeShipping: false,
          canCommunity: true,
          canWinTourTrash: true,
          canPerfInsights: true,
          canTryThenBuy: true,
          canExclProductDrops: true,
          canVirtualCoaching: true,
          canPlayAdvancedRound: true,
        };
      case 1:
        return {
          canCalculatedHandicap: true,
          canMFE: true,
          canMRP: true,
          canVideoInstruction: true,
          canFree2DaysShipping: true,
          canFreeShipping: false,
          canCommunity: true,
          canWinTourTrash: true,
          canPerfInsights: true,
          canTryThenBuy: true,
          canExclProductDrops: true,
          canVirtualCoaching: true,
          canPlayAdvancedRound: true,
        };
      default:
        return {
          canCalculatedHandicap: true,
          canMFE: true,
          canMRP: true,
          canVideoInstruction: false,
          canFree2DaysShipping: false,
          canFreeShipping: true,
          canCommunity: false,
          canWinTourTrash: false,
          canPerfInsights: false,
          canTryThenBuy: false,
          canExclProductDrops: false,
          canVirtualCoaching: false,
          canPlayAdvancedRound: false,
        };
    }
  }

  static transformTmUserIds(tmUserIds: any) {
    if (!tmUserIds) {
      return null;
    }

    return {
      id: tmUserIds.id,
      consumerId: tmUserIds.consumerId,
      myTM: tmUserIds.myTM,
      eCom: tmUserIds.eCom,
      mfe: tmUserIds.mfe,
      mrp: tmUserIds.mrp,
      auth0Id: tmUserIds.auth0Id,
      community: tmUserIds.community,
      playServicePreference: tmUserIds?.playServicePreference || PLAY_SERVICE.MYTMOC,
      arccosId: tmUserIds?.arccosId,
    };
  }

  static compareTermsAndPrivacyDateFromTosppCdm(account: any, tospp: any) {
    let acceptTermsRequired;
    let acceptPrivacyRequired;

    const acceptedTermsOn = account.acceptedTermsOn;
    const acceptedPrivacyOn = account.acceptedPrivacyOn;
    const lastUpdatedTermsDate = tospp.lastUpdatedTermsDate;
    const lastUpdatedPrivacyDate = tospp.lastUpdatedPrivacyDate;

    if (!acceptedTermsOn || acceptedTermsOn < lastUpdatedTermsDate) {
      acceptTermsRequired = true;
    } else if (
      !lastUpdatedTermsDate ||
      (!acceptedTermsOn && !lastUpdatedTermsDate) ||
      acceptedTermsOn >= lastUpdatedTermsDate
    ) {
      acceptTermsRequired = false;
    }

    if (!acceptedPrivacyOn || acceptedPrivacyOn < lastUpdatedPrivacyDate) {
      acceptPrivacyRequired = true;
    } else if (
      !lastUpdatedPrivacyDate ||
      (!acceptedPrivacyOn && !lastUpdatedPrivacyDate) ||
      acceptedPrivacyOn >= lastUpdatedPrivacyDate
    ) {
      acceptPrivacyRequired = false;
    }

    return {
      acceptTermsRequired,
      acceptPrivacyRequired,
    };
  }

  forceUserToProPlusPlan(email: string) {
    if (this.config.get('app.disableForceProPlusPlanForEmails').indexOf(email) > -1) {
      return false;
    }
    if (this.config.get('app.forceProPlusPlanForAllUsers') === 'enabled') {
      return true;
    }
    return this.config.get('app.forceProPlusPlanForEmails').indexOf(email) > -1;
  }

  cacheUserPermissionsWhenPurchase(userId: string, level: number, permissions: any) {
    return this.cacheManager.set(`${USER_PERMISSIONS_CACHE_PREFIX}${userId}`, JSON.stringify({ level, permissions }), {
      ttl: 3600,
    });
  }

  async getCacheUserPermissionsWhenPurchase(userId: string) {
    const result: string = await this.cacheManager.get(`${USER_PERMISSIONS_CACHE_PREFIX}${userId}`);
    if (result) {
      const parsedResult = JSON.parse(result);
      return {
        myTMSubscriptionLevel: parsedResult.level,
        myTMPermission: parsedResult.permissions,
      };
    }
    return null;
  }

  clearCacheUserPermissionsWhenPurchase(userId: string) {
    return this.cacheManager.del(`${USER_PERMISSIONS_CACHE_PREFIX}${userId}`);
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleRefreshCdmAuthToken(force = false) {
    if (!isCronJobHandlersEnabled(this.config) && !force) {
      return;
    }
    const response = await axiosInstance.post(`${this.config.get('app.cdmEndpoint')}/api/AuthUser`, {
      username: this.config.get('app.cdmUsername'),
      password: this.config.get('app.cdmPassword'),
    });
    await this.cacheManager.set(CDM_AUTH_TOKEN_CACHE_KEY, response.data?.token, {
      ttl: 0,
    });
    return response.data?.token;
  }

  profileUpdate(profileUpdate, data: any) {
    if (data?.dob) {
      return {
        ...profileUpdate,
        dob: data.dob,
      };
    }
    return profileUpdate;
  }

  async identifyMyTMOptIn(email: string, user: UserEntity) {
    if (user) {
      const responseConsumerOptIn = await this.getConsumerOptIn(
        user?.cdmUID,
        user?.regionId || this.config.get('app.defaultRegionId'),
        'NEWS_TAYLORMADE'
      );
      let otpIn = 'True';
      if (responseConsumerOptIn?.value === 'false' || !responseConsumerOptIn?.value) {
        otpIn = 'False';
      }
      if (responseConsumerOptIn?.value != '' && responseConsumerOptIn?.value != null) {
        this.klaviyoService.identify(email, { mytm_email_optin: otpIn });
      }

      if (otpIn && otpIn === 'True') {
        await this.loyaltyService.addPointUser(user, FeatureKey.SIGNUP_NEWSLETTER).catch((e) => e);
      }
    }
  }

  getDobOffset(dob: string) {
    const offset = new Date(dob).getTimezoneOffset();
    const diff = moment(dob).diff(moment(moment(dob).set({ hour: 0, minute: 0, second: 0, millisecond: 0 })), 'minute');

    return diff - offset;
  }

  convertDobWithLocalTz(dob: string, offset?: number) {
    if (_.isNil(offset) || dob.includes('Z')) return dob;
    return moment(moment(dob).add(offset, 'minute')).toISOString();
  }

  transformUserInputHandicap(averageScoreRange: AVERAGE_SCORE_RANGE) {
    switch (averageScoreRange) {
      case AVERAGE_SCORE_RANGE.RANGE_1:
        return 0;
      case AVERAGE_SCORE_RANGE.RANGE_2:
        return 4;
      case AVERAGE_SCORE_RANGE.RANGE_3:
        return 8;
      case AVERAGE_SCORE_RANGE.RANGE_4:
        return 12;
      case AVERAGE_SCORE_RANGE.RANGE_5:
        return 16;
      case AVERAGE_SCORE_RANGE.RANGE_6:
        return 20;
      case AVERAGE_SCORE_RANGE.RANGE_7:
        return 24;
      default:
        return null;
    }
  }
  async handleUpdateSubscriptionService(users: any[]) {
    if (users && users.length > 0) {
      for (const user of users) {
        try {
          const regionId = user.regionId ?? this.config.get('app.defaultRegionId');
          let subscriptionService = '';
          if (user.fromAccessCode) {
            subscriptionService = SUBSCRIPTION_SERVICES.MyTaylorMade;
          } else {
            const { platform } = user;
            switch (platform.toLowerCase()) {
              case 'ios':
                subscriptionService = SUBSCRIPTION_SERVICES.APPLE;
                break;
              case 'android':
                subscriptionService = SUBSCRIPTION_SERVICES.GOOGLE;
                break;
              case 'charge_bee':
                subscriptionService = SUBSCRIPTION_SERVICES.CHARGE_BEE;
                break;
            }
          }
          console.log(`Update user: ${user.email} ${subscriptionService}`);
          await this.updateAccount(user.email, regionId, { subscriptionService });
          console.log(`Update SubsService user: ${user.email} success...`);
          console.log(`Delay 0.2s ...`);
          await new Promise((resolve) => setTimeout(resolve, 200));
        } catch (error) {
          console.error(`ERROR: Update Subscription Service ${user.email} \n ${error.message}`);
        }
      }
      console.log(`Update total ${users.length} users.`);
    }
  }

  async getLocationFromGPS(lat: number, long: number) {
    const url = `${this.config.get(
      'app.googleMapApiEndpoint'
    )}/maps/api/geocode/json?latlng=${lat}, ${long}&sensor=true&key=${this.config.get('app.googleMapApiKey')}`;
    try {
      const result = await axiosInstance.get(url);
      const { data } = result;
      const address_components = _.result(data, 'results[0].address_components', null);
      if (address_components && address_components.length) {
        let addressArr = [];
        let city = null;
        address_components.map((val: any) => {
          if (val.types && val.types.find((value) => value === 'locality')) {
            city = val?.long_name;
            addressArr.push(val?.long_name);
          }
          if (val.types && val.types.find((value) => value === 'country')) {
            addressArr.push(val?.short_name);
          }
          if (val.types && val.types.find((value) => value === 'administrative_area_level_1')) {
            addressArr.push(val?.long_name);
          }
          if (val.types && !city && val.types.find((value) => value === 'administrative_area_level_2')) {
            addressArr.push(val?.long_name);
          }
        });
        addressArr = _.compact(addressArr);
        return addressArr.join(' - ');
      }
      return null;
    } catch (error) {
      console.log(`${error.message}:${url}`);
      return null;
    }
  }

  async checkEmailCanReceiveGift(email) {
    try {
      const user = await this.userRepo.findOne({
        where: {
          email,
          is_receive_gift: true,
          total_receive_gift: 1,
        },
      });
      if (!user) {
        return false;
      }
      const userReferral = await this.userReferralRepo.count({
        where: {
          userId: user.id,
          is_active: true,
        },
      });
      console.log('userReferral', userReferral);
      if (!userReferral) {
        return false;
      }
      return true;
    } catch (err) {
      this.logger.error(`ERR func checkEmailCanReceiveGift`);
      this.logger.log(JSON.stringify(err));
    }
  }

  private getMeasurementUnitFromUserCountry(userCountry: string): string {
    const countryMeasurementUnitMap = {
      CAN: 'meters',
      GBR: 'meters',
      DEU: 'meters',
      FRA: 'meters',
      SWE: 'meters',
      AUS: 'meters',
      USA: 'yards',
    };

    return countryMeasurementUnitMap[userCountry?.toUpperCase()] || 'yards';
  }
}

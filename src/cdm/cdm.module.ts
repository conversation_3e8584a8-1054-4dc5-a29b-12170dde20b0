import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { AdminForceSyncCDMCacheService } from './../admin/admin.cache.service';
import { UserPermissionCronService } from './cdm.cron.service';
import { CDMProcessor } from './cdm.processor';

@Module({
  imports: [SharedModule],
  exports: [UserPermissionCronService],
  providers: [CDMProcessor, UserPermissionCronService, AdminForceSyncCDMCacheService],
})
export class CdmModule {}

import { Job } from 'bull';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';

export class SaveConsumerOptInDto {
  @IsNotEmpty()
  @IsString()
  key: string;

  @IsNotEmpty()
  @IsBoolean()
  value: boolean;
}

export class DeleteConsumerOptInDto {
  @IsNotEmpty()
  id: string;
}

export enum AVERAGE_SCORE_RANGE {
  RANGE_1 = 'Under 71',
  RANGE_2 = '72 - 77',
  RANGE_3 = '78 - 85',
  RANGE_4 = '86 - 90',
  RANGE_5 = '91 - 95',
  RANGE_6 = '96 - 100',
  RANGE_7 = 'Over 100',
}

export type SyncCDMJob = Job<{
  userIds: string[];
}>;

import { <PERSON>, Get, Query, Req, Res } from '@nestjs/common';
import querystring from 'querystring';
import <PERSON><PERSON>Parser from 'set-cookie-parser';
import { BaseRequest } from '../types/core';
import { EbsService } from './ebs.service';

@Controller('ebs')
export class EbsController {
  constructor(private ebsService: EbsService) {}

  @Get('configure/initialize')
  async initialize(
    @Req() request: BaseRequest,
    @Query() query,
    @Res({ passthrough: true }) response: any
  ): Promise<any> {
    const res = await this.ebsService.configureInitialize(querystring.stringify(query));
    const cookies = CookieParser.parse(res, {
      decodeValues: true,
    });
    cookies.forEach((cookie) => {
      response.cookie(cookie.name, cookie.value);
    });
    return res.data;
  }

  @Get('configure/configure')
  async configure(@Req() request: BaseRequest, @Query() query): Promise<any> {
    return await this.ebsService.configure(querystring.stringify(query), request.headers['cookie']);
  }

  @Get('configure/preview')
  async preview(@Req() request: BaseRequest): Promise<any> {
    return await this.ebsService.preview(request.headers['cookie']);
  }

  @Get('configure/save')
  async save(@Req() request: BaseRequest): Promise<any> {
    return await this.ebsService.save(request.headers['cookie']);
  }

  @Get('configure/v2/initialize')
  async initializeV2(
    @Req() request: BaseRequest,
    @Query() query,
    @Res({ passthrough: true }) response: any
  ): Promise<any> {
    const res = await this.ebsService.configureInitializeV2(querystring.stringify(query));
    const cookies = CookieParser.parse(res, {
      decodeValues: true,
    });
    cookies.forEach((cookie) => {
      response.cookie(cookie.name, cookie.value);
    });
    return res.data;
  }

  @Get('configure/v2/configure')
  async configureV2(@Req() request: BaseRequest, @Query() query): Promise<any> {
    return await this.ebsService.configureV2(querystring.stringify(query), request.headers['cookie']);
  }

  @Get('configure/v2/preview')
  async previewV2(@Req() request: BaseRequest): Promise<any> {
    return await this.ebsService.previewV2(request.headers['cookie']);
  }

  @Get('configure/v2/save')
  async saveV2(@Req() request: BaseRequest): Promise<any> {
    return await this.ebsService.saveV2(request.headers['cookie']);
  }
}

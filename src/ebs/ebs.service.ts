import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { XMLBuilder, XMLParser } from 'fast-xml-parser';
import { ConfigService } from 'nestjs-config';

@Injectable()
export class EbsService {
  retriesCount = 0;
  private readonly logger = new Logger(EbsService.name);

  constructor(private readonly config: ConfigService) {
    this.config = config;
  }

  async configureInitialize(query) {
    try {
      this.logger.log(`EBS Initial: ${this.config.get('app.ebsConfigureEndpoint')}/initialize?${query}`);
      const response = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/initialize?${query}`);
      if (response.status === 200) {
        this.retriesCount = 0;
      }
      if (response.data) {
        const parser = new XMLParser();
        const jObj = parser.parse(response.data);
        if (
          jObj['config-model'] &&
          jObj['config-model']['model-nodes'] &&
          jObj['config-model']['model-nodes']['node'] &&
          jObj['config-model']['model-nodes']['node'][1] &&
          jObj['config-model']['model-nodes']['node'][1]['options'] &&
          jObj['config-model']['model-nodes']['node'][1]['options']['option'] &&
          jObj['config-model']['model-nodes']['node'][1]['options']['option'].length > 0
        ) {
          jObj['config-model']['model-nodes']['node'][1]['options']['option'] = jObj['config-model']['model-nodes'][
            'node'
          ][1]['options']['option']
            .filter((option) => option.name.indexOf('Shaft') < 0)
            .filter((option) => option.name.indexOf('MyStealth') < 0);
          const builder = new XMLBuilder({ format: true });
          response.data = builder
            .build(jObj)
            .replace('<?xml></?xml>', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>');
        }
      }
      return response;
    } catch (e) {
      console.log(e);
      if (this.retriesCount === 5) {
        this.retriesCount = 0;
        return { data: null, headers: {}, status: 404 };
      }
      await new Promise((resolve) => setTimeout(resolve, 1000));
      this.retriesCount += 1;
      console.log(`Retrying: GET ${this.config.get('app.ebsConfigureEndpoint')}/initialize?${query}`);
      return this.configureInitialize(query);
    }
  }

  async configure(query, cookie) {
    const { data } = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/configure?${query}`, {
      headers: { Cookie: cookie },
    });
    return data;
  }

  async preview(cookie) {
    const { data } = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/preview`, {
      headers: { Cookie: cookie },
    });
    return data;
  }

  async save(cookie) {
    const { data } = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/save`, {
      headers: { Cookie: cookie },
    });
    return data;
  }

  async configureInitializeV2(query) {
    try {
      this.logger.log(
        `EBS configureInitializeV2: ${this.config.get('app.ebsConfigureEndpoint')}/v2/initialize?${query}`
      );
      const response = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/v2/initialize?${query}`);

      if (response.data) {
        response.data = this.ignoreConfigModels(response.data);
        if (response.data == false) {
          throw new BadRequestException();
        } else {
          this.retriesCount = 0;
        }
      }
      return response;
    } catch (e) {
      console.log(e);
      if (this.retriesCount === 5) {
        this.retriesCount = 0;
        return { data: null, headers: {}, status: 404 };
      }
      await new Promise((resolve) => setTimeout(resolve, 1000));
      this.retriesCount += 1;
      console.log(`Retrying: GET ${this.config.get('app.ebsConfigureEndpoint')}/v2/initialize?${query}`);
      return await this.configureInitializeV2(query);
    }
  }

  async configureV2(query, cookie) {
    this.logger.log(`EBS configure v2: ${this.config.get('app.ebsConfigureEndpoint')}/v2/configure?${query}`);
    let { data } = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/v2/configure?${query}`, {
      headers: { Cookie: cookie },
    });
    data = this.ignoreConfigModels(data);
    return data;
  }
  ignoreConfigModels(data: any) {
    try {
      if (data) {
        const parser = new XMLParser();
        const jObj = parser.parse(data);
        if (this.configModelHasError(jObj)) {
          console.log(`Has error model config...`);
          return false;
        }
        const headModel = jObj['config-model']['model-nodes']['node'].find((node) => node['desc'] == 'head_model');
        if (headModel) {
          headModel['options']['option'] = headModel['options']['option']
            .filter((option) => option.name.indexOf('Shaft') < 0)
            .filter((option) => option.name.indexOf('BRNR') < 0)
            .filter((option) => option.name.indexOf('Red Bull') < 0)
            .filter((option) => option.name.indexOf('MyStealth') < 0);
          const builder = new XMLBuilder({ format: true });
          data = builder
            .build(jObj)
            .replace('<?xml></?xml>', '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>');
        }
      }
    } catch (error) {
      console.log(error);
    }
    return data;
  }
  configModelHasError(configModel: any) {
    return configModel['config-model']['messageCode'] || configModel['config-model']['errorTrace'];
  }
  async previewV2(cookie) {
    this.logger.log(`EBS previewV2: ${this.config.get('app.ebsConfigureEndpoint')}/v2/preview`);
    const { data } = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/v2/preview`, {
      headers: { Cookie: cookie },
    });
    return data;
  }

  async saveV2(cookie) {
    this.logger.log(`EBS saveV2: ${this.config.get('app.ebsConfigureEndpoint')}/v2/save`);
    const { data } = await axios.get(`${this.config.get('app.ebsConfigureEndpoint')}/v2/save`, {
      headers: { Cookie: cookie },
    });
    return data;
  }
}

import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { DeService } from '../content/de.service';
import { LoggingService } from '../logging/logging.service';
import { MfeService } from '../mfe/mfe.service';
import { ERROR_CODES } from '../utils/errors';
import {
  DesiredDriverShotShape,
  DriverBallFlight,
  Gender,
  Handed,
  LaunchMonitorDataDTO,
  TypicalDriverData,
  TypicalDriverShotShape,
} from './recommendation.type';

@Injectable()
export class RecommendationService {
  constructor(
    private readonly config: ConfigService,
    private readonly deService: DeService,
    private readonly mfeService: MfeService,
    private readonly loggingService: LoggingService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {
    this.config = config;
  }

  async getLaunchMonitorData(email: string, clubType: string) {
    try {
      const launchMonitorData = [];
      const latestRecommendation = await this.mfeService.getLatestRecommendation(email);
      const customClubs = latestRecommendation?.customClubs[clubType]?.filter(
        (club) => club.recommended && club.hasShots
      );
      if (customClubs?.length > 0) {
        const clubConfigIds = customClubs.map((club) => club.originalClubConfigId);
        const shotDataPromises = clubConfigIds.map(
          (clubConfigId) => new Promise(async (resolve) => resolve(await this.mfeService.getShotData(clubConfigId)))
        );
        const shotsData: any = await Promise.all(shotDataPromises);
        customClubs.forEach((club, index) => {
          launchMonitorData.push(shotsData[index]?.filter((shot) => shot.average));
        });
        return launchMonitorData;
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  async getClubRecommendationResults(payload: LaunchMonitorDataDTO, fromHealthCheck?: boolean) {
    let rhs = [];
    if (payload.gender === Gender.MALE) {
      rhs.push(1);
    } else {
      rhs.push(2);
    }
    if (payload.handed === Handed.RIGHT) {
      rhs.push(1);
    } else {
      rhs.push(2);
    }
    if (payload.handicap) {
      rhs.push(parseFloat(payload.handicap));
    } else {
      rhs.push({
        mwdata: 'NaN',
      });
    }
    rhs.push(parseInt(payload.age, 10));
    rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.height);
    if (payload.typicalDriverData === TypicalDriverData.YES) {
      rhs.push(1);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.ballSpeed);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.launchAngle);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.backspin);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.sidespin);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.deviationAngle);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.peakHeight);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.clubHeadSpeed);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.angleOfAttack);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.clubPath);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.clubLieAtImpact);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.clubLoftAtImpact);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.faceAngleToTargetLine);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.faceAngleRelativeToClubPath);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.toeHeelImpactLocation);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.highLowImpactLocation);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithDesiredDriverShotShape(rhs, payload.desiredDriverShotShape);
    }
    if (payload.typicalDriverData === TypicalDriverData.NO) {
      rhs.push(2);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, payload.carryYour7Iron);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithTypicalDriverShotShape(rhs, payload.typicalDriverShotShape);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithDriverBallFlight(rhs, payload.driverBallFlight);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
      rhs = this.buildRHSPayloadWithDesiredDriverShotShape(rhs, payload.desiredDriverShotShape);
      rhs = this.buildRHSPayloadWithFloatValue(rhs, null);
    }
    //Region code
    rhs.push(1);
    // 1 = TM, 2 = CA, 3 = EU
    const clubRecommenderEndpoint = `${this.config.get('app.clubRecommenderEndpoint')}/RL_FCD_myTM/RL_FCD_myTM`;
    const fetchRecommender = () => {
      return axios.post(clubRecommenderEndpoint, {
        nargout: 1,
        rhs: [rhs],
        outputFormat: {
          mode: 'small',
        },
      });
    };
    try {
      let response = await fetchRecommender();
      if (!response.data?.lhs) {
        response = await fetchRecommender();
      }
      if (!response.data?.lhs) {
        response = await fetchRecommender();
      }
      if (!response.data?.lhs) {
        response = await fetchRecommender();
      }
      if (!fromHealthCheck) {
        await this.loggingService.save({
          event: `CLUB_RECOMMENDER:RESULTS`,
          data: {
            payload,
            nargout: 1,
            rhs: [rhs],
            outputFormat: {
              mode: 'small',
            },
          },
          endpoint: clubRecommenderEndpoint,
          responseStatusCode: response.status,
        });
        return response.data;
      }
      return response;
    } catch (error) {
      console.log(error);
      return {
        error: {
          code: ERROR_CODES.RECOMMENDATION_CLUB_RESULTS_NOT_FOUND,
          message: 'Recommendation club not found!',
        },
      };
    }
  }

  buildRHSPayloadWithFloatValue(rhs, value) {
    if (value) {
      rhs.push(parseFloat(value));
    } else {
      rhs.push({
        mwdata: 'NaN',
      });
    }
    return rhs;
  }

  buildRHSPayloadWithDesiredDriverShotShape(rhs, value) {
    if (!value) {
      rhs.push({
        mwdata: 'NaN',
      });
      return;
    }
    if (value === DesiredDriverShotShape.DRAW) {
      rhs.push(1);
    }
    if (value === DesiredDriverShotShape.SMALL_DRAW) {
      rhs.push(2);
    }
    if (value === DesiredDriverShotShape.STRAIGHT) {
      rhs.push(3);
    }
    if (value === DesiredDriverShotShape.SMALL_FADE) {
      rhs.push(4);
    }
    if (value === DesiredDriverShotShape.FADE) {
      rhs.push(5);
    }
    return rhs;
  }

  buildRHSPayloadWithDriverBallFlight(rhs, value) {
    if (!value) {
      rhs.push({
        mwdata: 'NaN',
      });
      return;
    }
    if (value === DriverBallFlight.LOWER) {
      rhs.push(1);
    }
    if (value === DriverBallFlight.LOW) {
      rhs.push(2);
    }
    if (value === DriverBallFlight.MID) {
      rhs.push(3);
    }
    if (value === DriverBallFlight.HIGH) {
      rhs.push(4);
    }
    if (value === DriverBallFlight.HIGHER) {
      rhs.push(5);
    }
    return rhs;
  }

  buildRHSPayloadWithTypicalDriverShotShape(rhs, value) {
    if (!value) {
      rhs.push({
        mwdata: 'NaN',
      });
      return;
    }
    if (value === TypicalDriverShotShape.HOOK) {
      rhs.push(1);
    }
    if (value === TypicalDriverShotShape.DRAW) {
      rhs.push(2);
    }
    if (value === TypicalDriverShotShape.STRAIGHT) {
      rhs.push(3);
    }
    if (value === TypicalDriverShotShape.FADE) {
      rhs.push(4);
    }
    if (value === TypicalDriverShotShape.SLICE) {
      rhs.push(5);
    }
    return rhs;
  }
}

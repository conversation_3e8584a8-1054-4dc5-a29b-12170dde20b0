import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
}

export enum Handed {
  RIGHT = 'right',
  LEFT = 'left',
}

export enum TypicalDriverData {
  YES = 'yes',
  NO = 'no',
}

export enum DriverBallFlight {
  HIGHER = 'higher',
  HIGH = 'high',
  MID = 'mid',
  LOW = 'low',
  LOWER = 'lower',
}

export enum TypicalDriverShotShape {
  DRAW = 'draw',
  STRAIGHT = 'straight',
  FADE = 'fade',
  HOOK = 'hook',
  SLICE = 'slice',
}

export enum DesiredDriverShotShape {
  DRAW = 'draw',
  SMALL_DRAW = 'small_draw',
  STRAIGHT = 'straight',
  SMALL_FADE = 'small_fade',
  FADE = 'fade',
}

export class LaunchMonitorDataDTO {
  @IsNotEmpty()
  @IsNumber()
  clubTypeId: number;

  @IsNotEmpty()
  @IsEnum(Gender)
  gender: string;

  @IsNotEmpty()
  @IsString()
  age: string;

  @IsNotEmpty()
  @IsEnum(Handed)
  handed: string;

  @IsNotEmpty()
  @IsString()
  handicap: string;

  @IsOptional()
  height: string;

  @IsNotEmpty()
  @IsEnum(TypicalDriverData)
  typicalDriverData: string;

  @IsOptional()
  @IsString()
  ballSpeed: string;

  @IsOptional()
  @IsString()
  launchAngle: string;

  @IsOptional()
  @IsString()
  backspin: string;

  @IsOptional()
  @IsString()
  sidespin: string;

  @IsOptional()
  @IsString()
  deviationAngle: string;

  @IsOptional()
  @IsString()
  peakHeight: string;

  @IsOptional()
  @IsString()
  clubHeadSpeed: string;

  @IsOptional()
  @IsString()
  angleOfAttack: string;

  @IsOptional()
  @IsString()
  clubPath: string;

  @IsOptional()
  @IsString()
  clubLieAtImpact: string;

  @IsOptional()
  @IsString()
  clubLoftAtImpact: string;

  @IsOptional()
  @IsString()
  faceAngleToTargetLine: string;

  @IsOptional()
  @IsString()
  faceAngleRelativeToClubPath: string;

  @IsOptional()
  @IsString()
  toeHeelImpactLocation: string;

  @IsOptional()
  @IsString()
  highLowImpactLocation: string;

  @IsOptional()
  @IsEnum(DesiredDriverShotShape)
  desiredDriverShotShape: string;

  @IsString()
  @IsOptional()
  carryYour7Iron: string;

  @IsEnum(DriverBallFlight)
  @IsOptional()
  driverBallFlight: string;

  @IsEnum(TypicalDriverShotShape)
  @IsOptional()
  typicalDriverShotShape: string;
}

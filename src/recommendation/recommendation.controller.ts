import { BadRequestException, Body, Controller, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../guards/auth.guard';
import { ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { RecommendationService } from './recommendation.service';
import { LaunchMonitorDataDTO } from './recommendation.type';

@Controller('recommendation')
export class RecommendationController {
  constructor(private recommendationService: RecommendationService) {}

  @Get('club/launch-monitor/:clubType')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getLatestRecommendation(@Req() request: BaseRequest, @Param() params): Promise<any> {
    return await this.recommendationService.getLaunchMonitorData(request.user.email, params.clubType);
  }

  @Post('club/results')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getClubRecommendationResults(
    @Req() request: BaseRequest,
    @Body() launchMonitorData: LaunchMonitorDataDTO
  ): Promise<any> {
    const result = await this.recommendationService.getClubRecommendationResults(launchMonitorData);
    if (!result.error) {
      return result;
    }
    if (result.error) {
      throw new BadRequestException({
        internalErrorCode: result.error.code,
        errorMessage: result.error.message,
      });
    }
  }
}

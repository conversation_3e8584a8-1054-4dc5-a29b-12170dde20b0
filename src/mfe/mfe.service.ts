import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { DeService } from '../content/de.service';
import { CompleteFittingDTO } from './mfe.type';

@Injectable()
export class MfeService {
  constructor(
    private readonly config: ConfigService,
    private readonly deService: DeService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {
    this.config = config;
  }

  getRequestHeaderConfigs() {
    return {
      headers: {
        'api-key': `${this.config.get('app.mfeApiKey')}`,
        'api-username': `${this.config.get('app.mfeApiUserName')}`,
      },
    };
  }

  async getLatestRecommendation(userEmail: string) {
    try {
      const querystring = `golferEmail=${userEmail}&businessUnitCode=TM&eventTypes=PUBLIC,VIRTUAL&fittingInfo=true`;
      const response = await axios.get(
        `${this.config.get('app.mfeEndpoint')}/api/v2/recommendations/latest?${querystring}`,
        this.getRequestHeaderConfigs()
      );
      return response.data || {};
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        errorMessage: 'Email is not existed on mFE system',
      });
    }
  }

  async getHeadModels() {
    const querystring = `businessUnitCode=TM&clubTypeId=1&brandId=1`;
    const response = await axios.get(
      `${this.config.get('app.mfeEndpoint')}/api/v2/headModels?${querystring}`,
      this.getRequestHeaderConfigs()
    );
    return response.data || [];
  }

  async getShaftModelImages() {
    const querystring = `businessUnitCode=TM`;
    const response = await axios.get(
      `${this.config.get('app.mfeEndpoint')}/api/v2/shaftModelImages?${querystring}`,
      this.getRequestHeaderConfigs()
    );
    return response.data || [];
  }

  async getGripModelImages() {
    const querystring = `businessUnitCode=TM`;
    const response = await axios.get(
      `${this.config.get('app.mfeEndpoint')}/api/v2/gripModelImages?${querystring}`,
      this.getRequestHeaderConfigs()
    );
    return response.data || [];
  }

  async getPastFittings(userEmail: string) {
    try {
      const currentDateString = moment().utc().format('YYYY-MM-DD');
      const querystring = `completed=true&consumerUserEmail=${userEmail}&toDate=${currentDateString}&includeLocation=true`;

      const response = await axios.get(
        `${this.config.get('app.mfeEndpoint')}/api/v2/fittings?${querystring}`,
        this.getRequestHeaderConfigs()
      );

      const getFitterPromises = [];
      response.data.map((item) => {
        getFitterPromises.push(
          new Promise(async (resolve) => {
            resolve(await this.getFitter(item.fitterUserId));
          })
        );
      });
      const fitters = await Promise.all(getFitterPromises);

      return (
        response.data?.map((item, index) => ({
          ...item,
          fitter: {
            firstName: fitters[index]?.firstName,
            lastName: fitters[index]?.lastName,
            email: fitters[index]?.email,
          },
        })) || []
      );
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        errorMessage: 'Email is not existed on mFE system',
      });
    }
  }

  async getFitting(fittingId: string, query: string) {
    try {
      const response = await axios.get(
        `${this.config.get('app.mfeEndpoint')}/api/v2/fittings/${fittingId}?${query}`,
        this.getRequestHeaderConfigs()
      );

      return response.data || {};
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        errorMessage: `Can not get fitting with id ${fittingId}`,
      });
    }
  }

  async getComingFittings(userEmail: string) {
    try {
      const currentDateString = moment().utc().format('YYYY-MM-DD');
      const querystring = `completed=false&consumerUserEmail=${userEmail}&fromDate=${currentDateString}&includeLocation=true`;

      const response = await axios.get(
        `${this.config.get('app.mfeEndpoint')}/api/v2/fittings?${querystring}`,
        this.getRequestHeaderConfigs()
      );

      const getFitterPromises = [];
      response.data.map((item) => {
        getFitterPromises.push(
          new Promise(async (resolve) => {
            resolve(await this.getFitter(item.fitterUserId));
          })
        );
      });
      const fitters = await Promise.all(getFitterPromises);

      return (
        response.data?.map((item, index) => ({
          ...item,
          fitter: {
            firstName: fitters[index]?.firstName,
            lastName: fitters[index]?.lastName,
            email: fitters[index]?.email,
          },
        })) || []
      );
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        errorMessage: 'Email is not existed on mFE system',
      });
    }
  }

  async getShotData(clubConfigId: number) {
    try {
      const response = await axios.get(
        `${this.config.get('app.mfeEndpoint')}/api/v2/shots?clubConfigId=${clubConfigId}`,
        this.getRequestHeaderConfigs()
      );
      return response.data || [];
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        errorMessage: 'clubConfigId does not exist',
      });
    }
  }

  async getFitter(userId: number) {
    try {
      const response = await axios.get(
        `${this.config.get('app.mfeEndpoint')}/api/v2/users/${userId}`,
        this.getRequestHeaderConfigs()
      );
      return response.data || {};
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        errorMessage: 'Fitter does not exist',
      });
    }
  }

  async postCompleteFitting(payload: CompleteFittingDTO) {
    const user = await this.userRepo.findOne({ email: payload.email });
    if (!user) {
      return false;
    }
    await this.deService.triggerFittingCompleteEvent(user.cdmUID, payload.fittingID);
    return true;
  }
}

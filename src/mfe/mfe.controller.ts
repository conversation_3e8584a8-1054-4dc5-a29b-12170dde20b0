import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import querystring from 'querystring';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { MfeService } from './mfe.service';
import { CompleteFittingDTO } from './mfe.type';

@Controller('fitting')
export class MfeController {
  constructor(private mfeService: MfeService) {}

  @Get('latest-recommendations')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getLatestRecommendation(@Req() request: Request & { user: any }): Promise<any> {
    return await this.mfeService.getLatestRecommendation(request.user.email);
  }

  @Get('past')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getPastFittings(@Req() request: Request & { user: any }): Promise<any> {
    return await this.mfeService.getPastFittings(request.user.email);
  }

  @Get('detail/:fittingId')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getFitting(@Req() request: Request & { user: any }, @Param() params, @Query() query): Promise<any> {
    return await this.mfeService.getFitting(params.fittingId, querystring.stringify(query));
  }

  @Post('complete')
  @AccessedClients(CLIENTS.MFE)
  @UseGuards(ClientGuard)
  async postCompleteFitting(@Body() payload: CompleteFittingDTO): Promise<any> {
    await this.mfeService.postCompleteFitting(payload);
    return {
      success: true,
    };
  }

  @Get('coming')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getComingFittings(@Req() request: Request & { user: any }): Promise<any> {
    return await this.mfeService.getComingFittings(request.user.email);
  }

  @Get('shot-data/:clubConfigId')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getShotData(@Param('clubConfigId') clubConfigId: number): Promise<any> {
    return await this.mfeService.getShotData(clubConfigId);
  }
}

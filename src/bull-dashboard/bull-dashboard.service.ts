import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import * as _ from 'lodash';
import moment from 'moment';
import { Repository } from 'typeorm';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';

@Injectable()
export class BullDashboardService {
  private readonly logger = new Logger(BullDashboardService.name);
  constructor(
    private klaviyoService: KlaviyoService,
    @InjectRepository(UserNotifyAdminEntity) private readonly userNotifyAdminRepo: Repository<UserNotifyAdminEntity>,
    @InjectQueue('member-shop-notify') private memberShopNotifyQueue: Queue,
    @InjectQueue('ecom-shipment') private ecomShipmentQueue: Queue,
    @InjectQueue('ecom-shipment-overdue') private ecomShipmentOverdueQueue: Queue,
    @InjectQueue('ecom-delivering') private ecomDeliveringQueue: Queue,
    @InjectQueue('ecom-alert') private ecomAlertQueue: Queue,
    @InjectQueue('tourtrash') private tourTrashQueue: Queue,
    @InjectQueue('klaviyo-track') private klaviyoTrackQueue: Queue,
    @InjectQueue('notification') private notificationQueue: Queue,
    @InjectQueue('klaviyo-sync') private klaviyoSyncQueue: Queue,
    @InjectQueue('upload-s3') private uploadS3Queue: Queue,
    @InjectQueue('country-syncing') private countrySyncingQueue: Queue,
    @InjectQueue('bull-dashboard-jobs') private bullDashboardJobsQueue: Queue,
    @InjectQueue('mail-otp') private mailOtpQueue: Queue
  ) {}

  @Cron('2 01 * * *')
  async checkJobFailed() {
    const queues = [
      { name: 'member-shop-notify', queue: this.memberShopNotifyQueue },
      { name: 'ecom-shipment', queue: this.ecomShipmentQueue },
      { name: 'ecom-shipment-overdue', queue: this.ecomShipmentOverdueQueue },
      { name: 'ecom-delivering', queue: this.ecomDeliveringQueue },
      { name: 'ecom-alert', queue: this.ecomAlertQueue },
      { name: 'tourtrash', queue: this.tourTrashQueue },
      { name: 'klaviyo-track', queue: this.klaviyoTrackQueue },
      { name: 'notification', queue: this.notificationQueue },
      { name: 'klaviyo-sync', queue: this.klaviyoSyncQueue },
      { name: 'upload-s3', queue: this.uploadS3Queue },
      { name: 'country-syncing', queue: this.countrySyncingQueue },
      { name: 'mail-otp', queue: this.mailOtpQueue },
      { name: 'bull-dashboard-jobs', queue: this.bullDashboardJobsQueue },
    ];

    const failedJobsCount = {};

    for (const { name, queue } of queues) {
      const failedJobs = await queue.getFailed();
      failedJobsCount[name] = failedJobs.length;
    }
    await this.sendEmailToAdminListQueue(failedJobsCount, false);

    return failedJobsCount;
  }

  async sendEmailToAdminListQueue(jobs, isFromOC = false) {
    const userAdmins = await this.userNotifyAdminRepo.find({
      where: { is_check_jobs: true },
      relations: ['user'],
    });
    if (!userAdmins || !userAdmins.length) {
      return false;
    }
    const emailTos = _.compact(_.map(userAdmins, 'email'));
    if (emailTos && emailTos.length) {
      for (const value of emailTos) {
        try {
          const payload = {
            title: 'Daily Job Report for TM System',
            service: 'TM',
            dateTime: moment().format('MM/DD/YYYY'),
            jobs: jobs,
            linkBullDashboard: 'https://app-prd-mytaylormade-app-001.azurewebsites.net/admin/queues',
          };
          if (isFromOC) {
            payload.title = 'Daily Job Report for OC System';
            payload.service = 'OC';
            payload.linkBullDashboard = 'https://mytmoncourse.taylormadegolf.com/admin/queues';
          }

          const emailAdmin: any = value;
          await this.klaviyoService.track(emailAdmin, KlaviyoTrackEvents.CHECK_BULL_DASHBOARD_DAILY, payload);
        } catch (error) {
          this.logger.error('ERR_sendEmailToAdminListQueue:', error);
        }
      }
    }
  }
}

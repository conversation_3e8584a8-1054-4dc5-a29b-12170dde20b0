import { Body, Controller, Get, Post, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AccessedClients } from '../client/clients.decorator';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { BullDashboardService } from './bull-dashboard.service';

@Controller('bull-dashboard')
export class BullDashboardController {
  constructor(private readonly config: ConfigService, private readonly bullBoardService: BullDashboardService) {}

  @Get('check-job-tm')
  @UseGuards(ClientGuard)
  async checkJobFailed() {
    return this.bullBoardService.checkJobFailed();
  }

  @AccessedClients(CLIENTS.MRP)
  @Post('send-email-list')
  @UseGuards(ClientGuard)
  async sendEmailListQueue(@Body() payload: { jobs: any[]; isFromOC: boolean }) {
    const { jobs, isFromOC } = payload;
    return this.bullBoardService.sendEmailToAdminListQueue(jobs, isFromOC);
  }
}

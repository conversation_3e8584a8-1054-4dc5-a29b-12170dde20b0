import moment from 'moment-timezone';

export const sleeps = async (seconds = 1) => {
  return new Promise((r) => setTimeout(r, seconds * 1000));
};
export const convertSubscriptionDates = (originalDate: number) => {
  if (!originalDate) {
    return {
      pst: null,
      ect: null,
      ms: null,
    };
  }
  return {
    pst: `${moment(originalDate).tz('America/Los_Angeles').format(`YYYY-MM-DD HH:mm:ss`)} America/Los_Angeles`,
    ect: `${moment(originalDate).tz('Etc/GMT').format(`YYYY-MM-DD HH:mm:ss`)} Etc/GMT`,
    ms: `${originalDate}`,
  };
};

export const convertPstDate = (date?: Date | moment.Moment | any) => {
  if (!date) return moment().tz('America/Los_Angeles').format('MM/DD/YYYY');
  return moment(date).tz('America/Los_Angeles').toISOString();
};

export const convertSubscriptionDownloadPstDate = (subtractDays: number) => {
  return moment().tz('America/Los_Angeles').subtract(subtractDays, 'days').format('YYYY-MM-DD');
};

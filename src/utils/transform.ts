export const getBoolValue = (value) => {
  if (typeof value === 'undefined') {
    return false;
  }
  if (value === null) {
    return false;
  }
  return value;
};

export const getUpperCaseValue = (str: string) => {
  return str && typeof str === 'string' ? str.toUpperCase() : str;
};

export const isContainLetter = (str: string) => {
  const regexLetter = /[A-Za-z]/;
  return regexLetter.test(str);
};
export const isCanadaCountry = (countryCode: string) => {
  return countryCode && (countryCode.toUpperCase().trim() === 'CAN' || countryCode.toUpperCase().trim() === 'CA');
};

export const isCanOrUsaCountry = (countryCode: string) => {
  return (
    countryCode &&
    (countryCode.toUpperCase().trim() === 'CAN' ||
      countryCode.toUpperCase().trim() === 'CA' ||
      countryCode.toUpperCase().trim() === 'USA' ||
      countryCode.toUpperCase().trim() === 'US')
  );
};

export const isAllCountry = (countryCode: string) => {
  return countryCode && countryCode.toUpperCase().trim() === 'ALL';
};
export const isUSCountry = (countryCode: string) => {
  if (!countryCode) {
    return true;
  }
  return countryCode && (countryCode.toUpperCase().trim() === 'USA' || countryCode.toUpperCase().trim() === 'US');
};
export const percentRound = (ipt, precision) => {
  if (!precision) {
    precision = 0;
  }
  if (!Array.isArray(ipt)) {
    throw new Error('percentRound input should be an Array');
  }
  const iptPercents = ipt.slice();
  const length = ipt.length;
  const out = new Array(length);

  let total = 0;
  for (let i = length - 1; i >= 0; i--) {
    if (typeof iptPercents[i] === 'string') {
      iptPercents[i] = Number.parseFloat(iptPercents[i]);
    }
    total += iptPercents[i] * 1;
  }
  if (isNaN(total)) {
    throw new Error('percentRound invalid input');
  }

  if (total === 0) {
    out.fill(0);
  } else {
    const powPrecision = Math.pow(10, precision);
    const pow100 = 100 * powPrecision;
    let check100 = 0;
    for (let i = length - 1; i >= 0; i--) {
      iptPercents[i] = (100 * iptPercents[i]) / total;
      check100 += out[i] = Math.round(iptPercents[i] * powPrecision);
    }

    if (check100 !== pow100) {
      const totalDiff = check100 - pow100;
      const roundGrain = 1;
      let grainCount = Math.abs(totalDiff);
      const diffs = new Array(length);

      for (let i = 0; i < length; i++) {
        diffs[i] = Math.abs(out[i] - iptPercents[i] * powPrecision);
      }

      while (grainCount > 0) {
        let idx = 0;
        let maxDiff = diffs[0];
        for (let i = 1; i < length; i++) {
          if (maxDiff < diffs[i]) {
            // avoid negative result
            if (check100 > pow100 && out[i] - roundGrain < 0) {
              continue;
            }
            idx = i;
            maxDiff = diffs[i];
          }
        }
        if (check100 > pow100) {
          out[idx] -= roundGrain;
        } else {
          out[idx] += roundGrain;
        }
        diffs[idx] -= roundGrain;
        grainCount--;
      }
    }

    if (powPrecision > 1) {
      for (let i = 0; i < length; i++) {
        out[i] = out[i] / powPrecision;
      }
    }
  }

  return out;
};

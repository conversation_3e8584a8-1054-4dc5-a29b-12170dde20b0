import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTryThenBuyLogThresholdTable1684482472574 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.TryThenBuyLogThreshold (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        type nvarchar(255) NOT NULL,
        totalOrder int  NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TryThenBuyLogThreshold;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnSortToTableCountries1687426796925 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Countries ADD sort int NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Countries DROP COLUMN IF EXISTS sort`);
  }
}

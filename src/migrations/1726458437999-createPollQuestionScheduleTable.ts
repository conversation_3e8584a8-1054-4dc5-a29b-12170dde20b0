import { MigrationInterface, QueryRunner } from 'typeorm';

export class createPollQuestionScheduleTable1726458437999 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.PollQuestionSchedule (
              id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
              pollQuestionId nvarchar(255) NOT NULL,
              isRunActive bit NULL,
              isRunInActive bit NULL,
              activeAt datetime NULL,
              inActiveAt datetime NULL,
              createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updatedAt datetime NULL,
              deletedAt datetime NULL,
              createdBy nvarchar(50) NULL,
              updatedBy nvarchar(50) NULL,
              deletedBy nvarchar(50) NULL
            );
            CREATE INDEX idx_poll_question_id ON dbo.PollQuestionSchedule (pollQuestionId);
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.PollQuestionSchedule;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_poll_question_id ON dbo.PollQuestionSchedule;`);
  }
}

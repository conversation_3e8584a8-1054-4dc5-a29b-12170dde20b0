import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateExtUserTable1648540112515 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.ExtensionUser (
          email varchar(100) NOT NULL PRIMARY KEY,
          firstName varchar(100) NOT NULL,
          lastName varchar(100) NOT NULL,
          annexCurrentTier varchar(100) NOT NULL,
        );
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

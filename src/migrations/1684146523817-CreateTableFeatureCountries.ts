import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableFeatureCountries1684146523817 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
                  CREATE TABLE dbo.FeatureCountries (
                    id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
                    launchFeatureId nvarchar(100) NOT NULL,
                    featureType nvarchar(100) NOT NULL,
                    countryCode nvarchar(100) NULL,
                    enabled  bit  default 0,
                    createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updatedAt datetime NULL
                  );
                  CREATE INDEX uix_feature_id_column ON dbo.FeatureCountries (launchFeatureId);
                  CREATE INDEX uix_country_code_column ON dbo.FeatureCountries (countryCode);
                `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.FeatureCountries;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTourTrashTable1619285339907 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.TourTrashs (
          id int IDENTITY(1,1) NOT NULL,
          status varchar(20)  NOT NULL,
          title nvarchar(255)  NOT NULL,
          description nvarchar(255)  NOT NULL,
          startDate datetime NOT NULL,
          endDate datetime NOT NULL,
          participationLevel int NOT NULL,
          pushNotiToAll bit DEFAULT 0 NULL,
          sendEmailToAll bit DEFAULT 0 NULL,
          remindBeforeDay int NOT NULL,
          winnerUserId uniqueidentifier NULL,
          createdAt datetime DEFAULT getdate() NOT NULL,
          updatedAt datetime NULL,
          CONSTRAINT PK__TourTras__3213E83F0A505A35 PRIMARY KEY (id)
        );
        ALTER TABLE dbo.TourTrashs WITH NOCHECK ADD CONSTRAINT CK__TourTrash__statu__0CB1922D CHECK ([status]='DISABLED' OR [status]='CLOSED' OR [status]='RUNNING' OR [status]='NEW');
        ALTER TABLE dbo.TourTrashs ADD CONSTRAINT TourTrashs_FK FOREIGN KEY (winnerUserId) REFERENCES dbo.Users(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TourTrashs;`);
  }
}

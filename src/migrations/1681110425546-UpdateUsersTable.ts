import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUsersTable1681110425546 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD signUpIPAddress nvarchar(100) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD isSimulator bit DEFAULT 0;`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD fingerPrint nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD signUpLatitude float DEFAULT 0`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD signUpLongitude float DEFAULT 0`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD lastLatitude float DEFAULT 0`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD lastLongitude float DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS signUpIPAddress`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS isSimulator`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS fingerPrint`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS signUpLatitude`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS signUpLongitude`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS lastLatitude`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS lastLongitude`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableCountries1684146992983 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
                      CREATE TABLE dbo.Countries (
                        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
                        cdmRegionId nvarchar(100),
                        name nvarchar(100) NOT NULL,
                        code nvarchar(100) NOT NULL,
                        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updatedAt datetime NULL
                      );
                      CREATE INDEX uix_country_code_column ON dbo.Countries (code);
                    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Countries;`);
  }
}

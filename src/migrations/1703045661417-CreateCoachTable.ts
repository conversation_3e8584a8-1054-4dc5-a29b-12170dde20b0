import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCoachTable1703045661417 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.CoachProfiles
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            title nvarchar(255) NOT NULL,
            description text NULL,
            options nvarchar(255) NULL,
            tags nvarchar(255) NULL,
            imageLink nvarchar(255) NULL,
            videoLink nvarchar(255) NULL,
            countries nvarchar(255) NULL,
            sortOrder int DEFAULT 1,
            type nvarchar(20) NULL,
            status nvarchar(20) NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
      
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.CoachProfiles;`);
  }
}

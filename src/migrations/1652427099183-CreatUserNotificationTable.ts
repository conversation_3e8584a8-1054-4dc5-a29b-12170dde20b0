import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatUserNotificationTable1652427099183 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.UserNotification (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        isRead BIT NOT NULL DEFAULT 0,
        userId uniqueidentifier NOT NULL,
        notificationId uniqueidentifier NOT NULL,
        senderId uniqueidentifier NULL,
        hide BIT NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      ALTER TABLE dbo.UserNotification ADD CONSTRAINT UserNotification_FK FOREIGN KEY (notificationId) REFERENCES dbo.Notification(id);
      ALTER TABLE dbo.UserNotification ADD CONSTRAINT UserNotification_FK_1 FOREIGN KEY (userId) REFERENCES dbo.Users(id);
      ALTER TABLE dbo.UserNotification ADD CONSTRAINT UserNotification_FK_2 FOREIGN KEY (senderId) REFERENCES dbo.Users(id);
   
    `);
    await queryRunner.query(`
      CREATE INDEX idx_user_column ON dbo.UserNotification (userId);
      CREATE INDEX idx_sender_column ON dbo.UserNotification (senderId);
      CREATE INDEX idx_created_at_column ON dbo.UserNotification (createdAt);
      CREATE INDEX idx_updated_at_column ON dbo.UserNotification (updatedAt);
      CREATE INDEX idx_deleted_at_column ON dbo.UserNotification (deletedAt);
      CREATE INDEX idx_hide_column ON dbo.UserNotification (hide);
      CREATE INDEX idx_is_read_column ON dbo.UserNotification (isRead);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserNotification;`);
  }
}

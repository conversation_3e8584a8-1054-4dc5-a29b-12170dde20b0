import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGameProfileTable1751515664627 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.GameProfiles
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            header nvarchar(255) NOT NULL,
            subHeader text NULL,
            ctaButton nvarchar(255) NULL,
            options nvarchar(255) NULL,
            imageLink nvarchar(255) NULL,
            backgroundColor nvarchar(255) NULL,
            annexActionUsaId nvarchar(100) NULL,
            annexActionCanId nvarchar(100) NULL,
            type nvarchar(20) NULL,
            disabled bit default 1,
            thankYouHeader nvarchar(255) NULL,
            thankYouMessage nvarchar(255) NULL,
            startTime datetime NULL,
            endTime datetime NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
        )
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.GameProfiles;`);
  }
}

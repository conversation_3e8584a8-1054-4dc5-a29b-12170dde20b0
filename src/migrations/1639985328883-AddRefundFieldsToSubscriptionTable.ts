import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRefundFieldsToSubscriptionTable1639985328883 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'subtype',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'revocationDate',
        type: 'datetime',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'revocationReason',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

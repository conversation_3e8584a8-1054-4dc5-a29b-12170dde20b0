import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMeasurementUnitsFieldInUserTable1750220099655 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD measurementUnits nvarchar(50) default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS measurementUnits`);
  }
}

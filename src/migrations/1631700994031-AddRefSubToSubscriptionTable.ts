import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRefSubToSubscriptionTable1631700994031 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'refSubscriptionId',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIdUserTourTrashsTable1632632537092 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs ADD id uniqueidentifier NOT NULL DEFAULT NEWID()`);
    await queryRunner.query(
      `ALTER TABLE dbo.UserTourTrashs ADD CONSTRAINT PK__UserTour__7F427930643E57BC PRIMARY KEY (id)`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.UserTourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__UserTourTras__id%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP CONSTRAINT IF EXISTS PK__UserTour__7F427930643E57BC`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS id`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeMessageNotificationTTB1675071995540 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE Notification set variables =
        (SELECT REPLACE(variables, 'FedEx', 'UPS') FROM Notification WHERE variables like '%FedEx%'), 
        message =
        (SELECT REPLACE(message, 'FedEx', 'UPS') FROM Notification WHERE message like '%FedEx%') 
        WHERE message like '%FedEx%'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

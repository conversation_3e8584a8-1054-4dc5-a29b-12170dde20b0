import { MigrationInterface, QueryRunner } from 'typeorm';

export class Edit<PERSON>olumnHomeWidgetsTable1635819821050 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DECLARE @table nvarchar(50)
      set @table = 'dbo.HomeWidgets'
      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
        from sys.default_constraints
        where name LIKE 'DF__HomeWidge__activ%'
        exec sp_executesql @sql
      END`);

    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets DROP COLUMN IF EXISTS active`);
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets ADD status nvarchar(50) NOT NULL DEFAULT 'INACTIVE'`);
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets ADD show bit NOT NULL DEFAULT 1`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DECLARE @table nvarchar(50)
      set @table = 'dbo.HomeWidgets'
      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
        from sys.default_constraints
        where name LIKE 'DF__HomeWidge__statu%'
        exec sp_executesql @sql
      END`);
    await queryRunner.query(`
      DECLARE @table nvarchar(50)
      set @table = 'dbo.HomeWidgets'
      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
        from sys.default_constraints
        where name LIKE 'DF__HomeWidget__show%'
        exec sp_executesql @sql
      END`);
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets DROP COLUMN IF EXISTS status`);
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets DROP COLUMN IF EXISTS show`);
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets ADD active bit NOT NULL DEFAULT 1`);
  }
}

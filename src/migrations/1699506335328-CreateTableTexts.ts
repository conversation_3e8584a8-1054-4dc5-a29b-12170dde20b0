import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableTexts1699506335328 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Texts
        (
            id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            keyText              nvarchar(255) NOT NULL,
            value                nvarchar(255) NOT NULL,
            createdBy            nvarchar(255)     NULL,
            updatedBy            nvarchar(255)     NULL,
            deletedBy            nvarchar(255)     NULL,
            createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt            datetime         NULL,
            deletedAt            datetime         NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Texts;`);
  }
}

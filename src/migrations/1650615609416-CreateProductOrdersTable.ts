import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductOrdersTable1650615609416 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.ProductOrders (
          id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
          product_id nvarchar(255) NULL,
          image nvarchar(500) NULL,
          sortOrder int,
          productName nvarchar(255) NULL,
          sold_out BIT NOT NULL DEFAULT 0,
          createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt datetime NULL,
          deletedAt datetime NULL,
          createdBy nvarchar(50) NULL,
          updatedBy nvarchar(50) NULL,
          deletedBy nvarchar(50) NULL
        );
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ProductOrders;`);
  }
}

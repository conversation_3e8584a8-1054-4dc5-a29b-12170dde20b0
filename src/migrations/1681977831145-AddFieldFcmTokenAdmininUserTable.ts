import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldFcmTokenAdmininUserTable1681977831145 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD fcmTokenAdmin nvarchar(1000) NULL;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS fcmTokenAdmin`);
  }
}

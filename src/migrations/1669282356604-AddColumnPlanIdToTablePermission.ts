import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnPlanIdToTablePermission1669282356604 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Permission',
      new TableColumn({
        name: 'planId',
        type: 'int',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

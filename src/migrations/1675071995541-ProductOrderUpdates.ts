import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductOrderUpdates1675071995541 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD priceColor nvarchar(255) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD textColor nvarchar(255) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD buttonText nvarchar(255) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD buttonPosition nvarchar(255) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

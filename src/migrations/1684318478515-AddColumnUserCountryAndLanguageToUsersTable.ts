import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnUserCountryAndLanguageToUsersTable1684318478515 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD userCountry nvarchar(30) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD language nvarchar(20) NULL`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCreateUpdateDeleteStatusToAllTable1619285338777 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Users ADD createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.ViewedContents ADD deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.UserOtps ADD updatedAt datetime NULL,
        deletedAt datetime NULL,
        created<PERSON>y nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.UserOnboardingSteps ADD createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.Subscriptions ADD deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.FavoriteContents ADD deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCountryToTTBTable1687316206346 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD country nvarchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS country`);
  }
}

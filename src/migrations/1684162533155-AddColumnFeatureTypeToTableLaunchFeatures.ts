import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnFeatureTypeToTableLaunchFeatures1684162533155 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.LaunchFeatures ADD featureType nvarchar(50) NULL DEFAULT('LAUNCH_FEATURE') WITH VALUES;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.LaunchFeatures DROP COLUMN IF EXISTS featureType`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnExtensionUserTable1648701962617 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'ExtensionUser',
      new TableColumn({
        name: 'accessCode',
        type: 'varchar(50)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

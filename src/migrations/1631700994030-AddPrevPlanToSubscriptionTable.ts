import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddPrevPlanToSubscriptionTable1631700994030 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'prevPlan',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'SubscriptionEventLogs',
      new TableColumn({
        name: 'userId',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFieldUserTable1688448100453 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD is_receive_gift bit default 0`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD total_receive_gift int DEFAULT 0`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD address nvarchar(500) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS is_receive_gift`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS address`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS total_receive_gift`);
  }
}

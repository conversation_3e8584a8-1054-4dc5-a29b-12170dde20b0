import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserNotifyAdminTable1689214515005 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin ADD is_ttb_fraud bit default 0`);
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin ADD is_referral bit default 0`);
    await queryRunner.query(`UPDATE dbo.UserNotifyAdmin set is_ttb_fraud = 1`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin DROP COLUMN IF EXISTS is_ttb_fraud`);
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin DROP COLUMN IF EXISTS is_referral`);
  }
}

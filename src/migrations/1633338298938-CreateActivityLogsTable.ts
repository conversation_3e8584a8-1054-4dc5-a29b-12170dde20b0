import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateActivityLogsTable1633338298938 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.ActivityLogs (
        id uniqueidentifier NOT NULL PRIMARY KEY,
        itemId uniqueidentifier NULL,
        module nvarchar(50) NULL,
        command nvarchar(50) NULL,
        columnChanged nvarchar(255) NULL,
        prevColumnValue nvarchar(4000) NULL,
        modifiedBy uniqueidentifier NULL,
        modifiedAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ActivityLogs;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnFirstNameLastNameUserTourTrashsTable1632610762023 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.UserTourTrashs ADD
        firstName nvarchar(50) NULL,
        lastName nvarchar(50) NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS firstName`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS lastName`);
  }
}

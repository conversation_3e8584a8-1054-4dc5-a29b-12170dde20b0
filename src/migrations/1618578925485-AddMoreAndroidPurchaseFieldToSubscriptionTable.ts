import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMoreAndroidPurchaseFieldToSubscriptionTable1618578925485 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Subscriptions ADD status int DEFAULT(0),
            autoRenewing bit DEFAULT(1),
            purchaseToken nvarchar(255) NULL,
            purchaseState int DEFAULT(0),
            platform nvarchar(255) NOT NULL DEFAULT 'ios'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

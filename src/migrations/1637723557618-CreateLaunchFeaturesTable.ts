import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLaunchFeaturesTable1637723557618 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.LaunchFeatures (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        feature nvarchar(255) NOT NULL,
        enabled BIT NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.LaunchFeatures;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFirstNameLastNameAccessCodesTable1636681261423 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.AccessCodes ADD
        firstName nvarchar(50) NULL,
        lastName nvarchar(50) NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.AccessCodes DROP COLUMN IF EXISTS firstName`);
    await queryRunner.query(`ALTER TABLE dbo.AccessCodes DROP COLUMN IF EXISTS lastName`);
  }
}

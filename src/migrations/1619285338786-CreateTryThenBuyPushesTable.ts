import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTryThenBuyPushesTable1619285338786 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.TryThenBuyPushes (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId nvarchar(255) NOT NULL,
            orderId nvarchar(255) NOT NULL,
            status nvarchar(255) NOT NULL,
            pushedAt datetime,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
    `);
    await queryRunner.query(`
        CREATE INDEX idx_status ON dbo.TryThenBuyPushes (status);
    `);
    await queryRunner.query(`
        CREATE INDEX idx_orderId ON dbo.TryThenBuyPushes (orderId);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TryThenBuyPushes;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserTourStoryTable1709798537775 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.TourStoryUser
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            userId nvarchar(255) NOT NULL,
            tourStoryId nvarchar(255) NOT NULL,
            totalView int DEFAULT(0),
            viewed bit DEFAULT(1),
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
        )
       CREATE INDEX idx_userId ON dbo.TourStoryUser (userId);
       CREATE INDEX idx_tourStoryId ON dbo.TourStoryUser (tourStoryId);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TourStoryUser;`);
  }
}

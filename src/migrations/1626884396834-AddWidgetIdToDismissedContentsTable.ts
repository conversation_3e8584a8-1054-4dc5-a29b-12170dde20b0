import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddWidgetIdToDismissedContentsTable1626884396833 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.DismissedContents ADD widgetId varchar(50) NULL
    `);
    await queryRunner.changeColumns('dbo.DismissedContents', [
      {
        oldColumn: new TableColumn({
          name: 'contentId',
          type: 'nvarchar(50)',
          isNullable: false,
        }),
        newColumn: new TableColumn({
          name: 'contentId',
          type: 'nvarchar(50)',
          isNullable: true,
        }),
      },
      {
        oldColumn: new TableColumn({
          name: 'contentFormat',
          type: 'varchar(255)',
          isNullable: false,
        }),
        newColumn: new TableColumn({
          name: 'contentFormat',
          type: 'varchar(255)',
          isNullable: true,
        }),
      },
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.DismissedContents DROP COLUMN widgetId
    `);
  }
}

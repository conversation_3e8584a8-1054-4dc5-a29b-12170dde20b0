import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSurveysTable1633592151208 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.Surveys (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        feature nvarchar(255) NOT NULL,
        name nvarchar(255) NOT NULL,
        status BIT NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Surveys;`);
  }
}

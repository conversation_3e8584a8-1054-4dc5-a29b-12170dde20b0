import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTablePermission1669176837838 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Permission (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            community bit NULL DEFAULT 0,
            mfe bit NULL DEFAULT 0,
            mrp bit NULL DEFAULT 0,
            productDrops bit NULL DEFAULT 0,
            insights bit NULL DEFAULT 0,
            tryThenBuy bit NULL DEFAULT 0,
            calculateHandicap bit NULL DEFAULT 0,
            winTourTrash bit NULL DEFAULT 0,
            freeShipping bit NULL DEFAULT 0,
            freeTwoDaysShipping bit NULL DEFAULT 0,
            virtualCoaching bit NULL DEFAULT 0,
            videoInstruction bit NULL DEFAULT 0
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

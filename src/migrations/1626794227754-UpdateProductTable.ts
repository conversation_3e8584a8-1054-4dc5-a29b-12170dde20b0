import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateProductTable1626794227754 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products DROP COLUMN IF EXISTS videoLink, id, tourTrashId`);
    await queryRunner.query(
      `ALTER TABLE dbo.Products ADD videoType varchar(255) NULL, videoId varchar(255) NULL, uuid uniqueidentifier NOT NULL, tourTrashUuid uniqueidentifier NOT NULL`
    );
    await queryRunner.query(`
      ALTER TABLE [dbo].[Products] ADD CONSTRAINT [PK__Products__7F4279304B70D9C8] PRIMARY KEY (uuid)
      ALTER TABLE [dbo].[Products] ADD CONSTRAINT Products_FK_1 FOREIGN KEY (tourTrashUuid) REFERENCES dbo.TourTrashs(uuid);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

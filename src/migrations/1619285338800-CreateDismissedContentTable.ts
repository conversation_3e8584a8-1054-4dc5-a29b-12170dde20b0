import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDismissedContentTable1619285338800 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.DismissedContents (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId nvarchar(50) NOT NULL,
            contentId nvarchar(50) NOT NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
    `);
    await queryRunner.query(`
        CREATE INDEX idx_userId ON dbo.DismissedContents (userId);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.DismissedContents;`);
  }
}

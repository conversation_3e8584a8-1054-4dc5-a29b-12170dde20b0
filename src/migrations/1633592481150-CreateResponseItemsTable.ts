import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateResponseItemsTable1633592481150 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.ResponseItems (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        responseId uniqueidentifier NOT NULL,
        questionId uniqueidentifier NOT NULL,
        answerId uniqueidentifier NOT NULL,
        questionValue nvarchar(255) NOT NULL,
        answerValue nvarchar(255) NOT NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      ALTER TABLE dbo.ResponseItems ADD CONSTRAINT ResponseItems_Response_FK FOREIGN KEY (responseId) REFERENCES dbo.Responses(id);
      ALTER TABLE dbo.ResponseItems ADD CONSTRAINT ResponseItems_Question_FK FOREIGN KEY (questionId) REFERENCES dbo.Questions(id);
      ALTER TABLE dbo.ResponseItems ADD CONSTRAINT ResponseItems_Answer_FK FOREIGN KEY (answerId) REFERENCES dbo.Answers(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ResponseItems;`);
  }
}

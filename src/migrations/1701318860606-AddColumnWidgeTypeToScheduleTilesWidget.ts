import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnWidgeTypeToScheduleTilesWidget1701318860606 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ScheduleTilesWidget ADD widgetType nvarchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ScheduleTilesWidget DROP COLUMN IF EXISTS widgetType`);
  }
}

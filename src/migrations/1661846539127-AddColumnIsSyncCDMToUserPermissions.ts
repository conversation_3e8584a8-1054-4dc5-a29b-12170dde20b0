import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnIsSyncCDMToUserPermissions1661846539127 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'UserPermissions',
      new TableColumn({
        name: 'isSyncCDM',
        type: 'bit',
        default: 1,
        isNullable: false,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

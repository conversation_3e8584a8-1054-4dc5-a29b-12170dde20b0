import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddCardNameHomeWidgetsTable1634724993590 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'HomeWidgets',
      new TableColumn({
        name: 'name',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('HomeWidgets', 'name');
  }
}

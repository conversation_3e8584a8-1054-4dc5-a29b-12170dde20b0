import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateUserTourTrashTable1626884396832 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.changeColumns('dbo.UserTourTrashs', [
      {
        oldColumn: new TableColumn({
          name: 'tourTrashId',
          type: 'int',
        }),
        newColumn: new TableColumn({
          name: 'tourTrashUuid',
          type: 'uniqueidentifier',
          isNullable: false,
        }),
      },
    ]);
    await queryRunner.query(`
      ALTER TABLE dbo.UserTourTrashs ADD CONSTRAINT UserTourTrashs_FK FOREIGN KEY (userId) REFERENCES dbo.Users(id);
      ALTER TABLE dbo.UserTourTrashs ADD CONSTRAINT UserTourTrashs_FK_1 FOREIGN KEY (tourTrashUuid) REFERENCES dbo.TourTrashs(uuid);
    `);
    await queryRunner.query(`
      EXEC sys.sp_cdc_enable_db
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

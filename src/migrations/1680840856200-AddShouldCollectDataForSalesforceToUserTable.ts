import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddShouldCollectDataForSalesforceToUserTable1680840856200 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD shouldCollectSalesforceData bit DEFAULT 1;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS shouldCollectSalesforceData`);
  }
}

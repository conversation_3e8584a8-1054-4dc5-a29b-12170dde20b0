import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePerkTable1684123744237 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Benefits ADD subTitle nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Benefits ADD buttonList nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Benefits ADD buttonDetail nvarchar(250) NULL`);
    await queryRunner.query(`UPDATE dbo.Benefits SET buttonList = N'Let''s Go'`);
    await queryRunner.query(`UPDATE dbo.Benefits SET buttonDetail = 'Take Advantage'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Benefits DROP COLUMN IF EXISTS subTitle`);
    await queryRunner.query(`ALTER TABLE dbo.Benefits DROP COLUMN IF EXISTS buttonList`);
    await queryRunner.query(`ALTER TABLE dbo.Benefits DROP COLUMN IF EXISTS buttonDetail`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ChangePrevNextColumnValueTable1638783234387 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DELETE FROM dbo.ActivityLogs`);
    await queryRunner.dropColumn('ActivityLogs', 'prevColumnValue');
    await queryRunner.dropColumn('ActivityLogs', 'nextColumnValue');
    await queryRunner.addColumn(
      'ActivityLogs',
      new TableColumn({
        name: 'prevColumnValue',
        type: 'nvarchar(max)',
        isNullable: true,
      })
    );

    await queryRunner.addColumn(
      'ActivityLogs',
      new TableColumn({
        name: 'nextColumnValue',
        type: 'nvarchar(max)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('ActivityLogs', 'prevColumnValue');
    await queryRunner.dropColumn('ActivityLogs', 'nextColumnValue');
    await queryRunner.addColumn(
      'ActivityLogs',
      new TableColumn({
        name: 'prevColumnValue',
        type: 'nvarchar(2000)',
        isNullable: true,
      })
    );

    await queryRunner.addColumn(
      'ActivityLogs',
      new TableColumn({
        name: 'nextColumnValue',
        type: 'nvarchar(2000)',
        isNullable: true,
      })
    );
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLoyaltyPointsTable1694762083138 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.LoyaltyActions
        (
            id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            name                 nvarchar(255)  NULL,
            feature              nvarchar(255) NOT NULL,
            annexActionId        int NOT NULL,
            countries            nvarchar(255)     NULL,
            createdBy            nvarchar(255)     NULL,
            updatedBy            nvarchar(255)     NULL,
            createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt            datetime         NULL,
            deletedAt            datetime         NULL
        )
      
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.LoyaltyActions;`);
  }
}

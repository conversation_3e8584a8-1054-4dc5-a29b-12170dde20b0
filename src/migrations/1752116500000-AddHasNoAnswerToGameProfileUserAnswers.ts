import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddHasNoAnswerToGameProfileUserAnswers1752116500000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.GameProfileUserAnswers ADD hasNoAnswer bit NOT NULL DEFAULT 0;
      ALTER TABLE dbo.GameProfileUserAnswers UPDATE gameProfileAnswerId varchar(36) NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.GameProfileUserAnswers DROP COLUMN hasNoAnswer;
    `);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateBenefitsTable1678780933826 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Benefits;`);

    await queryRunner.query(`
            CREATE TABLE dbo.Benefits
            (
                id                   uniqueidentifier NOT NULL PRIMARY KEY,
                title                nvarchar(255)    NOT NULL,
                description          text,
                imageUrl             nvarchar(255),
                partnerId            uniqueidentifier NOT NULL,
                url                  nvarchar(255),
                disabled             bit   default 0,
                sortOrder            int      default 1,
                createdBy            nvarchar(255)    NOT NULL,
                updatedBy            nvarchar(255)    NOT NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            );
            ALTER TABLE dbo.Benefits ADD CONSTRAINT Benefits_FK_1 FOREIGN KEY (partnerId) REFERENCES dbo.Partners(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Benefits;`);
  }
}

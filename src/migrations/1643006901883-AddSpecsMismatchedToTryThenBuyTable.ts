import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSpecsMismatchedToTryThenBuyTable1643006901883 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReceivedSpecsMismatchedInfo',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReceivedSpecsMismatchedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

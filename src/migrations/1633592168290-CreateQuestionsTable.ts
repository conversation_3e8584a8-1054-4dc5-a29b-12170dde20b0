import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateQuestionsTable1633592168290 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.Questions (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        surveyId uniqueidentifier NOT NULL,
        question nvarchar(255) NOT NULL,
        status BIT NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      ALTER TABLE dbo.Questions ADD CONSTRAINT Questions_FK FOREIGN KEY (surveyId) REFERENCES dbo.Surveys(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Questions;`);
  }
}

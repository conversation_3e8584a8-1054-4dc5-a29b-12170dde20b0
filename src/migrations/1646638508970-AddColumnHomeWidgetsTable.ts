import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnHomeWidgetsTable1646638508970 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('HomeWidgets', [
      new TableColumn({
        name: 'colorOpacity',
        type: 'decimal(6,2)',
        isNullable: true,
      }),
      new TableColumn({
        name: 'textColor',
        type: 'varchar(255)',
        isNullable: true,
      }),
      new TableColumn({
        name: 'buttonAlign',
        type: 'varchar(50)',
        isNullable: true,
      }),
    ]);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

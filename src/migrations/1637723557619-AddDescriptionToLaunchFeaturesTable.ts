import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDescriptionToLaunchFeaturesTable1637723557619 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'LaunchFeatures',
      new TableColumn({
        name: 'description',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddProductReceivedDatesToTTBTable1637559375898 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnProductReceivedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnToTMProductReceivedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'scheduledArrivalDate',
        type: 'datetime',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

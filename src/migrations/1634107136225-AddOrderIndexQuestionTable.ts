import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddOrderIndexQuestionTable1634107136225 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Questions',
      new TableColumn({
        name: 'orderIndex',
        type: 'int',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('Questions', 'orderIndex');
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldIsWTBExportInUsersTable1713514911101 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD isWIBExport BIT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS isWIBExport`);
  }
}

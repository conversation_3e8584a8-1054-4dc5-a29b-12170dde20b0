import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedHomeTilesData1626884396833 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.HomeWidgets ADD extraData varchar(MAX) NULL
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, productId, ctaText, ctaLink, extraData, sortOrder, active)
        VALUES ('39ecb81c-675d-4f15-a294-f7810bc8238a', 'HomeTile', 'PROMOTION', 'Just in', 'COLOR BLASTED', 'SIM 2', '', 'https://mytmstoragedev.blob.core.windows.net/appassets/home/<USER>', 'V9502620', '', '/product/V9502620', '{}', 1, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, productId, ctaText, ctaLink, extraData, sortOrder, active)
        VALUES ('c357d5ce-f894-4b4c-99d2-3f0b856d7f51', 'HomeTile', 'INSIGHT_OVERVIEW', 'Insights', 'Lorem Ispum', 'It is a long established fact is a reader', '#262729', '', '', 'View All Insights', '/insights', '{"strokesGainAfterFitting":"{{strokesGainAfterFitting}}","strokesGainAfterFittingLabel":"Stokes Gained After Fitting","strokesGainBeforeFitting":"{{strokesGainBeforeFitting}}","strokesGainBeforeFittingLabel":"Stokes Gained Before Fitting"}', 2, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, productId, ctaText, ctaLink, extraData, sortOrder, active)
        VALUES ('723f43cd-d7d6-4e2c-87ab-0a4502ebf162', 'HomeTile', 'HANDICAP_INDEX', 'TaylorMade Handicap', 'Handicap Index', '{{handicapDescription}}', '#04BE4B', '', '', 'Post a Score', '/post-score', '{"handicapIndex":"{{handicapIndex}}","isTrendingDownward":"{{isTrendingDownward}}","isTrendingUpward":"{{isTrendingUpward}}"}', 3, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, productId, ctaText, ctaLink, extraData, sortOrder, active)
        VALUES ('bdb5dc03-7dc5-4ce6-b264-3d28030aae4a', 'HomeTile', 'AVERAGE_SCORE', 'Scores', '{{averageScore}}', 'Average Score', '#01666F', '', '', 'View All Scores', '/scores', '[{"label":"Round","count":"{{totalRounds}}"},{"label":"Low","count":"{{lowestScore}}"},{"label":"High","count":"{{highestScore}}"}]', 4, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, productId, ctaText, ctaLink, extraData, sortOrder, active)
        VALUES ('ce101168-a605-420f-a060-77dc83bd08d3', 'HomeTile', 'TARGET_SCORE', 'Target Score', '{{strokeToTarget}}', 'Stokes to target', '#4E5643', '', '', 'Adjust Target Score', '/target-score', '[{"label":"Average","count":"{{averageScore}}"},{"label":"Target","count":"{{targetScore}}"}]', 5, 1);
    `);
  }
  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateisManualInputAverageScoreInUser1680840856199 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD isManualInputAverageScore bit DEFAULT 0;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS isManualInputAverageScore`);
  }
}

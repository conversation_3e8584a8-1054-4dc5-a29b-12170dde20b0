import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddShipmentTrackingNumberToTTBTable1634724993591 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'shipmentTrackingNumber',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'shipmentCarrierName',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('TryThenBuy', 'shipmentTrackingNumber');
    await queryRunner.dropColumn('TryThenBuy', 'shipmentCarrierName');
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateEcomNotificationsTable1619285338791 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.EComNotifications (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            eventName varchar(50) NULL,
            orderId varchar(50) NULL,
            extraData varchar(8000) NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.EComNotifications;`);
  }
}

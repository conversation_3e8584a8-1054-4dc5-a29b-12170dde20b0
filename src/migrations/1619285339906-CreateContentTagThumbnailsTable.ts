import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateContentTagThumbnailsTable1619285339906 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.ContentTagThumbnails (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            tagSlug nvarchar(255) NOT NULL,
            tagGroup nvarchar(255) NOT NULL,
            imageUrl nvarchar(255) NOT NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ContentTagThumbnails;`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddOfferFieldsToSubscriptionTable1639985328884 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'offerType',
        type: 'int',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'offerIdentifier',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'SubscriptionEventLogs',
      new TableColumn({
        name: 'subtype',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

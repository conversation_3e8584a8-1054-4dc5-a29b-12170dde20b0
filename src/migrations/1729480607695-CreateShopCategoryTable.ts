import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateShopCategoryTable1729480607695 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.ShopCategory (
              id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
              title nvarchar(255) NOT NULL,
              description nvarchar(255) NULL,
              ctaLink nvarchar(255) NULL,
              disabled bit Null,
              country nvarchar(50) NULL,
              sortOrder int DEFAULT 1,
              createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updatedAt datetime NULL,
              deletedAt datetime NULL,
              createdBy nvarchar(50) NULL,
              updatedBy nvarchar(50) NULL,
              deletedBy nvarchar(50) NULL
            );
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ShopCategory;`);
  }
}

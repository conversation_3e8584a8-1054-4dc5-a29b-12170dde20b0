import { MigrationInterface, QueryRunner } from 'typeorm';

export class InsertRecordMasterToTablePermission1669177619872 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `INSERT INTO [dbo].[Permission] VALUES (N'0AF057C7-EB5F-493A-92B0-2E8EF11D625F', N'0', N'0', N'0', N'0', N'0', N'0', N'0', N'0', N'0', N'0', N'0', N'0')`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

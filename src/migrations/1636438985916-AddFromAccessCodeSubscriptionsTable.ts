import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddFromAccessCodeSubscriptionsTable1636438985916 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'fromAccessCode',
        type: 'nvarchar(50)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('Subscriptions', 'fromAccessCode');
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColorOpacityPromotionTable1646125640762 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Promotions',
      new TableColumn({
        name: 'colorOpacity',
        type: 'decimal(6,2)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnRegionToTableTourTrashs1684139172755 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD regions nvarchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS regions`);
  }
}

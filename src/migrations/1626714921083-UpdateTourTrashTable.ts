import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class UpdateTourTrashTable1626714921083 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      EXEC sys.sp_cdc_disable_db
    `);
    await queryRunner.query(`
      delete from dbo.Products
      delete from dbo.UserTourTrashs
      delete from dbo.TourTrashs
    `);
    await queryRunner.query(`
      DECLARE @table nvarchar(50)

      set @table = 'dbo.TourTrashs'

      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT ' + name
        from sys.default_constraints
        where name LIKE 'DF__TourTrash__p%'
        exec sp_executesql @sql
      END`);
    await queryRunner.query(`
      DECLARE @table nvarchar(50)

      set @table = 'dbo.TourTrashs'

      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT ' + name
        from sys.default_constraints
        where name LIKE 'DF__TourTrash__s%'
        exec sp_executesql @sql
      END
      ALTER TABLE dbo.TourTrashs DROP CONSTRAINT IF EXISTS CK__TourTrash__statu__0CB1922D;
      ALTER TABLE dbo.TourTrashs WITH NOCHECK ADD CONSTRAINT CK__TourTrash__statu__0CB1922D CHECK ([status]='DRAFT' OR [status]='CLOSED' OR [status]='ACTIVE');
      ALTER TABLE dbo.UserTourTrashs DROP CONSTRAINT IF EXISTS UserTourTrashs_FK;
      ALTER TABLE dbo.UserTourTrashs DROP CONSTRAINT IF EXISTS UserTourTrashs_FK_1;
      ALTER TABLE dbo.Products DROP CONSTRAINT IF EXISTS Products_FK_1;
      ALTER TABLE [dbo].[TourTrashs] DROP CONSTRAINT IF EXISTS [TourTrashs_FK]
      ALTER TABLE [dbo].[TourTrashs] DROP CONSTRAINT IF EXISTS [PK__TourTras__3213E83F0A505A35]
    `);

    await queryRunner.changeColumns('dbo.TourTrashs', [
      {
        oldColumn: new TableColumn({
          name: 'pushNotiToAll',
          type: 'bit',
        }),
        newColumn: new TableColumn({
          name: 'pushNotiTo',
          type: 'tinyint',
          isNullable: true,
        }),
      },
      {
        oldColumn: new TableColumn({
          name: 'sendEmailToAll',
          type: 'bit',
        }),
        newColumn: new TableColumn({
          name: 'sendEmailTo',
          type: 'tinyint',
          isNullable: true,
        }),
      },
      {
        oldColumn: new TableColumn({
          name: 'id',
          type: 'int',
        }),
        newColumn: new TableColumn({
          name: 'uuid',
          type: 'uniqueidentifier',
          isNullable: false,
        }),
      },
    ]);

    await queryRunner.query(`
      ALTER TABLE [dbo].[TourTrashs] ADD CONSTRAINT [PK__TourTrash__3213E83F0A505A35] PRIMARY KEY (uuid)
      ALTER TABLE [dbo].[TourTrashs] ADD CONSTRAINT [TourTrashs_FK] FOREIGN KEY (winnerUserId) REFERENCES dbo.Users(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

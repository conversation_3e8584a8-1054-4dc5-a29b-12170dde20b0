import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddPauseStatusToTTBTable1648701962618 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'paused',
        type: 'bit',
        default: 0,
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'pauseReason',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'pausedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'pausedBy',
        type: 'varchar(100)',
        isNullable: true,
      })
    );

    await queryRunner.query(`
        CREATE INDEX idx_paused ON dbo.TryThenBuy (paused);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

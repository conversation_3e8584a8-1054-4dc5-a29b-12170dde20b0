import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSortOrderToGameProfileQuestions1752116450000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.GameProfileQuestions ADD sortOrder int NOT NULL DEFAULT 1;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.GameProfileQuestions DROP COLUMN sortOrder;
    `);
  }
}

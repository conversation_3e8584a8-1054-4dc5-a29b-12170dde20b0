import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRegionsColumnToTableBenefits1684224511588 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Benefits ADD regions nvarchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Benefits DROP COLUMN IF EXISTS regions`);
  }
}

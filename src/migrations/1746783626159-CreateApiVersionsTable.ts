import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateApiVersionsTable1746783626159 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.ApiVersions (
              id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
              featureKey nvarchar(100) NOT NULL,
              country nvarchar(50) NULL,
              version BIGINT NOT NULL,
              updatedBy nvarchar(255) NULL,
              createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updatedAt datetime NULL,
              deletedAt datetime NULL,
            );
          `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX idx_feature_country ON dbo.ApiVersions (featureKey, country);
    `);

    const keys = [
      'HOME_THE_DAILY',
      'HOME_PRODUCT',
      'HOME_CARTNER_CONVOS',
      'SHOP_MARQUEE',
      'SHOP_PROMOTION',
      'SHOP_PRODUCT_CAROUSEL',
      'SHOP_CATEGORIES',
      'SHOP_MAINSTAYS',
      'SHOP_PRODUCT_CATALOG',
      'SHOP_FINAL_TILE',
      'CLUB_JUST_IN',
      'CLUB_TOUR_STORIES',
      'CLUB_COACH_PROFILE',
      'CLUB_PLAYER_PROFILE',
      'REWARD_PERK',
      'REWARD_LOYALTY_ACTIONS',
      'REWARD_TOUR_TRASH',
      'COUNTRY_FEATURES',
      'CONTENT_IMAGE',
      'CONTENT_TEXT',
    ];

    const countries = ['USA', 'CAN'];
    const version = Date.now();

    for (const key of keys) {
      for (const country of countries) {
        await queryRunner.query(
          `
      INSERT INTO dbo.ApiVersions (featureKey, country, version, createdAt, updatedAt)
      VALUES ('${key}', '${country}', ${version}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `
        );
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE dbo.ApiVersions;`);
  }
}

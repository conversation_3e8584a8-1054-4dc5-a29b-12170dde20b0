import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDeliveredAtToTTBTable1633338298948 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('TryThenBuy', 'productInfo');
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'productInfo',
        type: 'varchar(8000)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'deliveredAt',
        type: 'datetime',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTableCountries1684292122190 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Countries ADD dialCode nvarchar(40) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Countries ADD isoCode nvarchar(10) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Countries ADD status nvarchar(10) NULL`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}

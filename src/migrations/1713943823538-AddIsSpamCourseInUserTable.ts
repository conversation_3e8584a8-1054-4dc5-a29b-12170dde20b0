import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsSpamCourseInUserTable1713943823538 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD isSpamCourse BIT default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS isSpamCourse`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnSortToQuestions1702007895384 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions ADD sortOrder int`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions DROP COLUMN IF EXISTS sortOrder`);
  }
}

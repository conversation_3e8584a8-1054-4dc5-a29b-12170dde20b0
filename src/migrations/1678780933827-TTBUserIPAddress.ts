import { MigrationInterface, QueryRunner } from 'typeorm';

export class TTBUserIPAddress1678780933827 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD lastIPAddress nvarchar(100) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD purchaseIPAddress nvarchar(100) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

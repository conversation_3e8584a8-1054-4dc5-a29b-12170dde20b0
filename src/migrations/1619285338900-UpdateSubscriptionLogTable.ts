import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateSubscriptionLogTable1619285338900 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.SubscriptionEventLogs ALTER COLUMN notifyData varchar(MAX);
        ALTER TABLE dbo.SubscriptionEventLogs ALTER COLUMN receiptData varchar(MAX);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

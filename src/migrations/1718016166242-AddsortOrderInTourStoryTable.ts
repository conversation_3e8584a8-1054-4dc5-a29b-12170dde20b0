import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddsortOrderInTourStoryTable1718016166242 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourStory ADD sortOrder tinyint NOT NULL DEFAULT 1`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourStory DROP COLUMN IF EXISTS sortOrder`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCtaLinkToTableLoyaltyActions1705312597556 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions ADD ctaLink nvarchar(200) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions DROP COLUMN IF EXISTS ctaLink`);
  }
}

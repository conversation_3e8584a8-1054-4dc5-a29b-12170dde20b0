import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnAlignHomeWidgetsAndPromotionsTable1646650027234 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumns('Promotions', [
      new TableColumn({
        name: 'textAlign',
        type: 'varchar(50)',
        isNullable: true,
      }),
      new TableColumn({
        name: 'buttonAlign',
        type: 'varchar(50)',
        isNullable: true,
      }),
    ]);
    await queryRunner.addColumn(
      'HomeWidgets',
      new TableColumn({
        name: 'textAlign',
        type: 'varchar(50)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

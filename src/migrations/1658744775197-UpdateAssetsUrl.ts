import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAssetsUrl1658744775197 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE [dbo].[HomeWidgets]
        SET backgroundImage = REPLACE(backgroundImage, 'storagedev.blob.core.windows', 'assets.azureedge')
    `);
    await queryRunner.query(`
        UPDATE [dbo].[HomeWidgets]
        SET backgroundImage = REPLACE(backgroundImage, 'storageprod.blob.core.windows', 'assetsprod.azureedge')    
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

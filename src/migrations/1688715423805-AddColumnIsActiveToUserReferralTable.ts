import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsActiveToUserReferralTable1688715423805 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserReferral ADD is_active bit default 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserReferral DROP COLUMN IF EXISTS is_active`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBlacklistedTokensTable1619097203765 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.BlacklistedTokens (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            token varchar(1000) NOT NULL,
            type bit NOT NULL,
            createdAt datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.BlacklistedTokens;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserTourTrashTable1619285339908 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.UserTourTrashs (
          userId uniqueidentifier NOT NULL,
          tourTrashId int NOT NULL
        );
        ALTER TABLE dbo.UserTourTrashs ADD CONSTRAINT UserTourTrashs_FK FOREIGN KEY (userId) REFERENCES dbo.Users(id);
        ALTER TABLE dbo.UserTourTrashs ADD CONSTRAINT UserTourTrashs_FK_1 FOREIGN KEY (tourTrashId) REFERENCES dbo.TourTrashs(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserTourTrashs;`);
  }
}

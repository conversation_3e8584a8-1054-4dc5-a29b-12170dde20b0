import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAccessCodesTable1636011375711 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.AccessCodes (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        code nvarchar(50) NOT NULL,
        subscriptionPlan tinyint NOT NULL,
        subscriptionDuration int NOT NULL,
        target nvarchar(255) NULL,
        userId uniqueidentifier NULL,
        usedDate datetime NULL,
        expirationDate datetime NULL,
        status nvarchar(50) NOT NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      CREATE UNIQUE INDEX uix_code_column ON dbo.AccessCodes (code);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.AccessCodes;`);
  }
}

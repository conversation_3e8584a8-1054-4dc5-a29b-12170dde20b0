import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeDescriptionColumnProductTable1631700994029 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products DROP COLUMN IF EXISTS detail`);
    await queryRunner.query(`ALTER TABLE dbo.Products ADD detail nvarchar(4000) NULL;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products DROP COLUMN IF EXISTS detail`);
    await queryRunner.query(`ALTER TABLE dbo.Products ADD detail nvarchar(255) NULL;`);
  }
}

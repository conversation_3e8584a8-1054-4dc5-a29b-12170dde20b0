import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCreatedAtColumnUserTourTrashsTable1632397512000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.UserTourTrashs ADD
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DECLARE @table nvarchar(50)
      set @table = 'dbo.UserTourTrashs'
      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
        from sys.default_constraints
        where name LIKE 'DF__UserTourT__creat%'
        exec sp_executesql @sql
      END`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS createdAt`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS updatedAt`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS deletedAt`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS createdBy`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS updatedBy`);
    await queryRunner.query(`ALTER TABLE dbo.UserTourTrashs DROP COLUMN IF EXISTS deletedBy`);
  }
}

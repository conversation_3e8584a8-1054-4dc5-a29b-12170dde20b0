import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class RemoveColumnStatusAccessCodesTable1636516566489 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('AccessCodes', 'status');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'AccessCodes',
      new TableColumn({
        name: 'status',
        type: 'nvarchar(50)',
        isNullable: true,
      })
    );
  }
}

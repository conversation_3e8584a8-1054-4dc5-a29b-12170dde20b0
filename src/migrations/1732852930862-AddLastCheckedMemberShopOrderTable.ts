import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLastCheckedMemberShopOrderTable1732852930862 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrder ADD lastChecked datetime default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrder DROP COLUMN IF EXISTS lastChecked`);
  }
}

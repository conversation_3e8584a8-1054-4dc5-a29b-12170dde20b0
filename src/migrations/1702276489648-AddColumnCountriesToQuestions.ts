import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCountriesToQuestions1702276489648 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions ADD countries nvarchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions DROP COLUMN IF EXISTS countries`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRetryFlagToEcomNotificationTable1648701962619 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'EcomNotifications',
      new TableColumn({
        name: 'shouldRetry',
        type: 'bit',
        default: 0,
        isNullable: false,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

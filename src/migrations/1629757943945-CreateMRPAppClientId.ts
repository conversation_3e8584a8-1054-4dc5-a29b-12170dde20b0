import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMRPAppClientId1629757943945 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON><PERSON>, active)
        VALUES ('a0a6aef8-1832-4663-9365-f810e54bec9b', 'MRP_APP', '040c8293-395e-4727-af3a-9798bc60e96c', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

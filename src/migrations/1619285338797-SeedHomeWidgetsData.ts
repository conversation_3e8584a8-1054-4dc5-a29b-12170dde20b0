import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedHomeWidgetsData1619285338797 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, contentType, contentId, contentFormat, ctaText, ctaLink, sortOrder, active)
        VALUES ('3542b5f5-51ed-451d-8e3c-ba0fcf1d3fcc', 'HomeExplorer', 'DRILL_OF_THE_WEEK', 'DRILL OF THE WEEK', '{{content.title}}', '', '', '{{content.primaryImage}}', 'drill', 3051, 'videos', 'EXPLORE', '/content/{{content.id}}', 1, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, contentType, contentId, contentFormat, ctaText, ctaLink, sortOrder, active)
        VALUES ('b9c0125d-6636-4246-8a9a-4c2cdf5ef087', 'HomeExplorer', 'ON_TOUR', 'ON TOUR', '{{content.title}}', '', '', '{{content.primaryImage}}', 'entertainment', 66423, 'clubhouseArticles', 'EXPLORE', '/content/{{content.id}}', 2, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, contentType, contentId, contentFormat, ctaText, ctaLink, sortOrder, active)
        VALUES ('da5afbcf-33cb-473b-8f67-50ffcc353c1d', 'HomeExplorer', 'MEMBER_ONLY_EXCLUSIVE', 'MEMBER ONLY EXCLUSIVE', '{{content.title}}', '', '', '{{content.primaryImage}}', 'product', 19908, 'clubhouseArticles', 'EXPLORE', '/content/{{content.id}}', 3, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, contentType, contentId, contentFormat, ctaText, ctaLink, sortOrder, active)
        VALUES ('b5f8442d-8cbe-465b-a8ab-79e469be28fe', 'HomeExplorer', 'FEATURED_STORY', 'FEATURED STORY', '{{content.title}}', '', '', '{{content.primaryImage}}', 'entertainment', 143265, 'videos', 'EXPLORE', '/content/{{content.id}}', 4, 1);
    `);
    await queryRunner.query(`
        INSERT INTO dbo.HomeWidgets (id, groupKey, type, heading, title, description, backgroundColor, backgroundImage, contentType, contentId, contentFormat, ctaText, ctaLink, sortOrder, active)
        VALUES ('e6532547-a64d-4b32-a9db-d1e4fbadfbf8', 'HomeExplorer', 'FEATURED_VIDEO', 'FEATURED VIDEO', '{{content.title}}', '', '', '{{content.primaryImage}}', 'entertainment', 132914, 'videos', 'EXPLORE', '/content/{{content.id}}', 5, 1);
    `);
  }
  public async down(queryRunner: QueryRunner): Promise<void> {}
}

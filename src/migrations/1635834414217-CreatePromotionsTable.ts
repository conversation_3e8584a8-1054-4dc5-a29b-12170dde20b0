import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromotionsTable1635834414217 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.Promotions (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        title nvarchar(255) NOT NULL,
        description nvarchar(255) NULL,
        videoType nvarchar(255) NULL,
        videoId nvarchar(255) NULL,
        ctaText nvarchar(255) NULL,
        ctaLink nvarchar(255) NULL,
        position nvarchar(50) NOT NULL,
        status nvarchar(10) NOT NULL DEFAULT 'INACTIVE',
        show BIT NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      CREATE INDEX idx_position_column ON dbo.Promotions (position);
      CREATE INDEX idx_status_column ON dbo.Promotions (status);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Promotions;`);
  }
}

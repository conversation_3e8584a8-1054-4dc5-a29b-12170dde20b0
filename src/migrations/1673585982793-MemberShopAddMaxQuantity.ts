import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class MemberShopAddMaxQuantity1673585982793 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        TRUNCATE TABLE [dbo].[ProductOrders];
    `);
    await queryRunner.addColumn(
      'ProductOrders',
      new TableColumn({
        name: 'maxQty',
        type: 'int',
        default: 1,
        isNullable: false,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

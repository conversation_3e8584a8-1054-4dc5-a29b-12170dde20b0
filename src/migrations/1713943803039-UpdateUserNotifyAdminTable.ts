import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserNotifyAdminTable1713943803039 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin ADD isCourses bit default 0`);
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin ADD email nvarchar(255) default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin DROP COLUMN IF EXISTS is_courses`);
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin DROP COLUMN IF EXISTS email`);
  }
}

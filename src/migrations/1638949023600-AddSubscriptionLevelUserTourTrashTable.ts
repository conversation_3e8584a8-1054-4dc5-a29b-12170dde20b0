import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSubscriptionLevelUserTourTrashTable1638949023600 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'UserTourTrashs',
      new TableColumn({
        name: 'subscriptionLevel',
        type: 'tinyint',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('UserTourTrashs', 'subscriptionLevel');
  }
}

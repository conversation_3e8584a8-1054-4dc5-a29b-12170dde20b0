import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserBlacks1681442802997 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.UserBlackList
            (
                id                   uniqueidentifier NOT NULL PRIMARY KEY,
                userId               uniqueidentifier NOT NULL,
                email                nvarchar(255) NOT NULL,
                createdBy            nvarchar(50) NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            );
            ALTER TABLE dbo.UserBlackList ADD CONSTRAINT User_FK_1 FOREIGN KEY (userId) REFERENCES dbo.Users(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserBlackList;`);
  }
}

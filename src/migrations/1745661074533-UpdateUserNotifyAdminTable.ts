import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserNotifyAdminTable1745661074533 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin ADD is_check_jobs bit default 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserNotifyAdmin DROP COLUMN IF EXISTS is_check_jobs`);
  }
}

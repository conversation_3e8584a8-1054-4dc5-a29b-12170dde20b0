import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnLinkToProductDiscount1701138983368 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts ADD link nvarchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts DROP COLUMN IF EXISTS link`);
  }
}

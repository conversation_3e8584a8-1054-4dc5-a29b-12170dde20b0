import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTTBCancelReasonToTryThenByTable1652194970498 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'cancelReason',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnStatusToProductDiscounts1701078462940 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts ADD status nvarchar(20) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts DROP COLUMN IF EXISTS status`);
  }
}

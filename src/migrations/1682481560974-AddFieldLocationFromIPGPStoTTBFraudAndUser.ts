import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldLocationFromIPGPStoTTBFraudAndUser1682481560974 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuyFraud ADD purchaseIPAddressLocation nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuyFraud ADD purchaseGPSLocation nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD signUpIPAddressLocation nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD signUpGPSLocation nvarchar(250) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuyFraud DROP COLUMN IF EXISTS purchaseIPAddressLocation`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuyFraud DROP COLUMN IF EXISTS purchaseGPSLocation`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS signUpIPAddressLocation`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS signUpGPSLocation`);
  }
}

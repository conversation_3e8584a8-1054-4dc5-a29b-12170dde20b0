import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableQuestions1701742674655 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.PollQuestions
            (
                id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
                title                nvarchar(255) NOT NULL,
                description          nvarchar(255)     NULL,
                createdBy            nvarchar(255)     NULL,
                updatedBy            nvarchar(255)     NULL,
                deletedBy            nvarchar(255)     NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.PollQuestions;`);
  }
}

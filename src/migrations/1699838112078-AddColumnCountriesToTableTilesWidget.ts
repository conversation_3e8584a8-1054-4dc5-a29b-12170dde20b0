import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCountiesToTableTilesWidget1699838112078 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TilesWidget ADD countries nvarchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TilesWidget DROP COLUMN IF EXISTS countries`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserPermissionsTable1652804804285 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        SET ANSI_NULLS ON
        SET QUOTED_IDENTIFIER ON
        CREATE TABLE dbo.UserPermissions (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId uniqueidentifier NOT NULL, 
            subscriptionLength int NULL,
            canVirtualCoaching bit NULL,
            canExclProductDrops bit NULL,
            canTryThenBuy bit NULL,
            canPerfInsights bit NULL,
            canWinTourTrash bit NULL,
            canCommunity bit NULL,
            canFreeShipping bit NULL,
            canVideoInstruction bit NULL,
            canMRP bit NULL,
            canMFE bit NULL,
            canCalculatedHandicap bit NULL,
            canFree2DaysShipping bit NULL,
            canAccessToContentsTipsFromCoach bit NULL,
            canKeepDigitalScore bit NULL,
            canLoyalty bit NULL,
            canBookTeeTime bit NULL,
            canOnlineProductRec bit NULL,
            subscriptionExpirationDate datetime NULL,
            subscriptionStartDate datetime NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL
        );
        ALTER TABLE dbo.UserPermissions ADD CONSTRAINT UserPermissions_FK FOREIGN KEY (userId) REFERENCES dbo.Users(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

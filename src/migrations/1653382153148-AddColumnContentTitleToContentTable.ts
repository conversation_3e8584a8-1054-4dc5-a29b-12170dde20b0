import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnContentTitleToContentTable1653382153148 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'ViewedContents',
      new TableColumn({
        name: 'contentTitle',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

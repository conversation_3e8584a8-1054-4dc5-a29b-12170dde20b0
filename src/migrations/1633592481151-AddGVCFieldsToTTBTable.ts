import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddGVCFieldsToTTBTable1633592481151 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'carrierTrackingNumber',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'carrier',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'shipmentDate',
        type: 'datetime',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

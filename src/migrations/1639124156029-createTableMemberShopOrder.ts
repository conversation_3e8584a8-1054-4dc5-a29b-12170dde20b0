import { MigrationInterface, QueryRunner } from 'typeorm';

export class createTableMemberShopOrder1639124156029 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.memberShopOrder (
          id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
          orderId nvarchar(255) NOT NULL,
          cdmPrimaryEmail nvarchar(255) NOT NULL,
          description TEXT NULL,
          status  nvarchar(50) NOT NULL,
          createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt datetime NULL,
          deletedAt datetime NULL,
          createdBy nvarchar(50) NULL,
          updatedBy nvarchar(50) NULL,
          deletedBy nvarchar(50) NULL
        );
        CREATE INDEX idx_position_column ON dbo.memberShopOrder (orderId);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.memberShopOrder;`);
  }
}

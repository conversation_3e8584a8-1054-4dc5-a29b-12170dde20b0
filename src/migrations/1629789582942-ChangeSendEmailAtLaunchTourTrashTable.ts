import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeSendEmailAtLaunchTourTrashTable1629789582942 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.TourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__TourTrash__isP%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.TourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__TourTrash__isS%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS isSendEmailAtLaunch`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS isPushNotiAtLaunch`);
    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs ADD
        sendEmailAtLaunch bit NOT NULL DEFAULT 1,
        pushNotiAtLaunch bit NOT NULL DEFAULT 1;
      `
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.TourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__TourTrash__push%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.TourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__TourTrash__send%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS sendEmailAtLaunch`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS pushNotiAtLaunch`);
    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs ADD
        isSendEmailAtLaunch bit NOT NULL DEFAULT 1,
        isPushNotiAtLaunch bit NOT NULL DEFAULT 1;
      `
    );
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSeventeentrackCarrierKeyToTTBTable1637559375897 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnSeventeentrackCarrierKey',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnToTMSeventeentrackCarrierKey',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateColumnsLoyaltyActionsTable1705029082135 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions ADD actionName nvarchar(100) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions ADD actionPoints int NULL`);
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions ADD maxPoints int NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions DROP COLUMN IF EXISTS actionName`);
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions DROP COLUMN IF EXISTS actionPoints`);
    await queryRunner.query(`ALTER TABLE dbo.LoyaltyActions DROP COLUMN IF EXISTS maxPoints`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterNameColumnTTBTable1639047382547 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
			ALTER TABLE dbo.TryThenBuy ADD
			firstName nvarchar(50) NULL,
			lastName nvarchar(50) NULL;
      CREATE INDEX idx_first_name_last_name_column ON dbo.TryThenBuy (firstName, lastName);
		`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS firstName`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS lastName`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTryThenBuyFraudInfoTable1681443670889 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.TryThenBuyFraudInfo
            (
                id                   uniqueidentifier NOT NULL PRIMARY KEY,
                tryThenBuyId         uniqueidentifier NOT NULL,
                tryThenBuyIdFraudId  uniqueidentifier NOT NULL,
                isIPAddress          bit   default 0,
                isGPS                bit   default 0,
                isFingerPrint        bit   default 0,
                isLastName           bit   default 0,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TryThenBuyFraudInfo;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnOrderAbleToProductDiscount1706607970306 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts ADD orderable BIT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts DROP COLUMN IF EXISTS orderable`);
  }
}

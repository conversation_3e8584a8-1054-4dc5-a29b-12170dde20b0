import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnStatusToTableImages1701919409149 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Images ADD status nvarchar(20) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Images ADD type nvarchar(20) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Images DROP COLUMN IF EXISTS status`);
    await queryRunner.query(`ALTER TABLE dbo.Images DROP COLUMN IF EXISTS type`);
  }
}

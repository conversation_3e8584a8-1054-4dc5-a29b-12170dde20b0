import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTablePollQuestions1725522231034 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions ADD disabled bit default 1`);
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions ADD imageUrl nvarchar(255) default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions DROP COLUMN IF EXISTS disabled`);
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions DROP COLUMN IF EXISTS imageUrl`);
  }
}

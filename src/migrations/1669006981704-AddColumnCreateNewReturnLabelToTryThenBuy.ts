import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnCreateNewReturnLabelToTryThenBuy1669006981704 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'createNewReturnLabel',
        type: 'bit',
        default: 0,
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIndexesForSubscriptionTable1627489137764 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE INDEX idx_isChecked ON dbo.Subscriptions (isChecked);
    `);
    await queryRunner.query(`
        CREATE INDEX idx_createdAt ON dbo.Subscriptions (createdAt);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

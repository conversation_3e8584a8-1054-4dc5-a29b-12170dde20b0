import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGameProfileQuestions1752116333471 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.GameProfileQuestions
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            name nvarchar(255) NOT NULL,
            category nvarchar(255) NULL,  -- MY_GAME,MY_EQUIPMENT,MY_TEAMS
            subCategory nvarchar(255) NULL,
            sectionName nvarchar(255) NULL,
            questionType nvarchar(100) NULL, -- SINGLE, MULTIPLE
            answerType nvarchar(100) NULL,  -- SINGLE_ONE_COLUMN, SINGLE_TWO_COLUMNS, DROPDOWN_WITH_SEARCH, SEARCH, IMAGE_TILE
            disabled bit default 1, 
            options text NULL,
            createdBy nvarchar(50) NULL,
            updated<PERSON>y nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
        )
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.GameProfileQuestions;`);
  }
}

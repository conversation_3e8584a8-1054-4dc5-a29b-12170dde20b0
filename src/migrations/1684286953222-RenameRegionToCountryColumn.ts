import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameRegionToCountryColumn1684286953222 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.renameColumn('ProductOrders', 'region', 'country');
    await queryRunner.renameColumn('Benefits', 'regions', 'countries');
    await queryRunner.renameColumn('Promotions', 'regions', 'countries');
    await queryRunner.renameColumn('TourTrashs', 'regions', 'countries');
    await queryRunner.renameColumn('HomeWidgets', 'regions', 'countries');
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-unused-vars
  public async down(queryRunner: QueryRunner): Promise<void> {}
}

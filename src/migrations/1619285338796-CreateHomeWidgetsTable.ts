import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateHomeWidgetsTable1619285338796 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.HomeWidgets (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            groupKey nvarchar(255) NOT NULL,
            type nvarchar(255) NOT NULL,
            heading nvarchar(255) NOT NULL,
            title nvarchar(255) NOT NULL,
            description nvarchar(255),
            backgroundColor nvarchar(255),
            backgroundImage nvarchar(255),
            contentType nvarchar(255),
            contentId int,
            contentFormat nvarchar(255),
            ctaText nvarchar(255),
            ctaLink nvarchar(255),
            sortOrder tinyint NOT NULL DEFAULT 1,
            active bit NOT NULL DEFAULT 1,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
    `);
    await queryRunner.query(`
        CREATE INDEX idx_sortOrder ON dbo.HomeWidgets (sortOrder);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.HomeWidgets;`);
  }
}

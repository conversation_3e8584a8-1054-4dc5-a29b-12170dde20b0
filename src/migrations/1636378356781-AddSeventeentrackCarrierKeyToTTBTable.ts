import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSeventeentrackCarrierKeyToTTBTable1636378356781 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'shipmentSeventeentrackCarrierKey',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('TryThenBuy', 'shipmentSeventeentrackCarrierKey');
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStartedAtTryThenBuyTable1619285338785 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.TryThenBuy ADD startedAt datetime NULL
    `);
    await queryRunner.query(`
        CREATE INDEX idx_startedAt ON dbo.TryThenBuy (startedAt);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

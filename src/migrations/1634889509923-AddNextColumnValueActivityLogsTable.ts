import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddNextColumnValueActivityLogsTable1634889509923 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'ActivityLogs',
      new TableColumn({
        name: 'nextColumnValue',
        type: 'nvarchar(4000)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('ActivityLogs', 'nextColumnValue');
  }
}

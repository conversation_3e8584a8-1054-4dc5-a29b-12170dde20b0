import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromotionImagesTable1635834427734 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.PromotionImages (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        imageUrl nvarchar(255) NOT NULL,
        promotionId uniqueidentifier NOT NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      CREATE INDEX idx_promotion_id_column ON dbo.PromotionImages (promotionId);
      ALTER TABLE dbo.PromotionImages ADD CONSTRAINT PromotionImages_FK_1 FOREIGN KEY (promotionId) REFERENCES dbo.Promotions(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.PromotionImages;`);
  }
}

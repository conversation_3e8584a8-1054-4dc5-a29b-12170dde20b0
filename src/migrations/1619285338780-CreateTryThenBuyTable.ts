import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTryThenBuyTable1619285338780 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.TryThenBuy (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId nvarchar(255) NOT NULL,
            orderId nvarchar(255) NOT NULL,
            status nvarchar(255) NOT NULL,
            productInfo nvarchar(1000) NULL,
            pushedAt datetime,
            notifiedGVCAt datetime,
            notifiedEBSAt datetime,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
    `);
    await queryRunner.query(`
        CREATE INDEX idx_pushedAt ON dbo.TryThenBuy (pushedAt);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TryThenBuy;`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddOrderIndexAnswersTable1634097508873 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Answers',
      new TableColumn({
        name: 'orderIndex',
        type: 'int',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('Answers', 'orderIndex');
  }
}

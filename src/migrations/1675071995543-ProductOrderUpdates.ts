import { MigrationInterface, QueryRunner } from 'typeorm';

export class ProductOrderUpdates1675071995543 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD buttonBackgroundColor nvarchar(255) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD buttonTextColor nvarchar(255) NULL
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.ProductOrders ADD backgroundOpacity nvarchar(255) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

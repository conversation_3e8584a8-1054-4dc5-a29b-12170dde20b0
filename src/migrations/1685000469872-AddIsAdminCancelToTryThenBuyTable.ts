import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsAdminCancelToTryThenBuyTable1685000469872 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD isAdminCancel bit NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS isAdminCancel`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveColumnImageProductTable1632234210764 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products DROP COLUMN IF EXISTS image;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products ADD image nvarchar(255) NULL;`);
  }
}

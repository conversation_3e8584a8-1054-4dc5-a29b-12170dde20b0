import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateExternalToolsTable1647846337037 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.ExternalTools (
          id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
          name nvarchar(255) NULL,
          toolUrl nvarchar(500) NULL,
          createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt datetime NULL,
          deletedAt datetime NULL,
          createdBy nvarchar(50) NULL,
          updatedBy nvarchar(50) NULL,
          deletedBy nvarchar(50) NULL
        );
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ExternalTools;`);
  }
}

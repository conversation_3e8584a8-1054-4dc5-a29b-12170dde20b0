import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateVersionTable1733732859722 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.Versions (
              id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
              version nvarchar(255) NOT NULL,
              commitHash nvarchar(255) NULL,
              commitMessage nvarchar(255) NULL,
              branch nvarchar(255) NULL,
              author nvarchar(255) NULL,
              createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updatedAt datetime NULL,
              deletedAt datetime NULL,
            );
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Versions;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeConstraintTourTrashTable1688529417105 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP CONSTRAINT IF EXISTS CK__TourTrash__statu__0CB1922D`);
    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs WITH NOCHECK ADD CONSTRAINT CK__TourTrash__statu__0CB1922D CHECK ([status]='DRAFT' OR [status]='CLOSED' OR [status]='ACTIVE' OR [status]='DELETED' OR [status]='ARCHIVED' OR [status]='COMING SOON')`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP CONSTRAINT IF EXISTS CK__TourTrash__statu__0CB1922D`);
    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs WITH NOCHECK ADD CONSTRAINT CK__TourTrash__statu__0CB1922D CHECK ([status]='DRAFT' OR [status]='CLOSED' OR [status]='ACTIVE' OR [status]='DELETED')`
    );
  }
}

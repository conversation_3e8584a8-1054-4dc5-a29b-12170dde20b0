import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRegionColumnToTablePromotions1684207089065 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Promotions ADD regions nvarchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Promotions DROP COLUMN IF EXISTS regions`);
  }
}

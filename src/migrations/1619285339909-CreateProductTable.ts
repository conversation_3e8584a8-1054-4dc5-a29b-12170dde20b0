import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductTable1619285339909 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Products (
          id int IDENTITY(1,1) NOT NULL,
          status varchar(20)  NOT NULL CHECK (status IN('ENABLE', 'DISABLED')),
          name nvarchar(255)  NOT NULL,
          detail nvarchar(255)  NULL,
          image nvarchar(255)  NULL,
          videoLink nvarchar(255) NULL,
          createdAt datetime DEFAULT getdate() NOT NULL,
          updatedAt datetime NULL,
          tourTrashId int NOT NULL
        );
        ALTER TABLE dbo.Products ADD CONSTRAINT Products_FK_1 FOREIGN KEY (tourTrashId) REFERENCES dbo.TourTrashs(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Products;`);
  }
}

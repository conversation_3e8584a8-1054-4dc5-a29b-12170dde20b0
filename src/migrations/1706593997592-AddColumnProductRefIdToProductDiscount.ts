import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnProductRefIdToProductDiscount1706593997592 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts ADD productRefId nvarchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductDiscounts DROP COLUMN IF EXISTS productRefId`);
  }
}

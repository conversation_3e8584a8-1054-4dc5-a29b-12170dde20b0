import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddAmountPaidToTTBTable1635939345732 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'amountPaid',
        type: 'decimal(6,2)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('TryThenBuy', 'amountPaid');
  }
}

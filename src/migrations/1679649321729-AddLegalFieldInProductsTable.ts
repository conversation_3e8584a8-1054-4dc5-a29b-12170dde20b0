import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLegalFieldInProductsTable1679649321729 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products ADD legal nvarchar(4000) NULL;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Products DROP COLUMN IF EXISTS legal`);
  }
}

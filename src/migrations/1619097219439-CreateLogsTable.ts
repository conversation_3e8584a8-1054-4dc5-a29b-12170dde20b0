import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLogsTable1619097219439 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Logs (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            event varchar(255) NOT NULL,
            data varchar(8000) NOT NULL,
            createdAt datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Logs;`);
  }
}

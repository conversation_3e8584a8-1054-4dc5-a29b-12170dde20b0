import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddEmailVerifiedToUserTable1652804340269 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `
        ALTER TABLE dbo.Users ADD
        emailVerified bit NULL,
        tmAccessCode bit NULL,
        myTMSubscriptionLevel int NULL;
    `
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

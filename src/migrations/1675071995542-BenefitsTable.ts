import { MigrationInterface, QueryRunner } from 'typeorm';

export class BenefitsTable1675071995542 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Benefits
        (
            id                   uniqueidentifier NOT NULL PRIMARY KEY,
            title                nvarchar(255)    NOT NULL,
            description          text,
            imageUrl             nvarchar(255),
            partnerBrandImageUrl nvarchar(255),
            buttonTitle          nvarchar(255),
            url                  nvarchar(255),
            disabled             bigint   default 0,
            sortOrder            int      default 1,
            createdBy            nvarchar(255)    NOT NULL,
            updatedBy            nvarchar(255)    NOT NULL,
            createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt            datetime         NULL,
            deletedAt            datetime         NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

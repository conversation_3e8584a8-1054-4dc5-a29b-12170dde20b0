import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserPermissionChampion1673580199420 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE [dbo].[Permission] SET virtualCoaching = 1 WHERE planId = 1;
    `);
    await queryRunner.query(`
    UPDATE [dbo].[UserPermissions]
    SET UserPermissions.canVirtualCoaching = 1 
    FROM UserPermissions LEFT JOIN Users on UserPermissions.userId = Users.id  
    WHERE myTMSubscriptionLevel = 1
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

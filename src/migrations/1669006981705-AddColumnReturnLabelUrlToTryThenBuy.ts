import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddColumnReturnLabelUrlToTryThenBuy1669006981705 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'returnLabelUrl',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

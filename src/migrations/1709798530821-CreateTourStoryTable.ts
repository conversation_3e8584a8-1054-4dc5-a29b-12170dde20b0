import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTourStoryTable1709798530821 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.TourStory
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            type nvarchar(20) NULL,
            url nvarchar(255) NOT NULL,
            options nvarchar(255) NULL,
            countries nvarchar(255) NULL,
            totalView int DEFAULT(0),
            disabled bit DEFAULT(0),
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL
        )
      
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TourStory;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnSIUserIdToTableUsers1710489407354 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD isAgreeShareData BIT NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD siUserId varchar(90) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD siEmail varchar(90) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD service varchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS isAgreeShareData`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS siUserId`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS siEmail`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS service`);
  }
}

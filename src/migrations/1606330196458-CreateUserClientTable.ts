import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserClientTable1606330196458 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
            SET ANSI_NULLS ON
            SET QUOTED_IDENTIFIER ON
            CREATE TABLE [dbo].[Users](
                [id] [uniqueidentifier] NOT NULL,
                [email] [nvarchar](255) NOT NULL,
                [cdmUID] [nvarchar](255) NOT NULL,
                [regionId] [nvarchar](255) NULL,
                [isNewAccount] [bit] NULL,
                [fcmToken] [varchar](1000) NULL
            ) ON [PRIMARY]
            ALTER TABLE [dbo].[Users] ADD PRIMARY KEY CLUSTERED
            (
                [id] ASC
            )WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
        `);

    await queryRunner.query(`
            SET ANSI_NULLS ON
            SET QUOTED_IDENTIFIER ON
            CREATE TABLE [dbo].[Clients](
                [id] [uniqueidentifier] NOT NULL,
                [name] [varchar](255) NULL,
                [apiKey] [varchar](1000) NULL,
                [active] [bit] NULL
            ) ON [PRIMARY]
            ALTER TABLE [dbo].[Clients] ADD PRIMARY KEY CLUSTERED
            (
                [id] ASC
            )WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
            ALTER TABLE [dbo].[Clients] ADD  DEFAULT ((1)) FOR [active]
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {}
}

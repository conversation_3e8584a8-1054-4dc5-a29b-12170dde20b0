import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAdminPortalClientId1619285338795 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON><PERSON>, active)
        VALUES ('5bc1d523-59dd-4727-9659-b3c649b9060d', 'ADMIN_PORTAL', '8e748608-1ba9-45c5-98e4-8ccd5038d2e0', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDobOffsetUsersTable1642587454925 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Users',
      new TableColumn({
        name: 'dobOffset',
        type: 'int',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMRPClientId1619285338793 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON><PERSON>, active)
        VALUES ('5b3d4815-0b9c-4685-82c4-6148279b4814', 'MRP', '246b7741-8aac-4e70-becf-14286726ec1e', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGameProfileAnswers1752116355815 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.GameProfileAnswers
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            gameProfileQuestionId nvarchar(60) NOT NULL,
            title nvarchar(255) NOT NULL,
            subTitle nvarchar(255) NULL,
            imageLink nvarchar(255) NULL,
            options text NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
        )
        CREATE INDEX idx_game_profile_question_id ON dbo.GameProfileAnswers (gameProfileQuestionId);
          `);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS idx_game_profile_question_id ON dbo.GameProfileAnswers;`);
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.GameProfileAnswers;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePerksTable1693298266202 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Benefits ADD linkType varchar(255) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Benefits ADD deepLink varchar(255) NULL`);
    await queryRunner.query(`UPDATE dbo.Benefits set linkType = 'WEB_LINK'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Benefits DROP COLUMN IF EXISTS linkType`);
    await queryRunner.query(`ALTER TABLE dbo.Benefits DROP COLUMN IF EXISTS deepLink`);
  }
}

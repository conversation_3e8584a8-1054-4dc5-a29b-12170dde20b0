import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableProductDiscounts1699585618584 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.ProductDiscounts (
              id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
              product_id nvarchar(255) NULL,
              brandLogo nvarchar(500) NULL,
              image nvarchar(500) NULL,
              sortOrder int,
              tag nvarchar(255) NULL,
              productName nvarchar(255) NULL,
              description nvarchar(255) NULL,
              options nvarchar(255) NULL,
              country nvarchar(30) NULL,
              price nvarchar(20) NULL,
              priceDiscount nvarchar(20) NULL,
              createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updatedAt datetime NULL,
              deletedAt datetime NULL,
              createdBy nvarchar(50) NULL,
              updatedBy nvarchar(50) NULL,
              deletedBy nvarchar(50) NULL
            );
            CREATE INDEX idx_ProductDiscount_Product_Id ON dbo.ProductDiscounts (product_id);
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ProductDiscounts;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_ProductDiscount_Product_id ON dbo.ProductDiscounts;`);
  }
}

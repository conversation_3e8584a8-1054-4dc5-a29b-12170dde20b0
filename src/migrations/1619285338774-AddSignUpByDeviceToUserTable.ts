import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSignUpByDeviceToUserTable1619285338774 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Users ADD signUpByDevice nvarchar(50) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Users DROP COLUMN signUpByDevice
    `);
  }
}

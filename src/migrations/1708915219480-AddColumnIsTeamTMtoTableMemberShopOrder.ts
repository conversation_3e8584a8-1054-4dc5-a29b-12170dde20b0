import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnIsTeamTMtoTableMemberShopOrder1708915219480 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrder ADD isTeamTM BIT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrder DROP COLUMN IF EXISTS isTeamTM`);
  }
}

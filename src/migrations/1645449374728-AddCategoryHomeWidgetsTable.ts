import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddCategoryHomeWidgetsTable1645449374728 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'HomeWidgets',
      new TableColumn({
        name: 'category',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

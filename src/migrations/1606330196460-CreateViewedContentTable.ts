import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFavoriteContentTable1606330196459 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`
        CREATE TABLE dbo.FavoriteContents (
            id int IDENTITY(1,1) NOT NULL PRIMARY KEY,
            userId nchar(255) NOT NULL,
            contentId nchar(255) NOT NULL,
            createdAt datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.FavoriteContents;`);
  }
}

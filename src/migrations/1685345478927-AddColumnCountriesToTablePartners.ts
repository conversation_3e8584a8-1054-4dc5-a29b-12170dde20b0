import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnCountriesToTablePartners1685345478927 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Partners ADD countries nvarchar(30) NULL`);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  public async down(queryRunner: QueryRunner): Promise<void> {}
}

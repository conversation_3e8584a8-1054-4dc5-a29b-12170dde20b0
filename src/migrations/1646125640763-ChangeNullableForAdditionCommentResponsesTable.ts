import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ChangeNullableForAdditionCommentResponsesTable1646125640763 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('Responses', 'additionalComment');
    await queryRunner.addColumn(
      'Responses',
      new TableColumn({
        name: 'additionalComment',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

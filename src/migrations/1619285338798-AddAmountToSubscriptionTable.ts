import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAmountToSubscriptionTable1619285338798 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.Subscriptions ADD amount decimal(6,2) NULL, notificationType nvarchar(255) NULL, expirationDate datetime
    `);
    await queryRunner.query(`
        CREATE INDEX idx_expirationDate ON dbo.Subscriptions (expirationDate);
    `);
    await queryRunner.query(`
        CREATE INDEX idx_notificationType ON dbo.Subscriptions (notificationType);
    `);
    await queryRunner.query(`
        CREATE INDEX idx_userId ON dbo.Subscriptions (userId);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.Subscriptions DROP COLUMN amount, notificationType, expirationDate
    `);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddColumnRegionToTableHomeWidgets1684135801375 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets ADD regions nvarchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.HomeWidgets DROP COLUMN IF EXISTS regions`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeColumnTourTrashTable1629757943944 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS remindFrequency`);
    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs ADD
        remindPushNotiFrequency int NULL,
        isSendEmailAtLaunch bit NOT NULL DEFAULT 1,
        isPushNotiAtLaunch bit NOT NULL DEFAULT 1,
        timeToSendEmailBeforeEndDate int NULL,
        timeToPushNotiBeforeEndDate int NULL,
        timeToAutoCloseAfterEndDate int NULL;
      `
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.TourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__TourTrash__isP%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`
    DECLARE @table nvarchar(50)
    set @table = 'dbo.TourTrashs'
    DECLARE @sql nvarchar(255)
    BEGIN
      select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
      from sys.default_constraints
      where name LIKE 'DF__TourTrash__isS%'
      exec sp_executesql @sql
    END`);

    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS isSendEmailAtLaunch`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS isPushNotiAtLaunch`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS remindPushNotiFrequency`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS timeToSendEmailBeforeEndDate`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS timeToPushNotiBeforeEndDate`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS timeToAutoCloseAfterEndDate`);

    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD remindFrequency int NULL;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeStatusActiveToAvailableAccessCodesTable1638761031639 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `UPDATE dbo.AccessCodes SET status = 'AVAILABLE' WHERE status = 'ACTIVE' AND userId IS NULL;`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

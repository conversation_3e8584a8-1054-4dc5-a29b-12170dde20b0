import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldOptionsInTourTrashTable1745294114265 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD options nvarchar(255) default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS options`);
  }
}

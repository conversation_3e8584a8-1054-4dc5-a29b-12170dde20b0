import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDEClientId1634107136226 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON><PERSON>, active)
        VALUES ('51448e6a-4333-41ee-bdf7-37302e190014', 'DE', '2dab4015-4c9d-4fbc-90c4-be65ab6da565', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

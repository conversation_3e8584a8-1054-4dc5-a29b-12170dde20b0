import { MigrationInterface, QueryRunner } from 'typeorm';

export class createTableMemberShopOrderTrackingNumber1639714561720 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.MemberShopOrderTrackingNumber (
          id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
          orderId nvarchar(255) NOT NULL,
          trackingNumber nvarchar(255) NOT NULL,
          status nvarchar(50) NULL,
          createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt datetime NULL,
          deletedAt datetime NULL,
          createdBy nvarchar(50) NULL,
          updatedBy nvarchar(50) NULL,
          deletedBy nvarchar(50) NULL
        );
        CREATE INDEX idx_position_column ON dbo.MemberShopOrderTrackingNumber (orderId, trackingNumber);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.MemberShopOrderTrackingNumber;`);
  }
}

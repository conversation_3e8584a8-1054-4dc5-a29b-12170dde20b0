import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldGatStatustoTableTryThenBuy1682412686926 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD gatStatus nvarchar(250) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS gatStatus`);
  }
}

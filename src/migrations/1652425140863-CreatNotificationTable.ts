import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatNotificationTable1652425140863 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.Notification (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        ctaLink nvarchar(255) NULL,
        notificationType nvarchar(255) NOT NULL,
        name nvarchar(255) NOT NULL,
        message nvarchar(255) NULL,
        variables nvarchar(255) NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Notification;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class InsertPlanPermissionToTablePermission1669284589817 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO [dbo].[Permission] (id,planId,calculateHandicap,mfe,mrp,videoInstruction,freeTwoDaysShipping,freeShipping,community,winTourTrash,insights,tryThenBuy,productDrops,virtualCoaching) VALUES (N'75196014-4035-4a71-9909-8b9a0f3b7c2a',2,N'1',N'1',N'1',N'1',N'1',N'0',N'1',N'1',N'1',N'1',N'1',N'1');
        INSERT INTO [dbo].[Permission] (id,planId,calculateHandicap,mfe,mrp,videoInstruction,freeTwoDaysShipping,freeShipping,community,winTourTrash,insights,tryThenBuy,productDrops,virtualCoaching) VALUES (N'e0006e69-82c8-4d47-bccf-ff7f0536c703',1,N'1',N'1',N'1',N'1',N'1',N'0',N'1',N'1',N'1',N'1',N'1',N'0');
        UPDATE [dbo].[Permission] SET planId=0,freeShipping=N'1',mfe=N'1',mrp=N'1',calculateHandicap=N'1' WHERE id='0AF057C7-EB5F-493A-92B0-2E8EF11D625F';
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

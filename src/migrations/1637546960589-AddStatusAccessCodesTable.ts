import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddStatusAccessCodesTable1637546960589 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'AccessCodes',
      new TableColumn({
        name: 'status',
        type: 'nvarchar(50)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('AccessCodes', 'status');
  }
}

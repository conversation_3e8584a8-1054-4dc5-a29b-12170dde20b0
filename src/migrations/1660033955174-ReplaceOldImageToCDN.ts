import { MigrationInterface, QueryRunner } from 'typeorm';

export class ReplaceOldImageToCDN1660033955174 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        UPDATE [dbo].[ContentTagThumbnails]
        SET imageUrl = REPLACE(imageUrl, 'storagedev.blob.core.windows', 'assets.azureedge'),
         imageReg = REPLACE(imageReg, 'storagedev.blob.core.windows', 'assets.azureedge'),  
         imageLarge = REPLACE(imageLarge, 'storagedev.blob.core.windows', 'assets.azureedge'),
         imageSmall = REPLACE(imageSmall, 'storagedev.blob.core.windows', 'assets.azureedge'),  
         imageThumb = REPLACE(imageThumb, 'storagedev.blob.core.windows', 'assets.azureedge')  
    `);
    await queryRunner.query(`
        UPDATE [dbo].[ContentTagThumbnails]
        SET 
         imageUrl = REPLACE(imageUrl, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge'), 
         imageReg = REPLACE(imageReg, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge'),  
         imageLarge = REPLACE(imageLarge, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge'),
         imageSmall = REPLACE(imageSmall, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge'),  
         imageThumb = REPLACE(imageThumb, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge')  
    `);
    await queryRunner.query(`
        UPDATE [dbo].[ProductImages]
        SET imageUrl = REPLACE(imageUrl, 'storagedev.blob.core.windows', 'assets.azureedge')
    `);
    await queryRunner.query(`
        UPDATE [dbo].[ProductImages]
        SET imageUrl = REPLACE(imageUrl, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge')
    `);
    await queryRunner.query(`
        UPDATE [dbo].[PromotionImages]
        SET imageUrl = REPLACE(imageUrl, 'storagedev.blob.core.windows', 'assets.azureedge')
    `);
    await queryRunner.query(`
        UPDATE [dbo].[PromotionImages]
        SET imageUrl = REPLACE(imageUrl, 'mytmstorageprod.blob.core.windows', 'mymtmassetsprod.azureedge')
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

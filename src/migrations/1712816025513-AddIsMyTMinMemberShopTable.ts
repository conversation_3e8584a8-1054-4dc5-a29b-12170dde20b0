import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsMyTMinMemberShopTable1712816025513 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrder ADD isMyTM BIT NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrder DROP COLUMN IF EXISTS isMyTM`);
  }
}

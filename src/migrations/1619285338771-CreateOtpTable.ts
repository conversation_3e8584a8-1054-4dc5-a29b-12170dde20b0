import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateOtpTable1619285338771 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.UserOtps (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId nvarchar(255) NOT NULL,
            code nvarchar(20) NOT NULL,
            status varchar(10) NOT NULL CHECK (status IN('ACTIVE', 'DEACTIVE', 'VERIFIED')),
            entries int DEFAULT 0,
            expireTime datetime NOT NULL,
            createdAt datetime DEFAULT CURRENT_TIMESTAMP,
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserOtps;`);
  }
}

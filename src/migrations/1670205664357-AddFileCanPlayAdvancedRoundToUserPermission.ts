import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddFileCanPlayAdvancedRoundToUserPermission1670205664357 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Permission',
      new TableColumn({
        name: 'playAdvancedRound',
        type: 'bit',
        isNullable: true,
        default: 0,
      })
    );
    await queryRunner.addColumn(
      'UserPermissions',
      new TableColumn({
        name: 'canPlayAdvancedRound',
        type: 'bit',
        isNullable: true,
        default: 0,
      })
    );
    await queryRunner.query(`
        UPDATE [dbo].[Permission] SET playAdvancedRound = 1 WHERE planId != 0;
    `);
    await queryRunner.query(`
        UPDATE [dbo].[Permission] SET playAdvancedRound = 0 WHERE (planId = 0 OR planId is NULL);
    `);
    await queryRunner.query(`
        UPDATE [dbo].[UserPermissions] SET canPlayAdvancedRound = canExclProductDrops;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

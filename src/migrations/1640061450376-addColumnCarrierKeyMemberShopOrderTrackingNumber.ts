import { MigrationInterface, QueryRunner } from 'typeorm';

export class addColumnCarrierKeyMemberShopOrderTrackingNumber1640061450376 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            ALTER TABLE dbo.MemberShopOrderTrackingNumber ADD
            carrierKey nvarchar(250) NULL,
            carrier nvarchar(250) NULL;
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrderTrackingNumber DROP COLUMN IF EXISTS carrierKey`);
    await queryRunner.query(`ALTER TABLE dbo.MemberShopOrderTrackingNumber DROP COLUMN IF EXISTS carrier`);
  }
}

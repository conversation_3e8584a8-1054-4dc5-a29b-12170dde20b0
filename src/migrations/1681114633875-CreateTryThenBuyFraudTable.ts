import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTryThenBuyFraudTable1681114633875 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.TryThenBuyFraud
            (
                id                   uniqueidentifier NOT NULL PRIMARY KEY,
                tryThenBuyId         uniqueidentifier NULL,
                orderId              nvarchar(255) NULL,
                userId               nvarchar(255) NOT NULL,
                email                nvarchar(255) NULL,
                firstName            nvarchar(50) NULL,
                lastName             nvarchar(50) NULL,
                purchaseIPAddress    nvarchar(100) NULL,
                isSimulator          bit DEFAULT 0,
                isUserBlackList      bit DEFAULT 0,
                fingerPrint          nvarchar(250),
                purchaseLatitude     float DEFAULT 0,
                purchaseLongitude    float DEFAULT 0,
                potentialFraud       float DEFAULT 0,
                note                 nvarchar(255) NULL,
                duplicate            nvarchar(255) NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            );
            ALTER TABLE dbo.TryThenBuyFraud ADD CONSTRAINT TryThenBuy_FK_1 FOREIGN KEY (tryThenBuyId) REFERENCES dbo.TryThenBuy(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TryThenBuyFraud;`);
  }
}

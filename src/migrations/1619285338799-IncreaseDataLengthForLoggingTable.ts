import { MigrationInterface, QueryRunner } from 'typeorm';

export class IncreaseDataLengthForLoggingTable1619285338799 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Logs DROP COLUMN data
    `);
    await queryRunner.query(`
        ALTER TABLE dbo.Logs ADD data varchar(MAX)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

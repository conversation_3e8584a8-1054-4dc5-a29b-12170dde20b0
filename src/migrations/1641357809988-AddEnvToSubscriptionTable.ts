import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddEnvToSubscriptionTable1641357809988 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Subscriptions',
      new TableColumn({
        name: 'environment',
        type: 'nvarchar(250)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'SubscriptionEventLogs',
      new TableColumn({
        name: 'environment',
        type: 'nvarchar(250)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

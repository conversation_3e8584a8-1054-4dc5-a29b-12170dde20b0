import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateResponsesTable1633592470638 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.Responses (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        userId uniqueidentifier NOT NULL,
        surveyId uniqueidentifier NOT NULL,
        refId uniqueidentifier NULL,
        additionalComment nvarchar(4000) NOT NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      ALTER TABLE dbo.Responses ADD CONSTRAINT Responses_User_FK FOREIGN KEY (userId) REFERENCES dbo.Users(id);
      ALTER TABLE dbo.Responses ADD CONSTRAINT Responses_Survey_FK FOREIGN KEY (surveyId) REFERENCES dbo.Surveys(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Responses;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class alterTableMemberShopOrder1639644872278 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.memberShopOrder;`);
    await queryRunner.query(`
        CREATE TABLE dbo.MemberShopOrder (
          id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
          orderId nvarchar(255) NOT NULL,
          cdmPrimaryEmail nvarchar(255) NOT NULL,
          description TEXT NULL,
          status  nvarchar(50) NOT NULL,
          isRegistered17Track BIT NOT NULL DEFAULT 0,
          createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt datetime NULL,
          deletedAt datetime NULL,
          createdBy nvarchar(50) NULL,
          updatedBy nvarchar(50) NULL,
          deletedBy nvarchar(50) NULL
        );
        CREATE INDEX idx_position_column ON dbo.MemberShopOrder (orderId);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.MemberShopOrder;`);
  }
}

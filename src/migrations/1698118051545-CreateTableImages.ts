import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableImages1698118051545 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.Images
            (
                id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
                keyImage             nvarchar(255) NOT NULL,
                urlImage             nvarchar(255) NOT NULL,
                createdBy            nvarchar(255)     NULL,
                updatedBy            nvarchar(255)     NULL,
                deletedBy            nvarchar(255)     NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            )
          
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Images;`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class ChangeProductInfoColumnOnTTBTable1636530742561 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('TryThenBuy', 'productInfo');
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'productInfo',
        type: 'varchar(max)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

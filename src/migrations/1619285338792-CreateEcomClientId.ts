import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateEcomClientId1619285338792 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON>ey, active)
        VALUES ('4bd2b9da-7ca6-4cc3-8ac1-e0f1a3f5f764', 'ECOM', '4db73865-9ef5-40cc-b155-3d58f57ac1e4', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

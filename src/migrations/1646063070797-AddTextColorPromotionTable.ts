import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddTextColorPromotionTable1646063070797 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Promotions',
      new TableColumn({
        name: 'textColor',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

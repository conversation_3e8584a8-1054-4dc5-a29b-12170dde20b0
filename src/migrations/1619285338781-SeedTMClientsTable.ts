import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedTMClientsTable1619285338781 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON><PERSON>, active)
        VALUES ('a96f8212-c08e-47ba-8d4f-3435bbb0e6ab', 'EBS', 'fe8bc704-8703-474e-9994-510970091616', 1);
        INSERT INTO dbo.Clients (id, name, apiKey, active)
        VALUES ('f54b9f39-77d4-4110-b110-78312b841800', 'SWING_INDEX', '0f0929a4-31ef-4c6b-82e1-92abab5de6b3', 1);
        INSERT INTO dbo.Clients (id, name, apiKey, active)
        VALUES ('61ced299-1ff9-4e5e-bda9-60f97f4a8fd5', 'GVC', '4e018423-0918-4634-ac7f-f707053046a2', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

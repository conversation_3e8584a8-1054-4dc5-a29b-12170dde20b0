import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserReferralTable1688448059840 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.UserReferral
        (
            id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            email                nvarchar(255) NOT NULL,
            userId               nvarchar(255) NOT NULL,
            createdBy            nvarchar(255)     NULL,
            updatedBy            nvarchar(255)     NULL,
            createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt            datetime         NULL,
            deletedAt            datetime         NULL
        )
        CREATE INDEX idx_userId ON dbo.UserReferral (userId);
        CREATE INDEX idx_email ON dbo.UserReferral (email);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserReferral;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_userId ON dbo.UserReferral;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_email ON dbo.UserReferral;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCancelStatusToTryThenBuyTable1629792818982 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.TryThenBuy ADD canceledAt datetime NULL, canceledBy varchar(255) NULL;
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddGracePeriodToUsersTable1641357809989 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Users',
      new TableColumn({
        name: 'isInGracePeriod',
        type: 'bit',
        default: '0',
      })
    );
    await queryRunner.addColumn(
      'Users',
      new TableColumn({
        name: 'gracePeriodEndAt',
        type: 'datetime',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'Users',
      new TableColumn({
        name: 'lastActivatedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

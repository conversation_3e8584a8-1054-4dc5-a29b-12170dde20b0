import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateProductImagesTable1632217011932 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.ProductImages (
        uuid uniqueidentifier NOT NULL,
        productUuid uniqueidentifier NOT NULL,
        imageUrl nvarchar(255) NOT NULL,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      ALTER TABLE dbo.ProductImages ADD CONSTRAINT ProductImages_FK_1 FOREIGN KEY (productUuid) REFERENCES dbo.Products(uuid);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ProductImages;`);
  }
}

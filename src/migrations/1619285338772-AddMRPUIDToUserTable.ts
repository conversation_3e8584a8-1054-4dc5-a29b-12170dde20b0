import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMRPUIDToUserTable1619285338772 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Users ADD mrpUID nvarchar(20) NULL
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        ALTER TABLE dbo.Users DROP COLUMN mrpUID
    `);
  }
}

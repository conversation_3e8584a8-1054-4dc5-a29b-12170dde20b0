import { MigrationInterface, QueryRunner } from 'typeorm';

export class addIsCreateLoyaltyUserTable1695810132592 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD is_user_annex bit default 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS is_user_annex`);
  }
}

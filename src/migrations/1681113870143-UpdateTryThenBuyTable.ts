import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTryThenBuyTable1681113870143 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD isSimulator bit DEFAULT 0;`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD fingerPrint nvarchar(250) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD purchaseLatitude float DEFAULT 0`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy ADD purchaseLongitude float DEFAULT 0`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS isSimulator`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS fingerPrint`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS purchaseLatitude`);
    await queryRunner.query(`ALTER TABLE dbo.TryThenBuy DROP COLUMN IF EXISTS purchaseLongitude`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserCourseLogsTable1713943838802 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.UserCourseLogs
            (
                id                   uniqueidentifier NOT NULL PRIMARY KEY,
                userId               uniqueidentifier NOT NULL,
                params               varchar(255) NULL,
                requestTime          datetime         NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            );
    `);
    await queryRunner.query(`
        CREATE INDEX idx_userId ON dbo.UserCourseLogs (userId);
        CREATE INDEX idx_requestTime ON dbo.UserCourseLogs (requestTime);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserNotifyAdmin;`);
    await queryRunner.query(`DROP INDEX idx_userId ON dbo.UserNotifyAdmin;`);
    await queryRunner.query(`DROP INDEX idx_requestTime ON dbo.UserNotifyAdmin;`);
  }
}

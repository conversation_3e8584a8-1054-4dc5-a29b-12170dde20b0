import { MigrationInterface, QueryRunner } from 'typeorm';

export class PartnersTable1678780924750 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Partners
        (
            id                   uniqueidentifier NOT NULL PRIMARY KEY,
            title                nvarchar(255)    NOT NULL,
            partnerBrandImageUrl nvarchar(255),
            createdBy            nvarchar(255)    NOT NULL,
            updatedBy            nvarchar(255)    NOT NULL,
            createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt            datetime         NULL,
            deletedAt            datetime         NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Partners;`);
  }
}

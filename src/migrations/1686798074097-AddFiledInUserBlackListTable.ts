import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFiledInUserBlackListTable1686798074097 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserBlackList ADD updatedBy nvarchar(50) NULL`);
    await queryRunner.query(`ALTER TABLE dbo.UserBlackList ADD deletedBy nvarchar(50) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.UserBlackList DROP COLUMN IF EXISTS updatedBy`);
    await queryRunner.query(`ALTER TABLE dbo.UserBlackList DROP COLUMN IF EXISTS deletedBy`);
  }
}

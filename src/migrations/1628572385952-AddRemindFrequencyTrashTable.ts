import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRemindFrequencyTrashTable1628572385952 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS remindBeforeDay`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS participationLevel`);
    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs ADD remindFrequency int NULL, participationLevel varchar(50) NULL`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS remindFrequency`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS participationLevel`);

    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD remindBeforeDay int NULL, participationLevel int NULL`);
  }
}

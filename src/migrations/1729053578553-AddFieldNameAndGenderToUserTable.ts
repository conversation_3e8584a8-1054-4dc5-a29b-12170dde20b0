import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldNameAndGenderToUserTable1729053578553 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users ADD firstNameTM nvarchar(255) default NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD lastNameTM nvarchar(255) default NULL`);
    await queryRunner.query(`ALTER TABLE dbo.Users ADD genderTM nvarchar(255) default NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS firstNameTM`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS lastNameTM`);
    await queryRunner.query(`ALTER TABLE dbo.Users DROP COLUMN IF EXISTS genderTM`);
  }
}

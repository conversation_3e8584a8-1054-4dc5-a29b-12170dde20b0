// // import { MigrationInterface, QueryRunner, TableIndex, Table } from 'typeorm';
//
// const TABLE_NAME = 'sample';
//
// export class CreateTable1606330196457 implements MigrationInterface {
//   public async up(queryRunner: QueryRunner): Promise<void> {
//     await queryRunner.createTable(
//       new Table({
//         name: TABLE_NAME,
//         columns: [
//           {
//             name: 'id',
//             type: 'int',
//             isPrimary: true,
//           },
//         ],
//       }),
//       true,
//     );
//     await queryRunner.createIndex(
//       TABLE_NAME,
//       new TableIndex({
//         name: `IDX-${TABLE_NAME.toUpperCase()}`,
//         columnNames: ['id'],
//       }),
//     );
//   }
//
//   public async down(queryRunner: QueryRunner): Promise<void> {
//     await queryRunner.dropIndex(TABLE_NAME, `IDX-${TABLE_NAME}`);
//     await queryRunner.dropTable(TABLE_NAME);
//   }
// }

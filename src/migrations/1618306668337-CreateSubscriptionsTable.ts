import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionsTable1618306668337 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.Subscriptions (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId nvarchar(255) NOT NULL,
            quantity nvarchar(255),
            productId nvarchar(255),
            transactionId nvarchar(255),
            originalTransactionId nvarchar(255),
            purchaseDate nvarchar(255),
            purchaseDateMs nvarchar(255),
            purchaseDatePst nvarchar(255),
            originalPurchaseDate nvarchar(255),
            originalPurchaseDateMs nvarchar(255),
            originalPurchaseDatePst nvarchar(255),
            expiresDate nvarchar(255),
            expiresDateMs nvarchar(255),
            expiresDatePst nvarchar(255),
            webOrderLineItemId nvarchar(255),
            isTrialPeriod nvarchar(255),
            isInIntroOfferPeriod nvarchar(255),
            subscriptionGroupIdentifier nvarchar(255),
            createdAt datetime DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Subscriptions;`);
  }
}

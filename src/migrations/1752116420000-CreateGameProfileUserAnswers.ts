import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateGameProfileUserAnswers1752116420000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.GameProfileUserAnswers
        (
            id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
            userId nvarchar(60) NOT NULL,
            gameProfileQuestionId nvarchar(60) NOT NULL,
            gameProfileAnswerId nvarchar(60) NOT NULL,
            createdBy nvarchar(50) NULL,
            updatedBy nvarchar(50) NULL,
            deletedBy nvarchar(50) NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updatedAt datetime NULL,
            deletedAt datetime NULL,
        )
        CREATE INDEX idx_game_profile_user_answer_user_id ON dbo.GameProfileUserAnswers (userId);
        CREATE INDEX idx_game_profile_user_answer_question_id ON dbo.GameProfileUserAnswers (gameProfileQuestionId);
        CREATE INDEX idx_game_profile_user_answer_answer_id ON dbo.GameProfileUserAnswers (gameProfileAnswerId);
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS idx_game_profile_user_answer_user_id ON dbo.GameProfileUserAnswers;`);
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_game_profile_user_answer_question_id ON dbo.GameProfileUserAnswers;`
    );
    await queryRunner.query(
      `DROP INDEX IF EXISTS idx_game_profile_user_answer_answer_id ON dbo.GameProfileUserAnswers;`
    );
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.GameProfileUserAnswers;`);
  }
}

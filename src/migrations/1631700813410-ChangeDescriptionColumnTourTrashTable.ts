import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeDescriptionColumnTourTrashTable1631700813410 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS description`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD description nvarchar(4000) NULL;`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS description`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD description nvarchar(255) NULL;`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddSpecsMismatchedToTryThenBuyTable1643006901884 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnTrackingNumber',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnTrackingCarrier',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnSeventeentrackCarrierKey',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnReceivedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnWrongComponentsTrackingNumber',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnWrongComponentsSeventeentrackCarrierKey',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnWrongComponentsTrackingCarrier',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnWrongComponentsStatus',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'specsMismatchedReturnWrongComponentsReceivedAt',
        type: 'datetime',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

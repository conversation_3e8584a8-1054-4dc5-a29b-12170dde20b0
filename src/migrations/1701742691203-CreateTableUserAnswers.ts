import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableUserAnswers1701742691203 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
                    CREATE TABLE dbo.UserAnswers
                    (
                        id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
                        questionId           nvarchar(60) NOT NULL,
                        userId               nvarchar(60) NOT NULL,
                        answerId             nvarchar(60) NOT NULL,
                        createdBy            nvarchar(255)     NULL,
                        updatedBy            nvarchar(255)     NULL,
                        deletedBy            nvarchar(255)     NULL,
                        createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                        updatedAt            datetime         NULL,
                        deletedAt            datetime         NULL
                    );
                    CREATE INDEX idx_user_answer_question_id ON dbo.UserAnswers (questionId);
                    CREATE INDEX idx_user_answer_answer_id ON dbo.UserAnswers (answerId);
                    CREATE INDEX idx_user_answer_user_id ON dbo.UserAnswers (userId);
                `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS idx_user_answer_question_id ON dbo.UserAnswers;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_user_answer_answer_id ON dbo.UserAnswers;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_user_answer_user_id ON dbo.UserAnswers;`);
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserAnswers;`);
  }
}

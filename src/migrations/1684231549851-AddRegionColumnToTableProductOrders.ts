import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRegionColumnToTableProductOrders1684231549851 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductOrders ADD region nvarchar(40) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.ProductOrders DROP COLUMN IF EXISTS region`);
  }
}

import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddReturningTrackingNumberToTTBTable1637559375896 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnTrackingNumber',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnTrackingCarrier',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnToTMTrackingNumber',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.addColumn(
      'TryThenBuy',
      new TableColumn({
        name: 'GVCReturnToTMTrackingCarrier',
        type: 'nvarchar(255)',
        isNullable: true,
      })
    );
    await queryRunner.dropColumn('TryThenBuy', 'carrier');
    await queryRunner.dropColumn('TryThenBuy', 'carrierTrackingNumber');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

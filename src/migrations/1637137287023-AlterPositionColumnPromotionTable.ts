import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterPositionColumnPromotionTable1637137287023 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        EXEC sp_rename N'dbo.Promotions.position' , N'feature', 'COLUMN';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.Promotions DROP COLUMN IF EXISTS feature`);
  }
}

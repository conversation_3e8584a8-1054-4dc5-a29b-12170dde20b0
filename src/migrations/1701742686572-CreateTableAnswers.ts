import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableAnswers1701742686572 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
                CREATE TABLE dbo.PollAnswers
                (
                    id                   uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
                    questionId           nvarchar(60) NOT NULL,
                    answerTitle                nvarchar(255) NOT NULL,
                    answerDescription          nvarchar(255)     NULL,
                    createdBy            nvarchar(255)     NULL,
                    updatedBy            nvarchar(255)     NULL,
                    deletedBy            nvarchar(255)     NULL,
                    createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                    updatedAt            datetime         NULL,
                    deletedAt            datetime         NULL
                );
                CREATE INDEX idx_answer_question_id ON dbo.PollAnswers (questionId);

            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX IF EXISTS idx_answer_question_id ON dbo.PollAnswers;`);
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.PollAnswers;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class SeedMFEClientTable1619285338788 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        INSERT INTO dbo.Clients (id, name, api<PERSON><PERSON>, active)
        VALUES ('a44a4108-6a58-4a2d-aee0-328cfe4af16d', 'MFE', 'd10d2416-4d93-4b1c-b74a-536f941b0fac', 1);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

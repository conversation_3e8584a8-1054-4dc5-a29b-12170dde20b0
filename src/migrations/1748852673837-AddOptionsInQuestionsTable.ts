import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddOptionsInQuestionsTable1748852673837 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions ADD options nvarchar(255) NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.PollQuestions DROP COLUMN IF EXISTS options`);
  }
}

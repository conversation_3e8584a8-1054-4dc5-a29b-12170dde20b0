import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAnswersTable1633592421086 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE dbo.Answers (
        id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
        questionId uniqueidentifier NOT NULL,
        answer nvarchar(255) NOT NULL,
        status BIT NOT NULL DEFAULT 0,
        createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updatedAt datetime NULL,
        deletedAt datetime NULL,
        createdBy nvarchar(50) NULL,
        updatedBy nvarchar(50) NULL,
        deletedBy nvarchar(50) NULL
      );
      ALTER TABLE dbo.Answers ADD CONSTRAINT Answers_FK FOREIGN KEY (questionId) REFERENCES dbo.Questions(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.Answers;`);
  }
}

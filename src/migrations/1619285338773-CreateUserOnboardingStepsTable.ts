import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserOnboardingStepsTable1619285338773 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.UserOnboardingSteps (
            id uniqueidentifier NOT NULL PRIMARY KEY,
            userId nvarchar(255) NOT NULL,
            firstName bit NOT NULL DEFAULT 0,
            lastName bit NOT NULL DEFAULT 0,
            gender bit NOT NULL DEFAULT 0,
            dob bit NOT NULL DEFAULT 0,
            height bit NOT NULL DEFAULT 0,
            handed bit NOT NULL DEFAULT 0,
            measurement bit NOT NULL DEFAULT 0,
            timePlayingGolf bit NOT NULL DEFAULT 0,
            rpmComplete bit NOT NULL DEFAULT 0,
            targetScoreComplete bit NOT NULL DEFAULT 0,
            maximumDriverDistance bit NOT NULL DEFAULT 0,
            strongestArea bit NOT NULL DEFAULT 0,
            weakestArea bit NOT NULL DEFAULT 0,
            shotShape bit NOT NULL DEFAULT 0,
            ballStrike bit NOT NULL DEFAULT 0,
            mostScaredShot bit NOT NULL DEFAULT 0,
            misHit bit NOT NULL DEFAULT 0,
            homeCourse bit NOT NULL DEFAULT 0,
            handicapPreference bit NOT NULL DEFAULT 0,
            userInputHandicap bit NOT NULL DEFAULT 0,
            favoriteTeamMembers bit NOT NULL DEFAULT 0,
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserOnboardingSteps;`);
  }
}

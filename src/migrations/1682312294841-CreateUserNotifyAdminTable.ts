import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserNotifyAdminTable1682312294841 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.UserNotifyAdmin
            (
                id                   uniqueidentifier NOT NULL PRIMARY KEY,
                userId               uniqueidentifier NOT NULL,
                createdAt            datetime DEFAULT CURRENT_TIMESTAMP,
                updatedAt            datetime         NULL,
                deletedAt            datetime         NULL
            );
            ALTER TABLE dbo.UserNotifyAdmin ADD CONSTRAINT UserNotifyAdmin_FK_1 FOREIGN KEY (userId) REFERENCES dbo.Users(id);
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.UserNotifyAdmin;`);
  }
}

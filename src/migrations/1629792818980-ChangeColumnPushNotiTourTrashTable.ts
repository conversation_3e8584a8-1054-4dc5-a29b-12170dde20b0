import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeColumnPushNotiTourTrashTable1629792818980 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DECLARE @table nvarchar(50)
      set @table = 'dbo.TourTrashs'
      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
        from sys.default_constraints
        where name LIKE 'DF__TourTrash__push%'
        exec sp_executesql @sql
      END`);

    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS pushNotiAtLaunch`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS timeToPushNotiBeforeEndDate`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS pushNotiTo`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS remindPushNotiFrequency`);

    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs ADD
        pushNotificationAtLaunch bit NOT NULL DEFAULT 1,
        timeToPushNotificationBeforeEndDate int NULL,
        pushNotificationTo int NULL,
        remindPushNotificationFrequency int NULL;
      `
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      DECLARE @table nvarchar(50)
      set @table = 'dbo.TourTrashs'
      DECLARE @sql nvarchar(255)
      BEGIN
        select @sql = 'ALTER TABLE ' + @table + ' DROP CONSTRAINT IF EXISTS ' + name
        from sys.default_constraints
        where name LIKE 'DF__TourTrash__push%'
        exec sp_executesql @sql
      END`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS pushNotificationAtLaunch`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS timeToPushNotificationBeforeEndDate`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS pushNotificationTo`);
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs DROP COLUMN IF EXISTS remindPushNotificationFrequency`);

    await queryRunner.query(
      `ALTER TABLE dbo.TourTrashs ADD
        pushNotiAtLaunch bit NOT NULL DEFAULT 1,
        timeToPushNotiBeforeEndDate int NULL,
        pushNotiTo int NULL,
        remindPushNotiFrequency int NULL;
      `
    );
  }
}

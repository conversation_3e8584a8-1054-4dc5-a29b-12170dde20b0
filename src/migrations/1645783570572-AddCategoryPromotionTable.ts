import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddCategoryPromotionTable1645783570572 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'Promotions',
      new TableColumn({
        name: 'category',
        type: 'varchar(255)',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateSubscriptionEvenLogTable1619285338789 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.SubscriptionEventLogs (
            id int IDENTITY(1,1) NOT NULL PRIMARY KEY,
            eventName varchar(50) NULL,
            notifyData varchar(8000) NULL,
            receiptData varchar(8000) NULL,
            platform varchar(10) NULL,
            createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
        )
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.SubscriptionEventLogs;`);
  }
}

import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRoleToUser1619285338794 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.Users ADD role tinyint NOT NULL DEFAULT 1
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE dbo.Users DROP COLUMN role
    `);
  }
}

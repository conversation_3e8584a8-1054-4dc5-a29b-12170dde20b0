import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableScheduleTilesWidgets1700645425898 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE dbo.ScheduleTilesWidget (
              id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
              widgetId nvarchar(255) NOT NULL,
              isRunActive bit NULL,
              isRunInActive bit NULL,
              activeAt datetime NULL,
              inActiveAt datetime NULL,
              createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
              updatedAt datetime NULL,
              deletedAt datetime NULL,
              createdBy nvarchar(50) NULL,
              updatedBy nvarchar(50) NULL,
              deletedBy nvarchar(50) NULL
            );
            CREATE INDEX idx_schedule_tiles_widget_widget_id ON dbo.ScheduleTilesWidget (widgetId);
          `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.ScheduleTilesWidget;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_schedule_tiles_widget_widget_id ON dbo.ScheduleTilesWidget;`);
  }
}

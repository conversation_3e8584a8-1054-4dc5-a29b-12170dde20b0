import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableTilesWidget1699497982578 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
        CREATE TABLE dbo.TilesWidget (
          id uniqueidentifier NOT NULL DEFAULT NEWID() PRIMARY KEY,
          title nvarchar(255) NOT NULL,
          description nvarchar(255) NULL,
          options nvarchar(255) NULL,
          imageLink nvarchar(255) NULL,
          videoLink nvarchar(255) NULL,
          ctaText nvarchar(255) NULL,
          ctaLink nvarchar(255) NULL,
          sortOrder int DEFAULT 1,
          type nvarchar(20) NOT NULL,
          status nvarchar(20) NULL,
          createdAt datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updatedAt datetime NULL,
          deletedAt datetime NULL,
          createdBy nvarchar(50) NULL,
          updatedBy nvarchar(50) NULL,
          deletedBy nvarchar(50) NULL
        );
        CREATE INDEX idx_tiles_widget_type ON dbo.TilesWidget (type);
      `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS dbo.TilesWidget;`);
    await queryRunner.query(`DROP INDEX IF EXISTS idx_tiles_widget_type ON dbo.TilesWidget;`);
  }
}

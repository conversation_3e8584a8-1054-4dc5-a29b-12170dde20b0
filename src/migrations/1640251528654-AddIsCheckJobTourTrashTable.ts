import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsCheckJobTourTrashTable1640251528654 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE dbo.TourTrashs ADD
      isCheckOpen bit NOT NULL DEFAULT 0,
      isCheckReminderSendMail bit NOT NULL DEFAULT 0,
      isCheckReminderPushNotification bit NOT NULL DEFAULT 0,
      isCheckSelectWinner bit NOT NULL DEFAULT 0
    `);

    await queryRunner.query(`UPDATE dbo.TourTrashs SET isCheckOpen = 1 WHERE status = 'ACTIVE'`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {}
}

import { Controller, Get, Param, Request, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { ClientGuard } from '../guards/client.guard';
import { MiscService } from './misc.service';
import { Region } from './misc.type';

@Controller()
@UseGuards(ClientGuard)
export class MiscController {
  constructor(private miscService: MiscService, private readonly config: ConfigService) {
    this.config = config;
  }

  @Get('misc/regions')
  async getRegions(@Param() params): Promise<Region[]> {
    return this.miscService.getRegions(params.take || 9999, params.skip || 0);
  }

  @Get('misc/launch-features')
  async getLaunchFeatures(@Request() req: any) {
    return this.miscService.getLaunchFeatures(req?.headers?.country);
  }
  @Get('misc/country-features')
  getCountryFeature(@Request() req: any) {
    return this.miscService.getCountryFeatures(req?.headers?.country);
  }
  @Get('misc/paid-features')
  getPaidFeature(@Request() req: any) {
    return this.miscService.getPaidFeature(req?.headers?.country);
  }
}

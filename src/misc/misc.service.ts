import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { ConfigService } from 'nestjs-config';
import { LaunchFeatureService } from '../admin/launch-feature.service';
import { CdmService } from '../cdm/cdm.service';
import { Region } from './misc.type';

@Injectable()
export class MiscService {
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    private readonly launchFeatureService: LaunchFeatureService
  ) {
    this.config = config;
  }

  async getRegions(take: number, skip: number): Promise<Region[]> {
    const headers = await this.cdmService.getRequestHeaderConfigs();
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/region?take=${take}&skip=${skip}`,
      headers
    );
    return response.data.map((region) => ({ id: region.id, code: region.code, description: region.description }));
  }

  async isRegionIdValid(regionId: string): Promise<boolean> {
    const regions = await this.getRegions(999, 0);
    return !!regions.find((item) => item.id === regionId);
  }

  getLaunchFeatures(countryCode?: string) {
    return this.launchFeatureService.mappingLaunchFeatures(countryCode);
  }
  getCountryFeatures(countryCode?: string) {
    return this.launchFeatureService.mappingCountryFeatures(countryCode);
  }
  getPaidFeature(countryCode?: string) {
    return this.launchFeatureService.mappingPaidFeatures(countryCode);
  }
}

import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { AuthProcessorQueueName } from './auth.processor';
import { AuthRegisterJob } from './auth.type';

@Injectable()
export class AuthQueueService {
  constructor(@InjectQueue('auth') private authQueue: Queue) {}

  addRegisterJob(registerJobData: AuthRegisterJob['data']) {
    this.authQueue.add(AuthProcessorQueueName.REGISTER, registerJobData).then((r) => r);
  }
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ManagementClient } from 'auth0';
import axios from 'axios';
import camelcaseKeys from 'camelcase-keys';
import { plainToClass } from 'class-transformer';
import jwt from 'jsonwebtoken';
import * as _ from 'lodash';
import { orderBy } from 'lodash';
import ms from 'ms';
import { ConfigService } from 'nestjs-config';
import request from 'request';
import { In, Not, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { AccessCodeService } from 'src/accesscode/access-code.service';
import { COUNTRY_STATUS } from 'src/admin/country.types';
import { CountryEntity } from 'src/admin/entities/country.entity';
import { CdmService } from 'src/cdm/cdm.service';
import { EcomService } from 'src/ecom/ecom.service';
import { CLIENTS } from 'src/guards/client.guard';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { MrpService } from 'src/mrp/mrp.service';
import { SubscriptionEntity } from 'src/payment/entities/subscription.entity';
import { PermissionService } from 'src/permission/permission.service';
import { ERROR_CODES } from 'src/utils/errors';
import { getBoolValue } from 'src/utils/transform';
import { Auth0Error, AuthRegisterJob, JWT, RegisterDto, SIUserAddUpdateDto } from './auth.type';
import { BlacklistedTokenEntity } from './entities/blacklisted-token.entity';
import { UserPermissionsEntity } from './entities/user-permissions.entity';
import { UserEntity } from './entities/user.entity';
import { OTPService } from './otp.service';
import { Role } from './roles.decorator';

export enum MEMBERSHIP_LEVEL {
  FREE = 0,
}
@Injectable()
export class AuthService {
  auth0Management;
  constructor(
    private readonly config: ConfigService,
    private readonly cdmService: CdmService,
    private readonly mrpService: MrpService,
    private readonly otpService: OTPService,
    private readonly ecomService: EcomService,
    private readonly klaviyoService: KlaviyoService,
    private readonly accessCodeService: AccessCodeService,
    private readonly permissionService: PermissionService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepo: Repository<SubscriptionEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(BlacklistedTokenEntity) private readonly blacklistedTokenRepo: Repository<BlacklistedTokenEntity>,
    @InjectRepository(CountryEntity) private readonly countryEntity: Repository<CountryEntity>
  ) {
    this.config = config;
    this.auth0Management = new ManagementClient({
      domain: this.config.get('app.auth0ApiDomain'),
      clientId: this.config.get('app.auth0ClientId'),
      clientSecret: this.config.get('app.auth0ClientSecret'),
      scope: this.config.get('app.auth0Scope'),
    });
  }

  getAuth0LoginOptions(email: string, password: string) {
    return {
      method: 'POST',
      json: true,
      url: `https://${this.config.get('app.auth0Domain')}/oauth/token`,
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      form: {
        grant_type: 'password',
        username: email,
        password,
        audience: `https://${this.config.get('app.auth0ApiDomain') || this.config.get('app.auth0Domain')}/api/v2/`,
        scope: 'openid email profile offline_access',
        client_id: this.config.get('app.auth0ClientId'),
        client_secret: this.config.get('app.auth0ClientSecret'),
      },
    };
  }

  async login(email: string, password: string, isCalledBySwingIndex: boolean, adminLogin: boolean): Promise<JWT> {
    return new Promise((resolve, reject) => {
      request(this.getAuth0LoginOptions(email, password), async (error, response, body) => {
        if (error) {
          return reject(error);
        }
        if (body.access_token) {
          if (!adminLogin) {
            return resolve(
              await this.getAuth0TokenInfo(
                body.access_token,
                isCalledBySwingIndex,
                false,
                body.refresh_token,
                body.expires_in
              )
            );
          } else {
            return resolve(await this.getAuth0TokenAdminInfo(body.access_token));
          }
        }
        return resolve({ auth0Error: camelcaseKeys(body) });
      });
    });
  }

  async postGetAccessToken(email: string, password: string): Promise<any> {
    return new Promise((resolve, reject) => {
      request(this.getAuth0LoginOptions(email, password), async (error, response, body) => {
        if (error) {
          return reject(error);
        }
        if (body.access_token) {
          return resolve(body);
        }
        return resolve({ auth0Error: camelcaseKeys(body) });
      });
    });
  }

  async postAuth0RefreshTokenToken(refreshToken: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const options = {
        method: 'POST',
        json: true,
        url: `https://${this.config.get('app.auth0Domain')}/oauth/token`,
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        form: {
          grant_type: 'refresh_token',
          refresh_token: refreshToken,
          client_id: this.config.get('app.auth0ClientId'),
          client_secret: this.config.get('app.auth0ClientSecret'),
        },
      };
      request(options, async (error, response, body) => {
        if (error) {
          return reject(error);
        }
        if (body.access_token) {
          return resolve(body);
        }
        return resolve({ auth0Error: camelcaseKeys(body) });
      });
    });
  }

  async ssoLogin(accessToken: string, isCalledBySwingIndex: boolean, isCallByMrpApp: boolean): Promise<JWT> {
    return this.getAuth0TokenInfo(accessToken, isCalledBySwingIndex, isCallByMrpApp);
  }

  async getOrCreateConsumerByEmail(email: string, userId: string) {
    let cdmAccount;
    try {
      cdmAccount = await this.cdmService.getAccountByEmailWithNoRegion(email);
    } catch (e) {
      await this.cdmService
        .createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), { myTM: userId })
        .then((r) => r);
      cdmAccount = await this.cdmService.getAccountByEmail(email, this.config.get('app.defaultRegionId'));
    }
    return cdmAccount;
  }

  async getAuth0TokenInfo(
    token: string,
    isCalledBySwingIndex: boolean,
    isCallByMrpApp?: boolean,
    auth0RefreshToken?: string,
    auth0ExpiresIn?: number
  ) {
    try {
      const response = await axios.post(`https://${this.config.get('app.auth0Domain')}/userinfo`, {
        access_token: token,
      });
      const userId = v4();
      let cdmAccount = await this.getOrCreateConsumerByEmail(response.data.email, userId);
      let user = await this.userRepo.findOne({ email: response.data.email });
      let updateCdmAccountPayload: any = {};
      if (!user) {
        let consumerProfiles = null;
        let countryUser = null;
        let regionId = '';
        if (cdmAccount && cdmAccount.consumerProfiles && cdmAccount.consumerProfiles.length) {
          consumerProfiles = cdmAccount.consumerProfiles;
          const regionUSA = consumerProfiles.find((val) => val?.region?.code === 'USA');
          const regionCAN = consumerProfiles.find((val) => val?.region?.code === 'CAN');
          const otherRegion = consumerProfiles.find((val) => val?.region);
          const countryCAN = _.result(regionCAN, 'region.code', null);
          const countryUSA = _.result(regionUSA, 'region.code', null);
          const otherRegionId = _.result(otherRegion, 'region.id', null);
          if (countryCAN === 'CAN') {
            regionId = this.config.get('app.defaultCanRegionId');
            countryUser = 'CAN';
          } else if (countryUSA === 'USA' && consumerProfiles?.length === 1) {
            regionId = this.config.get('app.defaultRegionId');
            countryUser = 'USA';
          } else if (otherRegionId) {
            regionId = otherRegionId;
          }
        }
        const mrpUID = await this.createMRPUser(user, cdmAccount, token);
        user = await this.userRepo.save({
          id: userId,
          email: response.data.email,
          regionId: regionId,
          cdmUID: cdmAccount.id,
          mrpUID,
          auth0UID: response.data.sub,
          isNewAccount: true,
          createdBy: userId,
          emailVerified: cdmAccount.emailVerified,
          tmAccessCode: cdmAccount.tmAccessCode,
          myTMSubscriptionLevel: cdmAccount.myTMSubscriptionLevel,
          userCountry: countryUser,
        });
        const defaultPermissionList = this.cdmService.getPermissionListByPlan(
          cdmAccount.myTMSubscriptionLevel || MEMBERSHIP_LEVEL.FREE
        );
        updateCdmAccountPayload = {
          myTM: userId,
          mrp: mrpUID,
          auth0Id: response.data.sub,
          ...defaultPermissionList,
        };
        if (!isCallByMrpApp) {
          await this.otpService.sendOtp(user.email);
        }

        await this.accessCodeService.grantAccessCodeToSignUpUser(user);
      }

      if (!user.mrpUID) {
        user.mrpUID = await this.createMRPUser(user, cdmAccount, token);
        updateCdmAccountPayload.mrp = user.mrpUID;
        await this.userRepo.update({ email: response.data.email }, { mrpUID: user.mrpUID });
      }
      if (user.cdmUID === 'NULL') {
        await this.userRepo.update({ email: response.data.email }, { cdmUID: cdmAccount.id });
      }
      if (!user.auth0UID) {
        updateCdmAccountPayload.auth0Id = response.data.sub;
      }

      if (!user.tmAccessCode) {
        await this.userRepo.update({ email: response.data.email }, { tmAccessCode: cdmAccount.tmAccessCode });
        user.tmAccessCode = cdmAccount.tmAccessCode;
      }

      if (!user.emailVerified) {
        await this.userRepo.update({ email: response.data.email }, { emailVerified: cdmAccount.emailVerified });
        user.emailVerified = cdmAccount.emailVerified;
      }

      if (!user.myTMSubscriptionLevel) {
        await this.userRepo.update(
          { email: response.data.email },
          { myTMSubscriptionLevel: cdmAccount.myTMSubscriptionLevel }
        );
        user.myTMSubscriptionLevel = cdmAccount.myTMSubscriptionLevel;
      }

      if (Object.keys(updateCdmAccountPayload).length > 0) {
        cdmAccount = await this.cdmService
          .createOrUpdateAccount(
            response.data.email,
            this.config.get('app.defaultRegionId'),
            updateCdmAccountPayload,
            true,
            true
          )
          .then((r) => r);
      }
      if (user.deletedAt) {
        return {
          auth0Error: { error: ERROR_CODES.UNAUTHORIZED, errorDescription: 'Your account needs to be reactivated!' },
        };
      }
      const tokens = this.signAuthenticationTokens(
        user.id,
        response.data.email,
        cdmAccount.id,
        user.mrpUID,
        user.ecomUID,
        user.regionId,
        user.userCountry,
        user.language
      );
      const consumerData: any = await this.cdmService.transformConsumerData(cdmAccount, user, isCalledBySwingIndex);
      const isTrialSubscription: boolean = await this.checkUserTrialSubscription(user.id);
      return {
        ...tokens,
        ...consumerData,
        isTrialSubscription,
        auth0Token: token,
        auth0RefreshToken,
        auth0ExpiresIn,
      };
    } catch (e) {
      console.log(e);
      return { auth0Error: { error: ERROR_CODES.AUTH0_ERROR, errorDescription: e.message } };
    }
  }

  private async checkUserTrialSubscription(userId: string) {
    let isTrialPeriod = false;
    const subscription = await this.subscriptionRepo
      .createQueryBuilder('s')
      .where('s.userId = :userId', { userId: userId })
      .andWhere('s.expirationDate >= getdate()')
      .andWhere("s.isTrialPeriod = 'true'")
      .getCount();

    if (subscription) {
      isTrialPeriod = true;
    }
    return isTrialPeriod;
  }

  async forceCreateUser(email: string) {
    const userId = v4();
    const cdmAccount = await this.getOrCreateConsumerByEmail(email, userId);
    let updateCdmAccountPayload: any = {};

    let mrpUID = cdmAccount?.tmUserIds?.mrp;
    if (!cdmAccount?.tmUserIds?.mrp) {
      const mrpUser = await this.mrpService.createAccountByEmail(email);
      mrpUID = `${mrpUser?.id}`;
    }
    const user = await this.userRepo.save({
      id: userId,
      email,
      regionId: this.config.get('app.defaultRegionId'),
      cdmUID: 'NULL',
      mrpUID,
      isNewAccount: true,
      createdBy: userId,
    });
    const defaultPermissionList = this.cdmService.getPermissionListByPlan(MEMBERSHIP_LEVEL.FREE);
    updateCdmAccountPayload = {
      myTM: userId,
      mrp: mrpUID,
      ...defaultPermissionList,
    };

    await this.cdmService
      .createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), updateCdmAccountPayload, true, true)
      .then((r) => r);
    return user;
  }

  async getAuth0TokenAdminInfo(token: string) {
    try {
      const response = await axios.post(`https://${this.config.get('app.auth0Domain')}/userinfo`, {
        access_token: token,
      });

      const user = await this.userRepo.findOne({ email: response.data.email });

      if (!user) {
        return { auth0Error: { error: ERROR_CODES.AUTH0_ERROR, errorDescription: 'Invalid email or password' } };
      }

      if (
        ![Role.ADMIN, Role.SUPER_ADMIN, Role.CUSTOMER_SERVICE, Role.TTB_AGENT, Role.TTB_MANAGER].includes(user.role)
      ) {
        return { auth0Error: { error: ERROR_CODES.PERMISSION_DENIED, errorDescription: 'Permission denied!' } };
      }

      const cdmAccount = await this.cdmService.getAccountByEmail(
        response.data.email,
        this.config.get('app.defaultRegionId')
      );
      const consumerData = await this.cdmService.transformAdminData(cdmAccount, user);
      const tokens = this.signAuthenticationTokens(
        user.id,
        response.data.email,
        user.cdmUID,
        user.mrpUID,
        user.ecomUID,
        user.regionId,
        user.userCountry,
        user.language
      );
      return { ...tokens, role: user.role, ...consumerData };
    } catch (e) {
      console.log(e);
      return {
        auth0Error: {
          error: ERROR_CODES.AUTH0_ERROR,
          errorDescription: (e?.response && e.response?.errorMessage) || e.message,
        },
      };
    }
  }

  async signUp(
    email: string,
    password: string,
    regionId: string,
    phoneNumber?: string,
    userCountry?: string,
    language?: string,
    registerDto?: RegisterDto
  ): Promise<{ registerJobData: AuthRegisterJob['data']; auth0Error: Auth0Error }> {
    const userId = v4();
    regionId = this.getRegionIdByCountry(userCountry);
    let auth0User;
    try {
      auth0User = await this.auth0Management.createUser({
        email,
        password,
        connection: 'Username-Password-v2',
        verify_email: false,
      });
    } catch (e) {
      return { registerJobData: null, auth0Error: { error: ERROR_CODES.AUTH0_ERROR, errorDescription: e.message } };
    }
    const registerJobData: AuthRegisterJob['data'] = {
      userId,
      auth0UserId: auth0User.user_id,
      password,
      email,
      regionId,
      phoneNumber,
      userCountry,
      language,
      firstName: registerDto?.firstName ?? null,
      lastName: registerDto?.lastName ?? null,
    };
    return { registerJobData, auth0Error: null };
  }

  async changeUserPassword(uid: string, email: string, password: string, newPassword: string): Promise<any> {
    let accessToken;
    try {
      const accessTokenResponse = await this.postGetAccessToken(email, password);
      if (accessTokenResponse && accessTokenResponse.auth0Error) {
        return AuthService.invalidCurrentPasswordError();
      }
      accessToken = accessTokenResponse.access_token;
    } catch (e) {
      return AuthService.invalidCurrentPasswordError();
    }
    try {
      const response = await axios.post(`https://${this.config.get('app.auth0Domain')}/userinfo`, {
        access_token: accessToken,
      });
      await this.auth0Management.updateUser(
        { id: response.data.sub },
        {
          password: newPassword,
          connection: 'Username-Password-v2',
        }
      );
      return true;
    } catch (e) {
      return { auth0Error: { error: ERROR_CODES.AUTH0_ERROR, errorDescription: e.message } };
    }
  }

  signIdToken(
    email: string,
    uid: string,
    cdmUID: string,
    mrpUID: string,
    ecomUID: string,
    regionId: string,
    userCountry?: string,
    language?: string
  ) {
    if (email === '<EMAIL>') {
      return jwt.sign(
        { uid, email, cdmUID, mrpUID, ecomUID, regionId, type: 'AT', userCountry, language },
        this.config.get('app.jwtSecret'),
        {
          expiresIn: '60s', //1 minute
        }
      );
    }
    return jwt.sign(
      { uid, email, cdmUID, mrpUID, ecomUID, regionId, type: 'AT', userCountry, language },
      this.config.get('app.jwtSecret'),
      {
        expiresIn: '1d',
      }
    );
  }

  signRefreshToken(
    email: string,
    uid: string,
    cdmUID: string,
    mrpUID: string,
    ecomUID: string,
    regionId: string,
    userCountry?: string,
    language?: string
  ) {
    if (email === '<EMAIL>') {
      return jwt.sign(
        { uid, email, cdmUID, mrpUID, ecomUID, regionId, type: 'RT', userCountry, language },
        this.config.get('app.jwtSecret'),
        {
          expiresIn: '120s', //2 minutes
        }
      );
    }
    return jwt.sign(
      { uid, email, cdmUID, mrpUID, ecomUID, regionId, type: 'RT', userCountry, language },
      this.config.get('app.jwtSecret'),
      {
        expiresIn: '1y',
      }
    );
  }

  async refreshToken(refreshToken, ipAddress?: string, client?: any) {
    let decoded: any = null;
    try {
      decoded = jwt.verify(refreshToken, this.config.get('app.jwtSecret'));
    } catch (error) {
      if (error?.message?.toLowerCase().includes('expired')) {
        if ([CLIENTS.SWING_INDEX, CLIENTS.SWING_INDEX_APP].includes(client.name)) {
          return await this.refreshExpiredToken(refreshToken, null, CLIENTS.SWING_INDEX_APP == client.name);
        } else {
          throw new Error('Refresh token expired!');
        }
      }
      throw new Error('Invalid refresh token!');
    }
    if (decoded.type !== 'RT') {
      throw new Error('Invalid token type!');
    }

    let user;
    if (client.name == CLIENTS.SWING_INDEX_APP) {
      user = await this.userRepo.findOne({ id: decoded.uid });
    } else {
      user = await this.userRepo.findOne({ email: decoded.email });
    }

    if (!user) {
      throw new Error('Invalid refresh token!');
    }
    const userPayload: any = { ...user, lastActivatedAt: new Date(), lastIPAddress: ipAddress };
    if (user.cdmUID === 'NULL') {
      const { data } = await this.cdmService.createAccount(user.email, user.regionId, {
        myTM: user.id,
        auth0Id: user.auth0UID,
      });
      if (data) {
        userPayload.cdmUID = data.id;
        user.cdmUID = data.id;
      }
    }
    await this.userRepo.update({ email: decoded.email }, userPayload);
    const idToken = jwt.sign(
      {
        email: user.email,
        uid: user.id,
        cdmUID: user.cdmUID,
        mrpUID: user.mrpUID,
        regionId: user.regionId,
        type: 'AT',
      },
      this.config.get('app.jwtSecret'),
      {
        expiresIn: '1d',
      }
    );
    return {
      idToken,
      refreshToken,
      expiresIn: ms('1d'),
    };
  }

  async refreshExpiredToken(refreshToken, ipAddress?: string, isSIApp?: boolean) {
    let decoded: any = null;
    try {
      decoded = jwt.verify(refreshToken, this.config.get('app.jwtSecret'), { ignoreExpiration: true });
    } catch (error) {
      throw new Error('Invalid refresh token!');
    }
    if (decoded.type !== 'RT') {
      throw new Error('Invalid token type!');
    }
    let user;
    if (isSIApp) {
      user = await this.userRepo.findOne({ id: decoded.uid });
    } else {
      user = await this.userRepo.findOne({ email: decoded.email });
    }
    if (!user) {
      throw new Error('Invalid refresh token!');
    }
    const userPayload: any = { ...user, lastActivatedAt: new Date(), lastIPAddress: ipAddress };
    await this.userRepo.update({ email: decoded.email }, userPayload);
    const idToken = this.signIdToken(user.email, user.id, user.cdmUID, user.mrpUID, user.ecomUID, user.regionId);
    const newRefreshToken = this.signRefreshToken(
      user.email,
      user.id,
      user.cdmUID,
      user.mrpUID,
      user.ecomUID,
      user.regionId
    );
    return {
      idToken,
      refreshToken: newRefreshToken,
      expiresIn: ms('1d'),
    };
  }

  signAuthenticationTokens(
    uid: string,
    email: string,
    cdmUID: string,
    mrpUID: string,
    ecomUID: string,
    regionId: string,
    userCountry?: string,
    language?: string
  ): JWT {
    const idToken = this.signIdToken(email, uid, cdmUID, mrpUID, ecomUID, regionId, userCountry, language);
    const refreshToken = this.signRefreshToken(email, uid, cdmUID, mrpUID, ecomUID, regionId, userCountry, language);
    return {
      idToken,
      refreshToken,
      expiresIn: ms('1d'),
    };
  }

  static invalidCurrentPasswordError() {
    return {
      auth0Error: {
        error: ERROR_CODES.CURRENT_PASSWORD_INVALID,
        errorDescription: 'Your current password is invalid!',
      },
    };
  }

  async subscribeFirebaseCloudMessage(email: string, token: string) {
    await this.userRepo.update({ email }, { fcmToken: token });
    return true;
  }

  async adminSubscribeFirebaseCloudMessage(email: string, token: string) {
    await this.userRepo.update({ email }, { fcmTokenAdmin: token });
    return true;
  }

  async isTokenBlacklisted(token: string, isRefreshToken: boolean) {
    return (await this.blacklistedTokenRepo.count({ token, type: isRefreshToken })) > 0;
  }

  async getUserById(userId: string) {
    return this.userRepo.findOne({ id: userId });
  }

  async signOut(idToken: string, refreshToken: string) {
    const blacklistedRefreshToken = new BlacklistedTokenEntity();
    blacklistedRefreshToken.id = v4();
    blacklistedRefreshToken.token = refreshToken;
    blacklistedRefreshToken.type = true;
    const blacklistedIdToken = new BlacklistedTokenEntity();
    blacklistedIdToken.id = v4();
    blacklistedIdToken.token = idToken;
    blacklistedIdToken.type = false;
    await this.blacklistedTokenRepo.save(blacklistedRefreshToken);
    await this.blacklistedTokenRepo.save(blacklistedIdToken);
    return true;
  }

  async createMRPUser(user: UserEntity, cdmAccount: any, token: string): Promise<string> {
    let mrpUID;
    if (!user?.mrpUID && cdmAccount?.tmUserIds?.mrp) {
      mrpUID = cdmAccount?.tmUserIds?.mrp;
    }
    if (!user?.mrpUID && !cdmAccount?.tmUserIds?.mrp) {
      const mrpUser = await this.mrpService.createAccount(token);
      mrpUID = `${mrpUser?.id}`;
    }

    return mrpUID;
  }

  async unsubscribeFirebaseCloudMessage(email: string) {
    return this.userRepo.update({ email }, { fcmToken: null });
  }

  async adminUnsubscribeFirebaseCloudMessage(email: string) {
    return this.userRepo.update({ email }, { fcmTokenAdmin: null });
  }

  async saveUserLastIpAddress(email: string, ipAddress: string) {
    return this.userRepo.update({ email }, { lastIPAddress: ipAddress });
  }

  async saveUserSignUpIpAddress(email: string, ipAddress: string) {
    const locationIp = await this.getLocationFromIp(ipAddress);
    return this.userRepo.update({ email }, { signUpIPAddress: ipAddress, signUpIPAddressLocation: locationIp });
  }

  async createMRPUserWithAccessToken(token: string): Promise<string> {
    const mrpUser = await this.mrpService.createAccount(token);
    return mrpUser?.id;
  }

  async handleRegister(registerData: AuthRegisterJob['data']): Promise<{ user: any; cdmAccount: any }> {
    const { userId, email, regionId, auth0UserId, password, userCountry, language } = registerData;
    const isExistedAccount = await this.isExistedEmail(email);
    if (isExistedAccount) {
      const userExisted = await this.userRepo.findOne({ where: [{ email }, { siEmail: email }] });
      if (userExisted?.siUserId && userExisted.cdmUID == '') {
        registerData.userId = userExisted.id;
        registerData.mrpUID = userExisted.mrpUID;
        return this.handleRegisterMyTMSIAccount(registerData, false, userExisted);
      } else {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.YOUR_EMAIL_ALREADY_EXISTS,
          errorMessage: 'Your email already exists!',
        });
      }
    }
    await this.userRepo.save({
      id: userId,
      email,
      cdmUID: 'NULL',
      regionId,
      isNewAccount: true,
      auth0UID: auth0UserId,
      userCountry,
      language,
    });
    const user = await this.userRepo.findOne({ id: userId });
    // Revert smsOTP for feature referral
    await this.otpService.sendOtpV2(email, registerData.phoneNumber);
    // await this.otpService.sendOtp(email, registerData.phoneNumber);
    const cdmAccount = await this.cdmService.createOrUpdateAccount(
      email,
      regionId,
      {
        myTM: userId,
        auth0Id: auth0UserId,
        phoneCell: registerData.phoneNumber,
        userCountry,
      },
      true,
      true
    );
    const [auth0Tokens] = await Promise.all([this.postGetAccessToken(email, password)]);
    const [mrpUID] = await Promise.all([this.createMRPUserWithAccessToken(auth0Tokens.access_token)]);
    await this.cdmService.createOrUpdateAccount(
      email,
      regionId,
      {
        myTM: userId,
        mrp: mrpUID,
        auth0Id: auth0UserId,
      },
      true,
      true
    );
    await this.userRepo.update(
      {
        id: userId,
      },
      { mrpUID, email, cdmUID: cdmAccount.id }
    );

    await this.accessCodeService.grantAccessCodeToSignUpUser(user);
    return {
      user: {
        ...user,
        mrpUID,
        email,
        cdmUID: cdmAccount.id,
      },
      cdmAccount,
    };
  }

  async handleRegisterByPassOTP(registerData: AuthRegisterJob['data']): Promise<{ user: any; cdmAccount: any }> {
    const { userId, email, regionId, auth0UserId, password, userCountry, language, firstName, lastName } = registerData;
    const isExistedAccount = await this.isExistedEmail(email);
    if (isExistedAccount) {
      const userExisted = await this.userRepo.findOne({ where: [{ email }, { siEmail: email }] });
      if (userExisted?.siUserId && userExisted.cdmUID == '') {
        registerData.userId = userExisted.id;
        registerData.mrpUID = userExisted.mrpUID;
        return this.handleRegisterMyTMSIAccount(registerData, false, userExisted);
      } else {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.YOUR_EMAIL_ALREADY_EXISTS,
          errorMessage: 'Your email already exists!',
        });
      }
    }
    const measurementUnits = this.getMeasurementUnitFromUserCountry(userCountry);
    await this.userRepo.save({
      id: userId,
      email,
      cdmUID: 'NULL',
      regionId,
      isNewAccount: true,
      auth0UID: auth0UserId,
      userCountry,
      language,
      firstNameTM: firstName,
      lastNameTM: lastName,
      measurementUnits,
    });
    const user = await this.userRepo.findOne({ id: userId });
    await this.otpService.sendOtp(email, registerData.phoneNumber);
    const cdmAccount = await this.cdmService.createOrUpdateAccount(
      email,
      regionId,
      {
        myTM: userId,
        auth0Id: auth0UserId,
        phoneCell: registerData.phoneNumber,
        userCountry,
        firstName,
        lastName,
        measurementUnits,
      },
      true,
      true
    );
    const [auth0Tokens] = await Promise.all([this.postGetAccessToken(email, password)]);
    const [mrpUID] = await Promise.all([this.createMRPUserWithAccessToken(auth0Tokens.access_token)]);
    await this.cdmService.createOrUpdateAccount(
      email,
      regionId,
      {
        myTM: userId,
        mrp: mrpUID,
        auth0Id: auth0UserId,
      },
      true,
      true
    );
    await this.userRepo.update(
      {
        id: userId,
      },
      { mrpUID, email, cdmUID: cdmAccount.id }
    );

    await this.accessCodeService.grantAccessCodeToSignUpUser(user);
    return {
      user: {
        ...user,
        mrpUID,
        email,
        cdmUID: cdmAccount.id,
      },
      cdmAccount,
    };
  }

  async handleRegisterMyTMSIAccount(registerData: AuthRegisterJob['data'], isByPassOTP: boolean, user): Promise<any> {
    const { userId, email, regionId, auth0UserId, userCountry, language, mrpUID } = registerData;
    const payloadUpdate = {
      cdmUID: 'NULL',
      regionId,
      isNewAccount: true,
      auth0UID: auth0UserId,
      userCountry,
      language,
      emailVerified: isByPassOTP == true,
    };
    if (user?.email?.includes('@swingindex.app')) {
      payloadUpdate['email'] = email;
    }
    await this.userRepo.update({ id: userId }, payloadUpdate);
    if (!isByPassOTP) {
      await this.otpService.sendOtpV2(email, registerData.phoneNumber);
    }

    const cdmAccount = await this.cdmService.createOrUpdateAccount(
      email,
      regionId,
      {
        myTM: userId,
        mrp: mrpUID,
        auth0Id: auth0UserId,
        phoneCell: registerData.phoneNumber,
        userCountry,
      },
      true,
      true
    );
    await this.userRepo.update(
      {
        id: userId,
      },
      { cdmUID: cdmAccount.id }
    );

    await this.accessCodeService.grantAccessCodeToSignUpUser(user);
    return {
      user: {
        ...user,
        mrpUID,
        email,
        cdmUID: cdmAccount.id,
      },
      cdmAccount,
    };
  }
  async isExistedEmail(email: string) {
    try {
      const countEmail = await this.userRepo.count({ where: [{ email }, { siEmail: email }] });
      return countEmail > 0;
    } catch (error) {
      console.log(`Error check isExitedEmail: ${email}`, error.message);
      return false;
    }
  }
  async getTokensList(take = 500, skip = 0) {
    const users = await this.userRepo.find({
      take: Number(take),
      skip: Number(skip),
    });

    return await Promise.all(
      users.map((user) => {
        const { email, id, cdmUID, mrpUID, ecomUID, regionId } = user;
        const idToken = this.signIdToken(email, id, cdmUID, mrpUID, ecomUID, regionId);
        return {
          idToken,
        };
      })
    );
  }

  async triggerCreateUserFromMRP(email: string) {
    return true;
    // await this.klaviyoService.track(email, KlaviyoTrackEvents.MYTMOC_CREATE_USER_SUCCESSFUL);
  }
  async triggerCollectSaleForce(email: string) {
    await this.userRepo.update(email, { shouldCollectSalesforceData: true });
  }

  async triggerEndRoundFromMRP(payload: any) {
    await this.klaviyoService.track(payload?.email, KlaviyoTrackEvents.MYTMOC_PLAYED_ROUND, {
      event: KlaviyoTrackEvents.MYTMOC_PLAYED_ROUND,
      'Game Mode': payload?.round?.round_mode,
      'Game Type': payload?.round?.round_type,
      'Course Name': payload?.round?.course_name,
      Holes: payload?.round?.holes_completed?.map((hole) => ({
        HCP: hole?.stroke_index,
        Number: hole?.number,
        Par: hole?.par,
        Score: hole?.score,
        // Yards: hole?.par
      })),
      Tee: payload?.round?.tee_name,
      Total: payload?.round?.total_score,
    });
  }

  isBlackListDomainEmail(email: string) {
    const blackList = this.config.get('app.blackListDomainEmail');
    const domainEmail = email.split('@').pop();
    const lastDomailEmail = domainEmail.split('.').pop();
    const lstBlackList = blackList.split(',').map((item: string) => item.trim());
    return lstBlackList.includes(domainEmail) || lstBlackList.includes(lastDomailEmail);
  }
  async deleteProfile(userId: string) {
    try {
      await this.userRepo.update({ id: userId }, { deletedAt: new Date(Date.now()), deletedBy: userId });
      return { success: true };
    } catch (e) {
      console.error(`Delete user error: ${e.message}`);
      return { success: false };
    }
  }

  async getLocationFromIp(ipAddress: string) {
    const url = `${this.config.get('app.ip2LocationEndpoint')}/?key=${this.config.get(
      'app.ip2LocationKey'
    )}&ip=${ipAddress}`;
    try {
      const result = await axios.get(url);
      const { data } = result;
      if (data && data.country_code && data.region_name) {
        return `${data?.city_name} - ${data?.region_name} - ${data?.country_code}`;
      }
      return null;
    } catch (error) {
      console.log(`${error.message}:${url}`);
      return null;
    }
  }

  async getMyTMPermission(userId: string, email: string, isCalledBySwingIndex = false, isCallFromController = false) {
    try {
      const forceUserToProPlusPlan = this.cdmService.forceUserToProPlusPlan(email);

      const userPermission = await this.cdmService.getMyTMPermission(
        userId,
        email,
        isCalledBySwingIndex,
        isCallFromController
      );
      const user = await this.userRepo.findOne({ id: userId });
      const subscriptionService = user.subscriptionService;
      const cachedPermissions = await this.cdmService.getCacheUserPermissionsWhenPurchase(userId);

      let myTMSubscriptionLevel = forceUserToProPlusPlan
        ? 2
        : cachedPermissions
        ? cachedPermissions.myTMSubscriptionLevel
        : user.myTMSubscriptionLevel;
      // Force myTMSubscriptionLevel to level 2
      myTMSubscriptionLevel = 2;
      let myTMPermission;
      if (cachedPermissions && cachedPermissions.myTMPermission) {
        myTMPermission = { ...userPermission, ...cachedPermissions.myTMPermission };
        myTMPermission = CdmService.transformConsumerMyTMPermission(
          myTMPermission,
          forceUserToProPlusPlan,
          isCalledBySwingIndex
        );
      } else {
        myTMPermission = { ...userPermission };
        myTMPermission = CdmService.transformConsumerMyTMPermission(
          myTMPermission,
          forceUserToProPlusPlan,
          isCalledBySwingIndex
        );
      }
      if (isCallFromController && forceUserToProPlusPlan) {
        myTMPermission = CdmService.transformConsumerMyTMPermission(
          myTMPermission,
          forceUserToProPlusPlan,
          isCalledBySwingIndex
        );
      }
      if (isCalledBySwingIndex) {
        return {
          myTMSubscriptionLevel,
          subscriptionService,
          myTMPermission,
        };
      }
      myTMPermission = await this.permissionService.overrideMyPermission(myTMPermission, myTMSubscriptionLevel);
      const isTrialSubscription: boolean = await this.checkUserTrialSubscription(userId);
      return {
        myTMSubscriptionLevel,
        subscriptionService,
        emailVerified: getBoolValue(user.emailVerified),
        myTMPermission,
        isTrialSubscription,
      };
    } catch (error) {
      console.error(error);
    }
  }

  async getCountries(status?: string) {
    if (!status) {
      status = COUNTRY_STATUS.ACTIVE;
    } else {
      status = status.trim().toUpperCase();
    }
    return await this.countryEntity.find({
      where: { status },
      select: ['cdmRegionId', 'code', 'name', 'id', 'status', 'isoCode', 'sort'],
      order: { sort: 'ASC' },
    });
  }
  async getCountryByCode(code?: string) {
    if (!code) {
      return {};
    }
    code = code.trim().toUpperCase();
    return await this.countryEntity.findOne({
      where: { code },
      select: ['cdmRegionId', 'code', 'name', 'id', 'status', 'isoCode'],
    });
  }
  async addUpdateUserSI(payload: SIUserAddUpdateDto) {
    console.log({ payload });
    const siUser = await this.getSIUser(payload);

    if (siUser) {
      if (payload?.email && siUser?.siUserId && siUser.siUserId != payload.userId) {
        console.log(`The ${payload.userId} was linked to other account!`);
        throw new BadRequestException(`The ${payload.userId} was linked to other account!`);
      }
      // update information SI User
      if (payload.email && siUser.siEmail != payload.email) {
        const userMyTM = await this.userRepo.findOne({ siUserId: Not(In([siUser.siUserId])), siEmail: payload.email });
        if (userMyTM) {
          console.log(`The ${payload.email} is already existed!`);
          throw new BadRequestException(`The ${payload.email} is already existed!`);
        }
        await this.userRepo.update(
          { siUserId: payload.userId },
          {
            siEmail: payload.email,
          }
        );
      }
      if (!siUser.siUserId) {
        // Update siUserId
        console.log(`Update siUserId`);
        await this.userRepo.update(
          { id: siUser.id },
          {
            siUserId: payload.userId,
          }
        );
      }
      if (!siUser.siEmail && payload?.email) {
        // Update siUserId
        console.log(`Update siEmail`);
        await this.userRepo.update(
          { id: siUser.id },
          {
            siEmail: payload.email,
          }
        );
      }
      // create new mrpId if not exist
      let mrpUID = siUser.mrpUID;
      if (!mrpUID) {
        const mrpAccount = await this.mrpService.createAccountBySIEmail(payload);
        if (!mrpAccount?.id) {
          console.log(`Can't create OC!`);

          throw new BadRequestException({
            errorMessage: "Can't create OC!",
          });
        } else {
          mrpUID = mrpAccount?.id;
          await this.userRepo.update(
            { siUserId: payload.userId },
            {
              mrpUID,
            }
          );
        }
      }
      await this.mrpService.updateAccountSI({ ...payload, userId: mrpUID });
      const user = await this.userRepo.findOne({
        where: { siUserId: payload.userId },
        select: ['regionId', 'email', 'siEmail', 'siUserId', 'id'],
      });
      const token = this.signAuthenticationTokens(user.id, user.siEmail, null, mrpUID, null, user.regionId);
      return { ...token, ...user, mrpUID };
    }
    // create new user
    const userId = v4();
    const siEmail = payload?.email ? payload?.email : payload.userId + '@swingindex.app';
    await this.userRepo.save({
      id: userId,
      email: siEmail,
      siEmail: payload.email,
      siUserId: payload.userId,
      cdmUID: '',
      regionId: this.config.get('app.defaultRegionId'),
      isNewAccount: false,
      emailVerified: true,
      isAgreeShareData: true,
    });
    const mrpAccount = await this.mrpService.createAccountBySIEmail({ ...payload, email: siEmail } as any);
    if (!mrpAccount?.id) {
      throw new BadRequestException({
        errorMessage: "Can't create OC!",
      });
    } else {
      const result = await this.signTokenForNewUserSI(userId, mrpAccount, payload);
      return {
        ...result,
        regionId: this.config.get('app.defaultRegionId'),
        email: siEmail,
        siEmail: payload.email,
        siUserId: payload.userId,
      };
    }
  }

  async unlinkUserSI(userId) {
    const user = await this.userRepo.findOne({ id: userId });
    if (user && user?.siUserId) {
      await this.userRepo.update({ id: user.id }, { siUserId: null, siEmail: null });
      console.log(`Unlinked ${user.siUserId} successfully!`);
      return { success: true };
    }
    return { success: true };
  }

  async signTokenForNewUserSI(userId: string, mrpAccount: any, payload: SIUserAddUpdateDto) {
    await this.userRepo.update({ id: userId }, { mrpUID: mrpAccount.id });
    const token = this.signAuthenticationTokens(
      userId,
      payload.email,
      null,
      mrpAccount.id,
      null,
      this.config.get('app.defaultRegionId')
    );
    return { ...token, mrpUID: mrpAccount.id, isAgreeShareData: true, id: userId };
  }

  async getSIUser(payload: SIUserAddUpdateDto) {
    let siUser;
    siUser = await this.userRepo.findOne({ where: [{ siUserId: payload.userId }] });
    if (!siUser && payload?.email) {
      siUser = await this.userRepo.findOne({
        where: [{ siEmail: payload.email }, { email: payload.email }],
      });
    }
    return siUser;
  }

  getRegionIdByCountry(userCountry: string): string {
    const countryRegionMap = {
      CAN: this.config.get('app.defaultCanRegionId'),
      GBR: this.config.get('app.defaultRegionId'),
      DEU: this.config.get('app.defaultRegionId'),
      FRA: this.config.get('app.defaultRegionId'),
      SWE: this.config.get('app.defaultRegionId'),
      AUS: this.config.get('app.defaultRegionId'),
      USA: this.config.get('app.defaultRegionId'),
    };

    return countryRegionMap[userCountry.toUpperCase()] || this.config.get('app.defaultRegionId');
  }

  getMeasurementUnitFromUserCountry(userCountry: string): string {
    const countryMeasurementUnitMap = {
      CAN: 'meters',
      GBR: 'meters',
      DEU: 'meters',
      FRA: 'meters',
      SWE: 'meters',
      AUS: 'meters',
      USA: 'yards',
    };

    return countryMeasurementUnitMap[userCountry.toUpperCase()] || 'yards';
  }
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { TwilioService } from 'src/shared/services/twilio.service';
import { CdmService } from '../cdm/cdm.service';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { MailService } from '../mail/mail.service';
import { ERROR_CODES } from '../utils/errors';
import { isCanOrUsaCountry } from '../utils/transform';
import { OtpConfig, UserOtp, UserOtpStatus } from './entities/user-otp.entity';
import { UserEntity } from './entities/user.entity';

@Injectable()
export class OTPService {
  constructor(
    private readonly mailService: MailService,
    private readonly cdmService: CdmService,
    private readonly config: ConfigService,
    private readonly twilioService: TwilioService,
    @InjectRepository(UserOtp)
    private readonly userOtpRepository: Repository<UserOtp>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    private readonly klaviyoService: KlaviyoService
  ) {}

  async sendOtp(email: string, phoneNumber?: string) {
    email = email.toLowerCase();

    const user = await this.userRepo.findOne({ email });
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
        errorMessage: 'User not found!',
      });
    }
    const userCountry = user?.userCountry || null;
    if (!isCanOrUsaCountry(userCountry)) {
      return;
    }

    const latestOTPs = await this.userOtpRepository.find({
      where: { userId: user.id },
      order: { createdAt: 'DESC' },
      take: 1,
    });

    if (latestOTPs && latestOTPs.length > 0) {
      if (moment(latestOTPs[0].createdAt).add(2, 'minutes').isAfter(moment())) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.RESEND_OTP_DUPLICATE,
          errorMessage: 'DUPLICATED_OTP_SENDING (2 minutes)',
        });
      }
    }

    const otp = await this.createVerifyOTP(user);
    await this.mailService.sendConfirmationEmail(email, otp.code, userCountry, phoneNumber);
    return { success: true };
  }

  async sendOtpV2(email: string, phoneNumber?: string) {
    if (!phoneNumber) {
      return await this.sendOtp(email);
    }
    email = email.toLowerCase();

    const user = await this.userRepo.findOne({ email });
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
        errorMessage: 'User not found!',
      });
    }
    const resultSendOtp = await this.twilioService.createOtp(phoneNumber);
    if (resultSendOtp.success) {
      return { success: true };
    }
    return resultSendOtp;
  }

  async verifyOtpCodeV2(email: string, otp: string, phoneNumber: string) {
    email = email.toLowerCase();
    const user = await this.userRepo.findOne({ email });
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.OTP_WRONG,
        errorMessage: 'Invalid OTP',
      });
    }
    const resultVerifyOtp = await this.twilioService.verifyOtp(phoneNumber, otp);
    if (!resultVerifyOtp.success) {
      throw new BadRequestException({
        internalErrorCode: resultVerifyOtp.internalErrorCode,
        errorMessage: 'Invalid OTP',
      });
    }
    await this.userRepo.update({ email }, { emailVerified: true });
    await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
      emailVerified: true,
    });
    return { success: true };
  }

  async createVerifyOTP(user: UserEntity) {
    await this.userOtpRepository.update(
      { userId: user.id, status: UserOtpStatus.ACTIVE },
      { status: UserOtpStatus.DEACTIVE, updatedBy: user.id }
    );

    const otpCode = _.padStart(_.random(0, Math.pow(10, OtpConfig.LENGTH) - 1).toString(), OtpConfig.LENGTH, '0');

    const userOtp = await this.userOtpRepository.create({
      id: v4(),
      userId: user.id,
      code: otpCode,
      expireTime: new Date(Date.now() + OtpConfig.EXPIRE_TIME),
      status: UserOtpStatus.ACTIVE,
      entries: 0,
      createdBy: user.id,
    });
    await this.userOtpRepository.save(userOtp);
    return userOtp;
  }

  async verifyOtpCode(email: string, otp: string) {
    email = email.toLowerCase();
    const user = await this.userRepo.findOne({ email });
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.OTP_WRONG,
        errorMessage: 'Invalid OTP',
      });
    }
    const userOtp = await this.userOtpRepository.findOne({
      userId: user.id,
      status: UserOtpStatus.ACTIVE,
    });
    if (!userOtp) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.OTP_WRONG,
        errorMessage: 'Invalid OTP',
      });
    }
    const secretOTP = this.config.get('app.secretOTP');
    if (userOtp.code !== otp && otp !== secretOTP) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.OTP_WRONG,
        errorMessage: 'Invalid OTP',
      });
    }
    if (userOtp.code !== secretOTP && new Date().getTime() > new Date(userOtp.expireTime).getTime()) {
      await this.userOtpRepository.update(userOtp.id, {
        status: UserOtpStatus.DEACTIVE,
        updatedBy: user.id,
      });
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.OTP_EXPIRED,
        errorMessage: 'Expired OTP',
      });
    }
    userOtp.status = UserOtpStatus.VERIFIED;
    userOtp.updatedBy = user.id;
    await this.userOtpRepository.save(userOtp);
    await this.userRepo.update({ email }, { emailVerified: true });
    await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
      emailVerified: true,
    });
    this.klaviyoService.track(email, KlaviyoTrackEvents.OTP_VERIFIED).catch((e) => e);
    return { success: true };
  }
}

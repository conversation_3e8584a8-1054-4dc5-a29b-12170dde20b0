import { Column, CreateDate<PERSON>olumn, <PERSON><PERSON>ty, PrimaryColumn, UpdateDateColumn } from 'typeorm';

export enum UserOtpStatus {
  ACTIVE = 'ACTIVE',
  DEACTIVE = 'DEACTIVE',
  VERIFIED = 'VERIFIED',
}

export const OtpConfig = {
  MAX_TRIES: 5,
  LENGTH: 6,
  EXPIRE_TIME: 30 * 60 * 1000,
};

@Entity('UserOtps')
export class UserOtp {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  code: string;

  @Column()
  userId: string;

  @Column()
  status: UserOtpStatus;

  @Column()
  entries: number;

  @Column()
  expireTime: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

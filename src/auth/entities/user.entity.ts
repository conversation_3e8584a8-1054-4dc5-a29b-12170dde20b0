import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserTourTrashEntity } from '../../tourtrash/entities/user-tour-trash.entity';
import { Role } from '../roles.decorator';
import { UserBlackListEntity } from './user-black-list.entity';
import { UserPermissionsEntity } from './user-permissions.entity';

@Entity('Users')
export class UserEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  email: string;

  @Column()
  cdmUID: string;

  @Column()
  mrpUID: string;

  @Column()
  ecomUID: string;

  @Column()
  auth0UID: string;

  @Column()
  regionId: string;

  @Column()
  fcmToken: string;

  @Column()
  fcmTokenAdmin: string;

  @Column('bit')
  isNewAccount: boolean;

  @Column('bit')
  onboardingComplete: boolean;

  @Column('bit')
  isInGracePeriod: boolean;

  @Column('bit')
  emailVerified: boolean;

  @Column('bit')
  tmAccessCode: boolean;

  @Column()
  myTMSubscriptionLevel: number;

  @Column()
  gracePeriodEndAt: Date;

  @Column()
  lastActivatedAt: Date;

  @Column()
  subscriptionService: string;

  @Column()
  signUpByDevice: string;

  @Column()
  lastIPAddress: string;

  @Column()
  role: Role;

  @OneToMany(() => UserTourTrashEntity, (userTourTrashes) => userTourTrashes.user)
  userTourTrashes: UserTourTrashEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column()
  isArccosEmail: boolean;

  @Column()
  isManualInputAverageScore: boolean;

  @Column({ default: false })
  shouldCollectSalesforceData: boolean;

  @Column()
  dobOffset: number;

  @Column()
  signUpIPAddress: string;

  @Column()
  isSimulator: boolean;

  @Column()
  fingerPrint: string;

  @Column({ type: 'float' })
  signUpLatitude: number;

  @Column({ type: 'float' })
  signUpLongitude: number;

  @Column({ type: 'float' })
  lastLatitude: string;

  @Column({ type: 'float' })
  lastLongitude: string;

  @OneToOne(() => UserBlackListEntity)
  @JoinColumn({ name: 'id', referencedColumnName: 'userId' })
  userBlackList: UserBlackListEntity;

  @OneToOne(() => UserPermissionsEntity)
  @JoinColumn({ name: 'id', referencedColumnName: 'userId' })
  userPermission: UserPermissionsEntity;

  @Column()
  signUpIPAddressLocation: string;

  @Column()
  signUpGPSLocation: string;

  @BeforeInsert()
  @BeforeUpdate()
  handleInsertUpdate() {
    this.shouldCollectSalesforceData = true;
  }
  @Column()
  userCountry: string;

  @Column()
  language: string;

  @Column('bit')
  is_receive_gift: boolean;

  @Column('bit')
  is_user_annex: boolean;

  @Column()
  total_receive_gift: number;

  @Column()
  address: string;

  @Column()
  siUserId: string;

  @Column()
  siEmail: string;

  @Column()
  isAgreeShareData: boolean;

  @Column()
  isWIBExport: boolean;

  @Column()
  isSpamCourse: boolean;

  @Column()
  firstNameTM: string;

  @Column()
  lastNameTM: string;

  @Column()
  genderTM: string;

  @Column()
  measurementUnits: string;
}

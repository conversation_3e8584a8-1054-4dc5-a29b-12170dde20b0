import { Column, CreateDateColumn, <PERSON>tity, JoinColumn, OneToOne, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { UserEntity } from './user.entity';

@Entity('UserPermissions')
export class UserPermissionsEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  //   @OneToOne((type) => UserEntity)
  //   @JoinColumn({ name: 'userId' })
  //   user: UserEntity;

  @Column()
  subscriptionLength: number;

  @Column('bit')
  canVirtualCoaching: boolean;

  @Column('bit')
  canExclProductDrops: boolean;

  @Column('bit')
  canTryThenBuy: boolean;

  @Column('bit')
  canPerfInsights: boolean;

  @Column('bit')
  canWinTourTrash: boolean;

  @Column('bit')
  canCommunity: boolean;

  @Column('bit')
  canFreeShipping: boolean;

  @Column('bit')
  canVideoInstruction: boolean;

  @Column('bit')
  canMRP: boolean;

  @Column('bit')
  canMFE: boolean;

  @Column('bit')
  canCalculatedHandicap: boolean;

  @Column('bit')
  canFree2DaysShipping: boolean;

  @Column('bit')
  canAccessToContentsTipsFromCoach: boolean;

  @Column('bit')
  canKeepDigitalScore: boolean;

  @Column('bit')
  canLoyalty: boolean;

  @Column('bit')
  canBookTeeTime: boolean;

  @Column('bit')
  canOnlineProductRec: boolean;

  @Column('bit')
  canPlayAdvancedRound: boolean;

  @Column('bit')
  isSyncCDM: boolean;

  @Column()
  subscriptionExpirationDate: Date;

  @Column()
  subscriptionStartDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('UserOnboardingSteps')
export class UserOnboardingStep {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  firstName: boolean;

  @Column()
  lastName: boolean;

  @Column()
  gender: boolean;

  @Column()
  dob: boolean;

  @Column()
  height: boolean;

  @Column()
  handed: boolean;

  @Column()
  typical7IronDistance: boolean;

  @Column()
  measurement: boolean;

  @Column()
  timePlayingGolf: boolean;

  @Column()
  rpmComplete: boolean;

  @Column()
  targetScoreComplete: boolean;

  @Column()
  maximumDriverDistance: boolean;

  @Column()
  strongestArea: boolean;

  @Column()
  weakestArea: boolean;

  @Column()
  shotShape: boolean;

  @Column()
  ballStrike: boolean;

  @Column()
  avoidShot: boolean;

  @Column()
  mostScaredShot: boolean;

  @Column()
  misHit: boolean;

  @Column()
  homeCourse: boolean;

  @Column()
  handicapPreference: boolean;

  @Column()
  userInputHandicap: boolean;

  @Column()
  favoriteTeamMembers: boolean;

  @Column()
  averageScoreRange: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

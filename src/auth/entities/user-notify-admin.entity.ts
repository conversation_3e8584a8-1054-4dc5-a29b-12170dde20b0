import { Column, CreateDateColumn, Entity, JoinColumn, OneToOne, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { UserEntity } from './user.entity';

@Entity('UserNotifyAdmin')
export class UserNotifyAdminEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  email: string;

  @Column('bit')
  is_ttb_fraud: boolean;

  @Column('bit')
  is_referral: boolean;

  @Column('bit')
  is_check_jobs: boolean;

  @Column('bit')
  isCourses: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  deletedAt: Date;

  @OneToOne((type) => UserEntity)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;
}

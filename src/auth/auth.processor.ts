import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AuthService } from './auth.service';
import { AuthRegisterJob } from './auth.type';

export enum AuthProcessorQueueName {
  REGISTER = 'register',
}

@Processor('auth')
export class AuthProcessor {
  private readonly logger = new Logger(AuthProcessor.name);
  constructor(private readonly config: ConfigService, private readonly authService: AuthService) {}

  @Process(AuthProcessorQueueName.REGISTER)
  async register(job: AuthRegisterJob): Promise<any> {
    this.logger.log(`Register processing for email: ${job.data.email}`);
    return this.authService.handleRegister(job.data);
  }
}

import { BullModule } from '@nestjs/bull';
import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { SYSTEM_TAG } from '../utils/constants';
import { AuthController } from './auth.controller';
import { AuthProcessor } from './auth.processor';
import { AuthQueueService } from './auth.queue.service';

let processors = [];

if (process.env.TAG !== SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, AuthProcessor];
}

@Module({
  imports: [
    SharedModule,
    BullModule.registerQueue({
      name: 'auth',
    }),
  ],
  controllers: [AuthController],
  providers: [...processors, AuthQueueService],
})
export class AuthModule {}

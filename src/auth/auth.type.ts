import { Job } from 'bull';
import { IsBoolean, IsEmail, IsNotEmpty, IsOptional, IsString, MaxLength, MinLength } from 'class-validator';

export class JWT {
  idToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  auth0Error?: Auth0Error;
}

export class Auth0 {
  auth0Error?: Auth0Error;
}

export class JWTPayload {
  uid: string;
  email: string;
  cdmUID: string;
  mrpUID: string;
  ecomUID: string;
  regionId: string;
  type: string;
  userCountry?: string;
  language?: string;
}

export class Auth0Error {
  error?: string;
  errorDescription?: string;
}

export type AuthRegisterJob = Job<{
  userId: string;
  email: string;
  regionId: string;
  auth0UserId: string;
  password: string;
  phoneNumber?: string;
  userCountry?: string;
  language?: string;
  firstName?: string;
  lastName?: string;
  mrpUID?: string;
}>;

export class RegisterDto {
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(255)
  @MinLength(6)
  email: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @MinLength(6)
  password: string;

  @IsOptional()
  @IsString()
  regionId?: string;

  @IsOptional()
  @IsBoolean()
  noWait?: boolean;

  @IsOptional()
  @IsString()
  @MinLength(10)
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  userCountry?: string;

  @IsOptional()
  @IsString()
  language?: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  firstName?: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  lastName?: string;
}

export class LoginDto {
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(255)
  @MinLength(6)
  email: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @MinLength(6)
  password: string;
}
export class SIUserAddUpdateDto {
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsString()
  @IsOptional()
  firstName?: string;

  @IsString()
  @IsOptional()
  lastName?: string;

  @IsString()
  @IsOptional()
  gender?: string;

  @IsString()
  @IsOptional()
  strokesGainedBaseline?: string;
}

export class SSOLoginDto {
  @IsString()
  @IsNotEmpty()
  accessToken: string;
}

export class SubscribeFCMDto {
  @IsString()
  @IsNotEmpty()
  token: string;
}

export class Auth0RefreshTokenDto {
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}

export class TokenRefreshDto {
  @IsString()
  @IsNotEmpty()
  refreshToken: string;
}

export class AgreementCheckDto {
  @IsBoolean()
  @IsNotEmpty()
  TOS: boolean;

  @IsBoolean()
  @IsNotEmpty()
  PrivacyPolicy: boolean;
}

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @MinLength(6)
  password: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  @MinLength(6)
  newPassword: string;
}

export class OtpSendDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

export class SMSOtpSendDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  phoneNumber: string;
}
export class OtpVerifyDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsNotEmpty()
  otpCode: string;
}

export class TwilioOtpVerifyDto {
  @IsOptional()
  phoneNumber: string;

  @IsNotEmpty()
  otpCode: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;
}
export const LIST_LANGS = {
  English: 'en',
  Japan: 'ja',
  Korean: 'ko',
};

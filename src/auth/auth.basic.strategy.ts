import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from 'nestjs-config';
import { BasicStrategy as Strategy } from 'passport-http';

@Injectable()
export class AuthBasicStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly configService: ConfigService) {
    super({
      passReqToCallback: true,
    });
  }

  public validate = async (req, username, password): Promise<boolean> => {
    if (
      this.configService.get('app.httpBasicUserName') === username &&
      this.configService.get('app.httpBasicPassword') === password
    ) {
      return true;
    }
    throw new UnauthorizedException();
  };
}

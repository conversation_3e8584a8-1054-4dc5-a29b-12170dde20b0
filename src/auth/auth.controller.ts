import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  Request,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { result } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { CdmService } from 'src/cdm/cdm.service';
import { DeleteConsumerOptInDto, SaveConsumerOptInDto } from 'src/cdm/types/consumer.type';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { MiscService } from 'src/misc/misc.service';
import { TwilioService } from 'src/shared/services/twilio.service';
import { BaseRequest } from 'src/types/core';
import { convertPstDate } from 'src/utils/datetime';
import { ERROR_CODES } from 'src/utils/errors';
import { LoyaltyService } from '../loyalty/loyalty.service';
import { UserReferralService } from '../user-referral/user-referral.service';
import { isCanOrUsaCountry } from '../utils/transform';
import { AuthQueueService } from './auth.queue.service';
import { AuthService } from './auth.service';
import {
  AgreementCheckDto,
  Auth0RefreshTokenDto,
  ChangePasswordDto,
  JWT,
  LIST_LANGS,
  LoginDto,
  OtpSendDto,
  OtpVerifyDto,
  RegisterDto,
  SIUserAddUpdateDto,
  SMSOtpSendDto,
  SSOLoginDto,
  SubscribeFCMDto,
  TokenRefreshDto,
  TwilioOtpVerifyDto,
} from './auth.type';
import { OTPService } from './otp.service';
import { AccessedRoles, Role } from './roles.decorator';

@Controller()
@UseGuards(ClientGuard)
export class AuthController {
  constructor(
    private authService: AuthService,
    private cdmService: CdmService,
    private miscService: MiscService,
    private otpService: OTPService,
    private authQueueService: AuthQueueService,
    private twilioService: TwilioService,
    private userReferralService: UserReferralService,
    private loyaltyService: LoyaltyService,
    private readonly config: ConfigService
  ) {
    this.config = config;
  }

  @Post('auth/login')
  async postLogin(@Req() request: BaseRequest, @Request() req: Request, @Body() login: LoginDto): Promise<JWT> {
    const isCalledBySwingIndex = CLIENTS.SWING_INDEX === request.client.name;
    const ipAddress = req.headers['X-Client-IP'.toLowerCase()];
    const response = await this.authService.login(login.email, login.password, isCalledBySwingIndex, false);
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    this.authService.saveUserLastIpAddress(login.email, ipAddress).then((r) => r);
    this.cdmService.getPermission(login.email).then((r) => r);
    return response;
  }

  @Post('admin/auth/login')
  async postAdminAuthLogin(@Req() request: BaseRequest, @Body() login: LoginDto): Promise<JWT> {
    const response = await this.authService.login(login.email, login.password, false, true);
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    return response;
  }

  @Post('admin/login')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  async postAdminLogin(@Req() request: BaseRequest, @Body() login: LoginDto): Promise<JWT> {
    const response = await this.authService.login(login.email, login.password, false, true);
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    return response;
  }

  @Post('auth/auth0/access-token')
  async postGetAccessToken(@Body() login: LoginDto): Promise<JWT> {
    const response = await this.authService.postGetAccessToken(login.email, login.password);
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    return response;
  }

  @Post('auth/auth0/token/refresh')
  async postAuth0RefreshTokenToken(@Body() data: Auth0RefreshTokenDto): Promise<JWT> {
    const response = await this.authService.postAuth0RefreshTokenToken(data.refreshToken);
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    return response;
  }

  @Post('auth/sso/login')
  async postSSOLogin(@Req() request: BaseRequest, @Body() login: SSOLoginDto): Promise<JWT> {
    const isCalledBySwingIndex = CLIENTS.SWING_INDEX === request.client.name;
    const isCallByMrpApp = CLIENTS.MRP_APP === request.client.name;
    const response = await this.authService.ssoLogin(login.accessToken, isCalledBySwingIndex, isCallByMrpApp);
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    return response;
  }

  @Post('auth/register')
  async postRegister(
    @Req() request: BaseRequest,
    @Request() req: Request,
    @Body() registerDto: RegisterDto
  ): Promise<any> {
    if (registerDto.regionId && !(await this.miscService.isRegionIdValid(registerDto.regionId))) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.YOUR_REGION_ID_IS_NOT_VALID,
        errorMessage: 'Your region id is not valid!',
      });
    }
    if (this.authService.isBlackListDomainEmail(registerDto.email)) {
      throw new BadRequestException();
    }
    const { registerJobData, auth0Error } = await this.authService.signUp(
      registerDto.email,
      registerDto.password,
      registerDto.regionId || this.config.get('app.defaultRegionId'),
      registerDto.phoneNumber || null,
      registerDto.userCountry || null,
      registerDto.language || null
    );
    if (auth0Error) {
      if (auth0Error.errorDescription === 'The user already exists.') {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.YOUR_EMAIL_ALREADY_EXISTS,
          errorMessage: 'Your email already exists!',
        });
      }
      throw new BadRequestException({
        internalErrorCode: auth0Error?.error.toUpperCase(),
        errorMessage: auth0Error?.errorDescription,
      });
    }
    if (registerDto.noWait) {
      this.authQueueService.addRegisterJob(registerJobData);
      return { success: true };
    }
    const { user, cdmAccount } = await this.authService.handleRegister(registerJobData);
    const tokens = this.authService.signAuthenticationTokens(
      user.id,
      cdmAccount.primaryEmail,
      cdmAccount.id,
      user.mrpUID,
      user.ecomUID,
      user.regionId,
      user.userCountry,
      user.language
    );
    const consumerData = await this.cdmService.transformConsumerData(cdmAccount, user, false);
    const ipAddress = req.headers['X-Client-IP'.toLowerCase()];
    this.authService.saveUserSignUpIpAddress(registerDto.email, ipAddress).then((r) => r);
    // check user referral
    this.userReferralService.checkEmailReferral(user?.email, 'API').then((r) => r);
    if (isCanOrUsaCountry(registerDto?.userCountry)) {
      this.loyaltyService.createUserLoyalty({ userId: user?.id }).then((r) => r);
    }
    return {
      ...tokens,
      ...consumerData,
    };
  }
  @Post('auth/register/by-pass-otp')
  async postRegisterV2(
    @Req() request: BaseRequest,
    @Request() req: Request,
    @Body() registerDto: RegisterDto
  ): Promise<any> {
    if (registerDto.regionId && !(await this.miscService.isRegionIdValid(registerDto.regionId))) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.YOUR_REGION_ID_IS_NOT_VALID,
        errorMessage: 'Your region id is not valid!',
      });
    }
    if (this.authService.isBlackListDomainEmail(registerDto.email)) {
      throw new BadRequestException();
    }
    const { registerJobData, auth0Error } = await this.authService.signUp(
      registerDto.email,
      registerDto.password,
      registerDto.regionId || this.config.get('app.defaultRegionId'),
      registerDto.phoneNumber || null,
      registerDto.userCountry || 'USA',
      registerDto.language || null,
      registerDto
    );
    if (auth0Error) {
      if (auth0Error.errorDescription === 'The user already exists.') {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.YOUR_EMAIL_ALREADY_EXISTS,
          errorMessage: 'Your email already exists!',
        });
      }
      throw new BadRequestException({
        internalErrorCode: auth0Error?.error.toUpperCase(),
        errorMessage: auth0Error?.errorDescription,
      });
    }
    if (registerDto.noWait) {
      this.authQueueService.addRegisterJob(registerJobData);
      return { success: true };
    }
    const { user, cdmAccount } = await this.authService.handleRegisterByPassOTP(registerJobData);
    const tokens = this.authService.signAuthenticationTokens(
      user.id,
      cdmAccount.primaryEmail,
      cdmAccount.id,
      user.mrpUID,
      user.ecomUID,
      user.regionId,
      user.userCountry,
      user.language
    );
    const consumerData = await this.cdmService.transformConsumerData(cdmAccount, user, false);
    const ipAddress = req.headers['X-Client-IP'.toLowerCase()];
    this.authService.saveUserSignUpIpAddress(registerDto.email, ipAddress).then((r) => r);
    // check user referral
    this.userReferralService.checkEmailReferral(user?.email, 'API').then((r) => r);
    if (isCanOrUsaCountry(user?.userCountry)) {
      this.loyaltyService.createUserLoyalty({ userId: user?.id }).then((r) => r);
    }
    return {
      ...tokens,
      ...consumerData,
    };
  }

  @Post('auth/logout')
  @UseGuards(AuthGuard)
  async postLogout(@Req() request: BaseRequest, @Body() data: TokenRefreshDto): Promise<{ success: boolean }> {
    await this.authService.signOut(request.token, data.refreshToken);
    return {
      success: true,
    };
  }

  @Post('auth/token/refresh')
  async postTokenRefresh(@Body() tokenRefreshDto: TokenRefreshDto, @Request() req: any): Promise<JWT> {
    const isTokenBlacklisted = await this.authService.isTokenBlacklisted(tokenRefreshDto.refreshToken, true);
    if (isTokenBlacklisted) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.YOUR_REFRESH_TOKEN_INVALID,
        errorMessage: 'Your refresh token is invalid!',
      });
    }
    const ipAddress = req.headers['X-Client-IP'.toLowerCase()];
    try {
      return await this.authService.refreshToken(tokenRefreshDto.refreshToken, ipAddress, req?.client);
    } catch (e) {
      console.log(e);
      if (e.message.includes('expired')) {
        throw new UnauthorizedException({
          internalErrorCode: ERROR_CODES.YOUR_REFRESH_TOKEN_EXPIRED,
          errorMessage: e.message,
        });
      } else {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.REFRESH_TOKEN_INVALID,
          errorMessage: e.message,
        });
      }
    }
  }

  @Post('auth/token/refresh-expired-token')
  @AccessedClients(CLIENTS.SWING_INDEX)
  @UseGuards(ClientGuard)
  async postRefreshExpiredToken(@Body() tokenRefreshDto: TokenRefreshDto, @Request() req: Request): Promise<JWT> {
    const isTokenBlacklisted = await this.authService.isTokenBlacklisted(tokenRefreshDto.refreshToken, true);
    if (isTokenBlacklisted) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.YOUR_REFRESH_TOKEN_INVALID,
        errorMessage: 'Your refresh token is invalid!',
      });
    }
    const ipAddress = req.headers['X-Client-IP'.toLowerCase()];
    try {
      return await this.authService.refreshExpiredToken(tokenRefreshDto.refreshToken, ipAddress);
    } catch (e) {
      console.log(e);
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.REFRESH_TOKEN_INVALID,
        errorMessage: e.message,
      });
    }
  }

  @Get('me')
  @UseGuards(AuthGuard)
  async getMe(@Req() request: any): Promise<any> {
    const isCalledBySwingIndex = CLIENTS.SWING_INDEX === request.client.name;
    const ipAddress = request.headers['X-Client-IP'.toLowerCase()];
    this.authService.saveUserLastIpAddress(request.user.email, ipAddress).then((r) => r);
    const consumer = await this.cdmService.getConsumer(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      isCalledBySwingIndex
    );
    const userCountry = result(consumer, 'userCountry', null);
    if (isCanOrUsaCountry(userCountry)) {
      this.loyaltyService
        .createUserLoyalty({ userId: request.user?.uid })
        .then((r) => r)
        .catch((err) => console.error(err.message));
      this.loyaltyService
        .addPointUsgaForOldUser(request.user)
        .then((r) => r)
        .catch((err) => console.error(err.message));
    }

    if (consumer) {
      await this.cdmService.handleCheckResetPermissionFail(consumer, { primaryEmail: request.user.email });
    }
    return { ...consumer };
  }

  @Get('countries')
  async getCountries(@Query('status') status?: string): Promise<any> {
    return await this.authService.getCountries(status);
  }
  @Get('languages')
  getLanguages() {
    return LIST_LANGS;
  }
  @Get('countries/:code')
  async getCountyByCode(@Param('code') code?: string): Promise<any> {
    return await this.authService.getCountryByCode(code);
  }

  @Get('admin/me')
  @UseGuards(AuthGuard)
  async getMeFromAdminPortal(@Req() request: BaseRequest): Promise<any> {
    return await this.cdmService.getConsumerFromAdminPortal(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId')
    );
  }

  @Get('my-permission')
  @UseGuards(AuthGuard)
  async getPermission(@Req() request: BaseRequest): Promise<any> {
    const isCalledBySwingIndex = CLIENTS.SWING_INDEX === request.client.name;
    return await this.authService.getMyTMPermission(request.user.uid, request.user.email, isCalledBySwingIndex, true);
  }

  @Post('auth/change-password')
  @UseGuards(AuthGuard)
  async postChangePassword(@Req() request: BaseRequest, @Body() changePasswordDto: ChangePasswordDto): Promise<any> {
    const response = await this.authService.changeUserPassword(
      request.user.uid,
      request.user.email,
      changePasswordDto.password,
      changePasswordDto.newPassword
    );
    if (response.auth0Error) {
      throw new BadRequestException({
        internalErrorCode: response.auth0Error.error.toUpperCase(),
        errorMessage: response.auth0Error.errorDescription,
      });
    }
    return { success: true };
  }

  @Post('profile/update')
  @UseGuards(AuthGuard)
  async updateProfile(@Req() request: BaseRequest): Promise<any> {
    const account = await this.cdmService.createOrUpdateAccount(
      request.user.email,
      request.user.regionId,
      request.body,
      true
    );
    await this.cdmService.triggerSubscriberKlaviyo(request.user.email, convertPstDate());
    const consumer = await this.cdmService.transformConsumerData(account);
    return this.cdmService.profileUpdate(consumer, request.body);
  }

  @Post('profile/agreement')
  @UseGuards(AuthGuard)
  async updateProfileAgreement(@Req() request: BaseRequest, @Body() data: AgreementCheckDto): Promise<any> {
    return this.cdmService.agreementCheck(request.user.email, data);
  }

  @Get('profile/agreements')
  @UseGuards(AuthGuard)
  async getProfileAgreement(@Req() request: BaseRequest): Promise<any> {
    return this.cdmService.getAgreements(request.user.email);
  }

  @Delete('profile')
  @UseGuards(AuthGuard)
  async deleteProfile(@Req() request: BaseRequest): Promise<any> {
    return await this.authService.deleteProfile(request.user.uid);
  }

  @Post('fcm/subscribe')
  @UseGuards(AuthGuard)
  async subscribeFirebaseCloudMessage(@Req() request: BaseRequest, @Body() body: SubscribeFCMDto): Promise<any> {
    const success = await this.authService.subscribeFirebaseCloudMessage(request.user.email, body.token);
    if (!success) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.YOUR_TOKEN_IS_ALREADY_USED,
        errorMessage: 'Your token is already used!',
      });
    }
    return {
      success,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @Post('admin/fcm/subscribe')
  @UseGuards(AuthGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  async adminSubscribeFirebaseCloudMessage(@Req() request: BaseRequest, @Body() body: SubscribeFCMDto): Promise<any> {
    const success = await this.authService.adminSubscribeFirebaseCloudMessage(request.user.email, body.token);
    if (!success) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.YOUR_TOKEN_IS_ALREADY_USED,
        errorMessage: 'Your token is already used!',
      });
    }
    return {
      success,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @Post('admin/fcm/unsubscribe')
  @UseGuards(AuthGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  async adminUnsubscribeFirebaseCloudMessage(@Req() request: BaseRequest): Promise<any> {
    await this.authService.adminUnsubscribeFirebaseCloudMessage(request.user.email);
    return {
      success: true,
    };
  }

  @Post('fcm/unsubscribe')
  @UseGuards(AuthGuard)
  async unsubscribeFirebaseCloudMessage(@Req() request: BaseRequest): Promise<any> {
    await this.authService.unsubscribeFirebaseCloudMessage(request.user.email);
    return {
      success: true,
    };
  }

  @Post('auth/otp/send')
  async sendOtp(@Body() data: OtpSendDto): Promise<any> {
    return await this.otpService.sendOtp(data.email);
  }

  @Post('auth/sms-otp/v2/send')
  async sendOtpV2(@Body() data: SMSOtpSendDto): Promise<any> {
    // return await this.otpService.sendOtp(data.email, data.phoneNumber);
    return await this.otpService.sendOtpV2(data.email, data.phoneNumber);
  }

  @Post('auth/sms-otp/v2/verify')
  async verifyOtpCodeV2(@Body() data: TwilioOtpVerifyDto): Promise<any> {
    // return await this.otpService.verifyOtpCode(data.email, data.otpCode);
    return await this.otpService.verifyOtpCodeV2(data.email, data.otpCode, data.phoneNumber);
  }

  @Post('auth/sms-otp/send')
  async sendSMSOtp(@Body() data: SMSOtpSendDto): Promise<any> {
    return await this.otpService.sendOtp(data.email, data.phoneNumber);
  }
  @Post('auth/otp/verify')
  async verifyOtp(@Body() data: OtpVerifyDto): Promise<any> {
    return await this.otpService.verifyOtpCode(data.email, data.otpCode);
  }

  @Get('/consumer-opt-in')
  @UseGuards(AuthGuard)
  async getConsumerOptIn(@Req() request: BaseRequest, @Query('key') key): Promise<any> {
    return await this.cdmService.getConsumerOptIn(
      request.user.cdmUID,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      key
    );
  }

  @Post('/consumer-opt-in/save')
  @UseGuards(AuthGuard)
  async createOrUpdateConsumerOptIn(@Req() request: BaseRequest, @Body() data: SaveConsumerOptInDto): Promise<any> {
    return await this.cdmService.createOrUpdateConsumerOptIn(
      request.user.email,
      request.user.cdmUID,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      data
    );
  }

  @Post('/consumer-opt-in/delete')
  @UseGuards(AuthGuard)
  async deleteConsumerOptIn(@Body() data: DeleteConsumerOptInDto): Promise<any> {
    return await this.cdmService.deleteConsumerOptIn(data);
  }

  @Get('/auth/tokens/list')
  async getTokensList(@Query('take') take: number, @Query('skip') skip: number): Promise<any> {
    return await this.authService.getTokensList(take, skip);
  }

  @Post('/auth/trigger-user-success-klaviyo')
  @AccessedClients(CLIENTS.MRP)
  @UseGuards(ClientGuard)
  async triggerCreateUserSuccessKlaviyo(@Body('email') email: string): Promise<any> {
    await this.authService.triggerCreateUserFromMRP(email);
    return {
      success: true,
    };
  }

  @Post('/auth/trigger-end-round-klaviyo')
  @AccessedClients(CLIENTS.MRP)
  @UseGuards(ClientGuard)
  async triggerEndRoundKlaviyo(@Body() payload: any): Promise<any> {
    // await this.authService.triggerEndRoundFromMRP(payload);
    return {
      success: true,
    };
  }

  @Post('/user/trigger-collect-sale-force')
  @AccessedClients(CLIENTS.MRP)
  @UseGuards(ClientGuard)
  async triggerCollectSaleForce(@Body() payload: any): Promise<any> {
    await this.authService.triggerCollectSaleForce(payload);
    return {
      success: true,
    };
  }
  @Post('/si/user/add-update')
  @AccessedClients(CLIENTS.SWING_INDEX_APP)
  @UseGuards(ClientGuard)
  async addUpdateUserSI(@Body() payload: SIUserAddUpdateDto): Promise<any> {
    return await this.authService.addUpdateUserSI(payload);
  }
  @Delete('/si/user/unlink')
  @AccessedClients(CLIENTS.SWING_INDEX_APP)
  @UseGuards(AuthGuard)
  @UseGuards(ClientGuard)
  async unlinkUserSI(@Req() request: any): Promise<any> {
    return await this.authService.unlinkUserSI(request.user.uid);
  }
}

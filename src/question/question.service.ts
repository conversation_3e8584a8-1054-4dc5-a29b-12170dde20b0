import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { isEmpty, map, orderBy, result } from 'lodash';
import moment from 'moment/moment';
import { ConfigService } from 'nestjs-config';
import { EntityManager, In, IsNull, Not, Repository, getManager } from 'typeorm';
import { v4 } from 'uuid';
import { PollAnswer } from 'src/answer/entities/answer.entity';
import { UserAnswer } from 'src/user-answer/entities/user-answer.entity';
import { ERROR_CODES } from 'src/utils/errors';
import { percentRound } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { isCronJobHandlersEnabled, isProduction } from '../utils/cron';
import { CreateQuestionScheduleDto } from './dto/create-question-schedule.dto';
import { CreateQuestionDto } from './dto/create-question.dto';
import { QuestionResponseDto } from './dto/question.response.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { UpdateStatusQuestionDto } from './dto/update-status-question.dto';
import { PollQuestionScheduleEntity } from './entities/question-schedule.entity';
import { PollQuestion } from './entities/question.entity';
import { SortQuestionDto } from './question.types';

@Injectable()
export class PollQuestionService {
  private readonly logger = new Logger(PollQuestionService.name);
  @InjectRepository(PollQuestion) private readonly pollQuestionRepo: Repository<PollQuestion>;
  @InjectRepository(PollAnswer) private readonly pollAnswerRepo: Repository<PollAnswer>;
  @InjectRepository(UserAnswer) private readonly userAnswerRepo: Repository<UserAnswer>;
  @InjectRepository(PollQuestionScheduleEntity)
  private readonly questionScheduleRepo: Repository<PollQuestionScheduleEntity>;
  constructor(private readonly config: ConfigService, private readonly apiVersionsService: ApiVersionsService) {}

  async createQuestion(payload: CreateQuestionDto, userId: string) {
    const isExisted = await this.pollQuestionRepo.findOne({
      where: {
        title: payload.title.trim(),
      },
    });
    if (isExisted) {
      throw new BadRequestException(`${payload.title} already exists`);
    }
    try {
      payload.createdBy = userId;
      payload.title = payload.title.trim();
      payload.sortOrder = 1;
      payload.id = v4();
      if (payload.options) {
        payload.options = JSON.stringify(payload.options);
      }
      const question = await this.pollQuestionRepo.save(this.pollQuestionRepo.create(payload));
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_CARTNER_CONVOS, 'USA', payload.createdBy);
      return question;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(`Failed to create question. Please try again.`);
    }
  }

  async getUserQuestion(userId) {
    let questionAnsweredId = null;
    const userAnswer = await this.userAnswerRepo.find({
      select: ['questionId'],
      where: {
        userId,
      },
    });
    if (!isEmpty(userAnswer)) {
      questionAnsweredId = map(userAnswer, 'questionId');
    }
    let conditions: any = {
      select: ['id', 'title', 'description', 'createdAt', 'sortOrder', 'imageUrl', 'disabled', 'options'],
    };
    if (!isEmpty(questionAnsweredId)) {
      conditions = {
        ...conditions,
        where: { id: Not(In(questionAnsweredId)), disabled: false },
      };
    } else {
      conditions = {
        ...conditions,
        where: { disabled: false },
      };
    }

    conditions = {
      ...conditions,
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
      relations: ['answers'],
    };

    const questionDb = await this.pollQuestionRepo.findOne(conditions);
    const answers = result(questionDb, 'answers', null);
    if (answers) {
      questionDb.answers = orderBy(answers, 'createdAt');
    }

    return questionDb;
  }

  async getQuestions({ ...options }: any) {
    try {
      const nTake = options?.limit ? parseInt(options?.limit, 10) : 10;
      const nPage = options?.page ? parseInt(options?.page, 10) : 1;
      const skip = (nPage - 1) * nTake;
      const [result, total] = await this.pollQuestionRepo.findAndCount({
        select: ['id', 'title', 'description', 'createdAt', 'sortOrder', 'imageUrl', 'disabled', 'options'],
        order: { sortOrder: 'ASC', createdAt: 'DESC' },
        take: nTake,
        skip,
        relations: ['answers', 'questionSchedule'],
      });
      if (total === 0) {
        return {
          total: 0,
          questions: [],
        };
      }

      return { questions: result, total, limit: nTake, page: nPage };
    } catch (err) {
      console.error('error', err);
      throw new BadRequestException('Fail to view. Please try again!');
    }
  }

  async getUserLastSummary(userId) {
    try {
      const userAnswers = await this.userAnswerRepo.query(
        `SELECT TOP 1 ua.questionId, p.title, ua.createdAt, ua.answerId FROM UserAnswers as ua INNER JOIN PollQuestions as p on p.id = ua.questionId  WHERE ua.userId = '${userId}' and p.disabled = 0 ORDER BY ua.createdAt desc;`
      );
      if (isEmpty(userAnswers)) {
        return {};
      }
      const userAnswer = result(userAnswers, '[0]', null);
      const lastQuestionId = userAnswer?.questionId;
      const percentQuestion = await this.findPercent(lastQuestionId);
      return {
        ...percentQuestion?.question,
        userAnswerId: userAnswer?.answerId,
      };
    } catch (err) {
      console.log('Err getUserLastSummary:', err);
      throw new BadRequestException('Can not get summary. Please try again!');
    }
  }

  async findOne(id: string, userId?: string) {
    try {
      const questionDb = await this.pollQuestionRepo.findOne({
        select: ['id', 'title', 'description', 'createdAt', 'sortOrder', 'imageUrl', 'disabled', 'options'],
        where: { id },
        relations: ['answers'],
      });
      const answers = result(questionDb, 'answers', null);
      if (answers) {
        questionDb.answers = orderBy(answers, 'createdAt');
      }

      const question = plainToClass(QuestionResponseDto, questionDb, { excludeExtraneousValues: true });
      if (userId) {
        const userAnswer = await this.userAnswerRepo.findOne({ userId, questionId: id });
        question['isAnswered'] = userAnswer ? true : false;
      }
      return { question };
    } catch (error) {
      console.log(error);
      throw new NotFoundException(`Question ${id} not found`);
    }
  }

  async getDetailQuestion(id: string) {
    try {
      const questionDb = await this.pollQuestionRepo.findOne({
        select: ['id', 'title', 'description', 'createdAt', 'sortOrder', 'imageUrl', 'disabled', 'options'],
        where: { id },
        relations: ['answers'],
      });
      const answers = result(questionDb, 'answers', null);
      if (answers) {
        questionDb.answers = orderBy(answers, 'createdAt');
      }

      const question = plainToClass(QuestionResponseDto, questionDb, { excludeExtraneousValues: true });
      const userAnswer = await this.userAnswerRepo.findOne({ questionId: id });
      question['isAnswered'] = userAnswer ? true : false;
      return { question };
    } catch (error) {
      console.log(error);
      throw new NotFoundException(`Question ${id} not found`);
    }
  }

  async findPercent(id: string) {
    try {
      const questionDb = await this.pollQuestionRepo.findOne({
        select: ['id', 'title', 'description', 'imageUrl', 'disabled', 'createdAt', 'options'],
        where: { id },
        relations: ['answers'],
      });
      if (!result) {
        throw new NotFoundException(`Question ${id} not found`);
      }
      const answers = result(questionDb, 'answers', null);
      if (answers) {
        questionDb.answers = orderBy(answers, 'createdAt');
      }
      const question = plainToClass(QuestionResponseDto, questionDb, { excludeExtraneousValues: true });
      await this.calculatePercentAnswer(id, question);
      return { question };
    } catch (error) {
      console.log(error);
      throw new NotFoundException(`Question ${id} not found`);
    }
  }

  async postSortQuestion(payload: SortQuestionDto, userId: string) {
    try {
      const listQuestionId = [];
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listQuestionDTO = payload.sortQuestionList;
          await Promise.all(
            listQuestionDTO.map(async (question) => {
              const checkConstantSortOrder = listQuestionDTO.filter(
                (otherWidget) => otherWidget.sortOrder === question.sortOrder
              );
              if (checkConstantSortOrder.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }

              const id = question.id;
              listQuestionId.push(id);
              const questionNeedUpdate = await this.pollQuestionRepo.findOne({
                select: ['id'],
                where: { id },
              });
              if (!questionNeedUpdate) {
                throw new BadRequestException({
                  internalErrorCode: 'QUESTION_NOT_FOUND',
                  errorMessage: `Question ${id} not found`,
                });
              }
              await transactionalEntityManager.update(
                PollQuestion,
                { id: questionNeedUpdate.id },
                { sortOrder: question.sortOrder, updatedBy: userId, updatedAt: new Date() }
              );
            })
          );
        })
        .then(async () => {
          return await this.pollQuestionRepo
            .createQueryBuilder('question')
            .whereInIds(listQuestionId)
            .orderBy({
              'question.sortOrder': 'ASC',
            })
            .getMany();
        });
    } catch (e) {
      return e;
    }
  }

  async addUpdateQuestionSchedule(payload: CreateQuestionScheduleDto, userId) {
    try {
      this.validateDateActiveInActiveWidget(payload);
      const question = await this.pollQuestionRepo.findOne({ id: payload.pollQuestionId });
      if (isEmpty(question)) {
        throw new NotFoundException(`Question not found!`);
      }
      const questionSchedule = await this.questionScheduleRepo.findOne({
        where: { pollQuestionId: payload.pollQuestionId },
      });
      if (questionSchedule) {
        return this.updateQuestionSchedule(payload, userId);
      }
      if (payload.activeAt) {
        payload.isRunActive = false;
        payload.isRunInActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      } else {
        payload.isRunActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      }
      payload.createdBy = userId;
      await this.questionScheduleRepo.save(this.questionScheduleRepo.create(payload));
      return this.questionScheduleRepo.findOne({
        where: {
          pollQuestionId: payload.pollQuestionId,
        },
      });
    } catch (err) {
      console.log('ERR addUpdateQuestionSchedule', err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async updateQuestionSchedule(payload: CreateQuestionScheduleDto, userId) {
    try {
      payload.updatedAt = new Date();
      payload.updatedBy = userId;
      if (payload?.activeAt?.toString() == '') {
        payload.activeAt = null;
        payload.isRunActive = true;
      }
      if (payload?.inActiveAt?.toString() == '') {
        payload.inActiveAt = null;
        payload.isRunInActive = true;
      }
      if (payload.activeAt) {
        payload.isRunActive = false;
        payload.isRunInActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      } else {
        payload.isRunActive = true;
        if (payload.inActiveAt) {
          payload.isRunInActive = false;
        }
      }
      await this.questionScheduleRepo.update({ pollQuestionId: payload?.pollQuestionId }, payload);
      return this.questionScheduleRepo.findOne({
        where: { pollQuestionId: payload.pollQuestionId },
      });
    } catch (err) {
      console.log('ERR updateQuestionSchedule', err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async calculatePercentAnswer(id: string, question: QuestionResponseDto) {
    const queryUserAnswer = this.userAnswerRepo.createQueryBuilder('UAnswer');
    queryUserAnswer.where({ questionId: id });
    queryUserAnswer.select(['answerId', 'count(answerId) count']);
    queryUserAnswer.groupBy('answerId');
    const userAnswers = await queryUserAnswer.getRawMany();
    if (userAnswers?.length > 0) {
      let totalAnswer = 0;
      userAnswers.map((answer) => (totalAnswer += answer.count));
      let lstPercentAnswerForQuestions = [];
      lstPercentAnswerForQuestions = userAnswers.map((answer) => {
        return { ...answer, percent: +((+answer.count / +totalAnswer) * 100) };
      });
      const arrQuestionAnswerPercent = [];
      question.answers = question?.answers?.map((item) => {
        const percentAnswerForQuestion = lstPercentAnswerForQuestions.find(
          (percentAnswer) => percentAnswer.answerId == item.id
        );
        item['percent'] = percentAnswerForQuestion ? percentAnswerForQuestion.percent : 0;
        item['count'] = percentAnswerForQuestion ? percentAnswerForQuestion.count : 0;
        arrQuestionAnswerPercent.push({ id: item.id, percent: item['percent'] });
        return item;
      });
      const roundPercents = percentRound(
        arrQuestionAnswerPercent.map((item) => item.percent),
        2
      );
      // Remap percent
      question.answers = question.answers.map((item, index) => {
        item['percent'] = roundPercents[index];
        return item;
      });
      question['totalAnswer'] = totalAnswer;
    }
  }

  async updateStatusQuestion(id: string, payload: UpdateStatusQuestionDto, userId: string) {
    const question = await this.pollQuestionRepo.findOne(id);
    if (isEmpty(question)) {
      throw new NotFoundException(`Question not found`);
    }
    payload.updatedBy = userId;
    payload.updatedAt = new Date();
    try {
      await this.pollQuestionRepo.update(id, payload);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_CARTNER_CONVOS, 'USA', question.createdBy);
      return await this.findOne(id);
    } catch (error) {
      console.log(error);
      throw new BadRequestException(`Fail to update status. Please try again!`);
    }
  }

  async updateQuestion(id: string, payload: UpdateQuestionDto, userId: string) {
    const question: any = await this.pollQuestionRepo.findOne(id);
    if (!question) {
      throw new NotFoundException(`Fail to edit. Please try again!`);
    }
    if (payload.title) {
      const isExisted = await this.pollQuestionRepo.findOne({
        where: {
          id: Not(id),
          title: payload.title.trim(),
        },
      });
      if (isExisted) {
        throw new BadRequestException(`This question already exists.`);
      }
      payload.title = payload.title.trim();
    }

    if (payload.options) {
      payload.options = JSON.stringify(payload.options);
    }

    const userAnswer = await this.userAnswerRepo.findOne({ questionId: id });
    if (userAnswer) {
      throw new BadRequestException(`A question cannot be edited once answered`);
    }
    payload.updatedBy = userId;
    payload.updatedAt = new Date();
    try {
      await this.pollQuestionRepo.update(id, payload);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_CARTNER_CONVOS, 'USA', question.createdBy);
      return await this.findOne(id);
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error);
    }
  }

  async deleteQuestion(id: string) {
    const question: any = await this.pollQuestionRepo.findOne(id);
    if (!question) {
      throw new NotFoundException(`Question not found`);
    }
    try {
      await this.pollAnswerRepo.delete({ questionId: id });
      await this.pollQuestionRepo.delete({ id: id });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_CARTNER_CONVOS, 'USA', question.createdBy);
      return { success: true };
    } catch (e) {
      console.log(e);
      throw new BadRequestException(`Delete question ${id} failed`);
    }
  }

  @Cron(isProduction ? CronExpression.EVERY_10_MINUTES : CronExpression.EVERY_MINUTE)
  async runActiveQuestionSchedule() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.log(`START runActiveQuestionSchedule at ${new Date().toISOString()}`);
    try {
      const questions = await this.questionScheduleRepo.find({
        where: { isRunActive: false, activeAt: Not(IsNull()) },
      });
      if (questions) {
        for (const questionSchedule of questions) {
          try {
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const momentTz = require('moment-timezone');
            const currentDate = momentTz().utc();
            this.logger.log(`CURRENT TIME PST: ${currentDate.toISOString()}`);
            const convertTime = momentTz(questionSchedule.activeAt).format('YYYY-MM-DD HH:mm');
            const activeDate = momentTz.tz(convertTime, 'UTC');

            if (activeDate.diff(currentDate) > 0) {
              continue;
            }
            const questionDetail = await this.pollQuestionRepo.findOne(questionSchedule?.pollQuestionId);
            if (!questionDetail) {
              continue;
            }
            await this.pollQuestionRepo.update(questionSchedule.pollQuestionId, {
              disabled: false,
              updatedAt: new Date(),
            });
            await this.apiVersionsService.updateVersion(
              FEATURE_KEY_VERSION.HOME_CARTNER_CONVOS,
              'USA',
              questionDetail.createdBy
            );

            await this.questionScheduleRepo.update(questionSchedule.id, { isRunActive: true });
            this.logger.log(`Trigger Active ${questionSchedule.id} done!`);
          } catch (error) {
            this.logger.error(`Trigger Active ${questionSchedule.id} failed`);
            console.error(error);
          }
        }
      }
      this.logger.log(`FINISH runActiveQuestionSchedule at ${new Date().toISOString()}`);
    } catch (err) {
      console.log('Err runActiveQuestionSchedule', err);
      throw new BadRequestException(`Err runActiveQuestionSchedule ${err?.toString()}`);
    }
  }

  @Cron(isProduction ? CronExpression.EVERY_10_MINUTES : CronExpression.EVERY_MINUTE)
  async runInActiveQuestionSchedule() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.log(`START runInActiveQuestionSchedule at ${new Date().toISOString()}`);
    try {
      const questions = await this.questionScheduleRepo.find({
        where: { isRunActive: true, isRunInActive: false, inActiveAt: Not(IsNull()) },
      });
      if (questions) {
        for (const questionSchedule of questions) {
          try {
            // eslint-disable-next-line @typescript-eslint/no-var-requires
            const momentTz = require('moment-timezone');
            const currentDate = momentTz().utc();
            this.logger.log(`CURRENT TIME PST: ${currentDate.toISOString()}`);
            const convertTime = momentTz(questionSchedule.inActiveAt).format('YYYY-MM-DD HH:mm');
            const inActiveDate = momentTz.tz(convertTime, 'UTC');

            if (currentDate.diff(inActiveDate) <= 0) {
              continue;
            }

            const questionDetail = await this.pollQuestionRepo.findOne(questionSchedule.pollQuestionId);
            if (!questionDetail) {
              continue;
            }
            await this.pollQuestionRepo.update(questionSchedule.pollQuestionId, {
              disabled: true,
              updatedAt: new Date(),
            });
            await this.apiVersionsService.updateVersion(
              FEATURE_KEY_VERSION.HOME_CARTNER_CONVOS,
              'USA',
              questionDetail.createdBy
            );
            await this.questionScheduleRepo.update(questionSchedule.id, { isRunInActive: true });
            this.logger.log(`Trigger InActive ${questionSchedule.id} done!`);
          } catch (error) {
            this.logger.error(`Trigger InActive ${questionSchedule.id} failed!`);
            console.error(error);
          }
        }
      }
      this.logger.log(`FINISH runInActiveQuestionSchedule at ${new Date().toISOString()}`);
    } catch (err) {
      console.log('Err runInActiveQuestionSchedule', err);
      throw new BadRequestException(`Err runInActiveQuestionSchedule ${err?.toString()}`);
    }
  }

  private validateDateActiveInActiveWidget(createDto: CreateQuestionScheduleDto) {
    if (!createDto.inActiveAt && !createDto.activeAt) {
      throw new BadRequestException({ errorMessage: 'Should have InActiveAt or ActiveAt' });
    }
    this.validateCurrentDateWithActiveDate(createDto.activeAt, 'ActiveAt');
    this.validateCurrentDateWithActiveDate(createDto.inActiveAt, 'InActiveAt');
    if (createDto.inActiveAt && createDto.activeAt) {
      const dateInActive = moment(createDto.inActiveAt);
      const dateActive = moment(createDto.activeAt);
      if (dateActive.diff(dateInActive) > 0) {
        throw new BadRequestException({ errorMessage: 'InActiveAt should greater than ActiveAt' });
      }
    }
  }

  private validateCurrentDateWithActiveDate(strDate, labelDate) {
    const currentDate = moment();
    if (strDate) {
      const dateCompare = moment(strDate);
      if (dateCompare.isBefore(currentDate)) {
        throw new BadRequestException({ errorMessage: `${labelDate} should be greater than now!` });
      }
    }
  }
}

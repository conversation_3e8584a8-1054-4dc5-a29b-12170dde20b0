import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PollAnswer } from 'src/answer/entities/answer.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { SharedModule } from 'src/shared/shared.module';
import { UserAnswer } from 'src/user-answer/entities/user-answer.entity';
import { PollQuestionScheduleEntity } from './entities/question-schedule.entity';
import { PollQuestion } from './entities/question.entity';
import { QuestionController } from './question.controller';
import { PollQuestionService } from './question.service';

@Module({
  imports: [
    SharedModule,
    TypeOrmModule.forFeature([UserEntity, PollAnswer, PollQuestion, UserAnswer, PollQuestionScheduleEntity]),
  ],

  controllers: [QuestionController],
  providers: [PollQuestionService],
})
export class QuestionModule {}

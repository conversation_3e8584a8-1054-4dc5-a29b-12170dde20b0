import { Body, Controller, Delete, Get, Param, Patch, Post, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { CreateQuestionScheduleDto } from './dto/create-question-schedule.dto';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { UpdateStatusQuestionDto } from './dto/update-status-question.dto';
import { PollQuestionService } from './question.service';
import { SortQuestionDto } from './question.types';

@Controller('poll-questions')
export class QuestionController {
  constructor(private readonly questionService: PollQuestionService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('save')
  createQuestion(@Body() createQuestionDto: CreateQuestionDto, @Req() req: any) {
    return this.questionService.createQuestion(createQuestionDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get()
  getQuestions(@Request() req: any) {
    return this.questionService.getQuestions(req?.query);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user/last-summary')
  getUserLastSummary(@Req() req: any) {
    return this.questionService.getUserLastSummary(req.user.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user')
  getUserQuestion(@Req() req: any) {
    return this.questionService.getUserQuestion(req.user.uid);
  }

  @Get('/percent/:id')
  @UseGuards(AuthGuard)
  findPercent(@Param('id') id: string) {
    return this.questionService.findPercent(id);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.questionService.getDetailQuestion(id);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('/status/:id')
  updateStatusQuestion(@Param('id') id: string, @Body() payload: UpdateStatusQuestionDto, @Req() req: any) {
    return this.questionService.updateStatusQuestion(id, payload, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch(':id')
  updateQuestion(@Param('id') id: string, @Body() payload: UpdateQuestionDto, @Req() req: any) {
    return this.questionService.updateQuestion(id, payload, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  deleteQuestion(@Param('id') id: string) {
    return this.questionService.deleteQuestion(id);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  async postUpdateSortQuestions(@Req() request: BaseRequest, @Body() payload: SortQuestionDto) {
    return await this.questionService.postSortQuestion(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('/schedules/add-update')
  addUpdateQuestionSchedule(@Body() createScheduleDto: CreateQuestionScheduleDto, @Req() request: BaseRequest) {
    return this.questionService.addUpdateQuestionSchedule(createScheduleDto, request?.user?.uid);
  }
}

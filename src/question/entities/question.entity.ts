import { Exclude } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PollAnswer } from 'src/answer/entities/answer.entity';
import { PollQuestionScheduleEntity } from './question-schedule.entity';

@Entity('PollQuestions')
export class PollQuestion {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column()
  sortOrder: number;

  @Column()
  description: string;

  @Column()
  countries: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  @Exclude()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column('bit')
  disabled: boolean;

  @Column()
  imageUrl: string;

  @Column()
  options: string;

  @OneToMany(() => PollAnswer, (answer) => answer.question)
  answers?: PollAnswer[];

  @OneToOne(() => PollQuestionScheduleEntity)
  @JoinColumn({ name: 'id', referencedColumnName: 'pollQuestionId' })
  questionSchedule: PollQuestionScheduleEntity;
}

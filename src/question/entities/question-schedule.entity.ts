import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('PollQuestionSchedule')
export class PollQuestionScheduleEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  pollQuestionId: string;

  @Column()
  isRunActive: boolean;

  @Column()
  isRunInActive: boolean;

  @Column()
  activeAt: Date;

  @Column()
  inActiveAt: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

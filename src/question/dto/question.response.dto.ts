import { Expose, Type } from 'class-transformer';
import { AnswerResponseDto } from './answer.response.dto';

export class QuestionResponseDto {
  @Expose()
  id: string;

  @Expose()
  title: string;

  @Expose()
  description: string;

  @Expose()
  countries: string;

  @Expose()
  sortOrder: number;

  @Expose()
  disabled: boolean;

  @Expose()
  imageUrl: string;

  @Expose()
  options: string;

  @Expose()
  @Type(() => AnswerResponseDto)
  answers: AnswerResponseDto[];
}

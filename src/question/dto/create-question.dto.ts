import { IsNotE<PERSON>y, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsString, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateQuestionDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(78)
  title: string;

  @IsOptional()
  id: string;

  @IsOptional()
  imageUrl: string;

  @IsOptional()
  createdBy: string;

  @IsOptional()
  deletedBy: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  updatedAt: Date;

  @IsOptional()
  @IsNumber()
  sortOrder: number;

  @IsOptional()
  options: string;
}

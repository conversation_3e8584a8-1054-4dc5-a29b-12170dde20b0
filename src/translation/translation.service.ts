import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import { ConfigService } from 'nestjs-config';
import ServiceAccount from '../config/mytm-backend-service-account.json';

const TRANSLATION_CACHE_PREFIX = 'TRANSLATIONS:';

@Injectable()
export class TranslationService {
  constructor(private readonly config: ConfigService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {
    this.config = config;
  }

  async getFileTranslations(locale: string) {
    const stringifyTranslations: string = await this.cacheManager.get(
      `${TRANSLATION_CACHE_PREFIX}${locale.split('.')[0]?.toUpperCase()}`
    );
    return JSON.parse(stringifyTranslations || JSON.stringify({}));
  }

  async getTranslations() {
    const translationConfig = this.config.get('app.translations');
    const translations = translationConfig.split(',');
    return translations.map((translation) => translation.split(':')[0]?.toLowerCase());
  }

  async saveTranslations(locale: string, stringifyTranslations) {
    return this.cacheManager.set(`${TRANSLATION_CACHE_PREFIX}${locale}`, stringifyTranslations, {
      ttl: 0,
    });
  }

  async syncTranslations(onlyLanguage = null) {
    try {
      const translationConfig = this.config.get('app.translations');
      const translations = translationConfig.split(',');
      const results = {};
      for (const translation of translations) {
        const [locale, spreadsheetId] = translation.split(':');
        if (onlyLanguage && onlyLanguage?.toLowerCase() !== locale.toLowerCase()) {
        } else {
          const [errors, success] = await this.syncTranslation(locale, spreadsheetId);
          results[locale.toLowerCase()] = { errors, success };
        }
      }
      return results;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  async syncTranslation(locale: string, spreadsheetId: string) {
    const doc = new GoogleSpreadsheet(spreadsheetId);
    await doc.useServiceAccountAuth(ServiceAccount);
    await doc.loadInfo();
    const translations = {};
    const errors = [];
    for (let i = 0; i < doc.sheetCount; i++) {
      const sheet = doc.sheetsByIndex[i];
      try {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        const rows = await sheet.getRows();
        rows.forEach((row) => {
          if (!row.Key?.trim() || !row.Value?.trim()) {
            return row;
          }
          translations[row.Key.trim()] = row.Value.trim();
          return row;
        });
      } catch (e) {
        errors.push({
          sheetTitle: sheet.title,
          errorMessage: e.message,
        });
        console.log(e.message);
      }
    }
    if (errors.length > 0) {
      return [errors, false];
    }
    this.saveTranslations(`${locale}`, JSON.stringify(translations));
    return [null, true];
  }
}

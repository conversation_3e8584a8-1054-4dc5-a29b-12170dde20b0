import { Controller, Get, Param } from '@nestjs/common';
import { TranslationService } from './translation.service';

@Controller('translations')
export class TranslationController {
  constructor(private translationService: TranslationService) {}

  @Get('locales')
  async getTranslations() {
    return this.translationService.getTranslations();
  }

  @Get('locales/:file')
  async getTranslationFile(@Param() params: any) {
    return this.translationService.getFileTranslations(params.file);
  }
}

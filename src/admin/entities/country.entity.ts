import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('Countries')
export class CountryEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  cdmRegionId: string;

  @Column()
  code: string;

  @Column()
  isoCode: string;

  @Column()
  name: string;

  @Column()
  status: string;

  @Column()
  sort: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

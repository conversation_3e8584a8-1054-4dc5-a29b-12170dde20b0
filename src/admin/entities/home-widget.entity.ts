import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { HomeWidgetGroupKey } from '../admin.type';

@Entity('HomeWidgets')
export class HomeWidgetEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  groupKey: HomeWidgetGroupKey;

  @Column()
  name: string;

  @Column()
  type: string;

  @Column()
  heading: string;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  backgroundColor: string;

  @Column()
  backgroundImage: string;

  @Column()
  contentType: string;

  @Column()
  contentId: number;

  @Column()
  productId: string;

  @Column()
  contentFormat: string;

  @Column()
  ctaText: string;

  @Column()
  ctaLink: string;

  @Column()
  extraData: string;

  @Column()
  sortOrder: number;

  @Column()
  status: string;

  @Column()
  show: boolean;

  @Column()
  category: string;

  @Column()
  countries: string;

  @Column('decimal', { precision: 6, scale: 2 })
  colorOpacity: number;

  @Column()
  textColor: string;

  @Column()
  buttonAlign: string;

  @Column()
  textAlign: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('Images')
export class ImageEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  keyImage: string;

  @Column()
  status: string;

  @Column()
  type: string;

  @Column()
  urlImage: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

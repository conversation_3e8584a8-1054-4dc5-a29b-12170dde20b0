import { Column, CreateDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('FeatureCountries')
export class FeatureCountryEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  launchFeatureId: string;

  @Column()
  countryCode: string;

  @Column()
  featureType: string;

  @Column()
  enabled: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

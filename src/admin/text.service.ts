import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { TextEntity } from './entities/text.entity';
import { AddTextDto, UpdateTextDto } from './text.types';

@Injectable()
export class TextService {
  constructor(
    private readonly config: ConfigService,
    private readonly apiVersionsService: ApiVersionsService,
    @InjectRepository(TextEntity)
    private readonly textRepo: Repository<TextEntity>
  ) {
    this.config = config;
  }

  async findAll() {
    return this.textRepo.find({
      order: { createdAt: 'DESC' },
    });
  }
  async getTexts() {
    const texts = await this.textRepo.find({
      order: { createdAt: 'DESC' },
      select: ['keyText', 'value'],
    });
    if (!texts || texts.length == 0) {
      return {};
    }
    const transformText = {};
    for (const img of texts) {
      transformText[img.keyText] = img.value;
    }
    return transformText;
  }
  async findTextBy(options) {
    return await this.textRepo.find({
      where: options,
    });
  }
  async saveText(userId: string, textDTO: AddTextDto) {
    try {
      await this.validateKeyText(textDTO.keyText);
      textDTO.createdBy = userId;
      const image = this.textRepo.create(textDTO);
      await this.textRepo.save(image);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CONTENT_TEXT, 'USA', userId);
      return await this.findTextBy({ keyText: textDTO.keyText });
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
  async updateText(userId: string, textDTO: UpdateTextDto) {
    try {
      textDTO.updatedAt = new Date();
      textDTO.updatedBy = userId;
      const image = await this.textRepo.findOne({ id: textDTO.id?.toLocaleUpperCase() });

      if (!image) {
        throw new NotFoundException(`The key ${textDTO.keyText} not found`);
      }
      if (textDTO.keyText != image.keyText) {
        await this.validateKeyText(textDTO.keyText);
      }

      await this.textRepo.update(image.id, textDTO);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CONTENT_TEXT, 'USA', userId);
      return await this.findTextBy({ keyText: textDTO.keyText });
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
  async validateKeyText(keyText: string) {
    const isExisted = await this.textRepo.findOne({ keyText });
    if (isExisted) {
      throw new BadRequestException(`The Key ${keyText} already exists`);
    }
  }

  async deleteText(userId: string, textId: string) {
    try {
      const image = await this.textRepo.findOne({ id: textId?.toLocaleUpperCase() });
      if (!image) {
        throw new NotFoundException(`Text not found`);
      }
      await this.textRepo.update(image.id, {
        deletedAt: new Date(),
        deletedBy: userId,
      });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CONTENT_TEXT, 'USA', userId);
      return { success: true };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
}

import { Job } from 'bull';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { SUBSCRIPTION_SERVICES } from 'src/payment/payment.constants';

export enum HomeWidgetGroupKey {
  HomeTile = 'HomeTile',
  HomeExplorer = 'HomeExplorer',
}

export enum HomeTypeStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CLOSED = 'CLOSED',
}

export class HomeWidgetDto {
  @IsEnum(HomeWidgetGroupKey)
  groupKey: HomeWidgetGroupKey;

  @IsString()
  @IsOptional()
  name: string;

  @IsString()
  type: string;

  @IsString()
  heading: string;

  @IsString()
  title: string;

  @IsString()
  @IsOptional()
  description: string;

  @IsString()
  @IsOptional()
  backgroundColor: string;

  @IsString()
  @IsOptional()
  backgroundImage: string;

  @IsString()
  @IsOptional()
  contentType: string;

  @IsNumber()
  @IsOptional()
  contentId: number;

  @IsString()
  @IsOptional()
  productId: string;

  @IsString()
  @IsOptional()
  contentFormat: string;

  @IsString()
  @IsOptional()
  ctaText: string;

  @IsString()
  @IsOptional()
  ctaLink: string;

  @IsNumber()
  @IsOptional()
  sortOrder: number;

  @IsEnum(HomeTypeStatus)
  status: HomeTypeStatus;

  @IsBoolean()
  @IsOptional()
  show: boolean;

  @IsOptional()
  extraData: any;

  @IsOptional()
  @IsString()
  category: string;

  @IsOptional()
  @IsNumber()
  colorOpacity: number;

  @IsOptional()
  @IsString()
  textColor: string;

  @IsOptional()
  @IsString()
  buttonAlign: string;

  @IsOptional()
  @IsString()
  textAlign: string;

  @IsOptional()
  @IsString()
  countries: string;
}

export class TagThumbnailDto {
  @IsString()
  @IsNotEmpty()
  tagSlug: string;

  @IsString()
  @IsNotEmpty()
  imageReg: string;

  @IsString()
  @IsNotEmpty()
  imageLarge: string;

  @IsString()
  @IsNotEmpty()
  imageSmall: string;

  @IsString()
  @IsNotEmpty()
  imageThumb: string;

  @IsString()
  @IsNotEmpty()
  tagGroup: string;
}

export class CancelTTBDto {
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @IsString()
  @IsNotEmpty()
  cancelReason: string;
}

export class SortOrder {
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @IsString()
  @IsOptional()
  name: string;

  @IsEnum(HomeTypeStatus)
  @IsOptional()
  status: HomeTypeStatus;
}

export class SortOrderHomeWidgetDto {
  @IsNotEmpty()
  @IsArray()
  sortOrderList: SortOrder[];
  @IsString()
  @IsOptional()
  groupKey: HomeWidgetGroupKey;
}

export class DeleteHomeWidgetDto {
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class SyncUsersToCDMConsumersDto {
  @IsOptional()
  @IsArray()
  emails: string[];

  @IsOptional()
  @IsBoolean()
  start: boolean;

  @IsOptional()
  @IsNumber()
  limit: number;
}

export class ExternalToolPayload {
  @IsOptional()
  id: string;

  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  toolUrl: string;
}

export class NotificationDto {
  @IsOptional()
  id: string;

  @IsNotEmpty()
  @IsArray()
  target: string[];

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  ctaLink: string;
}
export class UserPermissionDto {
  @IsOptional()
  @IsEnum(SUBSCRIPTION_SERVICES)
  subscriptionService: SUBSCRIPTION_SERVICES;

  @IsNotEmpty()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  planId: string;

  @IsNotEmpty()
  @IsString()
  expirationDate: Date;
}

export type SubscriptionServiceJob = Job<{
  users: any[];
}>;

export class OrderIdsDto {
  @IsOptional()
  @IsArray()
  orders: string[];
}

export class SyncKlaviyoTrackDto {
  @IsOptional()
  @IsArray()
  emails: string[];

  @IsOptional()
  @IsBoolean()
  start: boolean;

  @IsOptional()
  @IsBoolean()
  isHadSubscription: boolean;

  @IsOptional()
  @IsNumber()
  limit: number;
}

export class UpdateMemberShopOrdersDto {
  @IsOptional()
  @IsArray()
  orderIds: string[];

  @IsOptional()
  @IsString()
  year: string;

  @IsString()
  status: string;
}

export class GetTokenUserDto {
  @IsNotEmpty()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  code: string;
}

export class UserNotifyAdminDto {
  @IsNotEmpty()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsOptional()
  @IsString()
  is_ttb_fraud: boolean;

  @IsOptional()
  @IsString()
  is_referral: boolean;

  @IsOptional()
  @IsString()
  isCourse: boolean;
}

export class SendTestEmailDto {
  @IsNotEmpty()
  @IsString()
  email: string;

  @IsNotEmpty()
  @IsString()
  content: string;

  @IsNotEmpty()
  @IsString()
  subject: string;
}

export class ForceDeleteEmailsDto {
  @IsNotEmpty()
  @IsArray()
  emails: [];
}

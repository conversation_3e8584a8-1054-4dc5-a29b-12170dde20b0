import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export const IMAGE_TYPE = {
  PLAY_BACKGROUND: 'PLAY_BACKGROUND',
  NORMAL: 'NORMAL',
};
export const IMAGE_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};
export class AddImageDto {
  @IsNotEmpty()
  @IsString()
  keyImage: string;

  @IsNotEmpty()
  @IsString()
  urlImage: string;

  @IsOptional()
  @IsString()
  type: string;

  @IsOptional()
  @IsString()
  createdBy: string;

  @IsOptional()
  @IsString()
  status: string;
}
export class UpdateImageDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsOptional()
  @IsString()
  keyImage: string;

  @IsOptional()
  @IsString()
  urlImage: string;

  @IsOptional()
  @IsString()
  status: string;

  @IsOptional()
  @IsString()
  type: string;

  @IsOptional()
  @IsString()
  updatedBy: string;

  @IsOptional()
  @IsString()
  deletedBy: string;

  @IsOptional()
  deletedAt: Date;

  @IsOptional()
  updatedAt: Date;

  @IsOptional()
  isDelete: boolean;
}

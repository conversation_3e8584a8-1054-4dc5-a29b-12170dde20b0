import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import sgMail from '@sendgrid/mail';
import { ManagementClient } from 'auth0';
import { Queue } from 'bull';
import fs from 'fs';
import KlaviyoApi, { ApiKeySession, EventsApi } from 'klaviyo-api';
import * as _ from 'lodash';
import { isEmpty } from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import path from 'path';
import {
  Between,
  EntityManager,
  FindConditions,
  In,
  IsNull,
  Not,
  Repository,
  getConnection,
  getManager,
} from 'typeorm';
import { v4 } from 'uuid';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { DeService } from 'src/content/de.service';
import { KlaviyoSyncProcessorQueueName } from 'src/klaviyo/klaviyo-sync.processor';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { MrpService } from 'src/mrp/mrp.service';
import { SubscriptionEntity } from 'src/payment/entities/subscription.entity';
import { SwingIndexService } from 'src/si/si.service';
import { PLANS } from 'src/utils/plans';
import { isUSCountry } from 'src/utils/transform';
import { AuthService } from '../auth/auth.service';
import { UserBlackListEntity } from '../auth/entities/user-black-list.entity';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { UserPermissionCronService } from '../cdm/cdm.cron.service';
import { CdmService } from '../cdm/cdm.service';
import { ClientEntity } from '../client/entities/client.entity';
import ServiceAccount from '../config/mytm-backend-service-account.json';
import { ContentCacheService } from '../content/content.cache.service';
import { ContentTagThumbnailEntity } from '../content/entities/content-tag-thumbnail.entity';
import { EcomService } from '../ecom/ecom.service';
import { MEMBER_SHOP_STATUS, MemberShopOrderEntity } from '../ecom/entities/member-shop.entity';
import { AdminForceSyncKlaviyoCacheService } from '../klaviyo/klaviyo.cache.service';
import { KlaviyoCronService } from '../klaviyo/klaviyo.cron.service';
import { LoggingService } from '../logging/logging.service';
import { UserNotificationEntity } from '../notification/entities/user-notification.entity';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ResponseEntity } from '../survey/entities/response.entity';
import { UserTourTrashEntity } from '../tourtrash/entities/user-tour-trash.entity';
import { TranslationService } from '../translation/translation.service';
import { TTBService } from '../ttb/ttb.service';
import { ERROR_CODES } from '../utils/errors';
import { AdminForceSyncCDMCacheService } from './admin.cache.service';
import { USER_MESSAGE_ERROR } from './admin.constants';
import { AdminProcessorQueueName } from './admin.processor';
import {
  DeleteHomeWidgetDto,
  ExternalToolPayload,
  ForceDeleteEmailsDto,
  GetTokenUserDto,
  HomeWidgetDto,
  HomeWidgetGroupKey,
  SendTestEmailDto,
  SortOrderHomeWidgetDto,
  SubscriptionServiceJob,
  TagThumbnailDto,
  UserNotifyAdminDto,
  UserPermissionDto,
} from './admin.type';
import { COUNTRY_ERRORS_CODE, COUNTRY_ERRORS_MESSAGE, COUNTRY_STATUS, UpdateCountryDto } from './country.types';
import { CountryEntity } from './entities/country.entity';
import { ExternalToolEntity } from './entities/external-tool.entity';
import { HomeWidgetEntity } from './entities/home-widget.entity';
import { SalesforceCSVBuildProcessorQueueName } from './salesforce.processor';
import { UploadS3ProcessorQueueName } from './uploads3.processor';

const { GoogleSpreadsheet } = require('google-spreadsheet');

sgMail.setApiKey(process.env.SEND_GRID_API_KEY);

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);
  auth0Management;
  constructor(
    private readonly config: ConfigService,
    private readonly translationService: TranslationService,
    private readonly contentCacheService: ContentCacheService,
    private readonly adminForceSyncCDMCacheService: AdminForceSyncCDMCacheService,
    private readonly cdmService: CdmService,
    private readonly mrpService: MrpService,
    private readonly cdmCronService: UserPermissionCronService,
    private readonly authService: AuthService,
    private readonly klaviyoCronService: KlaviyoCronService,
    private readonly adminForceSyncKlaviyoCacheService: AdminForceSyncKlaviyoCacheService,
    private readonly ttbService: TTBService,
    private readonly siService: SwingIndexService,
    private readonly ecomService: EcomService,
    private readonly deService: DeService,
    private readonly loggingService: LoggingService,
    private readonly klaviyoService: KlaviyoService,
    @InjectQueue('klaviyo-sync') private syncKlaviyoQueue: Queue,
    @InjectQueue('cdm') private syncCDMQueue: Queue,
    @InjectQueue('update-subscription-service') private updateSubsServiceQueue: Queue,
    @InjectQueue('salesforce-csv') private salesforceCSVQueue: Queue,
    @InjectQueue('upload-s3') private uploadS3: Queue,
    @InjectRepository(HomeWidgetEntity) private readonly homeWidgetRepo: Repository<HomeWidgetEntity>,
    @InjectRepository(ClientEntity) private readonly clientRepo: Repository<ClientEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepo: Repository<SubscriptionEntity>,
    @InjectRepository(ExternalToolEntity) private readonly externalToolRepo: Repository<ExternalToolEntity>,
    @InjectRepository(UserNotifyAdminEntity) private readonly userNotifyAdminRepo: Repository<UserNotifyAdminEntity>,
    @InjectRepository(UserTourTrashEntity) private readonly userTourTrashRepo: Repository<UserTourTrashEntity>,
    @InjectRepository(ResponseEntity) private readonly responseRepo: Repository<ResponseEntity>,
    @InjectRepository(UserNotificationEntity) private readonly userNotificationRepo: Repository<UserNotificationEntity>,
    @InjectRepository(UserBlackListEntity) private readonly userBlackListRepo: Repository<UserBlackListEntity>,
    @InjectRepository(MemberShopOrderEntity)
    private readonly memberShopOrderEntityRepository: Repository<MemberShopOrderEntity>,
    @InjectRepository(ContentTagThumbnailEntity)
    private readonly contentTagThumbnailRepo: Repository<ContentTagThumbnailEntity>,
    @InjectRepository(CountryEntity)
    private readonly countryEntity: Repository<CountryEntity>
  ) {
    this.config = config;
    this.auth0Management = new ManagementClient({
      domain: this.config.get('app.auth0ApiDomain'),
      clientId: this.config.get('app.auth0ClientId'),
      clientSecret: this.config.get('app.auth0ClientSecret'),
      scope: this.config.get('app.auth0Scope'),
    });
  }

  async getHomeWidgets(groupKey: HomeWidgetGroupKey, country?: string) {
    const conditions: FindConditions<HomeWidgetEntity> = {};

    let conditionCountry = ` 1 = 1 `;
    if (country) {
      if (isUSCountry(country)) {
        conditionCountry += ` AND (countries LIKE '%${country}%' OR countries IS NULL) `;
      } else {
        conditionCountry += ` AND countries LIKE '%${country}%' `;
      }
    } else {
      conditionCountry = `(countries LIKE '%US%'  OR countries IS NULL) `;
    }
    if (groupKey) {
      conditions.groupKey = groupKey;
      if (groupKey?.trim() === HomeWidgetGroupKey.HomeTile) {
        conditionCountry = ` 1 = 1 `;
      }
    }
    const widgets = await this.homeWidgetRepo
      .createQueryBuilder('hw')
      .where(conditions)
      .andWhere(conditionCountry)
      .orderBy({
        'hw.sortOrder': 'ASC',
      })
      .getMany();

    if (!widgets || !widgets.length) {
      return [];
    }

    const datas = [];
    for (const widget of widgets) {
      let isProductErr = false;
      if (widget.productId && widget.status && widget.status == 'ACTIVE') {
        const product = await this.cdmService.ecomService.getProduct(widget.productId, country);
        if (!product) {
          isProductErr = true;
        }
      }

      datas.push({
        ...widget,
        isProductErr,
        extraData: widget.extraData ? JSON.parse(widget.extraData) : {},
      });
    }

    return datas;
  }

  async postUpdateHomeWidget(id: string, payload: HomeWidgetDto, userId: string): Promise<any> {
    const results = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const homeWidgetNeedUpdate = await this.getHomeWidgetsById(id);
      if (!homeWidgetNeedUpdate) {
        return {
          internalErrorCode: ERROR_CODES.HOME_WIDGET_NOT_FOUND,
          errorMessage: 'Home Widget not found',
        };
      }
      if (typeof payload.extraData !== 'undefined') {
        payload.extraData = JSON.stringify(payload.extraData);
      }
      const entityUpdate = await this.creatHomeWidgetEntityUpdate(id, payload, homeWidgetNeedUpdate, userId);
      const widget = await transactionalEntityManager.save(HomeWidgetEntity, entityUpdate);
      if (widget.extraData) {
        widget.extraData = JSON.parse(widget.extraData);
      }
      return widget;
    });
    if (payload.groupKey === HomeWidgetGroupKey.HomeExplorer) {
      this.contentCacheService.removePrefetchHomeExplorerContentsStatus().then((r) => r);
    }
    return results;
  }

  async postUpdateUserPermission(data: UserPermissionDto): Promise<any> {
    const user = await this.userRepo.findOne({ email: data.email });
    try {
      if (user) {
        const plan = PLANS[data.planId];
        if (!plan) {
          return {
            errorMessage: 'Wrong planId. Select in [champion_monthly, champion_annual, legend_monthly, legend_annual]',
          };
        }
        if (!Date.parse(data.expirationDate.toString())) {
          return {
            errorMessage: 'Error: expirationDate format is YYYY-MM-DD HH:MM:SS example: 2022-10-27 15:00:00',
          };
        }
        const myTMSubscriptionLevel = plan.level;
        let permissionOnUser;
        if (data.subscriptionService) {
          permissionOnUser = {
            subscriptionService: data.subscriptionService,
            myTMSubscriptionLevel,
          };
        } else {
          permissionOnUser = {
            myTMSubscriptionLevel,
          };
        }
        await this.userRepo.update({ email: data.email }, permissionOnUser);
        const subscriptionExpirationDate = data.expirationDate;
        const permissionList = this.cdmService.getPermissionListByPlan(myTMSubscriptionLevel);
        const permissions = {
          subscriptionExpirationDate,
          ...permissionList,
        };
        const userPermission = await this.userPermissionRepo.findOne({ userId: user.id });
        if (!userPermission) {
          await this.cdmService.createMyTMUserPermission(user.id, permissions);
        } else {
          await this.userPermissionRepo.update({ userId: user.id }, permissions);
        }
        await this.cdmService.updateAccount(user.email, this.config.get('app.defaultRegionId'), {
          myTMSubscriptionLevel,
          ...permissions,
        });
        await this.userPermissionRepo.update({ userId: user.id }, { isSyncCDM: true });
        return { ...permissionOnUser, ...permissions, userId: user.id };
      }
      return {
        errorMessage: 'Can not find user',
      };
    } catch (err) {
      await this.userPermissionRepo.update({ userId: user.id }, { isSyncCDM: false });
      console.error('ERR Sync to CDM permission', err);
      this.logger.error('Error get syn user permissions: ', err.message);
      return null;
    }
  }

  async creatHomeWidgetEntityUpdate(
    id: string,
    payload: HomeWidgetDto,
    homeWidgetRecord: HomeWidgetEntity,
    userId: string
  ): Promise<HomeWidgetEntity> {
    const entityRecord = new HomeWidgetEntity();
    entityRecord.id = id.toUpperCase();
    entityRecord.groupKey = payload.groupKey || homeWidgetRecord.groupKey;
    entityRecord.name = payload.name || homeWidgetRecord.name;
    entityRecord.type = payload.type || homeWidgetRecord.type;
    entityRecord.heading = payload.heading || homeWidgetRecord.heading;
    entityRecord.title = payload.title;
    entityRecord.description = payload.description;
    entityRecord.backgroundColor = payload.backgroundColor;
    entityRecord.backgroundImage = payload.backgroundImage;
    entityRecord.contentType = payload.contentType;
    entityRecord.contentId = payload.contentId;
    entityRecord.productId = payload.productId;
    entityRecord.contentFormat = payload.contentFormat;
    entityRecord.ctaText = payload.ctaText;
    entityRecord.ctaLink = payload.ctaLink;
    entityRecord.extraData = payload.extraData;
    entityRecord.category = payload?.category;
    entityRecord.countries = payload?.countries || null;
    (entityRecord.colorOpacity =
      typeof payload?.colorOpacity === 'undefined' ? homeWidgetRecord?.colorOpacity : payload?.colorOpacity),
      (entityRecord.textColor =
        typeof payload?.textColor === 'undefined' ? homeWidgetRecord?.textColor : payload?.textColor),
      (entityRecord.buttonAlign =
        typeof payload?.buttonAlign === 'undefined' ? homeWidgetRecord?.buttonAlign : payload?.buttonAlign),
      (entityRecord.textAlign =
        typeof payload?.textAlign === 'undefined' ? homeWidgetRecord?.textAlign : payload?.textAlign),
      (entityRecord.sortOrder = _.isNumber(payload.sortOrder) ? payload.sortOrder : homeWidgetRecord.sortOrder);
    entityRecord.status = payload.status || homeWidgetRecord.status;
    entityRecord.show = _.isBoolean(payload.show) ? payload.show : homeWidgetRecord.show;
    entityRecord.updatedBy = userId;
    if (entityRecord.groupKey === HomeWidgetGroupKey.HomeExplorer) {
      this.contentCacheService.removePrefetchHomeExplorerContentsStatus().then((r) => r);
    }
    return entityRecord;
  }

  async postUpdateSortOrderHomeWidget(
    payload: SortOrderHomeWidgetDto,
    userId: string,
    groupKey?: HomeWidgetGroupKey
  ): Promise<{ success: boolean }> {
    const results = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      await Promise.all(
        payload.sortOrderList.map(async (order) => {
          if (!order?.id) {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.HOME_WIDGET_NOT_FOUND,
              errorMessage: 'Home Widget not found',
            });
          }
          await transactionalEntityManager.save(HomeWidgetEntity, {
            id: order.id,
            name: order.name,
            status: order.status,
            sortOrder: order.sortOrder,
            updatedBy: userId,
          });
        })
      );
      return { success: true };
    });
    if (groupKey === HomeWidgetGroupKey.HomeExplorer) {
      await this.contentCacheService.removeSortedHomeExplorerWidgetIds();
      const sorted = payload.sortOrderList.sort((a, b) => {
        return a.sortOrder - b.sortOrder;
      });
      await this.contentCacheService.cacheSortedHomeExplorerWidgetIds(sorted.map((item) => item.id));
    }
    this.contentCacheService.removePrefetchHomeExplorerContentsStatus().then((r) => r);
    return results;
  }

  async postDeleteHomeWidget(payload: DeleteHomeWidgetDto, userId: string) {
    const results = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const homeWidgetNeedDeleted = await this.getHomeWidgetsById(payload.id);
      if (!homeWidgetNeedDeleted) {
        return {
          internalErrorCode: ERROR_CODES.HOME_WIDGET_NOT_FOUND,
          errorMessage: 'Home Widget not found',
        };
      }
      await this.homeWidgetRepo.softDelete({ id: payload.id });
      await CommonSubscriber.activityLogCommandDelete(
        {
          entity: {
            uuid: payload.id,
            deletedBy: userId,
          },
          manager: transactionalEntityManager,
          databaseEntity: homeWidgetNeedDeleted,
        },
        HomeWidgetEntity.name
      );
      return { success: true };
    });
    this.contentCacheService.removePrefetchHomeExplorerContentsStatus().then((r) => r);
    return results;
  }

  async getClients() {
    return this.clientRepo.find();
  }

  async postUpdateTagThumbnail(payload: TagThumbnailDto) {
    let tagThumbnail = await this.contentTagThumbnailRepo.findOne({
      tagSlug: payload.tagSlug,
      tagGroup: payload.tagGroup,
    });
    if (!tagThumbnail) {
      tagThumbnail = await this.contentTagThumbnailRepo.save({
        id: v4(),
        ...payload,
        imageUrl: payload.imageReg,
      });
      return tagThumbnail;
    }
    await this.contentTagThumbnailRepo.update(
      { tagSlug: payload.tagSlug, tagGroup: payload.tagGroup },
      {
        ...payload,
        imageUrl: payload.imageReg,
      }
    );
    return { ...tagThumbnail, ...payload, updatedAt: new Date() };
  }

  async postSyncTranslations(onlyLanguage) {
    return this.translationService.syncTranslations(onlyLanguage);
  }

  async syncCdmConsumers(emails: string[]) {
    for (const email of emails) {
      const user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        return;
      }
      if (user.cdmUID && user.cdmUID !== 'NULL') {
        return;
      }
      const { data } = await this.cdmService.createAccount(user.email, user.regionId, {
        myTM: user.id,
        auth0Id: user.auth0UID,
      });
      if (data) {
        await this.userRepo.update({ email }, { cdmUID: data.id });
      }
    }
    return {
      success: true,
    };
  }

  async syncEComMissingCustomers(emails: string[]) {
    for (const email of emails) {
      const user = await this.userRepo.findOne({ where: { email } });
      console.log(`Syncing account: ${user.email}`);
      const consumer: any = await this.cdmService.getConsumer(
        user.email,
        user.regionId || this.config.get('app.defaultRegionId'),
        false,
        false
      );
      if (consumer) {
        const updateUserPayload: any = {};
        const { customer } = await this.ecomService.updateCustomer(
          {
            trigger: 'none',
            authentication: {
              provider: 'auth0',
              user: {
                id: user.auth0UID,
                name: consumer.firstName,
                given_name: consumer.firstName,
                family_name: consumer.lastName,
                emails: [{ value: user.email }],
                metadata: {
                  first_name: consumer.firstName,
                  last_name: consumer.lastName,
                },
              },
            },
            customer: {
              firstName: consumer.firstName,
              lastName: consumer.lastName,
            },
          },
          user.userCountry
        );
        if (customer) {
          updateUserPayload.ecomUID = customer.eCom;
          const permissionList = this.cdmService.getPermissionListByPlan(consumer.myTMSubscriptionLevel);
          await this.cdmService.updateAccount(user.email, this.config.get('app.defaultRegionId'), {
            eCom: customer.eCom,
            ...permissionList,
          });
          await this.userRepo.update({ email: user.email }, updateUserPayload);
        }
        console.log(`Synced account: ${user.email}`);
      }
    }
    return true;
  }

  async cleanUsers(emails: string[]) {
    const connection = getConnection();
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    const failedEmails = [];
    for (const email of emails) {
      const user = await this.userRepo.findOne({ where: { email } });
      if (user) {
        await queryRunner.startTransaction();
        try {
          await queryRunner.query(
            `DELETE w FROM ResponseItems w INNER JOIN Responses e ON w.responseId=e.id WHERE userId = '${user.id}'`
          );
          await queryRunner.query(`DELETE FROM Responses WHERE userId = '${user.id}'`);
          await queryRunner.query(`DELETE FROM SubscriptionEventLogs WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM Subscriptions WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM AccessCodes WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM DismissedContents WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM MemberShopOrder WHERE cdmPrimaryEmail='${user.email}'`);
          await queryRunner.query(`DELETE FROM TryThenBuy WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM TryThenBuyPushes WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM UserOnboardingSteps WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM UserOtps WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM UserTourTrashs WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM ViewedContents WHERE userId='${user.id}'`);
          await queryRunner.query(`DELETE FROM Users WHERE id='${user.id}'`);
          await queryRunner.query(`DELETE FROM UserPermissions WHERE userId='${user.id}'`);
          const permissionList = this.cdmService.getPermissionListByPlan(0);
          await this.cdmService.updateAccount(email, this.config.get('app.defaultRegionId'), {
            myTMSubscriptionLevel: 0,
            subscriptionLength: 0,
            subscriptionExpirationDate: null,
            subscriptionService: '',
            ...permissionList,
          });
          await queryRunner.commitTransaction();
        } catch (err) {
          console.log(err);
          failedEmails.push(user.email);
          await queryRunner.rollbackTransaction();
        } finally {
          await queryRunner.release();
        }
      }
    }
    return {
      success: failedEmails.length === 0,
      failedEmails,
    };
  }

  async syncUserInputHandicap(emails: string[]) {
    for (const email of emails) {
      const user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        return;
      }
      if (!user.onboardingComplete) {
        return;
      }
      const regionId = user?.regionId || this.config.get('app.defaultRegionId');
      const consumer = await this.cdmService.getConsumer(email, regionId, false, false);
      if (consumer) {
        const userInputHandicap = consumer?.golferProfile?.newHandicap?.userInputHandicap;
        if (_.isNil(userInputHandicap)) {
          const averageScoreRange = consumer?.golferProfile?.averageScoreRange;
          const handicap = this.cdmService.transformUserInputHandicap(averageScoreRange);
          if (!_.isNil(handicap)) {
            await this.cdmService.updateAccount(email, regionId, {
              userInputHandicap: handicap,
            });
          }
        }
      }
    }
    return {
      success: true,
    };
  }

  async ttbShipmentsScanning(dates) {
    const orderIds = await this.ttbService.getAllOrderIds();
    const scanningFileNames = await this.ttbService.scanShipmentFileNames(dates || []);
    const client = await this.ttbService.getFTPClient();
    const unhandledFilenames = [];
    const unhandledOrderIds = [];
    for (const filename of scanningFileNames) {
      const { orders } = await this.ttbService.getDailyEBSShipmentOrders(orderIds, filename);
      if (orders.length > 0) {
        for (const order of orders) {
          const orderId = order[2];
          const shipmentFilename = `TryThenBuy-${orderId}-Shipped.csv`;
          const EBSFileFormatPath = `/${this.config.get('app.ttbFTPRootPath')}/Archived/${shipmentFilename}`;
          const status = await client.exists(EBSFileFormatPath);
          if (!status) {
            !unhandledFilenames.includes(filename) && unhandledFilenames.push(filename);
            !unhandledOrderIds.includes(orderId) && unhandledOrderIds.push(orderId);
          }
        }
      }
    }
    await client.end();
    return {
      success: true,
      unhandledFilenames,
      unhandledOrderIds,
    };
  }

  async ttbUploadGVCShippedFiles(filenames: string[]) {
    const orderIds = await this.ttbService.getAllOrderIds();
    const client = await this.ttbService.getFTPClient();
    for (const filename of filenames) {
      const { heading, orders } = await this.ttbService.getDailyEBSShipmentOrders(orderIds, filename);
      if (orders.length > 0) {
        for (const order of orders) {
          const orderId = order[2];
          const shipmentFilename = `TryThenBuy-${orderId}-Shipped.csv`;
          const EBSFileFormatPath = `/${this.config.get('app.ttbFTPRootPath')}/Archived/${shipmentFilename}`;
          const status = await client.exists(EBSFileFormatPath);
          if (!status) {
            const buffer = xlsx.build(
              [
                {
                  name: orderId,
                  data: [heading, order],
                },
              ],
              { bookType: 'csv' }
            );
            this.logger.log(`Started to put ${shipmentFilename} to GVC...`);
            await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/GVC/${shipmentFilename}`);
            await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/Archived/${shipmentFilename}`);
            this.logger.log(`Putted ${shipmentFilename} to GVC...`);
          }
        }
      }
    }
    await client.end();
    return {
      success: true,
    };
  }

  async postHandleYoutubeThumbnails(spreadsheetId) {
    try {
      const doc = new GoogleSpreadsheet(spreadsheetId);
      await doc.useServiceAccountAuth(ServiceAccount);
      await doc.loadInfo();
    } catch (e) {
      return [e, false];
    }
    return [null, true];
  }

  async postCreateHomeWidget(payload: HomeWidgetDto, userId: string) {
    const widget = await this.homeWidgetRepo.save({
      id: v4(),
      ...payload,
      createdBy: userId,
    });
    if (widget.extraData) {
      widget.extraData = JSON.parse(widget.extraData);
    }
    return widget;
  }

  async getHomeWidgetsById(id: string) {
    return await this.homeWidgetRepo.findOne({
      where: {
        id: In([id.toUpperCase(), id.toLowerCase()]),
      },
    });
  }

  async getExternalTools({ ...options }) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.externalToolRepo.createQueryBuilder('ext').where({});
    const [tools, total] = await query
      .orderBy('ext.name', 'ASC')
      .addOrderBy('ext.createdAt', 'DESC')
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .getManyAndCount();

    if (total === 0) {
      return {
        total: 0,
        data: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      data: tools,
    };
  }

  async saveExternalTool(payload: ExternalToolPayload, userId: string): Promise<any> {
    const id = payload?.id || v4();
    const objPayload = {
      id,
      name: payload.name,
      toolUrl: payload.toolUrl,
      createdBy: userId,
      updatedBy: null,
    };

    if (payload?.id) {
      const external = await this.externalToolRepo.findOne(payload?.id);
      if (!external) {
        return {
          internalErrorCode: ERROR_CODES.EXTERNAL_NOT_FOUND,
          errorMessage: 'The external tool is not found!',
        };
      }
      delete objPayload.createdBy;
      objPayload.updatedBy = userId;
    }

    const entity = this.externalToolRepo.create(objPayload);

    return await this.externalToolRepo.save(entity);
  }

  async deleteExternalTool(id: string) {
    const external = await this.externalToolRepo.findOne(id);
    if (!external) {
      return {
        internalErrorCode: ERROR_CODES.EXTERNAL_NOT_FOUND,
        errorMessage: 'The external tool is not found!',
      };
    }
    await this.externalToolRepo.softDelete(id);
    return { success: true };
  }

  async updateSubscriptionService() {
    const currentDate = moment();
    const query = this.subscriptionRepo.createQueryBuilder('subs');
    const listUsers = await query
      .innerJoin('Users', 'user', 'user.id = subs.userId')
      .where('expirationDate >= :currentDate', { currentDate: currentDate.toDate() })
      .select('userId, platform, user.email, fromAccessCode, expirationDate, regionId')
      .orderBy('expirationDate', 'DESC')
      .getRawMany();

    const chunkListUsers = _.chunk(listUsers, 100);

    if (chunkListUsers.length > 0) {
      for (const users of chunkListUsers) {
        const jobData: SubscriptionServiceJob['data'] = {
          users,
        };
        this.logger.log(`Delay 10s add queue update subscriptionService...`);
        await new Promise((resolve) => setTimeout(resolve, 10000));
        await this.updateSubsServiceQueue.add(AdminProcessorQueueName.UPDATE_SUBSCRIPTION_SERVICE, jobData);
      }
    }
  }

  async syncUpdateSubscriptionService(emails: string[]) {
    const currentDate = moment();
    const query = this.subscriptionRepo.createQueryBuilder('subs');
    const listUsers = await query
      .innerJoin('Users', 'user', 'user.id = subs.userId')
      .where('expirationDate >= :currentDate', { currentDate: currentDate.toDate() })
      .andWhere('user.email IN (:...emails)', {
        emails,
      })
      .select('userId, platform, user.email, fromAccessCode, expirationDate, regionId')
      .orderBy('expirationDate', 'DESC')
      .getRawMany();
    const chunkListUsers = _.chunk(listUsers, 100);

    if (chunkListUsers.length > 0) {
      for (const users of chunkListUsers) {
        const jobData: SubscriptionServiceJob['data'] = {
          users,
        };
        this.logger.log(`Delay 10s add queue update subscriptionService...`);
        await new Promise((resolve) => setTimeout(resolve, 10000));
        await this.updateSubsServiceQueue.add(AdminProcessorQueueName.UPDATE_SUBSCRIPTION_SERVICE, jobData);
      }
    }
  }
  async getPermissionUserFromMyTM(email: string) {
    try {
      const query = this.userRepo.createQueryBuilder('U');
      return await query
        .innerJoin('UserPermissions', 'UP', 'U.id = UP.userId')
        .where('U.email = :email', {
          email,
        })
        .select('email, emailVerified, myTMSubscriptionLevel, subscriptionService, tmAccessCode, UP.*')
        .getRawOne();
    } catch (e) {
      this.logger.error('Error get getPermissionUserFromMyTM: ', e.message);
      return null;
    }
  }
  async forceSyncMyTMtoCDM(start = true, limit?: number, emails?: string[]) {
    try {
      if (emails) {
        for (const email of emails) {
          const user = await this.userRepo.findOne({ email });
          if (user) {
            await this.userPermissionRepo.update({ userId: user.id }, { isSyncCDM: false });
          }
        }
        return { success: true };
      } else {
        const addedQueueSyncUserPermission = await this.adminForceSyncCDMCacheService.getCurrentUserWhenForceSync();
        await this.adminForceSyncCDMCacheService.removeTotalUserWhenForceSync();
        await this.adminForceSyncCDMCacheService.removeCurrentUserWhenForceSync();
        if (start === false) {
          this.cdmCronService.stopForceSyncToCDMCronJob();
          return { success: true, addedQueueSyncUserPermission };
        }
        const total = await this.userPermissionRepo.count({});
        await this.adminForceSyncCDMCacheService.cacheTotalUserWhenForceSync(total);
        await this.cdmCronService.startForceSyncToCDMCronJob(limit);
        return { success: true };
      }
    } catch (error) {
      this.logger.error(error);
      return { success: false, errorMessage: error.message };
    }
  }
  async getPermissionUserFromCDM(email: string) {
    try {
      return await this.cdmService.getConsumer(email, this.config.get('app.defaultRegionId'), false, false);
    } catch (e) {
      this.logger.error('Error get getPermissionUserFromCDM: ', e.message);
      return null;
    }
  }

  async resetPermissionsToTheFreePlan(email: string) {
    const permissionList = this.cdmService.getPermissionListByPlan(0);
    const user = await this.userRepo.findOne({ where: { email } });
    await this.userRepo.update({ email }, { myTMSubscriptionLevel: 0, tmAccessCode: false });
    await this.userPermissionRepo.update(
      { userId: user.id },
      {
        subscriptionLength: 0,
        subscriptionExpirationDate: new Date().toISOString(),
        isSyncCDM: false,
        ...permissionList,
      }
    );
    await this.cdmService.clearCacheUserPermissionsWhenPurchase(user.id);
    return this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
      myTMSubscriptionLevel: 0,
      subscriptionLength: 0,
      tmAccessCode: false,
      subscriptionExpirationDate: new Date().toISOString(),
      ...permissionList,
    });
  }

  async ttbTriggerOrderConfirmation(orderIds) {
    if (orderIds) {
      for (const orderId of orderIds) {
        try {
          const ttb = await this.ttbService.getOrder(orderId);
          const user = await this.userRepo.findOne(ttb.userId);
          const ttbEcomInfo = await this.ttbService.getEcomOrder(orderId, ttb?.country);
          let producInfo = {};
          try {
            producInfo = JSON.parse(ttb.productInfo);
          } catch (error) {
            console.log(`Error JSON parse product info`);
            console.log(error);
          }
          await this.ttbService.trackingEventKlaviyoTTB(
            KlaviyoTrackEvents.TTB_ORDER_CONFIRMATION,
            user.email,
            ttbEcomInfo,
            producInfo
          );
          console.log(`Trigger TTB_ORDER_CONFIRMATION ${orderId} done. `);
        } catch (error) {
          console.error(`Trigger TTB_ORDER_CONFIRMATION ${orderId} fail. `);
          console.error(error.message);
        }
      }
    }
  }

  async forceSyncKlaviyoTrack(isHadSubscription = false, start = true, limit?: number, emails?: string[]) {
    try {
      if (emails) {
        for (const email of emails) {
          await this.syncKlaviyoQueue.add(KlaviyoSyncProcessorQueueName.FORCE_SYNC_KLAVIYO, {
            email,
          });
        }
        return { success: true };
      } else {
        const firstRows = await this.userRepo
          .createQueryBuilder()
          .where('myTMSubscriptionLevel is null')
          .andWhere('isNewAccount = 0')
          .select('email')
          .distinct(true)
          .orderBy('email')
          .getRawMany();
        const secondRows = await this.userRepo
          .createQueryBuilder()
          .where('myTMSubscriptionLevel = 0')
          .andWhere('isNewAccount = 0')
          .select('email')
          .distinct(true)
          .orderBy('email')
          .getRawMany();
        const rows = [...firstRows, ...secondRows];
        for (const row of rows) {
          await this.klaviyoCronService.addSyncKlaviyoQueue(row.email);
        }
        return { success: true };
      }
    } catch (error) {
      this.logger.error(error);
      return { success: false, errorMessage: error.message };
    }
  }

  async getMemberShopOrders(status: string) {
    const data = await this.memberShopOrderEntityRepository.find({
      where: { status },
      select: ['status', 'orderId', 'createdAt', 'cdmPrimaryEmail'],
    });
    const total = await this.memberShopOrderEntityRepository.count({ status });
    return {
      total,
      data,
    };
  }

  async getUserSubscriptions(userId: string) {
    return this.subscriptionRepo.find({
      where: { userId },
      order: {
        createdAt: 'ASC',
      },
    });
  }

  async getUserCollectDataForSalesforce(limit: number) {
    const rows = await this.userRepo
      .createQueryBuilder()
      .where({ onboardingComplete: true, mrpUID: Not(IsNull()), shouldCollectSalesforceData: true })
      .select('email')
      .distinct(true)
      .orderBy('email')
      .limit(limit)
      .getRawMany();
    if (rows.length === 0) {
      return [];
    }
    return _.chunk(rows, 20);
  }

  async updateShouldCollectSalesforceDataForUsers(emails: string[], shouldCollectSalesforceData: boolean) {
    const chunkEmails = _.chunk(emails, 100);
    for (const rows of chunkEmails) {
      await this.userRepo.update({ email: In(rows) }, { shouldCollectSalesforceData });
    }
    return chunkEmails;
  }

  async updateIsWIBExportForUsers(emails: string[], isWIBExport: boolean) {
    const chunkEmails = _.chunk(emails, 100);
    for (const rows of chunkEmails) {
      await this.userRepo.update({ email: In(rows) }, { isWIBExport });
    }
    return chunkEmails;
  }

  async uploadFileToS3(listFiles: string[]) {
    await this.uploadS3.add(UploadS3ProcessorQueueName.UPLOAD_S3, { files: listFiles });
  }

  async setDefaultShouldCollectSalesforceDataForUsers() {
    await this.userRepo.update({}, { shouldCollectSalesforceData: true });
  }

  async getUserProfileCollectDataForSalesforce(emails: string[]) {
    let users: UserEntity[];
    if (emails?.length > 0) {
      users = await this.userRepo.find({
        where: { email: In(emails), mrpUID: Not(IsNull()), shouldCollectSalesforceData: true },
      });
    }
    const modifiedAt = moment().format('MM/DD/YYYY HH:mm');
    const rows = [];
    const profileEmails = [];
    for (const user of users) {
      const consumer: any = await this.cdmService.getConsumerWithoutRegionId(user.email);
      if (!consumer) {
        continue;
      }
      const country = consumer.consumerProfiles[0]?.region?.code === 'CAN' ? 'CAN' : 'US';
      const playerInstructor = await this.siService.getPlayerInstructor(user.email);
      const recentSwingsShotVideos = (await this.siService.getRecentSwingShotVideos(user.email)) || [];
      const numberOfDrivingSwingsUploads = recentSwingsShotVideos.filter((video) =>
        [1, 2].includes(video.golfClub?.golfClubTypeId)
      )?.length;
      const numberOfApproachSwingsUploads = recentSwingsShotVideos.filter((video) =>
        [5, 4].includes(video.golfClub?.golfClubTypeId)
      )?.length;
      const numberOfPuttingSwingsUploads = recentSwingsShotVideos.filter((video) =>
        [6, 3].includes(video.golfClub?.golfClubTypeId)
      )?.length;
      let overallStats: any = {};
      let drills: any = {};
      try {
        overallStats = (await this.mrpService.getRoundStats(parseInt(user.mrpUID, 10), '')) || {};
        drills = await this.deService.requestContents(
          user.cdmUID,
          user.email,
          'drills',
          12,
          consumer.tmUserIds.playServicePreference,
          user.id
        );
      } catch (e) {}
      const drillCategories = drills?.content
        ? _.uniq(drills?.content?.videos?.map((video) => video.category)).join(', ')
        : '';
      const collectData = {
        email: user.email,
        gender: consumer.gender || 'male',
        dob: `${moment(new Date(consumer.dob)).format('MM/DD/YYYY')}`,
        handed: consumer.golferProfile.handed,
        handicap: consumer.golferProfile.handicap || consumer.golferProfile.newHandicap?.userInputHandicap,
        ghinHandicap: consumer.golferProfile.ghinHandicap,
        homeCourse: consumer.golferProfile.homeCourse,
        favoriteTeamMembers: consumer.golferProfile.favoriteTeamMembers,
        optIns: consumer.consumerProfiles[0]?.consumerOptIns[0]?.value ? 'Yes' : 'No',
        tier:
          user.myTMSubscriptionLevel === 0
            ? 'Free'
            : `${user.myTMSubscriptionLevel === 1 ? 'Champion' : 'Legend'} - ${
                consumer.myTMPermission.subscriptionLength === 12 ? 'Annual' : 'Monthly'
              }`,
        averageScore: consumer.golferProfile.averageScore,
        recentSwingsShotVideos,
        numberOfSwingsUploads: recentSwingsShotVideos.length,
        numberOfDrivingSwingsUploads,
        numberOfApproachSwingsUploads,
        numberOfPuttingSwingsUploads,
        playerInstructor: playerInstructor?.firstName
          ? `${playerInstructor.firstName} ${playerInstructor.lastName} (${
              playerInstructor.jobTitle || 'No job title'
            })`
          : '',
        overallStats,
        drillCategories,
        drills,
      };
      rows.push([
        collectData.email,
        collectData.gender,
        collectData.dob,
        collectData.handed,
        collectData.handicap,
        collectData.ghinHandicap,
        collectData.homeCourse,
        collectData.optIns,
        collectData.tier,
        collectData.overallStats?.averageScore || '',
        collectData.favoriteTeamMembers,
        collectData.numberOfSwingsUploads,
        collectData.numberOfPuttingSwingsUploads,
        collectData.numberOfApproachSwingsUploads,
        collectData.numberOfDrivingSwingsUploads,
        collectData.playerInstructor,
        collectData.overallStats?.strokesGainedTotal || '',
        collectData.overallStats?.strokesGainedDriving || '',
        collectData.overallStats?.strokesGainedApproach || '',
        collectData.overallStats?.strokesGainedShort || '',
        collectData.overallStats?.strokesGainedPutting || '',
        collectData.overallStats?.drivingFairwaysHit || '',
        collectData.overallStats?.shortGreenHit || '',
        collectData.overallStats?.classicStatsSandSaves || '',
        collectData.overallStats?.classicStatsPuttsPerRound || '',
        collectData.drillCategories,
        user.ecomUID,
        country,
        `${moment(new Date(user.createdAt)).format('MM/DD/YYYY HH:mm')}`,
        modifiedAt,
      ]);
      profileEmails.push(collectData.email);
    }
    return { rows, profileEmails };
  }

  async getUserWITBCollectDataForSalesforce(emails: string[]) {
    const users = await this.userRepo.find({
      where: { email: In(emails), mrpUID: Not(IsNull()), shouldCollectSalesforceData: true },
    });
    const rows = [];
    for (const user of users) {
      try {
        const witb = await this.cdmService.getWITB(user.email);
        witb
          .filter((item) => !item.deleted)
          .forEach((item) => {
            let created = '';
            try {
              created = moment(item.created).format('MM/DD/YYYY');
            } catch (error) {
              console.log(error.message);
            }
            const active = item?.inBag == true;
            const deleted = item?.disable == true;
            rows.push([
              user.email,
              item.clubFamily?.name,
              item.modelName?.name,
              item.manufacturer?.name,
              item.clubType?.type || '',
              created,
              deleted,
              active,
            ]);
          });
      } catch (e) {}
    }
    return rows;
  }

  async getUserWITBCollectDataForWIB(emails: string[]) {
    const users = await this.userRepo.find({
      where: { email: In(emails), mrpUID: Not(IsNull()), isWIBExport: IsNull() },
    });
    const rows = [];
    for (const user of users) {
      try {
        const witb = await this.cdmService.getWITB(user.email);
        witb
          .filter((item) => !item.deleted)
          .forEach((item) => {
            let created = '';
            try {
              created = moment(item.created).format('MM/DD/YYYY');
            } catch (error) {
              console.log(error.message);
            }
            const active = item?.inBag == true;
            const deleted = item?.disable == true;
            rows.push([
              user.email,
              item.clubFamily?.name,
              item.modelName?.name,
              item.manufacturer?.name,
              item.clubType?.type || '',
              created,
              deleted,
              active,
            ]);
          });
      } catch (e) {}
    }
    return rows;
  }

  async getUserRoundsCollectDataForSalesforce(emails: string[]) {
    const users = await this.userRepo.find({
      where: { email: In(emails), mrpUID: Not(IsNull()), shouldCollectSalesforceData: true },
    });
    const rows = [];
    for (const user of users) {
      try {
        const rounds = await this.mrpService.getRecentRounds(parseInt(user.mrpUID, 10), '?per=500&light=true');
        rounds.data.forEach((round) => {
          rows.push([
            user.email,
            round.courseName,
            `${moment(new Date(round.playedOn)).format('MM/DD/YYYY HH:mm')}`,
            round.totalScore,
          ]);
        });
      } catch (e) {}
    }
    return rows;
  }

  async updateMemberShopOrderStatuses(orderIds: string[], year: string, status: string) {
    if (year) {
      return this.memberShopOrderEntityRepository.update(
        { createdAt: Between(`${year}`, `${parseInt(year) + 1}`), status: Not(MEMBER_SHOP_STATUS.CANCELLED) },
        { status }
      );
    }
    if (orderIds && orderIds.length > 0) {
      for (const orderId of orderIds) {
        await this.memberShopOrderEntityRepository.update({ orderId }, { status });
      }
    }
    return null;
  }

  async deleteProfile(userId, adminUserId) {
    try {
      const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        const user = await this.userRepo.findOne({
          where: { id: userId },
        });
        if (!user) {
          throw new BadRequestException({
            internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
            errorMessage: USER_MESSAGE_ERROR.USER_NOT_FOUND,
          });
        }
        await transactionalEntityManager.update(
          UserEntity,
          { id: userId },
          { deletedAt: new Date(Date.now()), deletedBy: adminUserId }
        );
        await CommonSubscriber.activityLogCommandDelete(
          {
            entity: {
              uuid: userId,
              deletedBy: adminUserId,
            },
            manager: transactionalEntityManager,
            databaseEntity: user,
          },
          UserEntity.name
        );
        return true;
      });
      return { success: res };
    } catch (e) {
      console.error(`Delete user error: ${e.message}`);
      return { success: false };
    }
  }

  async updateUserProfile(userId, adminUserId, payload) {
    try {
      const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        const user = await this.userRepo.findOne({
          where: { id: userId },
          withDeleted: true,
        });
        if (!user) {
          throw new BadRequestException({
            internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
            errorMessage: USER_MESSAGE_ERROR.USER_NOT_FOUND,
          });
        }
        await transactionalEntityManager.update(UserEntity, { id: userId }, payload);
        await CommonSubscriber.activityLogCommandUpdate(
          {
            entity: {
              uuid: userId,
              updatedBy: adminUserId,
            },
            manager: transactionalEntityManager,
          },
          UserEntity.name,
          user,
          { ...payload, updatedBy: adminUserId }
        );
        return true;
      });
      return { success: res };
    } catch (e) {
      console.error(`Update user error: ${e.message}`);
      return { success: false };
    }
  }

  async restoreProfile(userId, adminUserId) {
    try {
      const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        const user = await this.userRepo.findOne({
          where: { id: userId },
          withDeleted: true,
        });
        if (!user) {
          throw new BadRequestException({
            internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
            errorMessage: USER_MESSAGE_ERROR.USER_NOT_FOUND,
          });
        }
        await transactionalEntityManager.update(
          UserEntity,
          { id: userId },
          { deletedAt: null, deletedBy: null, updatedBy: adminUserId }
        );
        await CommonSubscriber.activityLogCommandUpdate(
          {
            entity: {
              uuid: userId,
              updatedBy: adminUserId,
            },
            manager: transactionalEntityManager,
          },
          UserEntity.name,
          user,
          { deletedAt: null, deletedBy: null, updatedBy: adminUserId }
        );
        return true;
      });
      return { success: res };
    } catch (e) {
      console.error(`Active user error: ${e.message}`);
      return { success: false };
    }
  }
  async syncUpdatePermission({ limit = 20 }) {
    try {
      const query = this.userRepo
        .createQueryBuilder('u')
        .select(
          'u.id, email, myTMSubscriptionLevel, subscriptionExpirationDate, subscriptionService, subscriptionLength'
        )
        .innerJoin('UserPermissions', 'up', 'u.id = up.userId')
        .where('u.myTMSubscriptionLevel = 0')
        .andWhere('up.subscriptionExpirationDate > GETDATE()');
      const users = await query.limit(limit).offset(0).getRawMany();
      const result = [];
      if (users) {
        for (const user of users) {
          const currentYear = moment().format('YYYY');
          const expiredYear = moment(user.subscriptionExpirationDate).format('YYYY');
          const planId = +expiredYear - +currentYear > 0 ? 'champion_annual' : 'champion_monthly';
          const userPermissionDto: UserPermissionDto = {
            email: user.email,
            expirationDate: user.subscriptionExpirationDate,
            planId,
            subscriptionService: user.subscriptionService ?? 'MyTaylorMade',
          };
          const updated = await this.postUpdateUserPermission(userPermissionDto);
          result.push(updated);
        }
      }
      return result;
    } catch (e) {
      console.log(e);
      this.logger.error('Error get syn user permissions: ', e.message);
      return null;
    }
  }

  async syncCountries() {
    const regions = await this.cdmService.getRegions();
    if (regions) {
      for (const region of regions) {
        const code = region.code?.trim()?.toUpperCase();
        const country = await this.countryEntity.findOne({ where: { code } });
        if (country) {
          await this.countryEntity.update(
            { id: country.id },
            { cdmRegionId: region.id, code, name: region.description }
          );
        } else {
          const newCountry = new CountryEntity();
          newCountry.cdmRegionId = region.id;
          newCountry.code = code;
          newCountry.name = region.description;
          await this.countryEntity.save(newCountry);
        }
      }
    }
    return await this.countryEntity.find();
  }
  async syncCountriesFromOC() {
    const countries = await this.mrpService.getCountriesWithCode();
    if (countries) {
      let count = 0;
      for (const ct of countries.data) {
        count++;
        const code = ct.alpha2?.trim()?.toUpperCase();
        const country = await this.countryEntity.findOne({ where: { code } });
        if (country) {
          await this.countryEntity.update({ id: country.id }, { code, name: country.name, isoCode: country.isoCode });
          this.logger.log(`${count}: UPDATE COUNTRY ${country.name}`);
        } else {
          const newCountry = new CountryEntity();
          newCountry.code = code;
          newCountry.name = ct.name;
          newCountry.isoCode = ct.isoCode;
          newCountry.status = COUNTRY_STATUS.IN_ACTIVE;
          await this.countryEntity.save(newCountry);
          this.logger.log(`${count}: INSERT NEW COUNTRY ${newCountry.name}`);
        }
      }
    }
    return await this.countryEntity.find();
  }
  async updateCountry(payload: UpdateCountryDto) {
    const code = payload.code.trim().toUpperCase();
    let country = await this.countryEntity.findOne({ where: { code } });
    if (!country) {
      throw new NotFoundException({
        internalErrorCode: COUNTRY_ERRORS_CODE.NOT_FOUND,
        errorMessage: COUNTRY_ERRORS_MESSAGE.NOT_FOUND,
      });
    }
    await this.countryEntity.update({ code }, payload);
    country = await this.countryEntity.findOne({ where: { code } });
    return country;
  }

  async getTokenUser({ email, code }: GetTokenUserDto) {
    try {
      if (!code || code !== 'RhOCIsInR5cGUiOiJBV') {
        return { success: false };
      }
      const user = await this.userRepo.findOne({ email });
      if (!user) {
        return { success: false };
      }
      const token = this.authService.signIdToken(email, user.id, user.cdmUID, user.mrpUID, user.ecomUID, user.regionId);

      return { success: true, token };
    } catch (err) {
      console.log('Err getTokenUser', err);
      this.logger.error('Error get token user: ', err.message);
      return { success: false };
    }
  }

  async getUserCollectDataForWIB(limit: number) {
    const rows = await this.userRepo
      .createQueryBuilder()
      .where({ isWIBExport: IsNull(), mrpUID: Not(IsNull()) })
      .select('email')
      .orderBy('updatedAt', 'DESC')
      .limit(limit)
      .getRawMany();
    if (rows.length === 0) {
      return [];
    }
    return rows;
  }

  async sendEmailWibFile(body) {
    try {
      const { sentToEmail, fileName } = body;
      if (!sentToEmail || !fileName) return;
      const outputDir = path.join(process.cwd(), `/public/WITB/`);
      const files = fs.readdirSync(path.join(process.cwd(), `/public/WITB`));
      sgMail.setApiKey(this.config.get('app.sendGridApiKey'));
      if (!files || !files.length) {
        return { msg: 'Can not find folder.' };
      }
      const attachments = [];
      const pathToAttachment = `${outputDir}${fileName}.csv`;
      if (!fs.existsSync(pathToAttachment)) {
        return { msg: 'Can not find this file.' };
      }
      const attachment = await fs.readFileSync(pathToAttachment).toString('base64');
      attachments.push({
        content: attachment,
        filename: `${fileName}.csv`,
        type: 'application/csv',
        disposition: 'attachment',
      });

      const msg: any = {
        to: [sentToEmail],
        from: this.config.get('app.sendGridFrom'),
        subject: 'MyTM WITB File',
        text: 'MyTM WITB File',
        attachments: attachments,
      };
      return sgMail.send(msg);
    } catch (err) {
      await this.loggingService.save({
        event: 'ERR_WITB_SEND_EMAIL',
        data: {
          error: err,
        },
      });
      console.log('ERR_SEND_EMAIL', err);
      return { err: JSON.stringify(err) };
    }
  }

  async createUserNotifyAdmin(body: UserNotifyAdminDto) {
    return this.userNotifyAdminRepo.save({
      id: v4(),
      ...body,
    });
  }

  async testSendEmail(body: SendTestEmailDto) {
    try {
      const msg: any = {
        to: body?.email,
        from: this.config.get('app.sendGridFrom'),
        subject: body?.subject,
        text: body?.content,
      };

      return sgMail.send(msg);
    } catch (err) {
      this.logger.error('ERR_TEST_send_email:', err);
      await this.loggingService.save({
        event: 'ERR_TEST_send_email',
        data: {
          error: err,
        },
      });
      return err;
    }
  }

  async forceDeleteUser(data: ForceDeleteEmailsDto) {
    try {
      const emails = data?.emails;
      if (isEmpty(emails)) return;
      const errMsg = [];
      for (const email of emails) {
        const user: UserEntity = await this.userRepo.findOne({
          where: {
            email,
          },
        });
        if (!user) {
          errMsg.push({ email, success: false, msg: USER_MESSAGE_ERROR.USER_NOT_FOUND });
          continue;
        }
        const { mrpUID, auth0UID, id } = user;
        await this.userPermissionRepo.delete({ userId: id }).catch((e) => e);
        await this.userTourTrashRepo.delete({ userId: id }).catch((e) => e);
        await this.responseRepo.delete({ userId: id }).catch((e) => e);
        await this.userNotificationRepo.delete({ userId: id }).catch((e) => e);
        await this.userBlackListRepo.delete({ userId: id }).catch((e) => e);
        if (mrpUID) {
          await this.mrpService.forceDeleteUser(mrpUID).catch((e) => e);
        }
        await this.auth0Management
          .deleteUser({
            id: auth0UID,
          })
          .catch((e) => e);
        await this.userRepo.delete({ id });
        errMsg.push({ email, success: true });
      }
      return errMsg;
    } catch (err) {
      this.logger.error('ERR_DELETE_EMAIL:', err);
      return err;
    }
  }

  async userPushNotify(data) {
    const { email, link, title, body } = data;
    const payloadOrderDelivery = {
      title: title || 'header',
      body: body || 'body',
      link: link || 'https://www.fedex.com/wtrk/track/?trknbr=************',
    };
    await this.klaviyoService.track(email, KlaviyoTrackEvents.ORDER_PUSH_NOTIFICATION, payloadOrderDelivery);
    return true;
  }
}

import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class LaunchFeatureDto {
  @IsString()
  feature: string;

  @IsString()
  @IsOptional()
  description: string;

  @IsBoolean()
  enabled: boolean;

  @IsString()
  @IsOptional()
  countryCode: string;

  @IsBoolean()
  @IsOptional()
  isCountry: boolean;
}
export class DeleteFeatureDto {
  @IsString()
  @IsNotEmpty()
  featureId: string;
}

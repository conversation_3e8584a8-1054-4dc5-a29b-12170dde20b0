import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { TextService } from './text.service';
import { AddTextDto, UpdateTextDto } from './text.types';

@Controller('texts')
export class TextController {
  constructor(private textService: TextService, private readonly config: ConfigService) {
    this.config = config;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get()
  async findAll() {
    return this.textService.findAll();
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('save')
  async saveText(@Req() request: BaseRequest, @Body() data: AddTextDto) {
    return this.textService.saveText(request.user.uid, data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('update')
  async updateText(@Req() request: BaseRequest, @Body() data: UpdateTextDto) {
    return this.textService.updateText(request.user.uid, data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('/:textId')
  async deleteText(@Req() request: BaseRequest, @Param('textId') textId: string) {
    return await this.textService.deleteText(request.user.uid, textId);
  }
}

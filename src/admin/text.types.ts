import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class AddTextDto {
  @IsNotEmpty()
  @IsString()
  keyText: string;

  @IsNotEmpty()
  @IsString()
  value: string;

  @IsOptional()
  @IsString()
  createdBy: string;
}
export class UpdateTextDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsOptional()
  @IsString()
  keyText: string;

  @IsOptional()
  @IsString()
  value: string;

  @IsOptional()
  @IsString()
  updatedBy: string;

  @IsOptional()
  @IsString()
  deletedBy: string;

  @IsOptional()
  deletedAt: Date;

  @IsOptional()
  updatedAt: Date;

  @IsOptional()
  isDelete: boolean;
}

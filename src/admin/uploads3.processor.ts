import { Process, Processor } from '@nestjs/bull';
import * as AWS from 'aws-sdk';
import { Job } from 'bull';
import fs from 'fs';
import { ConfigService } from 'nestjs-config';
import path from 'path';

export type FilesUploadJob = Job<{ files: [] }>;

export enum UploadS3ProcessorQueueName {
  UPLOAD_S3 = 'upload-s3-processor-queue',
}

@Processor('upload-s3')
export class UploadS3Processor {
  s3: any;
  constructor(private configService: ConfigService) {
    this.s3 = new AWS.S3({
      accessKeyId: configService.get('s3.accessKeyId'),
      secretAccessKey: configService.get('s3.secretAccessKey'),
    });
  }
  @Process({
    name: UploadS3ProcessorQueueName.UPLOAD_S3,
    concurrency: 1,
  })
  async process(job: FilesUploadJob): Promise<any> {
    const files: any = job.data.files;
    const fileDir = path.join(process.cwd(), `/public/Salesforce/`);
    for (const file of files) {
      try {
        const fileName = `${file}.csv`;
        await this.uploadFileToS3(`${fileDir}${fileName}`, fileName);
      } catch (error) {
        console.error(error);
      }
    }
    return true;
  }
  async uploadFileToS3(filePath: string, fileName: string): Promise<boolean> {
    return new Promise((resolve) => {
      const data = fs.readFileSync(filePath);
      const params = {
        Bucket: this.configService.get('s3.bucket'),
        Key: `${this.configService.get('s3.prefixKey')}${fileName}`,
        Body: data,
      };
      this.s3.upload(params, (s3Err, data) => {
        if (s3Err) {
          console.error(`ERROR UPLOAD TO S3: ${s3Err.message}`);
          console.log(s3Err);
          throw s3Err;
        }
        console.log(`FILE UPLOADED SUCCESSFULLY AT ${data.Location}`);
        fs.unlinkSync(filePath);
        resolve(true);
      });
    });
  }
}

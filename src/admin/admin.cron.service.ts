import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Queue } from 'bull';
import { ConfigService } from 'nestjs-config';
import { isCronJobHandlersEnabled, isProduction } from '../utils/cron';
import { AdminUserPermissionProcessorQueueName } from './admin-sync-user-permission-cdm.processor';
import { AdminService } from './admin.service';
import { SalesforceCSVBuildProcessorQueueName } from './salesforce.processor';

@Injectable()
export class AdminCronService {
  private readonly logger = new Logger(AdminCronService.name);
  constructor(
    @InjectQueue('salesforce-csv') private salesforceCSVQueue: Queue,
    @InjectQueue('sync-user-sub-level-cdm') private syncUserPermissionToCdmQueue: Queue,
    private adminService: AdminService,
    private readonly config: ConfigService
  ) {}
  // @Cron(CronExpression.EVERY_HOUR)
  // async scanDailyUpdateProfileToSaleForce() {
  //   if (this.config.get('app.tag') !== 'HEALTH_CHECK') {
  //     return;
  //   }
  //   // eslint-disable-next-line @typescript-eslint/no-var-requires
  //   const momentTz = require('moment-timezone');
  //   const currentTime = momentTz().tz('America/Los_Angeles');
  //   const hour = currentTime.format('HH');
  //   if (hour != 0) {
  //     return;
  //   }
  //
  //   // check job waiting or running
  //   const jobsActive = await this.salesforceCSVQueue.getActive();
  //   const jobsWaiting = await this.salesforceCSVQueue.getWaiting();
  //   this.logger.debug(`START SCANNING DAILY UPDATE PROFILE TO SALE FORCE`);
  //   if (jobsActive?.length > 0 || jobsWaiting?.length > 0) {
  //     console.log(`HAVE JOB RUN UPLOAD...`);
  //     return;
  //   }
  //   const chunkRows: any = await this.adminService.getUserCollectDataForSalesforce(1000000);
  //   let count = 0;
  //   for (const chunkRow of chunkRows) {
  //     count++;
  //     await this.salesforceCSVQueue.add(SalesforceCSVBuildProcessorQueueName.SALESFORCE_CSV_BUILD, {
  //       emails: chunkRow.map((item) => item.email),
  //       suffixName: currentTime.format('YYYYMMDD'),
  //       shouldUpload: count === chunkRows.length,
  //     });
  //     console.log(`ADDED LIST ${JSON.stringify(chunkRow)} TO JOB BUILD CSV...`);
  //   }
  //   return {
  //     success: true,
  //   };
  // }

  // @Cron(isProduction ? CronExpression.EVERY_DAY_AT_9AM : CronExpression.EVERY_5_MINUTES)
  // async handleSyncUserPermissionToCDMCron() {
  //   try {
  //     if (!isCronJobHandlersEnabled(this.config)) {
  //       return;
  //     }
  //     this.logger.log('Job sync user permission to CDM run...');
  //     await this.syncUserPermissionToCdmQueue.add(AdminUserPermissionProcessorQueueName.SYNC_USER_SUB_LEVEL_TO_CDM);
  //   } catch (err) {
  //     console.error('ERR handleSyncUserPermissionToCDMCron', err);
  //     this.logger.error(err);
  //   }
  // }
}

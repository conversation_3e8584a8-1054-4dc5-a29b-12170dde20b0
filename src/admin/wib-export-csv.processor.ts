import { Process, Processor } from '@nestjs/bull';
import { CACHE_MANAGER, Inject } from '@nestjs/common';
import { Job } from 'bull';
import { Cache } from 'cache-manager';
import fs from 'fs';
import moment from 'moment';
import xlsx from 'node-xlsx';
import path from 'path';
import { LoggingService } from '../logging/logging.service';
import { AdminService } from './admin.service';

export type WIBCSVBuildJob = Job<{ emails: string[]; suffixName?: string }>;

export enum WIBCSVBuildProcessorQueueName {
  WIB_CSV_BUILD = 'wib-csv-build',
}

const SheetHeadingForWITB = [
  'Email',
  'Club',
  'Model',
  'Manufacturer',
  'ClubType',
  'Club Added Date',
  'Deleted',
  'Active',
];

const WIB_FILE_NAME_CSV = 'WIB_FILE_NAME_CSV';
// const LIMIT_ROWS = 100000;
const LIMIT_ROWS = 300;

@Processor('witb-export-csv')
export class WIBCSVBuildProcessor {
  constructor(
    private adminService: AdminService,
    private loggingService: LoggingService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  @Process({
    name: WIBCSVBuildProcessorQueueName.WIB_CSV_BUILD,
    concurrency: 1,
  })
  async process(job: WIBCSVBuildJob): Promise<any> {
    try {
      const outputDir = path.join(process.cwd(), `/public/WITB/`);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      const suffixName = job.data?.suffixName || moment().format('YYYYMMDD');
      const listFile = 'WITB';
      let buildFile = (await this.cacheManager.get(WIB_FILE_NAME_CSV)) || `${listFile}_${suffixName}_1`;

      // create file export if not exists
      const isCreateFileNotExists = this.checkCreateFileWIBNotExists(buildFile);
      if (isCreateFileNotExists) {
        buildFile = isCreateFileNotExists;
        await this.createDefaultCSVFile(buildFile, SheetHeadingForWITB);
      } else {
        const isCreateFileLimit = this.checkCreateFileLimit(buildFile);
        if (isCreateFileLimit) {
          buildFile = isCreateFileLimit;
          await this.createDefaultCSVFile(buildFile, SheetHeadingForWITB);
        }
      }

      const wibRows = await this.adminService.getUserWITBCollectDataForWIB(job.data.emails);
      if (wibRows && wibRows.length) {
        await this.appendRowToCSVFile(buildFile, wibRows);

        await this.adminService.updateIsWIBExportForUsers(job.data.emails, true);
      }

      return true;
    } catch (err) {
      await this.loggingService.save({
        event: 'ERR_WITB',
        data: {
          error: err,
        },
      });
      console.log('ERR_WITB', err);
      return;
    }
  }

  checkCreateFileWIBNotExists(buildFile) {
    const outputDir = path.join(process.cwd(), `/public/WITB/`);
    const fileName = `${outputDir}${buildFile}.csv`;
    if (!fs.existsSync(fileName)) {
      return buildFile;
    }
    return false;
  }

  checkCreateFileLimit(buildFile) {
    const csvPath = path.join(process.cwd(), `/public/WITB/${buildFile}.csv`);
    const workSheetsFromFile = xlsx.parse(csvPath, {
      raw: true,
    });
    const sheet = workSheetsFromFile[0].data;
    if (sheet && sheet.length < LIMIT_ROWS) {
      return false;
    }
    const nameFileArr = buildFile?.split('_');
    const numberFile = nameFileArr?.slice(-1).toString();
    const numberReplace = (Number(numberFile) + 1).toString();
    let nameFileNew = nameFileArr.slice(0, -1);
    nameFileNew.push(numberReplace);
    nameFileNew = nameFileNew.join('_');
    return nameFileNew;
  }

  async createDefaultCSVFile(name, heading) {
    const buffer: any = xlsx.build(
      [
        {
          name,
          data: [heading],
        },
      ],
      { bookType: 'csv' }
    );
    const csvPath = path.join(process.cwd(), `/public/WITB/${name}.csv`);
    await this.cacheManager.set(WIB_FILE_NAME_CSV, name, { ttl: 1000 * 60 * 60 * 24 * 5 });
    await fs.writeFileSync(csvPath, buffer);
  }

  async appendRowToCSVFile(nameFile, rows) {
    const csvPath = path.join(process.cwd(), `/public/WITB/${nameFile}.csv`);
    const workSheetsFromFile = xlsx.parse(csvPath, {
      raw: true,
    });
    const sheet = workSheetsFromFile[0].data;
    if (!sheet) {
      throw new Error('Error when parsing csv file!');
    }
    const buffer: any = xlsx.build(
      [
        {
          name: nameFile,
          data: [...sheet, ...rows],
        },
      ],
      { bookType: 'csv' }
    );
    await fs.writeFileSync(csvPath, buffer);
  }
}

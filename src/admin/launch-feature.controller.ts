import { Body, Controller, Delete, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { LaunchFeatureService } from './launch-feature.service';
import { DeleteFeatureDto, LaunchFeatureDto } from './launch-feature.type';

@Controller('launch-features')
export class LaunchFeatureController {
  constructor(private launchFeatureService: LaunchFeatureService, private readonly config: ConfigService) {
    this.config = config;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get()
  async findAll(@Query('country') country?: string) {
    return this.launchFeatureService.findLaunchFeature(country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('country-features')
  findAllCountryFeature(@Query('country') country?: string) {
    return this.launchFeatureService.findAllCountryFeature(country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('paid-features')
  findAllPaidFeature(@Query('country') country?: string) {
    return this.launchFeatureService.findAllPaidFeature(country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('save')
  async saveLaunchFeature(@Req() request: BaseRequest, @Body() data: LaunchFeatureDto) {
    return this.launchFeatureService.saveLaunchFeature(request.user.uid, data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('save-paid-feature')
  savePaidFeature(@Req() request: BaseRequest, @Body() data: LaunchFeatureDto) {
    return this.launchFeatureService.savePaidFeature(request.user.uid, data);
  }
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('remove')
  removeFeature(@Req() request: BaseRequest, @Body() data: DeleteFeatureDto) {
    return this.launchFeatureService.removeFeature(data.featureId, request.user.uid);
  }
}

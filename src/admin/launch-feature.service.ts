import { BadRequestException, CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Cache } from 'cache-manager';
import { isEmpty } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, Repository, getManager } from 'typeorm';
import { v4 } from 'uuid';
import { isAllCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { ProductOrderEntity } from '../member-shop/entities/product-order.entity';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { COUNTRY_STATUS } from './country.types';
import { CountryEntity } from './entities/country.entity';
import { FeatureCountryEntity } from './entities/feature-country.entity';
import { LaunchFeatureEntity } from './entities/launch-feature.entity';
import { FeatureCountryTypes } from './feature.types';
import { LaunchFeatureDto } from './launch-feature.type';

const LAUNCH_FEATURE_CACHE = 'LAUNCH_FEATURE_CACHE';
const REGION_FEATURE_CACHE = 'FEATURE_REGRION_CACHE';
const PAID_FEATURE_CACHE = 'PAID_FEATURE_CACHE';

@Injectable()
export class LaunchFeatureService {
  constructor(
    private readonly config: ConfigService,
    private readonly apiVersionsService: ApiVersionsService,
    @InjectRepository(LaunchFeatureEntity)
    private readonly launchFeatureEntityRepository: Repository<LaunchFeatureEntity>,
    @InjectRepository(FeatureCountryEntity)
    private readonly featureCountry: Repository<FeatureCountryEntity>,
    @InjectRepository(CountryEntity)
    private readonly countryEntity: Repository<CountryEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {
    this.config = config;
  }

  async findLaunchFeature(country?: string) {
    if (country) {
      const features = await this.launchFeatureEntityRepository.find({
        where: `featureType IS NULL OR featureType = '${FeatureCountryTypes.LAUNCH_FEATURE}'`,
      });
      if (isAllCountry(country)) {
        return await this.getFeaturesForAllCountryByFeatureType(features, FeatureCountryTypes.LAUNCH_FEATURE);
      }
      return await this.mapFeatureCountryWithCountryCode(country, features, FeatureCountryTypes.LAUNCH_FEATURE, false);
    } else {
      return this.launchFeatureEntityRepository.find({
        where: `featureType IS NULL OR featureType = '${FeatureCountryTypes.LAUNCH_FEATURE}'`,
      });
    }
  }
  async findAllPaidFeature(country?: string) {
    if (country) {
      const features = await this.launchFeatureEntityRepository.find({
        where: {
          featureType: FeatureCountryTypes.PAID_FEATURE,
        },
      });
      if (isAllCountry(country)) {
        return await this.getFeaturesForAllCountryByFeatureType(features, FeatureCountryTypes.PAID_FEATURE);
      }
      return await this.mapFeatureCountryWithCountryCode(country, features, FeatureCountryTypes.PAID_FEATURE, false);
    } else {
      return this.launchFeatureEntityRepository.find({
        where: `featureType IS NULL OR featureType = '${FeatureCountryTypes.PAID_FEATURE}'`,
      });
    }
  }

  async findAllCountryFeature(country?: string) {
    if (country) {
      const features = await this.launchFeatureEntityRepository.find({
        where: {
          featureType: FeatureCountryTypes.REGION_FEATURE,
        },
      });
      if (isAllCountry(country)) {
        return await this.getFeaturesForAllCountryByFeatureType(features, FeatureCountryTypes.REGION_FEATURE);
      }
      return await this.mapFeatureCountryWithCountryCode(country, features, FeatureCountryTypes.REGION_FEATURE, false);
    } else {
      return this.launchFeatureEntityRepository.find({
        where: {
          featureType: FeatureCountryTypes.REGION_FEATURE,
        },
      });
    }
  }

  async getFeaturesForAllCountryByFeatureType(originFeatures: any, featureType: string): Promise<any> {
    const counties = await this.countryEntity.find({ where: { status: COUNTRY_STATUS.ACTIVE } });
    const lstReturns = {};
    await Promise.all(
      counties.map(async (ct) => {
        const featuresByCountry = await this.mapFeatureCountryWithCountryCode(
          ct.code,
          originFeatures,
          featureType,
          false
        );
        lstReturns[ct.code] = featuresByCountry;
      })
    );
    return lstReturns;
    // return lstFeatures;
  }
  async saveLaunchFeature(userId, payload: LaunchFeatureDto) {
    const isCountry = payload?.isCountry === true ? payload.isCountry : false;
    const featureType = isCountry ? FeatureCountryTypes.REGION_FEATURE : FeatureCountryTypes.LAUNCH_FEATURE;
    const feature = await this.launchFeatureEntityRepository.findOne({
      feature: payload.feature,
      featureType: featureType,
    });

    delete payload.isCountry;
    const keyCache = isCountry
      ? this.getKeyCacheCountryFeature(payload.countryCode)
      : this.getKeyCacheLaunchFeature(payload.countryCode);
    await this.cacheManager.del(keyCache);
    const countryCode = payload.countryCode || null;
    if (feature) {
      return this.updateFeature(payload, feature, featureType, countryCode, userId);
    }
    return this.createFeature(userId, featureType, countryCode, payload);
  }

  async createFeature(userId, featureType, countryCode, payload) {
    await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const feature = new LaunchFeatureEntity();
      feature.id = v4();
      feature.feature = payload.feature;
      feature.description = payload.description;
      feature.enabled = payload.enabled;
      feature.createdBy = userId;
      feature.featureType = featureType;
      await transactionalEntityManager.save(LaunchFeatureEntity, feature);
      await this.addUpdateFeatureCountry(feature, countryCode, featureType);
      await CommonSubscriber.activityLogCommandInsert(
        {
          entity: {
            uuid: feature.id,
            createdBy: userId,
            feature: feature.feature,
          },
          manager: transactionalEntityManager,
        },
        this.getNameFeatureLogs(featureType)
      );
      return true;
    });
    await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.COUNTRY_FEATURES, 'USA', userId);
    return this.launchFeatureEntityRepository.findOne({ feature: payload.feature, featureType });
  }

  async updateFeature(payload, feature, featureType, countryCode, userId) {
    return await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      delete payload.countryCode;
      await transactionalEntityManager.update(
        LaunchFeatureEntity,
        { id: feature.id },
        {
          ...payload,
          updatedBy: userId,
          featureType,
        }
      );
      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: feature.id,
            updatedBy: userId,
          },
          manager: transactionalEntityManager,
        },
        this.getNameFeatureLogs(featureType),
        {
          ...feature,
          enabled: !payload.enabled,
          countryCode,
        },
        {
          ...payload,
          updatedBy: userId,
          featureType,
          countryCode,
          enabled: payload.enabled,
        }
      );
      feature.enabled = payload.enabled;
      await this.addUpdateFeatureCountry(feature, countryCode, featureType);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.COUNTRY_FEATURES, 'USA', userId);
      return {
        ...feature,
        ...payload,
      };
    });
  }

  async savePaidFeature(userId, payload: LaunchFeatureDto) {
    const featureType = FeatureCountryTypes.PAID_FEATURE;
    const feature = await this.launchFeatureEntityRepository.findOne({
      feature: payload.feature,
      featureType: featureType,
    });
    delete payload.isCountry;
    const keyCache = this.getKeyCachePaidFeature(payload.countryCode);
    await this.cacheManager.del(keyCache);
    if (!payload.countryCode) {
      throw new BadRequestException({
        errorMessage: 'countryCode is required',
      });
    }
    const countryCode = payload.countryCode || null;
    if (feature) {
      return this.updateFeature(payload, feature, featureType, countryCode, userId);
    }
    return this.createFeature(userId, featureType, countryCode, payload);
  }

  async addUpdateFeatureCountry(feature: LaunchFeatureEntity, countryCode: string, featureType: string) {
    if (!countryCode) {
      return;
    }
    countryCode = countryCode.toUpperCase().trim();
    const isExistedCountry = await this.countryEntity.count({
      where: [{ code: countryCode }, { isoCode: countryCode }],
    });
    if (!isExistedCountry) {
      console.error(`COUNTRY CODE : ${countryCode} IS INVALID`);
      return;
    }
    const ftCountry = await this.featureCountry.findOne({
      launchFeatureId: feature.id,
      countryCode,
      featureType,
    });
    if (ftCountry) {
      ftCountry.enabled = feature.enabled;
      return await this.featureCountry.save(ftCountry);
    } else {
      const newFeatureCountry = new FeatureCountryEntity();
      newFeatureCountry.countryCode = countryCode;
      newFeatureCountry.launchFeatureId = feature.id;
      newFeatureCountry.featureType = featureType;
      newFeatureCountry.enabled = feature.enabled;
      return this.featureCountry.save(newFeatureCountry);
    }
  }

  async mappingLaunchFeatures(countryCode?: string) {
    const keyCache = this.getKeyCacheLaunchFeature(countryCode);
    const result = null; // await this.cacheManager.get(keyCache);

    if (!result) {
      const features = await this.launchFeatureEntityRepository.find({
        where: `featureType IS NULL OR featureType = '${FeatureCountryTypes.LAUNCH_FEATURE}'`,
      });
      let map = {};
      if (countryCode) {
        map = await this.mapFeatureCountryWithCountryCode(countryCode, features, FeatureCountryTypes.LAUNCH_FEATURE);
      } else {
        features.map((feature) => {
          map[feature.feature] = feature.enabled;
        });
      }

      await this.cacheManager.set(keyCache, map, { ttl: 0 });
      return map;
    }

    return result;
  }

  async mappingCountryFeatures(countryCode?: string) {
    const keyCache = this.getKeyCacheCountryFeature(countryCode);
    // const result = await this.cacheManager.get(keyCache);
    const result = null;

    if (!result) {
      const features = await this.launchFeatureEntityRepository.find({
        where: {
          featureType: FeatureCountryTypes.REGION_FEATURE,
        },
      });
      let map = {};
      if (countryCode) {
        map = await this.mapFeatureCountryWithCountryCode(countryCode, features, FeatureCountryTypes.REGION_FEATURE);
      } else {
        features.map((feature) => {
          map[feature.feature] = feature.enabled;
        });
      }

      await this.cacheManager.set(keyCache, map, { ttl: 0 });
      return map;
    }

    return result;
  }

  async mappingPaidFeatures(countryCode?: string) {
    const keyCache = this.getKeyCachePaidFeature(countryCode);
    const result = null;
    // const result = await this.cacheManager.get(keyCache);

    if (!result) {
      const features = await this.launchFeatureEntityRepository.find({
        where: {
          featureType: FeatureCountryTypes.PAID_FEATURE,
        },
      });
      let map = {};
      if (countryCode) {
        map = await this.mapFeatureCountryWithCountryCode(countryCode, features, FeatureCountryTypes.PAID_FEATURE);
      } else {
        features.map((feature) => {
          map[feature.feature] = feature.enabled;
        });
      }

      await this.cacheManager.set(keyCache, map, { ttl: 0 });
      return map;
    }

    return result;
  }

  private getKeyCacheLaunchFeature(countryCode: any) {
    return countryCode ? `${LAUNCH_FEATURE_CACHE}_${countryCode?.trim()?.toUpperCase()}` : LAUNCH_FEATURE_CACHE;
  }
  private getKeyCachePaidFeature(countryCode: any) {
    return countryCode ? `${PAID_FEATURE_CACHE}_${countryCode?.trim()?.toUpperCase()}` : PAID_FEATURE_CACHE;
  }
  private getKeyCacheCountryFeature(countryCode: any) {
    return countryCode ? `${REGION_FEATURE_CACHE}_${countryCode?.trim()?.toUpperCase()}` : REGION_FEATURE_CACHE;
  }

  private async mapFeatureCountryWithCountryCode(
    countryCode: string,
    features: LaunchFeatureEntity[],
    featureType = FeatureCountryTypes.LAUNCH_FEATURE,
    isReturnMap = true
  ) {
    if (!features || isEmpty(features)) {
      return {};
    }
    let isoCode = countryCode;
    if (isoCode.length > 2) {
      isoCode = isoCode.slice(0, isoCode.length - 1);
    }
    const queryBuilder = this.featureCountry.createQueryBuilder('featureCountry');
    queryBuilder.where({ featureType });
    queryBuilder.andWhere([{ countryCode: countryCode?.toUpperCase() }, { countryCode: isoCode.toUpperCase() }]);
    const featureCountries = await queryBuilder.getMany();
    let mapFeatures = [...features];
    const mapFt = {};
    if (featureCountries) {
      mapFeatures = mapFeatures.map((origin) => {
        const ftCountry = featureCountries.find((f) => f.launchFeatureId === origin.id);
        if (ftCountry) {
          origin.enabled = ftCountry.enabled;
        }
        return { ...origin };
      });
    }
    if (!isReturnMap) {
      return mapFeatures;
    }
    mapFeatures.map((feature) => {
      mapFt[feature.feature] = feature.enabled;
    });
    return mapFt;
  }

  async removeFeature(featureId: string, userId: string) {
    const feature = await this.launchFeatureEntityRepository.findOne(featureId);
    if (feature) {
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        await transactionalEntityManager.update(
          LaunchFeatureEntity,
          { id: featureId },
          { deletedAt: new Date(), deletedBy: userId }
        );
        await this.featureCountry.delete({ launchFeatureId: featureId });
        await CommonSubscriber.activityLogCommandDelete(
          {
            entity: {
              uuid: featureId,
              deletedBy: userId,
            },
            manager: transactionalEntityManager,
            databaseEntity: feature,
          },
          this.getNameFeatureLogs(feature?.featureType)
        );
      });
      return { success: true };
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.FEATURE_NOT_FOUND,
        errorMessage: `Feature ${featureId} not found`,
      });
    }
  }

  getNameFeatureLogs(type) {
    let name = null;
    switch (type) {
      case FeatureCountryTypes.LAUNCH_FEATURE:
        name = 'Launch Features';
        break;
      case FeatureCountryTypes.REGION_FEATURE:
        name = 'Show/Hide Features';
        break;
      case FeatureCountryTypes.PAID_FEATURE:
        name = 'Paid Features';
        break;
    }
    return name;
  }
}

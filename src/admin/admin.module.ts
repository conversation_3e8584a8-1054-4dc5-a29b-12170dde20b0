import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserPermissionCronService } from 'src/cdm/cdm.cron.service';
import { CmsService } from 'src/cms/cms.service';
import { ContentTagThumbnailEntity } from 'src/content/entities/content-tag-thumbnail.entity';
import { AdminForceSyncKlaviyoCacheService } from 'src/klaviyo/klaviyo.cache.service';
import { KlaviyoCronService } from 'src/klaviyo/klaviyo.cron.service';
import { SharedModule } from 'src/shared/shared.module';
import { TranslationService } from 'src/translation/translation.service';
import { TTBPushEntity } from 'src/ttb/entities/ttb-push.entity';
import { TTBEntity } from 'src/ttb/entities/ttb.entity';
import { TTBService } from 'src/ttb/ttb.service';
import { SYSTEM_TAG } from 'src/utils/constants';
import { DeService } from '../content/de.service';
import { SwingIndexService } from '../si/si.service';
import { AdminUserPermissionProcessor } from './admin-sync-user-permission-cdm.processor';
import { AdminSyncingProcessor } from './admin-syncing.processor';
import { AdminYoutubeThumbnailsProcessor } from './admin-youtube-thumbnails.processor';
import { AdminForceSyncCDMCacheService } from './admin.cache.service';
import { AdminController } from './admin.controller';
import { AdminCronService } from './admin.cron.service';
import { AdminProcessor } from './admin.processor';
import { AdminService } from './admin.service';
import { CountrySyncingProcessor } from './country-syncing.processor';
import { ImageEntity } from './entities/image.entity';
import { ImageController } from './image.controller';
import { ImageService } from './image.service';
import { LaunchFeatureController } from './launch-feature.controller';
import { LaunchFeatureService } from './launch-feature.service';
import { SalesforceCSVBuildProcessor } from './salesforce.processor';
import { TextController } from './text.controller';
import { TextService } from './text.service';
import { UploadS3Processor } from './uploads3.processor';
import { WIBCSVBuildProcessor } from './wib-export-csv.processor';

let processors = [];
if (process.env.TAG !== SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, AdminYoutubeThumbnailsProcessor, AdminProcessor, AdminUserPermissionProcessor];
}

if (process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [...processors, AdminSyncingProcessor, CountrySyncingProcessor];
}

if (process.env.TAG === SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, SalesforceCSVBuildProcessor, UploadS3Processor, WIBCSVBuildProcessor];
}
if (process.env.STAGE === 'development') {
  processors = [...processors, WIBCSVBuildProcessor];
}

@Module({
  imports: [
    SharedModule,
    BullModule.registerQueue({
      name: 'admin-youtube-thumbnails',
    }),
    BullModule.registerQueue({
      name: 'admin-syncing',
    }),
    BullModule.registerQueue({
      name: 'update-subscription-service',
    }),
    BullModule.registerQueue({
      name: 'salesforce-csv',
      defaultJobOptions: {
        removeOnComplete: true,
      },
      settings: {
        maxStalledCount: 0,
      },
    }),
    BullModule.registerQueue({
      name: 'sync-user-sub-level-cdm',
      defaultJobOptions: {
        removeOnComplete: false,
        removeOnFail: false,
        attempts: 10,
        backoff: 30000,
      },
    }),
    BullModule.registerQueue({
      name: 'witb-export-csv',
      defaultJobOptions: {
        removeOnComplete: false,
        removeOnFail: false,
        attempts: 3,
        backoff: 30000,
      },
    }),
    TypeOrmModule.forFeature([ContentTagThumbnailEntity, TTBEntity, TTBPushEntity]),
  ],
  controllers: [AdminController, LaunchFeatureController, ImageController, TextController],
  providers: [
    ...processors,
    AdminService,
    AdminCronService,
    LaunchFeatureService,
    CmsService,
    TranslationService,
    TTBService,
    AdminForceSyncCDMCacheService,
    UserPermissionCronService,
    KlaviyoCronService,
    AdminForceSyncKlaviyoCacheService,
    SwingIndexService,
    DeService,
    ImageService,
    TextService,
  ],
})
export class AdminModule {}

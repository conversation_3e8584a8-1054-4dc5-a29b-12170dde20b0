import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { ImageService } from './image.service';
import { AddImageDto, IMAGE_TYPE, UpdateImageDto } from './image.types';

@Controller('images')
export class ImageController {
  constructor(private imageService: ImageService, private readonly config: ConfigService) {
    this.config = config;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get()
  async findAll() {
    return this.imageService.findAll();
  }
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('play-bg')
  async findAllPlayBackgroundImage() {
    return this.imageService.findAll(IMAGE_TYPE.PLAY_BACKGROUND);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('save')
  async saveImage(@Req() request: BaseRequest, @Body() data: AddImageDto) {
    data.type = IMAGE_TYPE.NORMAL;
    return this.imageService.saveImage(request.user.uid, data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('play-bg-save')
  async saveImagePlayBackground(@Req() request: BaseRequest, @Body() data: AddImageDto) {
    data.type = IMAGE_TYPE.PLAY_BACKGROUND;
    return this.imageService.saveImage(request.user.uid, data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('update')
  async updateImage(@Req() request: BaseRequest, @Body() data: UpdateImageDto) {
    return this.imageService.updateImage(request.user.uid, data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('/:imageId')
  async deleteImage(@Req() request: BaseRequest, @Param('imageId') imageId: string) {
    return await this.imageService.deleteImage(request.user.uid, imageId);
  }
}

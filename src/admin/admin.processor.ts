import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { CdmService } from 'src/cdm/cdm.service';
import { SubscriptionServiceJob } from './admin.type';

export enum AdminProcessorQueueName {
  UPDATE_SUBSCRIPTION_SERVICE = 'process_update_subs_service',
}

@Processor('update-subscription-service')
export class AdminProcessor {
  private readonly logger = new Logger(AdminProcessor.name);
  constructor(private readonly cdmService: CdmService) {}

  @Process(AdminProcessorQueueName.UPDATE_SUBSCRIPTION_SERVICE)
  async update(job: SubscriptionServiceJob): Promise<any> {
    this.logger.log(`UpdateSubscriptionService processing for : ${job.data.users.length} users`);
    return this.cdmService.handleUpdateSubscriptionService(job.data.users);
  }
}

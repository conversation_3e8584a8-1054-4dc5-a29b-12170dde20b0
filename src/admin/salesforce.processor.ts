import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import fs from 'fs';
import moment from 'moment';
import xlsx from 'node-xlsx';
import path from 'path';
import { AdminService } from './admin.service';

export type SalesforceCSVBuildJob = Job<{ emails: string[]; suffixName?: string; shouldUpload?: boolean }>;

export enum SalesforceCSVBuildProcessorQueueName {
  SALESFORCE_CSV_BUILD = 'salesforce-csv-build',
}

const SheetHeadingForRounds = ['Email', 'Where Rounds Played', 'When Rounds Played', 'Total Score'];
const SheetHeadingForWITB = [
  'Email',
  'Club',
  'Model',
  'Manufacturer',
  'ClubType',
  'Club Added Date',
  'Deleted',
  'Active',
];
const SheetHeadingForProfiles = [
  'Email',
  'Gender',
  'Age (DOB)',
  'Handed',
  'Handicap',
  'GHIN',
  'Location / Home Course',
  'Opt-In for Push Notifications and Email',
  'Tier',
  'Average Score',
  'Favorite TM Player',
  'Number of Swings Uploaded',
  'Number of Putting Swings Uploaded',
  'Number of Approach Swings Uploaded',
  'Number of Driving Swings Uploaded',
  'Coach',
  'SG Total',
  'SG Driving',
  'SG Approach',
  'SG Short',
  'SG Putting',
  'FIRS',
  'GIRS',
  '% Sand Saves',
  'Putts',
  'Insights - Key Categories',
  'eCom ID',
  'Country',
  'Created Date',
  'Modified Date',
];

@Processor('salesforce-csv')
export class SalesforceCSVBuildProcessor {
  constructor(private adminService: AdminService) {}

  @Process({
    name: SalesforceCSVBuildProcessorQueueName.SALESFORCE_CSV_BUILD,
    concurrency: 1,
  })
  async process(job: SalesforceCSVBuildJob): Promise<any> {
    const outputDir = path.join(process.cwd(), `/public/Salesforce/`);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      // await this.createDefaultCSVFile('SalesforceRounds', SheetHeadingForRounds);
      // await this.createDefaultCSVFile('SalesforceWITB', SheetHeadingForWITB);
      // await this.createDefaultCSVFile('SalesforceProfiles', SheetHeadingForProfiles);
    }
    const suffixName = job.data?.suffixName || moment().format('YYYYMMDD');
    const listFiles = ['SalesforceRounds', 'SalesforceWITB', 'SalesforceProfiles'];
    const buildFiles = listFiles.map((file) => `${file}_${suffixName}`);

    const sheetHeadings = [SheetHeadingForRounds, SheetHeadingForWITB, SheetHeadingForProfiles];
    // create file export if not exists
    if (!fs.existsSync(`${outputDir}${buildFiles[0]}.csv`)) {
      // tslint:disable-next-line: forin
      for (const idxFile in buildFiles) {
        this.createDefaultCSVFile(buildFiles[idxFile], sheetHeadings[idxFile]);
      }
    }

    const { rows, profileEmails } = await this.adminService.getUserProfileCollectDataForSalesforce(job.data.emails);
    const witbRows = await this.adminService.getUserWITBCollectDataForSalesforce(profileEmails);

    const roundRows = await this.adminService.getUserRoundsCollectDataForSalesforce(profileEmails);

    const fileProfile = buildFiles[2];
    const fileWitb = buildFiles[1];
    const fileRow = buildFiles[0];

    await this.appendRowToCSVFile(fileRow, roundRows);
    await this.appendRowToCSVFile(fileWitb, witbRows);
    await this.appendRowToCSVFile(fileProfile, rows);

    if (job.data.shouldUpload) {
      await this.adminService.uploadFileToS3(buildFiles);
    }

    await this.adminService.updateShouldCollectSalesforceDataForUsers(profileEmails, false);
    return true;
  }

  createDefaultCSVFile(name, heading) {
    const buffer: any = xlsx.build(
      [
        {
          name,
          data: [heading],
        },
      ],
      { bookType: 'csv' }
    );
    const csvPath = path.join(process.cwd(), `/public/Salesforce/${name}.csv`);
    fs.writeFileSync(csvPath, buffer);
  }

  appendRowToCSVFile(name, rows) {
    const csvPath = path.join(process.cwd(), `/public/Salesforce/${name}.csv`);
    const workSheetsFromFile = xlsx.parse(csvPath, {
      raw: true,
    });
    const sheet = workSheetsFromFile[0].data;
    if (!sheet) {
      throw new Error('Error when parsing csv file!');
    }
    const buffer: any = xlsx.build(
      [
        {
          name,
          data: [...sheet, ...rows],
        },
      ],
      { bookType: 'csv' }
    );
    fs.writeFileSync(csvPath, buffer);
  }
}

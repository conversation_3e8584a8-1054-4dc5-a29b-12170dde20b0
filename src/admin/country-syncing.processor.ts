import { Process, Processor } from '@nestjs/bull';
import { AdminService } from './admin.service';

export enum CountrySyncingProcessorQueueName {
  SYNC_COUNTRY = 'country-sync-from-oc',
}

@Processor('country-syncing')
export class CountrySyncingProcessor {
  constructor(private adminService: AdminService) {}

  @Process({
    name: CountrySyncingProcessorQueueName.SYNC_COUNTRY,
  })
  async process(): Promise<any> {
    await this.adminService.syncCountriesFromOC();
    return true;
  }
}

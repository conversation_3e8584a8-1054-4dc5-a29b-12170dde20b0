import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { AdminService } from './admin.service';

export enum AdminUserPermissionProcessorQueueName {
  SYNC_USER_SUB_LEVEL_TO_CDM = 'process_sync_user_sub_level_to_cdm',
}

@Processor('sync-user-sub-level-cdm')
export class AdminUserPermissionProcessor {
  private readonly logger = new Logger(AdminUserPermissionProcessor.name);
  constructor(private readonly adminService: AdminService) {}

  @Process(AdminUserPermissionProcessorQueueName.SYNC_USER_SUB_LEVEL_TO_CDM)
  async syncUserPermissionToCdm(): Promise<any> {
    this.logger.log(`process_sync_user_sub_level_to_cdm running ....`);
    const results = await this.adminService.syncUpdatePermission({ limit: 30 });
    const decodeResult = (results && results.length && JSON.stringify(results)) || [];
    this.logger.log(`process_sync_user_sub_level_to_cdm Result: ${decodeResult}`);
    return results;
  }
}

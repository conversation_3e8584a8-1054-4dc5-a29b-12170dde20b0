import { IsNotEmpty, <PERSON>N<PERSON>ber, IsN<PERSON>berString, IsOptional, IsString } from 'class-validator';

export const COUNTRY_STATUS = {
  ACTIVE: 'ACTIVE',
  IN_ACTIVE: 'IN_ACTIVE',
};
export const COUNTRY_ERRORS_CODE = {
  NOT_FOUND: 'NOT_FOUND',
};
export const COUNTRY_ERRORS_MESSAGE = {
  NOT_FOUND: 'Country not found',
};
export class UpdateCountryDto {
  @IsOptional()
  @IsString()
  cdmRegionId?: string;

  @IsNotEmpty()
  @IsString()
  code: string;

  @IsOptional()
  @IsString()
  isoCode?: string;

  @IsOptional()
  @IsString()
  dialCode?: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  status?: string;

  @IsOptional()
  @IsNumber()
  sort?: number;
}

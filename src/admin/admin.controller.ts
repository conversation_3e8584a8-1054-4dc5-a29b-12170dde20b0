import { InjectQueue } from '@nestjs/bull';
import {
  BadRequestException,
  Body,
  CACHE_MANAGER,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import * as fs from 'fs';
import _ from 'lodash';
import momentTimeZone from 'moment-timezone';
import { ConfigService } from 'nestjs-config';
import path from 'path';
import { IsNull, Repository } from 'typeorm';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from 'src/auth/entities/user.entity';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { CdmService } from 'src/cdm/cdm.service';
import { AccessedClients } from 'src/client/clients.decorator';
import { CmsService } from 'src/cms/cms.service';
import { EcomService } from 'src/ecom/ecom.service';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { NotificationService } from 'src/notification/notification.service';
import { BaseRequest } from 'src/types/core';
import { ERROR_CODES } from 'src/utils/errors';
import { AdminSyncingProcessorQueueName } from './admin-syncing.processor';
import { AdminYoutubeThumbnailsProcessorQueueName } from './admin-youtube-thumbnails.processor';
import { AdminService } from './admin.service';
import {
  DeleteHomeWidgetDto,
  ExternalToolPayload,
  ForceDeleteEmailsDto,
  GetTokenUserDto,
  HomeWidgetDto,
  HomeWidgetGroupKey,
  NotificationDto,
  OrderIdsDto,
  SendTestEmailDto,
  SortOrderHomeWidgetDto,
  SyncKlaviyoTrackDto,
  SyncUsersToCDMConsumersDto,
  TagThumbnailDto,
  UpdateMemberShopOrdersDto,
  UserNotifyAdminDto,
  UserPermissionDto,
} from './admin.type';
import { CountrySyncingProcessorQueueName } from './country-syncing.processor';
import { UpdateCountryDto } from './country.types';
import { SalesforceCSVBuildProcessorQueueName } from './salesforce.processor';
import { WIBCSVBuildProcessorQueueName } from './wib-export-csv.processor';

@Controller('admin')
export class AdminController {
  constructor(
    private adminService: AdminService,
    private cmsService: CmsService,
    private cdmService: CdmService,
    private ecomService: EcomService,
    private readonly config: ConfigService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private readonly notificationService: NotificationService,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectQueue('admin-youtube-thumbnails') private adminYoutubeThumbnailsQueue: Queue,
    @InjectQueue('salesforce-csv') private salesforceCSVQueue: Queue,
    @InjectQueue('witb-export-csv') private wibCSVQueue: Queue,
    @InjectQueue('admin-syncing') private adminSyncingQueue: Queue,
    @InjectQueue('country-syncing') private countrySyncingQueue: Queue
  ) {
    this.config = config;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('home-widgets/update/:homeWidgetId')
  async postUpdateHomeWidget(@Req() request: BaseRequest, @Param() params, @Body() data: HomeWidgetDto) {
    if (
      data.contentId &&
      data.contentType !== 'drill' &&
      !(await this.cmsService.isValidContent(data.contentId, data.contentFormat))
    ) {
      AdminController.throwContentInvalidError();
    }
    if (data.productId && !(await this.ecomService.getProduct(data.productId, data?.countries))) {
      AdminController.throwProductInvalidError();
    }
    const result = await this.adminService.postUpdateHomeWidget(params.homeWidgetId, data, request.user.uid);

    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('home-widgets/create')
  async postCreateHomeWidget(@Req() request: BaseRequest, @Body() data: HomeWidgetDto) {
    if (data.contentId && !(await this.cmsService.isValidContent(data.contentId, data.contentFormat))) {
      AdminController.throwContentInvalidError();
    }
    if (data.productId && !(await this.ecomService.getProduct(data.productId, data.countries))) {
      AdminController.throwProductInvalidError();
    }
    return this.adminService.postCreateHomeWidget(data, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('home-widgets')
  async getHomeWidgets(@Query('groupKey') groupKey: HomeWidgetGroupKey, @Query('country') country?: string) {
    return this.adminService.getHomeWidgets(groupKey, country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('home-widgets/sort')
  async postUpdateSortOrderHomeWidget(@Req() request: BaseRequest, @Body() payload: SortOrderHomeWidgetDto) {
    return this.adminService.postUpdateSortOrderHomeWidget(payload, request.user.uid, payload.groupKey);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('home-widgets/delete')
  async postDeleteHomeWidget(@Req() request: BaseRequest, @Body() payload: DeleteHomeWidgetDto) {
    const result = await this.adminService.postDeleteHomeWidget(payload, request.user.uid);

    if (result.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('clients')
  async getClients() {
    return this.adminService.getClients();
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('content/tag/thumbnail')
  async postUpdateTagThumbnail(@Body() data: TagThumbnailDto) {
    return this.adminService.postUpdateTagThumbnail(data);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('translations/sync')
  async postSyncTranslations(@Query('onlyLanguage') onlyLanguage: number) {
    const results = await this.adminService.postSyncTranslations(onlyLanguage);
    return {
      results,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('cdm/sync')
  async syncCdmConsumers(@Body() body: SyncUsersToCDMConsumersDto) {
    return this.adminService.syncCdmConsumers(body.emails);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('ecom/sync/customers')
  async syncEComCustomers() {
    const rows = await this.userRepo
      .createQueryBuilder()
      .where({ onboardingComplete: true, ecomUID: IsNull() })
      .select('email')
      .distinct(true)
      .orderBy('email')
      .getRawMany();
    if (rows.length === 0) {
      return true;
    }
    const chunkRows = _.chunk(rows, 50);
    for (const chunkRow of chunkRows) {
      await this.adminSyncingQueue.add(AdminSyncingProcessorQueueName.SYNC_ECOM_CUSTOMERS, {
        emails: chunkRow.map((item) => item.email),
      });
    }
    return true;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('users/me')
  async cleanUsers(@Req() request: BaseRequest, @Query() query) {
    if (this.config.get('app.cleanUpUsersSecretKey') !== request.headers['secret']) {
      throw new ForbiddenException('Permission denied!');
    }
    return await this.cdmService.getConsumer(
      query.email,
      this.config.get('app.defaultRegionId'),
      query.fromSwingIndex == 1
    );
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('handicap/sync')
  async syncUserInputHandicap(@Body() body: SyncUsersToCDMConsumersDto) {
    return this.adminService.syncUserInputHandicap(body.emails);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('ttb/shipments-scanning')
  async ttbShipmentsScanning(@Body() body: any) {
    return this.adminService.ttbShipmentsScanning(body.dates || []);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('ttb/upload-gvc-shipped-files')
  async ttbUploadGVCShippedFiles(@Body() body: any) {
    if (!body.filenames || body.filenames.length === 0) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.INVALID_PAYLOAD,
        errorMessage: 'Please provide shipment file names!',
      });
    }
    return this.adminService.ttbUploadGVCShippedFiles(body.filenames || []);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('youtube/thumbnails')
  async postHandleYoutubeThumbnails(@Body() data) {
    const [error, status] = await this.adminService.postHandleYoutubeThumbnails(data.spreadsheetId);
    if (!error && status) {
      await this.adminYoutubeThumbnailsQueue.add(AdminYoutubeThumbnailsProcessorQueueName.CHECK_YOUTUBE_THUMBNAILS, {
        spreadsheetId: data.spreadsheetId,
      });
    }
    return {
      success: status,
      error,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('nodeInstance')
  async getNodeInstance() {
    return {
      environments: process.env,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('external-tools')
  async getExternalTools(@Req() req) {
    return this.adminService.getExternalTools(req.query);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('external-tools/save')
  async saveExternalTool(@Req() request: BaseRequest, @Body() payload: ExternalToolPayload) {
    const result = await this.adminService.saveExternalTool(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('external-tools/delete')
  async deleteExternalTool(@Req() request: BaseRequest, @Body() payload: ExternalToolPayload) {
    const result = await this.adminService.deleteExternalTool(payload.id);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    return result;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('notification/send')
  async createNotification(@Req() request: BaseRequest, @Body() payload: NotificationDto) {
    const variables = {
      title: payload.title,
      message: payload.message,
    };
    const result = await this.notificationService.sendNotification(
      payload.target,
      payload.ctaLink,
      variables,
      request.user.uid
    );
    if (result?.internalErrorCode) {
      AdminController.throwSendNotificationError(result?.errorMessage);
    }

    return result;
  }
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('update-subs-service')
  async updateSubscriptionService() {
    this.adminService.updateSubscriptionService();
    return { success: true, message: 'App start update subscription service...' };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('cdm/update-list-subs-service')
  async syncUpdateSubscriptionService(@Body() body: SyncUsersToCDMConsumersDto) {
    this.adminService.syncUpdateSubscriptionService(body.emails);
    return { success: true, message: 'App start update subscription service...' };
  }

  static throwContentInvalidError() {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.CMS_CONTENT_INVALID,
      errorMessage: 'Content is not valid, please check your content id or content format!',
    });
  }

  static throwProductInvalidError() {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.CMS_CONTENT_INVALID,
      errorMessage: 'Product is not valid, please check your product id!',
    });
  }

  static throwSendNotificationError(message: string) {
    throw new BadRequestException({
      internalErrorCode: ERROR_CODES.SEND_NOTIFICATION_ERROR,
      errorMessage: message || 'Can not send Notification to these user!',
    });
  }

  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user-permission/update')
  async postUpdateUserPermission(@Req() request: BaseRequest, @Body() data: UserPermissionDto) {
    const result = await this.adminService.postUpdateUserPermission(data);
    if (!result || result.errorMessage) {
      return { success: false, message: result.errorMessage || 'Can not update user permission' };
    }
    return { success: true, result };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('permissions/user-mytm')
  async getPermissionUserFromMyTM(@Query('email') email: string) {
    const data = await this.adminService.getPermissionUserFromMyTM(email);
    return { data };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('permissions/user-cdm')
  async getPermissionUserFromCDM(@Query('email') email: string) {
    const data = await this.adminService.getPermissionUserFromCDM(email);
    return { data };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('permissions/reset-to-free/:email')
  async resetPermissionsToTheFreePlan(@Param('email') email: string) {
    const data = await this.adminService.resetPermissionsToTheFreePlan(email);
    return { data };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @Put('cdm/sync-user-permission')
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async forceSyncMyTMtoCDM(@Req() request: BaseRequest, @Body() body: SyncUsersToCDMConsumersDto) {
    const { start, limit, emails } = body;
    return await this.adminService.forceSyncMyTMtoCDM(start, limit, emails);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('ttb/trigger-ttb-confirmation')
  async triggerTTBConfirmation(@Body() body: OrderIdsDto) {
    await this.adminService.ttbTriggerOrderConfirmation(body.orders);
    return { success: true, message: 'App start trigger ttb confirmation service...' };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('klaviyo/sync-user-subscription')
  async forceSyncKlaviyoTrack(@Req() request: BaseRequest, @Body() body: SyncKlaviyoTrackDto) {
    const { start, limit, emails, isHadSubscription } = body;
    return await this.adminService.forceSyncKlaviyoTrack(isHadSubscription, start, limit, emails);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('member-shop/update-order-statuses')
  async updateMemberShopOrderStatuses(@Body() body: UpdateMemberShopOrdersDto) {
    const { orderIds, year, status } = body;
    return await this.adminService.updateMemberShopOrderStatuses(orderIds, year, status);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('member-shop/orders/:status')
  async getMemberShopOrders(@Param('status') status: string) {
    return await this.adminService.getMemberShopOrders(status);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('users/:userId/subscriptions')
  async getUserSubscriptions(@Param('userId') userId: string) {
    return await this.adminService.getUserSubscriptions(status);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/sf/collect-data')
  async getUserCollectDataForSalesforce(@Body() body: any) {
    const chunkRows: any = await this.adminService.getUserCollectDataForSalesforce(parseInt(body.limit, 10) || 1000000);
    let count = 0;
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const momentTz = require('moment-timezone');
    const currentTime = momentTz().tz('America/Los_Angeles');
    for (const chunkRow of chunkRows) {
      count++;
      await this.salesforceCSVQueue.add(SalesforceCSVBuildProcessorQueueName.SALESFORCE_CSV_BUILD, {
        emails: chunkRow.map((item) => item.email),
        suffixName: currentTime.format('YYYYMMDD'),
        shouldUpload: count === chunkRows.length,
      });
      console.log(`ADDED LIST ${JSON.stringify(chunkRow)} TO JOB BUILD CSV...`);
    }
    return {
      success: true,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/sf/update-should-collect-salesforce-data')
  async updateShouldCollectSalesforceDataForUsers(@Body() body: any) {
    await this.adminService.updateShouldCollectSalesforceDataForUsers(
      body.emails || [],
      body.shouldCollectSalesforceData
    );
    return {
      success: true,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/sf/set-default-should-collect-salesforce-data')
  async setDefaultShouldCollectSalesforceDataForUsers(@Body() body: any) {
    await this.adminService.setDefaultShouldCollectSalesforceDataForUsers();
    return {
      success: true,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/sf/collect-data/test')
  async getUserCollectDataForSalesforceTest(@Body() body: any) {
    const profileRows = await this.adminService.getUserProfileCollectDataForSalesforce(body.emails);
    const witbRows = await this.adminService.getUserWITBCollectDataForSalesforce(body.emails);
    const roundRows = await this.adminService.getUserRoundsCollectDataForSalesforce(body.emails);
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const momentTz = require('moment-timezone');
    const currentTime = momentTz().tz('America/Los_Angeles');
    await this.salesforceCSVQueue.add(SalesforceCSVBuildProcessorQueueName.SALESFORCE_CSV_BUILD, {
      emails: body.emails,
      suffixName: currentTime.format('YYYYMMDD'),
      shouldUpload: false,
    });
    return {
      profileRows,
      witbRows,
      roundRows,
    };
  }
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('users/sf/collect-data/clear')
  async clearJobCollectData() {
    const jobWaitings = await this.salesforceCSVQueue.getWaiting();
    const removeJobs = { success: 0, failure: 0 };
    if (jobWaitings) {
      for (const job of jobWaitings) {
        try {
          await job.remove();
          removeJobs.success += 1;
        } catch (error) {
          console.log(`Remove Job Fail`);
          removeJobs.failure += 1;
        }
      }
    }
    return {
      removeJobs,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/sf/download-csv-file')
  async downloadBuiltSalesforceCSVFile(@Body() body: any, @Res() res) {
    const { fileName } = body;
    return res.sendFile(path.join(process.cwd(), `/public/Salesforce/${fileName}.csv`));
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/sf/delete-csv-files')
  async deleteBuiltSalesforceCSVFile() {
    return fs.rmdirSync(path.join(process.cwd(), `/public/Salesforce`), { recursive: true });
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user-profile/update/:userId')
  async updateUserProfile(
    @Body() body: any,
    @Req() request: BaseRequest,
    @Param('userId') userId?: string
  ): Promise<any> {
    return await this.adminService.updateUserProfile(userId, request.user.uid, body);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('user-profile/:userId')
  async deleteProfile(@Req() request: BaseRequest, @Param('userId') userId?: string): Promise<any> {
    return await this.adminService.deleteProfile(userId, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user-profile/:userId')
  async restoreProfile(@Req() request: BaseRequest, @Param('userId') userId?: string): Promise<any> {
    return await this.adminService.restoreProfile(userId, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user-permission/sync-up-permissions')
  async syncUpPermission(@Body() payload: { limit: number }): Promise<any> {
    return await this.adminService.syncUpdatePermission(payload);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('sync-countries')
  async syncCountries() {
    await this.countrySyncingQueue.add(CountrySyncingProcessorQueueName.SYNC_COUNTRY);
    return { success: true };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('update-country')
  async updateCountry(@Body() payload: UpdateCountryDto) {
    return await this.adminService.updateCountry(payload);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user/access-token')
  async getTokenUser(@Body() payload: GetTokenUserDto): Promise<any> {
    return await this.adminService.getTokenUser(payload);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/witb/collect-data')
  async runJobWIBData(@Body() body: any): Promise<any> {
    let chunkWIBRows: any = await this.adminService.getUserCollectDataForWIB(body.limit || 1000000);
    if (!chunkWIBRows || !chunkWIBRows.length) {
      return null;
    }
    chunkWIBRows = _.uniq(_.map(chunkWIBRows, 'email'));
    chunkWIBRows = _.chunk(chunkWIBRows, 20);
    const currentTime = momentTimeZone().tz('America/Los_Angeles');
    for (const chunkRow of chunkWIBRows) {
      await this.wibCSVQueue.add(WIBCSVBuildProcessorQueueName.WIB_CSV_BUILD, {
        emails: chunkRow,
        suffixName: currentTime.format('YYYYMMDD'),
      });
      console.log(`ADDED LIST ${JSON.stringify(chunkRow)} TO JOB BUILD CSV...`);
    }
    return {
      success: true,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/witb/delete-csv-files')
  async deleteBuiltWIBCSVFile() {
    await this.cacheManager.del('WIB_FILE_NAME_CSV');
    await fs.rmdirSync(path.join(process.cwd(), `/public/WITB`), { recursive: true });
    return true;
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('users/witb/files')
  async getListFileWIB() {
    return fs.readdirSync(path.join(process.cwd(), `/public/WITB`));
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('users/witb/clear-job')
  async clearJobWIBCollectData() {
    const jobWaitings = await this.wibCSVQueue.getWaiting();
    const jobActive = await this.wibCSVQueue.getActive();
    const removeJobs = { success: 0, failure: 0 };
    const removeActiveJobs = { success: 0, failure: 0 };
    if (jobWaitings) {
      for (const job of jobWaitings) {
        try {
          await job.remove();
          removeJobs.success += 1;
        } catch (error) {
          removeJobs.failure += 1;
        }
      }
    }
    if (jobActive) {
      for (const job2 of jobActive) {
        try {
          await job2.remove();
          removeActiveJobs.success += 1;
        } catch (error) {
          removeActiveJobs.failure += 1;
        }
      }
    }
    await this.cacheManager.del('WIB_FILE_NAME_CSV');
    return {
      removeJobs,
      removeActiveJobs,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('users/witb/download-csv-file')
  async sendEmailWIBFile(@Body() body: any) {
    return this.adminService.sendEmailWibFile(body);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user/notify-admin')
  async createUserNotifyAdmin(@Body() body: UserNotifyAdminDto) {
    return this.adminService.createUserNotifyAdmin(body);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user/send-email')
  async testSendEmail(@Body() body: SendTestEmailDto) {
    return this.adminService.testSendEmail(body);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('user/force-delete')
  async forceDeleteUser(@Body() body: ForceDeleteEmailsDto) {
    return this.adminService.forceDeleteUser(body);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('user/push-notification')
  async userPushNotify(@Body() body: any) {
    return this.adminService.userPushNotify(body);
  }
}

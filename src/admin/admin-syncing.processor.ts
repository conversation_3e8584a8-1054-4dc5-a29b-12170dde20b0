import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { AdminService } from './admin.service';

export type AdminSyncingJob = Job<{ emails: string[] }>;

export enum AdminSyncingProcessorQueueName {
  SYNC_ECOM_CUSTOMERS = 'sync-ecom-customers',
}

@Processor('admin-syncing')
export class AdminSyncingProcessor {
  constructor(private adminService: AdminService) {}

  @Process({
    name: AdminSyncingProcessorQueueName.SYNC_ECOM_CUSTOMERS,
    concurrency: 2,
  })
  async process(job: AdminSyncingJob): Promise<any> {
    await this.adminService.syncEComMissingCustomers(job.data.emails);
    return true;
  }
}

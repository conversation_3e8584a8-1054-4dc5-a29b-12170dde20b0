import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import axios from 'axios';
import { Job } from 'bull';
import chunk from 'lodash/chunk';
import ServiceAccount from '../config/mytm-backend-service-account.json';

const { GoogleSpreadsheet } = require('google-spreadsheet');

export type AdminYoutubeThumbnailsJob = Job<{ spreadsheetId: string }>;

export enum AdminYoutubeThumbnailsProcessorQueueName {
  CHECK_YOUTUBE_THUMBNAILS = 'check-youtube-thumbnails',
}

@Processor('admin-youtube-thumbnails')
export class AdminYoutubeThumbnailsProcessor {
  private readonly logger = new Logger(AdminYoutubeThumbnailsProcessor.name);

  @Process(AdminYoutubeThumbnailsProcessorQueueName.CHECK_YOUTUBE_THUMBNAILS)
  async process(job: AdminYoutubeThumbnailsJob): Promise<any> {
    this.logger.log(`Admin Youtube Thumbnails: checking spreadsheet id ${job.data.spreadsheetId}`);
    try {
      const doc = new GoogleSpreadsheet(job.data.spreadsheetId);
      await doc.useServiceAccountAuth(ServiceAccount);
      await doc.loadInfo();
      for (let i = 0; i < doc.sheetCount; i++) {
        const sheet = doc.sheetsByIndex[i];
        await sheet.loadCells();
        const allRows = await sheet.getRows();
        const chunkRows = chunk(allRows.slice(0, allRows.length), 50);
        for (const rows of chunkRows) {
          this.logger.log(`Delay 10s...`);
          await new Promise((resolve) => setTimeout(resolve, 10000));
          this.logger.log(`Handling ${rows.length} videos...`);
          await this.handleRows(sheet, rows);
        }
      }
    } catch (e) {
      console.log(e);
      return false;
    }
    return true;
  }

  getVideoId(input) {
    return input.match(
      /(?:youtu\.be\/|youtube\.com(?:\/embed\/|\/v\/|\/watch\?v=|\/user\/\S+|\/ytscreeningroom\?v=|\/sandalsResorts#\w\/\w\/.*\/))([^\/&]{10,12})/
    )[1];
  }

  async isVideoHasThumb(youtubeId, type) {
    try {
      const url = `https://img.youtube.com/vi/${youtubeId}/${type}.jpg`;
      this.logger.log(`Getting: ${url}`);
      const response = await axios.get(url);
      return response.status === 200;
    } catch (e) {
      return false;
    }
  }

  async handleRows(sheet, rows) {
    for (const row of rows) {
      const youtubeId = this.getVideoId(row.Hyperlink);
      const hq = await this.isVideoHasThumb(youtubeId, 'hqdefault');
      const max = await this.isVideoHasThumb(youtubeId, 'maxresdefault');
      const hqCell = sheet.getCellByA1(`E${row._rowNumber}`);
      const maxCell = sheet.getCellByA1(`F${row._rowNumber}`);
      hqCell.value = hq.toString();
      maxCell.value = max.toString();
    }
    await sheet.saveUpdatedCells();
  }
}

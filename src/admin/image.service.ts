import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { ImageEntity } from './entities/image.entity';
import { AddImageDto, IMAGE_STATUS, IMAGE_TYPE, UpdateImageDto } from './image.types';

@Injectable()
export class ImageService {
  constructor(
    private readonly config: ConfigService,
    private readonly apiVersionsService: ApiVersionsService,
    @InjectRepository(ImageEntity)
    private readonly imageRepo: Repository<ImageEntity>
  ) {
    this.config = config;
  }

  async findAll(imageType = IMAGE_TYPE.NORMAL) {
    const conditions = [{ type: imageType }];
    if (imageType == IMAGE_TYPE.NORMAL) {
      conditions.push({ type: null });
    }
    return this.imageRepo.find({
      where: conditions,
      order: { createdAt: 'DESC' },
    });
  }
  async getImages(imageType = IMAGE_TYPE.NORMAL) {
    const conditions = [{ type: imageType }];
    if (imageType == IMAGE_TYPE.NORMAL) {
      conditions.push({ type: null });
    }
    const images = await this.imageRepo.find({
      where: conditions,
      order: { createdAt: 'DESC' },
      select: ['keyImage', 'urlImage', 'status', 'type'],
    });
    if (!images || images.length == 0) {
      return {};
    }
    const transformImg = {};
    for (const img of images) {
      if (imageType == IMAGE_TYPE.PLAY_BACKGROUND && img.status == IMAGE_STATUS.ACTIVE) {
        transformImg[img.keyImage] = img.urlImage;
      } else if (imageType == IMAGE_TYPE.NORMAL) {
        transformImg[img.keyImage] = img.urlImage;
      }
    }
    return transformImg;
  }
  async findImageBy(options) {
    return await this.imageRepo.find({
      where: options,
    });
  }
  async saveImage(userId: string, imageDTO: AddImageDto) {
    try {
      await this.validateKeyImage(imageDTO.keyImage);
      imageDTO.createdBy = userId;
      imageDTO.status = IMAGE_STATUS.ACTIVE;

      const image = this.imageRepo.create(imageDTO);
      await this.imageRepo.save(image);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CONTENT_IMAGE, 'USA', userId);
      return await this.findImageBy({ keyImage: imageDTO.keyImage });
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
  async updateImage(userId: string, imageDTO: UpdateImageDto) {
    try {
      imageDTO.updatedAt = new Date();
      imageDTO.updatedBy = userId;
      const image = await this.imageRepo.findOne({ id: imageDTO.id?.toLocaleUpperCase() });

      if (!image) {
        throw new NotFoundException(`The key ${imageDTO.keyImage} not found`);
      }
      if (imageDTO.keyImage != image.keyImage) {
        await this.validateKeyImage(imageDTO.keyImage);
      }

      await this.imageRepo.update(image.id, imageDTO);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CONTENT_IMAGE, 'USA', userId);
      return await this.findImageBy({ keyImage: imageDTO.keyImage });
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
  async validateKeyImage(keyImage: string) {
    const isExisted = await this.imageRepo.findOne({ keyImage });
    if (isExisted) {
      throw new BadRequestException(`The Key ${keyImage} already exists`);
    }
  }

  async deleteImage(userId: string, imageId: string) {
    try {
      const image = await this.imageRepo.findOne({ id: imageId?.toLocaleUpperCase() });
      if (!image) {
        throw new NotFoundException(`Image not found`);
      }
      await this.imageRepo.update(image.id, {
        deletedAt: new Date(),
        deletedBy: userId,
      });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.CONTENT_IMAGE, 'USA', userId);
      return { success: true };
    } catch (err) {
      throw new BadRequestException(err.message);
    }
  }
}

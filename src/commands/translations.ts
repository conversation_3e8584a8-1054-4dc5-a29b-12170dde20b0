import fs from 'fs';
import ServiceAccount from '../config/mytm-backend-service-account.json';

const { GoogleSpreadsheet } = require('google-spreadsheet');

async function run() {
  const doc = new GoogleSpreadsheet('1b7bxSwDvfmnCIPk9k2hDsq_nK6CTdcugvQH-_xxW3yI');
  await doc.useServiceAccountAuth(ServiceAccount);
  await doc.loadInfo();
  const translations = {};
  for (let i = 0; i < doc.sheetCount; i++) {
    const sheet = doc.sheetsByIndex[i];
    const rows = await sheet.getRows();
    rows.forEach((row) => {
      if (!row.Key?.trim() || !row.Value?.trim()) {
        return row;
      }
      translations[row.Key.trim()] = row.Value.trim();
      return row;
    });
  }
  fs.writeFileSync('translations.json', JSON.stringify(translations, null, 2));
}

run().then(() => process.exit(0));

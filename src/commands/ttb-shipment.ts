import axios from 'axios';
import moment from 'moment';
import xlsx from 'node-xlsx';
import path from 'path';
import Client from 'ssh2-sftp-client';

class TtbShipment {
  endpoint = 'https://www.17track.net';
  apiEndpoint = 'https://api.17track.net';
  async shipmentScans(orderIds: string[]): Promise<any> {
    const EBSShipmentFileNames = await this.getDailyShipmentFileNames();
    if (!EBSShipmentFileNames || EBSShipmentFileNames.length === 0) return;
    let orders = [];
    let canceledOrders = [];
    let heading = [];
    for (const EBSShipmentFileName of EBSShipmentFileNames) {
      const {
        heading: fileHeading,
        orders: fileOrders,
        canceledOrders: fileCanceledOrders,
      } = await this.getDailyEBSShipmentOrders(orderIds, EBSShipmentFileName);
      if (fileHeading && fileOrders.length > 0) {
        heading = [...fileHeading];
        orders = [...orders, ...fileOrders];
        canceledOrders = [...canceledOrders, ...fileCanceledOrders];
      }
    }
    if (canceledOrders.length > 0) {
      await this.handleCanceledOrders(canceledOrders);
    }
    if (heading && orders.length > 0) {
      const client = await this.getFTPClient();
      for (const order of orders) {
        const orderId: string = order[2];
        const shipmentTrackingNumber: string = order[23];
        const shipmentCarrierName: string = order[22];
        const filename = `TryThenBuy-${orderId}-Shipped.csv`;
        const buffer = xlsx.build(
          [
            {
              name: orderId,
              data: [heading, order],
            },
          ],
          { bookType: 'csv' }
        );
        // this.logger.log(`Started to put ${filename} to GVC...`);
        // await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/GVC/${filename}`);
        // await client.put(buffer, `/${this.config.get('app.ttbFTPRootPath')}/Archived/${filename}`);
        // this.logger.log(`Putted ${filename} to GVC...`);
        const shipmentSeventeentrackCarrierKey = await this.registerTrackingNumber(
          shipmentTrackingNumber,
          shipmentCarrierName
        );
        // await this.ttbRepo.update(
        //   { orderId },
        //   {
        //     status: TTBStatus.TRIAL_COMING,
        //     shipmentTrackingNumber,
        //     shipmentCarrierName,
        //     shippedAt: new Date(),
        //     shipmentSeventeentrackCarrierKey,
        //   },
        // );
      }
      return client.end();
    }
  }

  getHeaders() {
    return { '17token': '839771A43E7E85973CCE376170742BAC' };
  }

  async getAllCarriers(): Promise<Array<any>> {
    const { data } = await axios.get(`${this.endpoint}/en/apicarrier`);
    return data;
  }

  async registerTrackingNumber(number: string, carrier: string): Promise<string | null> {
    const carriers = await this.getAllCarriers();
    const matchCarrier = carriers.find((item) => item._name.toUpperCase() === carrier.toUpperCase());
    if (!matchCarrier) {
      return null;
    }
    const response = await axios.post(
      `${this.apiEndpoint}/track/v1/register`,
      [
        {
          number,
          carrier: matchCarrier.key,
          final_carrier: 0,
          auto_detection: false,
        },
      ],
      { headers: this.getHeaders() }
    );
    console.log(response.status);
    return response.status === 200 ? `${matchCarrier.key}` : null;
  }

  async getDailyEBSShipmentOrders(
    orderIds: string[],
    EBSFileName: string
  ): Promise<{ heading: string[] | null; orders: Array<string[]>; canceledOrders: Array<string[]> }> {
    const client = await this.getFTPClient();
    try {
      const EBSFileFormatPath = `/STAGING/EBS/${EBSFileName}`;
      const status = await client.exists(EBSFileFormatPath);
      if (!status) {
        await client.end();
        return {
          heading: null,
          orders: [],
          canceledOrders: [],
        };
      }
      const localEBSShipmentFilePath = path.join(process.cwd(), `/public/EBS/${EBSFileName}`);
      await client.fastGet(EBSFileFormatPath, localEBSShipmentFilePath);
      const workSheetsFromFile = xlsx.parse(localEBSShipmentFilePath);
      const sheet = workSheetsFromFile[0].data;
      if (sheet[0]?.length > 1) {
        sheet.unshift([`TTB${moment().format('MMDDYYYY')}`]);
      }
      const sheetHeading = sheet[1] as string[];
      const orders = sheet.slice(2, sheet.length);
      const matchedOrders = [];
      const canceledOrders = [];
      orders.forEach((order: string[]) => {
        if (orderIds.includes(order[2]) && order[12] === 'SHIPPED') {
          matchedOrders.push(order);
        }
        if (orderIds.includes(order[2]) && order[12] === 'CANCELED') {
          canceledOrders.push(order);
        }
      });
      await client.end();
      return {
        heading: sheetHeading,
        orders: matchedOrders,
        canceledOrders,
      };
    } catch (e) {
      console.log(e);
      client.end();
      return {
        heading: null,
        orders: [],
        canceledOrders: [],
      };
    }
  }

  async getFTPClient() {
    const client = new Client();
    try {
      await client.connect({
        host: 'sftp.tmag.com',
        user: 'trythenbuy',
        password: 'IddjSaCs',
        port: 22,
        algorithms: {
          cipher: ['3des-cbc'],
        },
      });
    } catch (error) {
      console.log(error);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      return this.getFTPClient();
    }
    return client;
  }

  async handleCanceledOrders(orders: Array<string[]>) {
    for (const order of orders) {
      const orderId: string = order[2];
      console.log(orderId);
    }
  }

  async getDailyShipmentFileNames(): Promise<string[]> {
    const client = await this.getFTPClient();
    try {
      const EBSFileFormatPath = `/STAGING/EBS/`;
      const files = await client.list(
        EBSFileFormatPath,
        new RegExp(`TM_TRYTHENBUY_SHIPMENTS_${moment().format('DDMMYYYY')}`)
      );
      await client.end();
      return files.map((file) => file.name);
    } catch (e) {
      console.log(e);
      await client.end();
      return [];
    }
  }
}

async function run() {
  const shipmentChecker = new TtbShipment();
  const orderIds = ['**********'];
  await shipmentChecker.shipmentScans(orderIds);
}

run().then(() => process.exit(0));

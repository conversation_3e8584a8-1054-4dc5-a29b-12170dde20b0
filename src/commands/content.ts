import axios from 'axios';
import chunk from 'lodash/chunk';
import ServiceAccount from '../config/mytm-backend-service-account.json';

const { GoogleSpreadsheet } = require('google-spreadsheet');

function getVideoId(input) {
  return input.match(
    /(?:youtu\.be\/|youtube\.com(?:\/embed\/|\/v\/|\/watch\?v=|\/user\/\S+|\/ytscreeningroom\?v=|\/sandalsResorts#\w\/\w\/.*\/))([^\/&]{10,12})/
  )[1];
}

async function isVideoHasThumb(youtubeId, type) {
  try {
    const url = `https://img.youtube.com/vi/${youtubeId}/${type}.jpg`;
    const response = await axios.get(url);
    return response.status === 200;
  } catch (e) {
    return false;
  }
}

async function handleRows(sheet, rows) {
  for (const row of rows) {
    const youtubeId = getVideoId(row.Hyperlink);
    const hq = await isVideoHasThumb(youtubeId, 'hqdefault');
    const max = await isVideoHasThumb(youtubeId, 'maxresdefault');
    const hqCell = sheet.getCellByA1(`E${row._rowNumber}`);
    const maxCell = sheet.getCellByA1(`F${row._rowNumber}`);
    hqCell.value = hq.toString();
    maxCell.value = max.toString();
  }
  await sheet.saveUpdatedCells();
}

async function run() {
  const doc = new GoogleSpreadsheet('1RmWZJbhCuj6J1iSfzHgP_JMwxgxvzNvxFbdOiGX-RJk');
  await doc.useServiceAccountAuth(ServiceAccount);
  await doc.loadInfo();
  for (let i = 0; i < doc.sheetCount; i++) {
    const sheet = doc.sheetsByIndex[i];
    await sheet.loadCells();
    const allRows = await sheet.getRows();
    const chunkRows = chunk(allRows.slice(0, allRows.length), 50);
    for (const rows of chunkRows) {
      await new Promise((resolve) => setTimeout(resolve, 10000));
      await handleRows(sheet, rows);
    }
  }
}

run().then(() => process.exit(0));

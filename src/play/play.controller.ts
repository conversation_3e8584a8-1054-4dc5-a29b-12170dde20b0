import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import querystring from 'querystring';
import { LogRequestDto } from 'src/mrp/dto/log.request.dto';
import { ArccosService } from '../arccos/arccos.service';
import { CdmService } from '../cdm/cdm.service';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { MrpService } from '../mrp/mrp.service';
import { BaseRequest } from '../types/core';
import { ERROR_CODES } from '../utils/errors';
import { PlayService } from './play.service';
import { PostScoreDto, ServicePreferenceDto } from './play.type';

@Controller()
@UseGuards(ClientGuard)
export class PlayController {
  constructor(
    private mRPService: MrpService,
    private arccosService: ArccosService,
    private playService: PlayService,
    private readonly cdmService: CdmService,
    private readonly config: ConfigService
  ) {
    this.config = config;
  }

  @Get('play/round/recent')
  @UseGuards(AuthGuard)
  async getRecentRounds(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    return await this.playService.getRecentRounds(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      mrpUID,
      query
    );
  }

  @Get('play/round-tm-usga/recent')
  @UseGuards(AuthGuard)
  async getRecentRoundTMAndUsga(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    return await this.playService.getRecentRoundTMAndUsga(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      mrpUID,
      query
    );
  }

  @Post('play/round')
  @UseGuards(AuthGuard)
  async postPostScore(@Req() request: BaseRequest, @Body() body: PostScoreDto): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    if (body.postScoreGolfNet) {
      return await this.mRPService.postGolfNetPostScore(parseInt(mrpUID, 10), body);
    }
    return await this.mRPService.postPostScore(parseInt(mrpUID, 10), body, request?.client?.name);
  }

  @Post('play/round-usga')
  @UseGuards(AuthGuard)
  async postPostScoreUSGA(@Req() request: BaseRequest, @Body() body: PostScoreDto): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.postPostScoreUSGA(parseInt(mrpUID, 10), body, request?.client?.name);
  }

  @Delete('play/round/:id')
  @UseGuards(AuthGuard)
  async deleteRound(@Req() request: BaseRequest, @Param('id') id: number) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return this.mRPService.deleteRound(id);
  }

  @Put('play/round/:id')
  @UseGuards(AuthGuard)
  async updateRound(@Req() request: BaseRequest, @Param('id') id: number, @Body() body: PostScoreDto) {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return this.mRPService.updateRound(id, body, request?.client?.name);
  }

  @Get('play/courses')
  @UseGuards(AuthGuard)
  async getCourses(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const { referenceLatitude, referenceLongitude, isCamelCase } = query;
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    if (referenceLatitude && referenceLongitude) {
      await this.playService.updateGPSUser(request?.user?.uid, referenceLatitude, referenceLongitude);
    }
    // const isLimitRequest = await this.playService.checkUserLimitRequestCourses(
    //   request?.user?.uid,
    //   querystring.stringify(query)
    // );
    // if (isLimitRequest) {
    //   return { isLimitRequest: true };
    // }
    return await this.mRPService.getCourses(query, request, isCamelCase);
  }
  @Post('play/courses/logs')
  @UseGuards(AuthGuard)
  async getCoursesLogRequest(@Req() request: BaseRequest, @Body() payload: LogRequestDto): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getCoursesLogRequest(payload, request?.client?.name);
  }

  @Get('play/courses/recent')
  @UseGuards(AuthGuard)
  async getRecentCourses(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getRecentCourses(mrpUID, querystring.stringify(query), request?.client?.name);
  }
  @Get('play/courses-ghin/recent')
  @UseGuards(AuthGuard)
  async getRecentGHINCourses(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getGHINRecentCourses(mrpUID, querystring.stringify(query));
  }

  @Get('play/courses/details/:courseId')
  @UseGuards(AuthGuard)
  async getCourseDetails(
    @Param() params,
    @Query('isCamelCase') isCamelCase,
    @Req() request: BaseRequest
  ): Promise<any> {
    return await this.mRPService.getCourseDetails(params.courseId, request?.client?.name, isCamelCase);
  }

  @Get('play/courses/details/:courseId/:informationType')
  @UseGuards(AuthGuard)
  async getCourseInformation(
    @Param() params,
    @Query('isCamelCase') isCamelCase,
    @Req() request: BaseRequest
  ): Promise<any> {
    if (!['gps', 'scorecard', 'tee', 'gps_vector', 'elevation_data'].includes(params.informationType)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.COURSE_INFORMATION_TYPE_INVALID,
        errorMessage: 'Course information type is not valid!',
      });
    }
    return await this.mRPService.getCourseInformation(
      params.courseId,
      params.informationType,
      isCamelCase,
      request?.client?.name
    );
  }

  @Get('stats/club/:clubId')
  @UseGuards(AuthGuard)
  async getClubStats(@Req() request: BaseRequest, @Param() params, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getClubStats(parseInt(mrpUID, 10), params.clubId, querystring.stringify(query));
  }

  @Get('stats/club')
  @UseGuards(AuthGuard)
  async getDistanceClubStats(@Req() request: BaseRequest, @Query() query): Promise<any> {
    return await this.playService.getClubs(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      request.user.mrpUID,
      query
    );
  }

  @Get('stats/overall')
  @UseGuards(AuthGuard)
  async getRoundStats(@Req() request: BaseRequest, @Query() query): Promise<any> {
    return await this.playService.getRoundStats(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      request.user.mrpUID,
      query
    );
  }

  @Get('arccos/stats/round')
  @UseGuards(AuthGuard)
  async getArccosRoundStats(@Req() request: BaseRequest, @Query('roundId') roundId: number): Promise<any> {
    if (!roundId) {
      return null;
    }
    return await this.arccosService.getArccosRoundStats(request.user.email, roundId);
  }

  @Get('arccos/authorization')
  @UseGuards(AuthGuard)
  async getRequestHeaderConfigs(@Req() request: BaseRequest): Promise<any> {
    return this.arccosService.getRequestHeaderConfigs();
  }

  @Get('play-service')
  @UseGuards(AuthGuard)
  async getPlayService(@Req() request: BaseRequest, @Param('onlyGetMode') onlyGetMode): Promise<any> {
    const result = await this.playService.getPlayService(request.user.email);
    if (!onlyGetMode) {
      await this.cdmService.updatePlayService(request.user.email, request.user.regionId, result.playService);
    }
    return result;
  }

  @Post('play/service-preference/update')
  @UseGuards(AuthGuard)
  async updateSettingServicePreference(
    @Req() request: BaseRequest,
    @Body() payload: ServicePreferenceDto
  ): Promise<any> {
    await this.cdmService.updatePlayService(request.user.email, request.user.regionId, payload.service);
    return { success: true };
  }

  @Get('play/show-advance')
  @UseGuards(AuthGuard)
  async getShowAdvance(@Req() request: BaseRequest): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getShowAdvance(parseInt(mrpUID, 10));
  }

  @Get('stats/club/:clubId/putter')
  @UseGuards(AuthGuard)
  async getClubStatsPutter(@Req() request: BaseRequest, @Param() params, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getClubStatsPutter(parseInt(mrpUID, 10), params.clubId, querystring.stringify(query));
  }

  @Get('stats/driving')
  @UseGuards(AuthGuard)
  async getStatsDriving(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getStatsDriving(parseInt(mrpUID, 10), querystring.stringify(query));
  }

  @Get('stats/driving/overall')
  @UseGuards(AuthGuard)
  async getStatsDrivingOverall(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getStatsDrivingOverall(parseInt(mrpUID, 10), querystring.stringify(query));
  }

  @Get('stats/short')
  @UseGuards(AuthGuard)
  async getStatsShort(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    const isNotProximity = query.proximity === 'false';
    if (isNotProximity) {
      delete query.proximity;
    }
    return await this.mRPService.getStatsShort(parseInt(mrpUID, 10), querystring.stringify(query), isNotProximity);
  }

  @Get('stats/approach')
  @UseGuards(AuthGuard)
  async getStatsApproach(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    const isNotProximity = query.proximity === 'false';
    if (isNotProximity) {
      delete query.proximity;
    }
    return await this.mRPService.getStatsApproach(parseInt(mrpUID, 10), querystring.stringify(query), isNotProximity);
  }

  @Get('stats/putting')
  @UseGuards(AuthGuard)
  async getStatsPutting(@Req() request: BaseRequest, @Query() query): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getStatsPutting(parseInt(mrpUID, 10), querystring.stringify(query));
  }

  @Get('play/clubs/active')
  @UseGuards(AuthGuard)
  async getClubsActive(@Req() request: BaseRequest, @Query('take') take, @Query('page') page): Promise<any> {
    const mrpUID = await this.playService.getMRPUserId(request);
    if (!mrpUID) {
      return null;
    }
    return await this.mRPService.getClubsActive(parseInt(mrpUID, 10), take, page);
  }

  @AccessedClients(CLIENTS.MRP)
  @Get('play/spam/:email')
  @UseGuards(ClientGuard)
  async sendEmailSpamIgolf(@Param('email') email: string, @Query('limit') limit, @Query('time') timeBlock) {
    return this.playService.sendEmailSpamIgolf(email, limit, timeBlock);
  }
}

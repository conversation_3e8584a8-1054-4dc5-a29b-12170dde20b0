import { BullModule } from '@nestjs/bull';
import { Modu<PERSON> } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { SYSTEM_TAG } from '../utils/constants';
import { PlayController } from './play.controller';
import { PlayCronService } from './play.cron.service';
import { PlayArccosProcessor } from './play.processor';
import { PlayService } from './play.service';
import { PLAY_BULL_QUEUE } from './play.type';

let processors = [];

if (process.env.TAG !== SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, PlayArccosProcessor];
}

@Module({
  imports: [
    SharedModule,
    BullModule.registerQueue({
      name: PLAY_BULL_QUEUE.PLAY_ARCCOS,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 10,
        backoff: 300000,
      },
    }),
  ],
  controllers: [PlayController],
  providers: [...processors, PlayService, PlayCronService],
})
export class PlayModule {}

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import fs from 'fs';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import path from 'path';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { PlayService } from './play.service';
import { PLAY_BULL_QUEUE, PLAY_SERVICE, PlayArccosProcessorQueueName } from './play.type';

export type PlayArccosScanJob = Job<any>;

@Processor(PLAY_BULL_QUEUE.PLAY_ARCCOS)
export class PlayArccosProcessor {
  private readonly logger = new Logger(PlayArccosProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly playService: PlayService,
    private readonly cdmService: CdmService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}

  @Process(PlayArccosProcessorQueueName.ARCCOS_SCANS)
  async arccosScans(job: PlayArccosScanJob): Promise<any> {
    this.logger.log(`Scan file Arccos email starts...`);
    const arccosFileNames = await this.getDailyArccosFileNames();
    if (!arccosFileNames || arccosFileNames.length === 0) return;
    await Promise.all(
      arccosFileNames.map(async (arccosFileName) => {
        const { emails } = await this.getDailyArccosEmail(arccosFileName);
        await Promise.all(
          emails.map(async (email) => {
            const user = await this.userRepo.findOne({ email });
            if (!user) {
              this.logger.log('Can not find user: ' + email);
              return;
            }
            if (!!user.isArccosEmail) return;
            await this.userRepo.update({ email }, { isArccosEmail: true });
            await this.cdmService.updatePlayService(email, user.regionId, PLAY_SERVICE.ARCCOS);
          })
        );
        await this.moveFileToArchivedFolder(arccosFileName);
      })
    );
    this.logger.log(`Scan file Arccos email ends...`);
  }

  async moveFileToArchivedFolder(arccosFileName: string) {
    const client = await this.playService.getFTPClient();
    this.logger.log(`Moving file: ${arccosFileName} to Archived folder...`);
    try {
      const arccosFileFormatPath = `/${this.config.get('app.ttbFTPRootPath')}/Arccos/${arccosFileName}`;
      const status = await client.exists(arccosFileFormatPath);
      if (!status) {
        await client.end();
        return {
          success: false,
        };
      }
      const archivedFilePath = `/${this.config.get('app.ttbFTPRootPath')}/Arccos/Archived`;
      const archivedFolderExist = await client.exists(archivedFilePath);
      if (!archivedFolderExist) {
        const recursive = true;
        await client.mkdir(archivedFilePath, recursive);
      }
      const moveFilePath = `/${archivedFilePath}/${arccosFileName}`;
      await client.rename(arccosFileFormatPath, moveFilePath);
      // await client.delete(arccosFileFormatPath);
      this.logger.log(`Moved file: ${arccosFileName} successfully!`);
      await client.end();
      return { success: true };
    } catch (e) {
      this.logger.error(`Faild to move file ${arccosFileName} by error: ${e}`);
      client.end();
      return {
        success: false,
      };
    }
  }

  async getDailyArccosEmail(arccosFileName: string): Promise<{ emails: string[] }> {
    const client = await this.playService.getFTPClient();
    this.logger.log(`Arccos scans file: ${arccosFileName}`);
    try {
      const arccosFileFormatPath = `/${this.config.get('app.ttbFTPRootPath')}/Arccos/${arccosFileName}`;
      const status = await client.exists(arccosFileFormatPath);
      if (!status) {
        this.logger.log('Can not find arccos file!');
        await client.end();
        return {
          emails: [],
        };
      }
      const localArccosFolder = path.join(process.cwd(), `/public/Arccos`);
      const localArccosFilePath = path.join(process.cwd(), `/public/Arccos/${arccosFileName}`);
      if (!fs.existsSync(localArccosFolder)) fs.mkdirSync(localArccosFolder, { recursive: true });
      await client.fastGet(arccosFileFormatPath, localArccosFilePath);
      const workSheetsFromFile = xlsx.parse(localArccosFilePath);
      const sheet = workSheetsFromFile[0].data;
      const emails = sheet.slice(1, sheet.length);
      this.logger.log(`Arccos scans file: ${arccosFileName} got ${emails.length} emails`);
      await client.end();
      return { emails: emails.map((r: any) => r[0]) };
    } catch (e) {
      console.log(e);
      client.end();
      return {
        emails: [],
      };
    }
  }

  async getDailyArccosFileNames(): Promise<string[]> {
    const client = await this.playService.getFTPClient();
    try {
      const arccosFileName = `/${this.config.get('app.ttbFTPRootPath')}/Arccos/`;
      const files = await client.list(arccosFileName, new RegExp(`TM_ARCCOS_${moment().format('DDMMYYYY')}`));
      await client.end();
      return files.map((file: any) => file.name);
    } catch (e) {
      console.log(e);
      await client.end();
      return [];
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import moment from 'moment/moment';
import { ConfigService } from 'nestjs-config';
import querystring from 'querystring';
import Client from 'ssh2-sftp-client';
import { EntityManager, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { ArccosService } from 'src/arccos/arccos.service';
import { UserEntity } from 'src/auth/entities/user.entity';
import { CdmService } from 'src/cdm/cdm.service';
import { MrpService } from 'src/mrp/mrp.service';
import { BaseRequest } from 'src/types/core';
import { isArccosService } from 'src/utils/service.preference';
import { TextEntity } from '../admin/entities/text.entity';
import { UserCourseLogsEntity } from '../auth/entities/user-course-logs.entity';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { LoggingService } from '../logging/logging.service';
import { PLAY_SERVICE } from './play.type';

const LIMIT_TIME_REQUEST_COURSES = 'LIMIT_TIME_REQUEST_COURSES';

@Injectable()
export class PlayService {
  private readonly logger = new Logger(PlayService.name);
  constructor(
    private readonly config: ConfigService,
    private arccosService: ArccosService,
    private mRPService: MrpService,
    private cdmService: CdmService,
    private loggingService: LoggingService,
    private klaviyoService: KlaviyoService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserNotifyAdminEntity) private readonly userNotifyAdminRepo: Repository<UserNotifyAdminEntity>,
    @InjectRepository(TextEntity) private readonly textRepo: Repository<TextEntity>,
    @InjectRepository(UserCourseLogsEntity) private readonly userCourseLogRepo: Repository<UserCourseLogsEntity>,
    @InjectEntityManager()
    private readonly entityManager: EntityManager
  ) {
    this.config = config;
  }

  async getMRPUserId(req: BaseRequest): Promise<string> {
    if (isNaN(parseInt(req.user.mrpUID))) {
      const user = await this.userRepo.findOne(req.user.uid);
      if (isNaN(parseInt(user.mrpUID))) {
        const { userId } = await this.mRPService.createAccountByEmail(req.user.email);
        if (!userId) {
          return null;
        }
        await this.userRepo.update({ id: req.user.uid }, { mrpUID: userId });
        await this.cdmService.updateAccount(user.email, this.config.get('app.defaultRegionId'), {
          mrp: userId,
        });
        return userId;
      }
      return user.mrpUID;
    }
    return req.user.mrpUID;
  }

  async getPlayService(email: string): Promise<{ playService: PLAY_SERVICE }> {
    let playService = PLAY_SERVICE.MYTMOC;
    const user = await this.userRepo.findOne({ email });
    if (!!user?.isArccosEmail) {
      const status = await this.arccosService.getArccosUserStatus(email);
      if (status && status.userStatus.isMember) {
        playService = PLAY_SERVICE.ARCCOS;
      }
    }
    return { playService };
  }

  async getRecentRoundTMAndUsga(email: string, regionId: string, mrpUID: string, params: any): Promise<any> {
    const query = querystring.stringify(params);
    if (!mrpUID) {
      return [];
    }
    return await this.mRPService.getRecentRoundTMAndUsga(parseInt(mrpUID, 10), query);
  }

  async getRecentRounds(email: string, regionId: string, mrpUID: string, params: any): Promise<any> {
    const query = querystring.stringify(params);
    if (isArccosService(params)) {
      const rounds = await this.arccosService.getArccosRoundsList(email, query);
      if (rounds.userStatus.isMember) {
        return rounds;
      }
      if (!mrpUID) {
        return [];
      }
      return await this.mRPService.getRecentRounds(parseInt(mrpUID, 10), query);
    }
    if (!mrpUID) {
      return [];
    }
    return await this.mRPService.getRecentRounds(parseInt(mrpUID, 10), query);
  }

  async getRoundStats(email: string, regionId: string, mrpUID: string, params: any): Promise<any> {
    const query = querystring.stringify(params);
    if (isArccosService(params)) {
      const stats = await this.arccosService.getArccosUser(email, query);
      if (stats.userStatus.isMember) {
        const deepData = {
          ...stats,
          ...stats.stats,
        };
        delete deepData?.stats;
        return deepData;
      }
      if (!mrpUID) {
        return [];
      }
      return await this.mRPService.getRoundStats(parseInt(mrpUID, 10), query);
    }
    if (!mrpUID) {
      return [];
    }
    return await this.mRPService.getRoundStats(parseInt(mrpUID, 10), query);
  }

  async getClubs(email: string, regionId: string, mrpUID: any, params: any): Promise<any> {
    const query = querystring.stringify(params);
    if (isArccosService(params)) {
      const rounds = await this.arccosService.getArccosClubs(email);
      if (rounds.userStatus.isMember) {
        return (
          rounds?.clubs?.map((club) => ({
            club: club?.club,
            value: club?.value,
            low: club?.low,
            high: club?.high,
            avg: club?.avg,
          })) || []
        );
      }
      if (!mrpUID) {
        return [];
      }
      return await this.mRPService.getDistanceClubStats(parseInt(mrpUID, 10), query);
    }
    if (!mrpUID) {
      return [];
    }
    return await this.mRPService.getDistanceClubStats(parseInt(mrpUID, 10), query);
  }

  async getFTPClient() {
    const client = new Client();
    try {
      await client.connect({
        host: this.config.get('app.ttbFTPHost'),
        user: this.config.get('app.ttbFTPUser'),
        password: this.config.get('app.ttbFTPPassword'),
        port: 22,
        algorithms: {
          cipher: ['3des-cbc'],
        },
      });
    } catch (error) {
      this.logger.error(error.message);
      this.logger.log(`Delay 3s...`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      this.logger.log('Retry connect SFTP...');
      return this.getFTPClient();
    }
    return client;
  }

  async updateGPSUser(userId, lat, long) {
    try {
      if (!lat || !long) return;
      await this.userRepo.update(
        { id: userId },
        { lastLatitude: Number(lat).toFixed(5), lastLongitude: Number(long).toFixed(5) }
      );
      return true;
    } catch (err) {
      this.logger.error('ERR_UPDATE_USER_GPS:', err);
      return false;
    }
  }

  async checkUserLimitRequestCourses(userId: string, querystring: string) {
    try {
      const user = await this.userRepo.findOne(userId);
      if (!user) return false;
      const { isSpamCourse } = user;
      if (isSpamCourse) {
        return this.checkUserUnBlockRequest(userId, querystring);
      }
      const limitRequest = await this.textRepo.findOne({
        where: {
          keyText: LIMIT_TIME_REQUEST_COURSES,
        },
      });
      const limit = limitRequest.value ? Number(limitRequest.value) : 60;
      const sqlQuery = `SELECT COUNT(*) AS requestCount, MAX(createdAt) AS lastRequestTime
            FROM UserCourseLogs
            WHERE userId = '${user.id}'
                AND createdAt >= DATEADD(mi, -1, GETDATE());`;
      const result = await this.entityManager.query(sqlQuery);
      if (!result || !result?.length) return false;
      const requestCount = parseInt(result[0]?.requestCount);
      if (requestCount >= limit) {
        const currentTime: any = new Date();
        await this.userRepo.update({ id: userId }, { isSpamCourse: true });
        // send email to admin
        this.sendEmailToAdminUserBlockKlaviyo(user.email, currentTime, limit).catch((e) => e);
        return true;
      } else {
        await this.createUserCourseLog(userId, querystring);
      }
      return false;
    } catch (err) {
      this.logger.error('ERR_COURSE_CheckUserLimitRequestCourses:', err.message);
      return true;
    }
  }

  async createUserCourseLog(userId, querystring) {
    return this.userCourseLogRepo.save({
      id: v4(),
      userId,
      params: querystring,
      requestTime: new Date(),
    });
  }

  async checkUserUnBlockRequest(userId, queryString) {
    const sqlQuery = `SELECT MAX(requestTime) AS lastRequestTime
            FROM UserCourseLogs
            WHERE userId = '${userId}';`;
    const result = await this.entityManager.query(sqlQuery);
    if (!result || !result?.length) return false;
    const lastRequestTime: any = new Date(result[0]?.lastRequestTime);
    const currentTime: any = new Date();
    const timeSinceLastRequest: any = currentTime - lastRequestTime;
    if (timeSinceLastRequest < 5 * 60 * 1000) {
      return true;
    }

    await this.userRepo.update({ id: userId }, { isSpamCourse: false });
    await this.createUserCourseLog(userId, queryString);
    return false;
  }

  async sendEmailToAdminUserBlockKlaviyo(email, detectTime, limitTime) {
    const googleMap = 'https://www.google.com/maps/place';
    const timeDetect = moment(detectTime).format('MM/DD/YYYY HH:mm:ss');
    const user = await this.userRepo.findOne({
      where: { email },
    });
    const userAdmins = await this.userNotifyAdminRepo.find({
      where: { isCourses: true },
      relations: ['user'],
    });
    if (!userAdmins || !userAdmins.length) {
      return false;
    }
    const emailTos = _.compact(_.map(userAdmins, 'email'));
    if (emailTos && emailTos.length) {
      for (const value of emailTos) {
        try {
          const payloadOrderInfo = {
            timeDetect,
            limitTime,
            email,
            location: `Can't get location.`,
          };
          if (user) {
            payloadOrderInfo.location = `${googleMap}/${user?.lastLatitude},${user?.lastLongitude}`;
          }
          const emailAdmin: any = value;
          await this.klaviyoService.track(emailAdmin, KlaviyoTrackEvents.MYTM_SPAM_REQUEST_COURSE, payloadOrderInfo);
        } catch (error) {
          this.logger.error('ERR_COURSE_send_email:', error);
          await this.loggingService.save({
            event: 'ERR_COURSE_send_email',
            data: {
              error: error,
            },
          });
        }
      }
    }
  }

  sendEmailSpamIgolf(email, limit, timeBlock) {
    if (!email || !limit) return;
    this.sendEmailToAdminUserBlockKlaviyo(email, timeBlock, limit).catch((e) => e);
    return true;
  }
}

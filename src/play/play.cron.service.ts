import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import { PLAY_BULL_QUEUE, PlayArccosProcessorQueueName } from './play.type';

@Injectable()
export class PlayCronService {
  private readonly logger = new Logger(PlayCronService.name);
  constructor(
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private readonly config: ConfigService,
    @InjectQueue(PLAY_BULL_QUEUE.PLAY_ARCCOS) private playArccosQueue: Queue
  ) {}

  // @Cron(CronExpression.EVERY_5_MINUTES)
  // async handleCheckArccosEmailCron() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   this.logger.log('Job scans check arccos email run...');
  //   await this.playArccosQueue.add(PlayArccosProcessorQueueName.ARCCOS_SCANS);
  // }
}

import { IsDateString, <PERSON>NotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString, MaxLength } from 'class-validator';

export class PostScoreDto {
  @IsString()
  @IsOptional()
  @MaxLength(255)
  courseID: string;

  @IsString()
  @IsOptional()
  courseName: string;

  @IsString()
  @IsOptional()
  teeName: string;

  @IsNumber()
  @IsNotEmpty()
  numberOfHolesPlayed: number;

  @IsOptional()
  front9Score: number;

  @IsOptional()
  back9Score: number;

  @IsOptional()
  eighteenHoleScore: number;

  @IsOptional()
  postScoreGhin: boolean;

  @IsOptional()
  postScoreGolfNet: boolean;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  roundType: string;

  @IsString()
  @MaxLength(255)
  roundMode?: string;

  @IsOptional()
  sourceType?: string;

  @IsNumber()
  userTimezone?: number;

  @IsDateString()
  @MaxLength(255)
  datePlayed?: string;

  @IsOptional()
  @IsDateString()
  @MaxLength(255)
  datePlayedUTC?: string;

  @IsString()
  @IsNotEmpty()
  generatedBy: string;

  @IsOptional()
  ghinCourseId?: string;

  @IsOptional()
  ghinCourseName?: string;

  @IsOptional()
  ghinTeeSetId?: string;

  @IsOptional()
  ghinTeeSetName?: string;

  @IsOptional()
  golfNetCourseId?: string;

  @IsOptional()
  golfNetCourseName?: string;

  @IsOptional()
  golfNetTeeSetId?: string;

  @IsOptional()
  golfNetTeeSetName?: string;

  @IsOptional()
  roundId?: number;
}

export enum PLAY_BULL_QUEUE {
  PLAY_ARCCOS = 'play-arccos',
}

export enum PlayArccosProcessorQueueName {
  ARCCOS_SCANS = 'arccos-scans',
}

export enum MEMBER_STATUS {
  ACTIVE = 'active',
  CANCELED = 'canceled',
}

export enum PLAY_SERVICE {
  ARCCOS = 'Arccos',
  MYTMOC = 'MyTMOC',
}

export class ServicePreferenceDto {
  @IsString()
  @IsNotEmpty()
  service: PLAY_SERVICE;
}

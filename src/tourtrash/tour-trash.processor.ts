import { Process, Processor } from '@nestjs/bull';
import { Inject, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { Job } from 'bull';
import { messaging } from 'firebase-admin';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { NotificationService } from 'src/notification/notification.service';
import { PushAndSendTo, TourTrashEntity } from './entities/tour-trash.entity';
import { TOUR_TRASH_NOTIFICATION, TOUR_TRASH_TITLE } from './tour-trash.constants';
import { TourTrashCronService } from './tour-trash.cron.service';

type TourTrashJob = Job<{ tourTrash: TourTrashEntity }>;

export enum TourTrashProcessorQueueName {
  CLOSE_TOURTRASH = 'close-tourtrash',
  OPEN_TOURTRASH = 'open-tourtrash',
  REMIND_CLOSING = 'remind-closing',
  CANCEL_TOURTRASH = 'cancel-tourtrash',
}

interface DataPushNotificationType {
  deepLink?: string;
  title: string;
  message: string;
  userNotificationId: string;
}

@Processor('tourtrash')
export class TourTrashProcessor {
  private readonly logger = new Logger(TourTrashProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private readonly notificationService: NotificationService,
    @Inject(forwardRef(() => TourTrashCronService)) private tourTrashCronService: TourTrashCronService,
    private firebaseMessaging: FirebaseMessagingService,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>
  ) {}

  @Process(TourTrashProcessorQueueName.CLOSE_TOURTRASH)
  async pushNotificationCloseTourTrash(job: TourTrashJob) {
    // MYT-2042: disable all notification tour trash
    return null;
    try {
      const tourTrash = job.data?.tourTrash;
      if (tourTrash) {
        const { pushNotificationTo, winnerUser } = tourTrash;
        const users = await this.tourTrashCronService.getUserEmailAndFcmTokens(pushNotificationTo);
        if (users.length > 0) {
          const winner = users.find((x) => x.email === winnerUser.email);
          if (winner && winner.fcmToken) {
            // SAVE NOTIFICATION CLOSE_AND_WINNER //
            const userNotificationId = await this.saveNotificationToMyTMDB(
              winnerUser.id,
              TOUR_TRASH_TITLE.CLOSE_AND_WINNER,
              TOUR_TRASH_NOTIFICATION.CLOSE_AND_WINNER,
              TourTrashProcessorQueueName.CLOSE_TOURTRASH,
              pushNotificationTo
            );
            // - END - //
            // SEND NOTIFICATION CLOSE_AND_WINNER TO FIREBASE//
            await this.pushNotificationTourTrash(
              winner,
              {
                title: TOUR_TRASH_TITLE.CLOSE_AND_WINNER,
                message: TOUR_TRASH_NOTIFICATION.CLOSE_AND_WINNER,
                deepLink: this.config.get('app.tourTrashCtaLink'),
                userNotificationId,
              },
              TourTrashProcessorQueueName.CLOSE_TOURTRASH
            );
          }
          const isNotEnableNotifyNonWinner = this.config.get('app.tourTrashPushNotifyNonWinner') != 'enabled';
          console.log(`--- tourTrashPushNotifyNonWinner: ${this.config.get('app.tourTrashPushNotifyNonWinner')}`);
          console.log(`--- tourTrashPushNotifyNonWinner: ${isNotEnableNotifyNonWinner}`);
          if (isNotEnableNotifyNonWinner) {
            return;
          }
          // SAVE NOTIFICATION CLOSE_AND_NON_WINNER //
          const notWins = users
            .filter((x) => x.email !== winnerUser.email)
            .map((v) => {
              return { id: v.id, token: v.fcmToken };
            });
          for (const target of notWins) {
            try {
              const userNotificationId = await this.saveNotificationToMyTMDB(
                target.id,
                TOUR_TRASH_TITLE.CLOSE_AND_NON_WINNER,
                TOUR_TRASH_NOTIFICATION.CLOSE_AND_NON_WINNER,
                TourTrashProcessorQueueName.CLOSE_TOURTRASH,
                pushNotificationTo
              );
              // - END - //
              // SEND NOTIFICATION CLOSE_AND_NON_WINNER TO FIREBASE//
              const message: messaging.Message = {
                data: {
                  deepLink: this.config.get('app.tourTrashCtaLink'),
                  userNotificationId,
                },
                token: target.token,
                notification: {
                  title: TOUR_TRASH_TITLE.CLOSE_AND_NON_WINNER,
                  body: TOUR_TRASH_NOTIFICATION.CLOSE_AND_NON_WINNER,
                },
              };
              this.firebaseMessaging.send(message).then((r) => r);
            } catch (error) {
              this.logger.error(
                `Fail to send notification ${TOUR_TRASH_TITLE.CLOSE_AND_NON_WINNER} to ${target.token}`
              );
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  @Process(TourTrashProcessorQueueName.OPEN_TOURTRASH)
  async pushNotificationTourTrashOpen(job: TourTrashJob): Promise<void> {
    // MYT-2042: disable all notification tour trash
    return null;
    try {
      const tourTrash = job.data?.tourTrash;
      if (tourTrash) {
        const { pushNotificationTo } = tourTrash;
        const pushNotificationAtLaunch = !!tourTrash.pushNotificationAtLaunch;
        if (pushNotificationAtLaunch) {
          const users = await this.tourTrashCronService.getUserEmailAndFcmTokens(pushNotificationTo);
          if (users.length > 0) {
            const targets = [];
            // SAVE NOTIFICATION TOUR_TRASH_OPEN //
            for (const user of users) {
              try {
                const userId = user.id;
                const userNotificationId = await this.saveNotificationToMyTMDB(
                  userId,
                  TOUR_TRASH_TITLE.TOUR_TRASH_OPEN,
                  TOUR_TRASH_NOTIFICATION.TOUR_TRASH_OPEN,
                  TourTrashProcessorQueueName.OPEN_TOURTRASH,
                  pushNotificationTo
                );
                targets.push({ token: user.fcmToken, userNotificationId });
              } catch (error) {
                this.logger.error(`Fail to save notification of ${user.id} to MyTM BE`);
              }
            }
            // - END - //
            for (const target of targets) {
              try {
                // SEND NOTIFICATION TOUR_TRASH_OPEN TO FIREBASE//
                const message: messaging.Message = {
                  data: {
                    deepLink: this.config.get('app.tourTrashCtaLink'),
                    userNotificationId: target.userNotificationId,
                  },
                  token: target.token,
                  notification: {
                    title: TOUR_TRASH_TITLE.TOUR_TRASH_OPEN,
                    body: TOUR_TRASH_NOTIFICATION.TOUR_TRASH_OPEN,
                  },
                };
                this.firebaseMessaging.send(message).then((r) => r);
              } catch (error) {
                this.logger.error(`Fail to send notification ${TOUR_TRASH_TITLE.TOUR_TRASH_OPEN} to ${target.token}`);
              }
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  @Process(TourTrashProcessorQueueName.REMIND_CLOSING)
  async pushNotificationRemindClosing(job: TourTrashJob): Promise<void> {
    // MYT-2042: disable all notification tour trash
    return null;
    try {
      const tourTrash = job.data?.tourTrash;
      if (tourTrash) {
        const { pushNotificationTo, enteredUsers } = tourTrash;
        const users = await this.tourTrashCronService.getUserEmailAndFcmTokens(pushNotificationTo);
        if (users.length > 0) {
          const targets = users
            .filter((x) => enteredUsers.length > 0 && enteredUsers.findIndex((y) => x.email === y.email) > -1)
            .map((v) => {
              return { id: v.id, token: v.fcmToken };
            });
          for (const target of targets) {
            try {
              // SAVE NOTIFICATION REMINDER //
              const userNotificationId = await this.saveNotificationToMyTMDB(
                target.id,
                TOUR_TRASH_TITLE.REMINDER,
                TOUR_TRASH_NOTIFICATION.REMINDER,
                TourTrashProcessorQueueName.REMIND_CLOSING,
                pushNotificationTo
              );
              // - END - //
              // SEND NOTIFICATION REMINDER TO FIREBASE//
              const message: messaging.Message = {
                data: {
                  deepLink: this.config.get('app.tourTrashCtaLink'),
                  userNotificationId,
                },
                token: target.token,
                notification: {
                  title: TOUR_TRASH_TITLE.REMINDER,
                  body: TOUR_TRASH_NOTIFICATION.REMINDER,
                },
              };
              this.firebaseMessaging.send(message).then((r) => r);
            } catch (error) {
              this.logger.error(`Fail to send notification ${TOUR_TRASH_TITLE.REMINDER} to ${target.token}`);
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  @Process(TourTrashProcessorQueueName.CANCEL_TOURTRASH)
  async pushNotificationCancelTourtrash(job: TourTrashJob): Promise<void> {
    // MYT-2042: disable all notification tour trash
    return null;
    try {
      const tourTrash = job.data?.tourTrash;
      if (tourTrash) {
        const { pushNotificationTo } = tourTrash;
        const users = await this.tourTrashCronService.getUserEmailAndFcmTokens(pushNotificationTo);
        if (users.length > 0) {
          const targets = [];
          // SAVE NOTIFICATION TOUR_TRASH_OPEN //
          for (const user of users) {
            try {
              const userId = user.id;
              const userNotificationId = await this.saveNotificationToMyTMDB(
                userId,
                TOUR_TRASH_TITLE.CANCEL_TOURTRASH,
                TOUR_TRASH_NOTIFICATION.CANCEL_TOURTRASH,
                TourTrashProcessorQueueName.CANCEL_TOURTRASH,
                pushNotificationTo
              );
              targets.push({ token: user.fcmToken, userNotificationId });
            } catch (error) {
              this.logger.error(`Fail to save notification of ${user.id} to MyTM BE`);
            }
          }
          // - END - //
          for (const target of targets) {
            try {
              // SEND NOTIFICATION TOUR_TRASH_OPEN TO FIREBASE//
              const message: messaging.Message = {
                data: {
                  deepLink: this.config.get('app.tourTrashCtaLink'),
                  userNotificationId: target.userNotificationId,
                },
                token: target.token,
                notification: {
                  title: TOUR_TRASH_TITLE.CANCEL_TOURTRASH,
                  body: TOUR_TRASH_NOTIFICATION.CANCEL_TOURTRASH,
                },
              };
              this.firebaseMessaging.send(message).then((r) => r);
            } catch (error) {
              this.logger.error(`Fail to send notification ${TOUR_TRASH_TITLE.CANCEL_TOURTRASH} to ${target.token}`);
            }
          }
        }
      }
    } catch (error) {
      throw error;
    }
  }

  private async pushNotificationTourTrash(
    item: { email: string; fcmToken?: string },
    configPushNotification: DataPushNotificationType,
    queueName: string
  ) {
    const { fcmToken, email } = item;
    try {
      this.logger.log(`pushing notification ${queueName} to ${email}`);
      if (!fcmToken) {
        this.logger.error(`can't found fcmToken for ${email}`);
        return null;
      }
      const message: messaging.Message = {
        data: {
          deepLink: configPushNotification.deepLink,
          userNotificationId: configPushNotification.userNotificationId,
        },
        token: fcmToken,
        notification: {
          title: configPushNotification.title,
          body: configPushNotification.message,
        },
      };
      await this.firebaseMessaging.send(message);
      this.logger.log(`pushed notification ${queueName} to ${email}`);
    } catch (error) {
      this.logger.error(`Failed to push notification for ${queueName} to ${email}`);
    }
  }

  private async saveNotificationToMyTMDB(
    userId: string,
    title: string,
    message: string,
    process: TourTrashProcessorQueueName,
    pushTo: PushAndSendTo
  ) {
    const variables = {
      title,
      message,
      service: 'TOURTRASH',
      process,
      target: pushTo === PushAndSendTo.ALL_USER ? 'ALL_USER' : 'SUBSCRIPTION_USER',
    };
    this.logger.log(`Pushing Tourtrash notification ${process} to MyTM BE`);
    const ctaLink = this.config.get('app.tourTrashCtaLink');
    const save = await this.notificationService.saveNotificationFromOtherService(userId, ctaLink, variables);
    if (!save) {
      this.logger.log(`Fail to save Tourtrash notification ${process} to MyTM BE`);
      return null;
    } else {
      return save.id;
    }
  }

  async sendToTopicFireBase(tokens: string[], topic: string, payload: messaging.MessagingPayload) {
    await this.firebaseMessaging.subscribeToTopic(tokens, topic);
    this.logger.log(`Subscribe To Topic ${topic}`);
    await this.firebaseMessaging.sendToTopic(topic, payload);
    this.logger.log(`Pushed notification to Topic: ${topic}`);
    await this.firebaseMessaging.unsubscribeFromTopic(tokens, topic);
    this.logger.log(`Unsubscribe From Topic: ${topic}`);
  }
}

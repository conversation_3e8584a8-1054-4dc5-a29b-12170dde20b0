import { BadRequestException, Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { TourTrashStatus } from './entities/tour-trash.entity';
import { TourTrashCronService } from './tour-trash.cron.service';
import { TourTrashService } from './tour-trash.service';
import {
  EnteredEmailResponse,
  JoinTouTrashDto,
  PickTourTrashResponse,
  SelectRandomDto,
  SelectRandomWinnerResponse,
  TourTrashActiveAndSoonResponse,
  TourTrashArchiveFromDateDto,
  TourTrashListResponse,
  TourTrashRecord,
  TourTrashUpdateStatusDto,
  TourTrashWinnerDto,
  UpdateStatusResponse,
} from './tour-trash.type';

@Controller()
export class TourTrashController {
  constructor(private tourTrashService: TourTrashService, private tourTrashCronService: TourTrashCronService) {}

  @Get('tour-trash')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getActiveTourTrash(
    @Req() request: Request & { user: any; country?: any },
    @Query('take') take: number,
    @Query('page') page: number,
    @Query('status') status: string
  ): Promise<TourTrashActiveAndSoonResponse> {
    return await this.tourTrashService.getActiveTourTrash(request.user.uid, take, page, status, request?.country);
  }

  @Post('tour-trash/join')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async joinTourTrash(@Req() request: Request & { user: any }, @Body() data: JoinTouTrashDto): Promise<any> {
    const result = await this.tourTrashService.joinTourTrash(request.user.uid, data.id);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    return { success: true };
  }

  @Post('tour-trash/un-join')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async unJoinTourTrash(@Req() request: Request & { user: any }): Promise<any> {
    const result = await this.tourTrashService.unJoinTourTrash(request.user.uid);
    return result;
  }

  @Post('admin/tour-trash/save')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async saveTourTrash(@Req() request: Request & { user: any }, @Body() payload: any): Promise<TourTrashRecord> {
    const result = await this.tourTrashService.saveTourTrash(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    return result.record;
  }

  @Post('admin/tour-trash/active')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async activeTourTrash(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashUpdateStatusDto
  ): Promise<UpdateStatusResponse> {
    const result = await this.tourTrashService.activateTourTrash(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    if (result.success) {
      return {
        id: payload.id,
        status: TourTrashStatus.ACTIVE,
      };
    }
  }

  @Post('admin/tour-trash/close')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async closeTourTrash(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashUpdateStatusDto
  ): Promise<UpdateStatusResponse> {
    const result = await this.tourTrashService.closeTourTrash(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    if (result.success) {
      await this.tourTrashCronService.handleCloseTourTrash(result.tourTrash);
      return {
        id: payload.id,
        status: TourTrashStatus.CLOSED,
      };
    }
  }

  @Post('admin/tour-trash/archive')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async archiveTourTrash(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashUpdateStatusDto
  ): Promise<UpdateStatusResponse> {
    const result = await this.tourTrashService.archiveTourTrash(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    if (result.success) {
      return {
        id: payload.id,
        status: TourTrashStatus.ARCHIVED,
      };
    }
  }

  @Post('admin/tour-trash/archive-to-date')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async archiveTourTrashToDate(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashArchiveFromDateDto
  ): Promise<any> {
    const result = await this.tourTrashService.archiveTourTrashToDate(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    return result;
  }

  @Post('admin/tour-trash/cancel')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async cancelTourTrash(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashUpdateStatusDto
  ): Promise<TourTrashRecord> {
    const result = await this.tourTrashService.cancelTourTrash(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    if (result.success) {
      if (result.isSendPush) {
        await this.tourTrashCronService.handleCancelTourTrash(result.tourTrash);
      }
      return {
        id: payload.id,
        status: TourTrashStatus.DRAFT,
      };
    }
  }

  @Post('admin/tour-trash/delete')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async deleteTourTrash(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashUpdateStatusDto
  ): Promise<UpdateStatusResponse> {
    const result = await this.tourTrashService.deleteTourTrash(payload, request.user.uid);

    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }

    if (result.success) {
      return {
        id: payload.id,
        status: TourTrashStatus.DELETED,
      };
    }
  }

  @Get('admin/tour-trash/list')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getTourtrashList(
    @Query('take') take: number,
    @Query('page') page: number,
    @Query('status') status: TourTrashStatus,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('country') country?: string
  ): Promise<TourTrashListResponse> {
    return await this.tourTrashService.getTourtrashList(take, page, {
      status,
      startDate,
      endDate,
      country,
    });
  }

  @Post('admin/tour-trash/winner/pick')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async pickTourTrashWinner(
    @Req() request: Request & { user: any },
    @Body() payload: TourTrashWinnerDto
  ): Promise<PickTourTrashResponse> {
    const result = await this.tourTrashService.pickTourTrashWinner(payload, request.user.uid);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    return result;
  }

  @Get('admin/tour-trash/entered-email')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getEnteredEmailTourTrash(
    @Query('tourTrashId') tourTrashId: string,
    @Query('take') take: number,
    @Query('page') page: number,
    @Query('emailOrName') emailOrName: string
  ): Promise<EnteredEmailResponse> {
    const result = await this.tourTrashService.getEnteredEmailTourTrash(tourTrashId, take, page, { emailOrName });
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    return result;
  }

  @Post('admin/tour-trash/winner/select-random')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async selectRandomWinner(@Body() payload: SelectRandomDto): Promise<SelectRandomWinnerResponse> {
    const result = await this.tourTrashService.selectRandomWinner(payload);
    if (result?.internalErrorCode) {
      throw new BadRequestException(result);
    }
    return result;
  }
}

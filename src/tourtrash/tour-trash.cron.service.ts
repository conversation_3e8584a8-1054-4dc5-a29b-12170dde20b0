import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { Brackets, Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { CdmService } from 'src/cdm/cdm.service';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import {
  PushAndSendTo as PushNotificationConfig,
  TourTrashEntity,
  TourTrashStatus,
} from './entities/tour-trash.entity';
import { SETTING_ADD_START_PUSH, SETTING_SUBTRACT_END_PUSH, TOUR_TRASH_MESSAGE_ERROR } from './tour-trash.constants';
import { TourTrashProcessorQueueName } from './tour-trash.processor';
import { TourTrashService } from './tour-trash.service';

enum SUBSCRIPTION_LEVEL {
  LEVEL_ONE = 1,
  LEVEL_TWO = 2,
}

@Injectable()
export class TourTrashCronService {
  private readonly logger = new Logger(TourTrashCronService.name);
  constructor(
    private readonly cdmService: CdmService,
    private readonly config: ConfigService,
    @InjectRepository(TourTrashEntity)
    private readonly tourTrashRepo: Repository<TourTrashEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    @InjectQueue('tourtrash')
    private tourTrashQueue: Queue,
    private tourTrashService: TourTrashService,
    private klaviyoService: KlaviyoService
  ) {
    this.config = config;
  }

  async handleCancelTourTrash(tourTrash: TourTrashEntity) {
    // track eventKlaviyo TOUR_TRASH_CANCELLED
    await this.klaviyoService.trackOnlyEvent(KlaviyoTrackEvents.TOUR_TRASH_CANCELLED, {
      mytm_tour_trash_title: tourTrash.title,
      mytm_tour_trash_image_url: tourTrash?.product?.images?.map((r) => ({ imageUrl: r.imageUrl })),
      mytm_tour_trash_description: tourTrash.description,
      mytm_tour_trash_start_date: tourTrash.startDate,
      mytm_tour_trash_end_date: tourTrash.endDate,
    });

    await this.addPushTourtrashToQueue(TourTrashProcessorQueueName.CANCEL_TOURTRASH, tourTrash);
  }

  async handleCloseTourTrash(tourTrash: TourTrashEntity) {
    // track eventKlaviyo TOUR_TRASH_CLOSED
    await this.klaviyoService.trackOnlyEvent(KlaviyoTrackEvents.TOUR_TRASH_CLOSED, {
      mytm_tour_trash_title: tourTrash.title,
      mytm_tour_trash_image_url: tourTrash?.product?.images?.map((r) => ({ imageUrl: r.imageUrl })),
      mytm_tour_trash_description: tourTrash.description,
      mytm_tour_trash_start_date: tourTrash.startDate,
      mytm_tour_trash_end_date: tourTrash.endDate,
    });
    await this.addPushTourtrashToQueue(TourTrashProcessorQueueName.CLOSE_TOURTRASH, tourTrash);
  }

  private async addPushTourtrashToQueue(queueName: string, tourTrash: TourTrashEntity) {
    try {
      await this.tourTrashQueue.add(queueName, { tourTrash });
    } catch (error) {
      this.logger.error(`error ${queueName}`);
      return false;
    }
  }

  async getUserEmailAndFcmTokens(configPush: number): Promise<{ email: string; fcmToken?: string; id: string }[]> {
    let users: { email: string; fcmToken?: string; id: string }[] = [];
    switch (configPush) {
      case PushNotificationConfig.ALL_USER:
        users = await this.getEmailMyTm();
        break;
      case PushNotificationConfig.SUBSCRIPTION_USER:
        const emailsFromCdm = await this.getEmailsBySubLevelFromCDM();
        if (emailsFromCdm && emailsFromCdm.length > 0) {
          users = await Promise.all(
            emailsFromCdm.map(async (v: { consumerId: string; email: string }) => {
              const user = await this.userRepo.findOne({ email: v.email });
              return { email: v.email, fcmToken: user?.fcmToken || null, id: user.id };
            })
          );
        }
        break;
      default:
        users = [];
        break;
    }

    return users.length > 0
      ? users
          .filter((x) => !!x.fcmToken)
          .map((user) => ({
            email: user.email,
            fcmToken: user.fcmToken,
            id: user.id,
          }))
      : [];
  }

  async getEmailMyTm() {
    let users: UserEntity[] = [];
    let skip = 0;
    let lastResult = [];
    do {
      const userRecords = await this.userRepo.find({
        select: ['email', 'fcmToken', 'id'],
        take: 500,
        skip,
      });
      lastResult = userRecords;
      skip += 500;
      if (userRecords.length > 0) {
        users = users.concat(...userRecords);
      }
    } while (lastResult.length > 0);

    return users;
  }

  async getEmailsBySubLevelFromCDM() {
    const searchLevel = `${SUBSCRIPTION_LEVEL.LEVEL_ONE},${SUBSCRIPTION_LEVEL.LEVEL_TWO}`;
    let users = [];
    let skip = 0;
    let lastResult = [];
    do {
      try {
        const respCdm = await this.cdmService.getEmailsBySubLevel(searchLevel, 500, skip);
        lastResult = respCdm.data;
        skip += 500;
        if (respCdm.data.length > 0) {
          users = users.concat(...respCdm.data);
        }
      } catch (err) {
        console.error(`something is wrong ${err}`);
      }
    } while (lastResult.length > 0);

    return users;
  }

  @Cron(process.env.REMINDER_CLOSING_CRON || CronExpression.EVERY_DAY_AT_8PM)
  async handleRunCronJobPushNotificationFrequency() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.debug('Cron job Push Notification Frequency run ...');
    await this.cronJobPushNotificationFrequency();
    this.logger.debug('Cron job Push Notification Frequency ended ...');
  }

  private async cronJobPushNotificationFrequency() {
    this.logger.debug(`Finding tour trash list active...`);

    const tourTrashesActive = await this.tourTrashRepo.find({
      where: { status: TourTrashStatus.ACTIVE },
      relations: ['enteredUsers'],
    });

    this.logger.debug(`End of finding tour trash list active`);

    if (tourTrashesActive.length === 0) {
      this.logger.error(`No tour trash active yet`);
      return;
    }
    const handleTourTrashPushPromises = [];
    tourTrashesActive.forEach(
      (tourTrashActive) =>
        new Promise<void>(async (resolve) => {
          this.logger.debug(`Checking time to pushing notification tourTrashId: ${tourTrashActive.uuid}`);
          const { startDate, endDate, remindPushNotificationFrequency, pushNotificationTo, enteredUsers } =
            tourTrashActive;
          const now = moment(new Date());
          const momentStartDate = moment(startDate).add(SETTING_ADD_START_PUSH, 'day');
          const momentEndDate = moment(endDate).subtract(SETTING_SUBTRACT_END_PUSH, 'day');
          const period = moment(endDate).diff(moment(startDate), 'day');
          if (
            remindPushNotificationFrequency !== 0 &&
            now.isBetween(startDate, endDate) &&
            period >= SETTING_ADD_START_PUSH + SETTING_SUBTRACT_END_PUSH
          ) {
            const daysInThePeriod = momentEndDate.diff(momentStartDate, 'day');
            const floorFrequency = Math.floor(daysInThePeriod / remindPushNotificationFrequency);
            const apartToPush = floorFrequency < 1 ? 1 : floorFrequency;
            const periodNowToEnd = momentEndDate.diff(now, 'day');
            this.logger.debug(`cronJobPushNotificationFrequency_daysInThePeriod = ${daysInThePeriod} ...`);
            this.logger.debug(`cronJobPushNotificationFrequency_floorFrequency = ${floorFrequency} ...`);
            this.logger.debug(`cronJobPushNotificationFrequency_apartToPush = ${apartToPush} ...`);
            this.logger.debug(`cronJobPushNotificationFrequency_periodNowToEnd = ${periodNowToEnd} ...`);
            if (
              periodNowToEnd >= 0 &&
              periodNowToEnd % apartToPush === 0 &&
              periodNowToEnd < apartToPush * remindPushNotificationFrequency
            ) {
              this.logger.debug(`pushing notification...`);
              await this.addPushTourtrashToQueue(TourTrashProcessorQueueName.REMIND_CLOSING, tourTrashActive);
            }
          }

          this.logger.debug(`End of checking to pushing notification tourTrashId: ${tourTrashActive.uuid}`);
          resolve();
        })
    );
    await Promise.all(handleTourTrashPushPromises);
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleTourTrashOpenCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    const tourTrashes = await this.tourTrashRepo
      .createQueryBuilder('t')
      .leftJoinAndSelect('t.product', 'product')
      .leftJoinAndSelect('product.images', 'images')
      .where('t.status = :status', { status: TourTrashStatus.ACTIVE })
      .andWhere('t.isCheckOpen = 0')
      .getMany();
    this.logger.debug(`Handling TourTrashOpenCron ${tourTrashes.length} tourTrashes...`);
    const handleTourTrashPushPromises = [];
    tourTrashes.forEach(
      (tourTrash) =>
        new Promise<void>(async (resolve) => {
          if (this.isActive(tourTrash)) {
            await this.handleTourTrashOpen(tourTrash);
          }
          resolve();
        })
    );
    await Promise.all(handleTourTrashPushPromises);
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleReminderCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    const tourTrashes = await this.tourTrashRepo
      .createQueryBuilder('t')
      .leftJoinAndSelect('t.enteredUsers', 'enteredUsers')
      .leftJoinAndSelect('t.product', 'product')
      .leftJoinAndSelect('product.images', 'images')
      .where('t.status = :status', { status: TourTrashStatus.ACTIVE })
      .andWhere(
        new Brackets((qb) => {
          qb.where('t.isCheckReminderSendMail = 0').orWhere('t.isCheckReminderPushNotification = 0');
        })
      )
      .getMany();
    const handleTourTrashPushPromises = [];
    tourTrashes.forEach(
      (tourTrash) =>
        new Promise<void>(async (resolve) => {
          const {
            timeToSendEmailBeforeEndDate,
            timeToPushNotificationBeforeEndDate,
            startDate,
            endDate,
            isCheckReminderSendMail,
            isCheckReminderPushNotification,
          } = tourTrash;
          const momentEndDate = moment(endDate);
          const momentStartDate = moment(startDate);
          const diffTime = momentEndDate.diff(momentStartDate, 'second');
          const canSend =
            momentEndDate.subtract(timeToSendEmailBeforeEndDate, 'second').isSameOrBefore(moment()) &&
            diffTime > timeToSendEmailBeforeEndDate &&
            !isCheckReminderSendMail &&
            this.isActive(tourTrash);
          const canPush =
            momentEndDate.subtract(timeToPushNotificationBeforeEndDate, 'second').isSameOrBefore(moment()) &&
            diffTime > timeToPushNotificationBeforeEndDate &&
            !isCheckReminderPushNotification &&
            this.isActive(tourTrash);
          if (canSend) {
            this.logger.debug(`Handling SendMailReminder ${tourTrash.uuid} ...`);
            await this.handleSendMailReminder(tourTrash);
          }
          if (canPush) {
            this.logger.debug(`Handling PushReminder ${tourTrash.uuid} ...`);
            await this.handlePushReminder(tourTrash);
          }
          resolve();
        })
    );
    await Promise.all(handleTourTrashPushPromises);
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async handleSelectWinnerCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    const tourTrashes = await this.tourTrashRepo
      .createQueryBuilder('t')
      .leftJoinAndSelect('t.userTourTrashes', 'userTourTrashes')
      .where('t.status = :status', { status: TourTrashStatus.ACTIVE })
      .andWhere('t.isCheckSelectWinner = 0')
      .getMany();
    const handleTourTrashPushPromises = [];
    tourTrashes.forEach(
      (tourTrash) =>
        new Promise<void>(async (resolve) => {
          const canSelectWinner =
            moment(tourTrash.endDate).add(tourTrash.timeToAutoCloseAfterEndDate, 'second').isSameOrBefore(moment()) &&
            moment().isAfter(moment(tourTrash.endDate));
          if (canSelectWinner) {
            this.logger.debug(`Handling select winner Tour Trash ${tourTrash.uuid} ...`);
            await this.handleSelectWinner(tourTrash);
          }
          resolve();
        })
    );
    await Promise.all(handleTourTrashPushPromises);
  }

  async handleTourTrashOpen(tourTrash: TourTrashEntity) {
    if (!!tourTrash.sendEmailAtLaunch) {
      // track eventKlaviyo TOUR_TRASH_OPEN
      await this.klaviyoService.trackOnlyEvent(KlaviyoTrackEvents.TOUR_TRASH_OPEN, {
        mytm_tour_trash_title: tourTrash.title,
        mytm_tour_trash_image_url: tourTrash?.product?.images?.map((r) => ({ imageUrl: r.imageUrl })),
        mytm_tour_trash_description: tourTrash.description,
        mytm_tour_trash_start_date: tourTrash.startDate,
        mytm_tour_trash_end_date: tourTrash.endDate,
      });
    }

    await this.tourTrashRepo.update({ uuid: tourTrash.uuid }, { isCheckOpen: true });

    await this.addPushTourtrashToQueue(TourTrashProcessorQueueName.OPEN_TOURTRASH, tourTrash);
  }

  async handleSendMailReminder(tourTrash: TourTrashEntity) {
    // track eventKlaviyo TOUR_TRASH_REMINDER
    await this.klaviyoService.trackOnlyEvent(KlaviyoTrackEvents.TOUR_TRASH_REMINDER, {
      mytm_tour_trash_title: tourTrash.title,
      mytm_tour_trash_image_url: tourTrash?.product?.images?.map((r) => ({ imageUrl: r.imageUrl })),
      mytm_tour_trash_description: tourTrash.description,
      mytm_tour_trash_start_date: tourTrash.startDate,
      mytm_tour_trash_end_date: tourTrash.endDate,
    });

    await this.tourTrashRepo.update({ uuid: tourTrash.uuid }, { isCheckReminderSendMail: true });
  }

  async handlePushReminder(tourTrash: TourTrashEntity) {
    await this.tourTrashRepo.update({ uuid: tourTrash.uuid }, { isCheckReminderPushNotification: true });
    await this.addPushTourtrashToQueue(TourTrashProcessorQueueName.REMIND_CLOSING, tourTrash);
  }

  async handleSelectWinner(tourTrash: TourTrashEntity) {
    const { userTourTrashes, winnerUserId } = tourTrash;

    if (tourTrash.userTourTrashes.length === 0) {
      this.logger.warn(TOUR_TRASH_MESSAGE_ERROR.NO_USER_JOINED);
    } else {
      if (!winnerUserId) {
        const randomPick = Math.floor(Math.random() * userTourTrashes.length);
        const userRandom = userTourTrashes[randomPick];
        await this.tourTrashRepo.update(
          { uuid: tourTrash.uuid },
          { winnerUserId: userRandom?.userId, isCheckSelectWinner: true, updatedBy: null }
        );
      } else {
        const user = await this.tourTrashService.getUserById(winnerUserId, false);
        if (!user) {
          this.logger.error(TOUR_TRASH_MESSAGE_ERROR.USER_NOT_FOUND);
          return;
        }

        await this.tourTrashRepo.update({ uuid: tourTrash.uuid }, { isCheckSelectWinner: true });
      }
      // track eventKlaviyo TOUR_TRASH_READY_TO_CLOSE
      await this.klaviyoService.trackOnlyEvent(KlaviyoTrackEvents.TOUR_TRASH_READY_TO_CLOSE, {
        mytm_tour_trash_title: tourTrash.title,
        mytm_tour_trash_start_date: tourTrash.startDate,
        mytm_tour_trash_end_date: tourTrash.endDate,
      });
    }
  }

  private isActive(tourTrash: TourTrashEntity): boolean {
    return (
      tourTrash.status === TourTrashStatus.ACTIVE &&
      moment().isBetween(moment(tourTrash.startDate), moment(tourTrash.endDate))
    );
  }
}

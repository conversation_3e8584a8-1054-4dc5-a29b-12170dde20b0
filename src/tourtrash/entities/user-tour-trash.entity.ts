import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { TourTrashEntity } from './tour-trash.entity';

@Entity('UserTourTrashs')
export class UserTourTrashEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  tourTrashUuid: string;

  @ManyToOne(() => TourTrashEntity, (tourTrash) => tourTrash.userTourTrashes, {
    onDelete: 'CASCADE',
    orphanedRowAction: 'delete',
  })
  tourTrash: TourTrashEntity;

  @ManyToOne(() => UserEntity, (tourTrash) => tourTrash.userTourTrashes)
  user: UserEntity;

  @Column()
  firstName: string;

  @Column()
  lastName: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column({ type: 'tinyint' })
  subscriptionLevel: number;
}

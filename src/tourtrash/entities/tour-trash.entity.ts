import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  <PERSON>tity,
  Join<PERSON><PERSON>umn,
  Join<PERSON><PERSON>,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from './../../auth/entities/user.entity';
import { ProductEntity } from './product.entity';
import { UserTourTrashEntity } from './user-tour-trash.entity';

export enum TourTrashStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  CLOSED = 'CLOSED',
  DELETED = 'DELETED',
  COMING_SOON = 'COMING SOON',
  ARCHIVED = 'ARCHIVED',
}

export enum PushAndSendTo {
  ALL_USER = 0,
  SUBSCRIPTION_USER = 1,
}

@Entity('TourTrashs')
export class TourTrashEntity {
  @PrimaryColumn('uuid')
  uuid: string;

  @Column()
  title: string;

  @Column()
  status: TourTrashStatus;

  @Column()
  description: string;

  @Column()
  startDate: Date;

  @Column()
  endDate: Date;

  @Column({ type: 'tinyint' })
  pushNotificationTo: number;

  @Column({ type: 'tinyint' })
  sendEmailTo: number;

  @Column()
  participationLevel: string;

  @Column()
  remindPushNotificationFrequency: number;

  @Column('bit')
  sendEmailAtLaunch: boolean;

  @Column('bit')
  pushNotificationAtLaunch: boolean;

  @Column()
  timeToSendEmailBeforeEndDate: number;

  @Column()
  timeToPushNotificationBeforeEndDate: number;

  @Column()
  timeToAutoCloseAfterEndDate: number;

  @Column('uuid')
  winnerUserId: string;

  @OneToOne((type) => ProductEntity, (product) => product.tourTrash)
  product: ProductEntity;

  @OneToOne((type) => UserEntity)
  @JoinColumn({ name: 'winnerUserId' })
  winnerUser: UserEntity;

  @ManyToMany((type) => UserEntity)
  @JoinTable({ name: 'UserTourTrashs', joinColumn: { name: 'tourTrashUuid' }, inverseJoinColumn: { name: 'userId' } })
  enteredUsers: UserEntity[];

  @OneToMany(() => UserTourTrashEntity, (userTourTrashes) => userTourTrashes.tourTrash, { cascade: true })
  userTourTrashes: UserTourTrashEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column()
  isCheckOpen: boolean;

  @Column()
  isCheckReminderSendMail: boolean;

  @Column()
  isCheckReminderPushNotification: boolean;

  @Column()
  isCheckSelectWinner: boolean;

  @Column()
  countries: string;

  @Column()
  options: string;
}

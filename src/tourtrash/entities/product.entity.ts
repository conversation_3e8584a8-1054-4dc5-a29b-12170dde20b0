import {
  Column,
  CreateDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProductImageEntity } from './product-image.entity';
import { TourTrashEntity } from './tour-trash.entity';

export enum ProductStatus {
  ENABLE = 'ENABLE',
  DISABLED = 'DISABLED',
}

@Entity('Products')
export class ProductEntity {
  @PrimaryColumn('uuid')
  uuid: string;

  @Column()
  name: string;

  @Column()
  status: ProductStatus;

  @Column()
  detail: string;

  @Column()
  legal: string;

  @Column()
  videoType: string;

  @Column()
  videoId: string;

  @Column('uuid')
  tourTrashUuid: string;

  @OneToOne((type) => TourTrashEntity, (tourTrash) => tourTrash.product)
  @JoinColumn({ name: 'tourTrashUuid' })
  tourTrash: TourTrashEntity;

  @OneToMany(() => ProductImageEntity, (image) => image.product, { cascade: true })
  images: ProductImageEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsDateString,
  <PERSON><PERSON>mpty,
  IsEnum,
  IsISO8601,
  IsNotEmpty,
  <PERSON>N<PERSON>ber,
  IsOptional,
  IsString,
  IsUUID,
  NotEquals,
  ValidationArguments,
  ValidationOptions,
  registerDecorator,
} from 'class-validator';
import moment from 'moment';
import { ProductStatus } from './entities/product.entity';
import { PushAndSendTo, TourTrashEntity, TourTrashStatus } from './entities/tour-trash.entity';

export enum GroupTypeDto {
  CREATE_NEW = 'CREATE_NEW',
  CAN_UPDATE_ALL = 'CAN_UPDATE_ALL',
  UPDATE_FOR_ACTIVE = 'UPDATE_FOR_ACTIVE',
}

export interface TourTrashRecord {
  id?: string;
  status?: TourTrashStatus;
  title?: string;
  description?: string;
  startDate?: Date; // ISO string date,
  endDate?: Date; // ISO string date,
  pushNotificationTo?: number; // null | 0 | 1
  sendEmailTo?: number; // null | 0 | 1
  participationLevel?: string; // "0, 1, 2"
  joined?: boolean;
  product?: {
    id?: string;
    name: string;
    status?: ProductStatus;
    detail: string;
    legal: string;
    images?: ProductImage[];
    videoType: string;
    videoId: string;
  };
  winner?: User;
  countries?: string;
  options?: string;
}

export interface ProductImage {
  id: string;
  imageUrl: string;
}

export interface OtherTourTrashRecord {
  timeToSendEmailBeforeEndDate: number;
  timeToAutoCloseAfterEndDate: number;
  sendEmailAtLaunch: boolean;
  pushNotificationAtLaunch: boolean;
  timeToPushNotificationBeforeEndDate: number;
  remindPushNotificationFrequency: number;
  enteredUsers: boolean;
}

export interface TourTrashActiveAndSoonResponse {
  total: number;
  take?: number;
  page?: number;
  data: TourTrashRecord[];
}

export interface TourTrashListResponse {
  total: number;
  take?: number;
  page?: number;
  data: (TourTrashRecord & OtherTourTrashRecord)[];
}

export interface PickTourTrashResponse {
  winner?: User;
}

export interface PickTourTrashWinner extends PickTourTrashResponse, InternalError {}

export interface EnteredEmailResponse {
  total?: number;
  take?: number;
  page?: number;
  data?: User[];
}

export interface EnteredEmailTourTrash extends EnteredEmailResponse, InternalError {}

export interface User {
  id?: string;
  email?: string;
  fullName?: string;
  myTMSubscriptionLevel?: string | number;
  enteredDate?: string | Date;
  avatar?: string;
}

export interface SelectRandomWinner extends SelectRandomWinnerResponse, InternalError {}
export interface SelectRandomWinnerResponse {
  tourTrashId?: string;
  perPage?: number;
  winner?: User;
  existOnPage?: number;
}

export interface InternalError {
  internalErrorCode?: string;
  errorMessage?: string;
}

export interface SuccessResponse {
  success?: boolean;
  tourTrash?: TourTrashEntity;
  isSendPush?: boolean;
}

export interface UpdateStatusTourTrash extends InternalError, SuccessResponse {}
export interface SaveTourTrashResponse extends InternalError {
  record?: TourTrashRecord;
  tourTrash?: TourTrashEntity;
  product?: any;
}

export interface UpdateStatusResponse {
  id: string;
  status: string;
}

export function IsAfterDate(property: string, validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'IsAfterDate',
      target: object.constructor,
      propertyName,
      constraints: [property],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          const [relatedPropertyName] = args.constraints;
          const relatedValue = (args.object as any)[relatedPropertyName];
          return moment(value).isAfter(moment(relatedValue));
        },
      },
    });
  };
}

export function MinDateNow(validationOptions?: ValidationOptions) {
  return (object: any, propertyName: string) => {
    registerDecorator({
      name: 'MinDateNow',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          return moment(value).isAfter(moment(new Date()));
        },
      },
    });
  };
}

export class ProductImageDto {
  @IsUUID('4')
  @IsOptional()
  id: string;

  @IsString({
    message: 'Product image url must be a string',
  })
  @IsNotEmpty({
    message: 'Product image url should not be empty',
  })
  imageUrl: string;
}

export class ProductDto {
  @IsUUID('4', { always: true })
  @IsOptional({ always: true })
  id: string;

  @IsString({
    message: 'Product name must be a string',
    always: true,
  })
  @IsNotEmpty({
    message: 'Product name should not be empty',
    always: true,
  })
  name: string;

  @IsEnum(ProductStatus, {
    message: 'Product status must be a valid enum value',
    always: true,
  })
  @IsOptional({ always: true })
  status: ProductStatus;

  @IsString({
    message: 'Product detail must be a string',
    always: true,
  })
  @IsNotEmpty({
    message: 'Product detail should not be empty',
    always: true,
  })
  detail: string;

  @IsString({
    message: 'Product legal must be a string',
    always: true,
  })
  @IsOptional({ always: true })
  legal: string;

  @IsString({
    message: 'Product videoType must be a string',
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
  })
  @IsOptional({
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
  })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  videoType: string;

  @IsString({
    message: 'Product videoId must be a string',
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
  })
  @IsOptional({
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
  })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  videoId: string;

  @IsArray({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsNotEmpty({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  images: ProductImageDto[];
}

export class TourTrashDto {
  @IsUUID('4', { always: true })
  @IsNotEmpty({ groups: [GroupTypeDto.CAN_UPDATE_ALL, GroupTypeDto.UPDATE_FOR_ACTIVE] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW] })
  id: string;

  @IsString({ always: true })
  @IsNotEmpty({ groups: [GroupTypeDto.CREATE_NEW] })
  @IsOptional({ groups: [GroupTypeDto.CAN_UPDATE_ALL, GroupTypeDto.UPDATE_FOR_ACTIVE] })
  title: string;

  @IsString({ always: true })
  @IsNotEmpty({ groups: [GroupTypeDto.CREATE_NEW] })
  @IsOptional({ groups: [GroupTypeDto.CAN_UPDATE_ALL, GroupTypeDto.UPDATE_FOR_ACTIVE] })
  description: string;

  @IsDateString({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @MinDateNow({
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
    message: `minimal allowed date for startDate is ${new Date()}`,
  })
  @IsNotEmpty({ groups: [GroupTypeDto.CREATE_NEW] })
  @IsOptional({ groups: [GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  startDate: string;

  @IsDateString({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsAfterDate('startDate', {
    message: 'The start date must be before the end date',
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
  })
  @MinDateNow({
    groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL],
    message: `minimal allowed date for startDate is ${new Date()}`,
  })
  @IsNotEmpty({ groups: [GroupTypeDto.CREATE_NEW] })
  @IsOptional({ groups: [GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  endDate: string;

  @IsEnum(PushAndSendTo, { groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  pushNotificationTo: number;

  @IsEnum(PushAndSendTo, { groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  sendEmailTo: number;

  @IsString({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  participationLevel: string;

  @IsNumber({}, { groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsNotEmpty({ groups: [GroupTypeDto.CREATE_NEW] })
  @IsOptional({ groups: [GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  remindPushNotificationFrequency: number;

  @IsBoolean({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  sendEmailAtLaunch: boolean;

  @IsBoolean({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  pushNotificationAtLaunch: boolean;

  @IsNumber({}, { groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @NotEquals(0)
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  timeToSendEmailBeforeEndDate: number;

  @IsNumber({}, { groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @NotEquals(0)
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  timeToPushNotificationBeforeEndDate: number;

  @IsNumber({}, { groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsOptional({ groups: [GroupTypeDto.CREATE_NEW, GroupTypeDto.CAN_UPDATE_ALL] })
  @IsEmpty({ groups: [GroupTypeDto.UPDATE_FOR_ACTIVE] })
  timeToAutoCloseAfterEndDate: number;

  @IsOptional({ always: true })
  product: ProductDto;

  @IsOptional({ always: true })
  countries?: string;

  @IsOptional({ always: true })
  options?: string;
}

export class TourTrashUpdateStatusDto {
  @IsUUID()
  @IsNotEmpty()
  id: string;
}
export class TourTrashArchiveFromDateDto {
  @IsNotEmpty()
  @IsISO8601()
  date: string;

  @IsOptional()
  isRollback: boolean;
}

export class TourTrashWinnerDto {
  @IsUUID()
  @IsOptional()
  userId: string;

  @IsUUID()
  @IsNotEmpty()
  tourTrashId: string;
}

export class TourTrashListDto {
  @IsEnum(TourTrashStatus)
  @IsOptional()
  status: TourTrashStatus;

  @IsDateString()
  @IsOptional()
  startDate: string;

  @IsDateString()
  @IsOptional()
  endDate: string;

  @IsString()
  @IsOptional()
  country: string;
}

export class JoinTouTrashDto {
  @IsUUID()
  @IsNotEmpty()
  id: string;
}

export class SelectRandomDto {
  @IsUUID()
  @IsNotEmpty()
  tourTrashId: string;

  @IsNumber()
  @IsOptional()
  @NotEquals(0)
  perPage: number;
}

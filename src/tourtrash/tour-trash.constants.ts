export const TOUR_TRASH_NOTIFICATION = {
  CLOSE_AND_WINNER: `Check your email to claim your prize.`,
  CLOSE_AND_NON_WINNER:
    'While you did not win this round, a new Tour Trash is opening soon. Be on the lookout for your next chance to win!',
  CLOSE_TOURTRASH: `A Tour Trash has been closed.`,
  TOUR_TRASH_OPEN: `Enter for a chance to win Team TaylorMade equipment. Tap to join.`,
  CANCEL_TOURTRASH: `We apologize for the inconvenience.`,
  REMINDER: 'Tap here for a chance to win.',
};

export const MESSAGE_NOTIFICATION = {
  TITLE: 'MyTaylorMade',
};

export const TOUR_TRASH_TITLE = {
  CLOSE_AND_WINNER: `Congratulations, You’re a Winner!`,
  CLOSE_AND_NON_WINNER: `Another Golfer's Treasure`,
  TOUR_TRASH_OPEN: `Tour Trash Now Open!`,
  CANCEL_TOURTRASH: 'Tour Trash Has Been Canceled',
  REMINDER: `Tour Trash Closing Soon!`,
};

export const DEFAULT_DELAY_TIME = {
  AUTO_SELECT_WINNER: 432000, // 5 days * 24 hours * 60 minutes * 60 seconds,
  SEND_EMAIL: 172800, // 2 days * 24 hours * 60 minutes * 60 seconds
  PUSH_NOTI: 7200, // 2 hours * 60 minutes * 60 seconds
};

export const SETTING_ADD_START_PUSH = 1; // 1 day
export const SETTING_SUBTRACT_END_PUSH = 2; // 1 day

export const TOUR_TRASH_MESSAGE_ERROR = {
  NOW_AFTER_START_DATE: `Minimal allowed date for startDate is ${new Date()}`,
  CAN_NOT_UPDATE_STATUS: (status: string) => `Can't ${status} this Tour Trash.`,
  PRODUCT_NOT_EXISTS: 'The product does not exists in the Tour Trash.',
  TOUR_TRASH_NOT_FOUND: 'The Tour Trash is not found.',
  TOUR_TRASH_NOT_ACTIVATED_YET: 'The Tour Trash is not activated yet.',
  WINNER_NOT_ASSIGNED: `Can't close this Tour Trash. A winner must be assigned first.`,
  INVALID_CLOSE: `Can't close the Tour Trash right now. Please try again later.`,
  CAN_NOT_UPDATE_TOURTRASH: (status: string) => `Can't update the ${status} Tour Trash `,
  PRODUCT_IMAGE_NOT_FOUND: 'The product image is not found',
  TOUR_TRASH_PRODUCT_ALREADY: 'The product has already existed in another Tour Trash.',
  OTHER_TOUR_TRASH_PRODUCT_ALREADY: 'Another product has already existed in this Tour Trash.',
  USER_NOT_FOUND: 'The user is not found',
  NOW_BEFORE_START_DATE: 'The Tour Trash has not started yet.',
  NOW_AFTER_END_DATE: 'The Tour Trash has ended.',
  ALREADY_JOINED_TOUR_TRASH: 'You have already joined the Tour Trash.',
  SUBSCRIPTION_DENIED: 'You cannot join the Tour Trash. Please upgrade your subscription.',
  NOT_PARTICIPATE: `You have not join the Tour Trash.`,
  NO_USER_JOINED: 'No one has joined the Tour Trash yet.',
  TIME_TO_SELECT_WINNER_INVALID: `Can't choose a winner during the event.`,
};

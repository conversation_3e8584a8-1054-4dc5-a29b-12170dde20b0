import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { validate } from 'class-validator';
import * as _ from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { Brackets, EntityManager, In, LessThan, LessThanOrEqual, Repository, getManager } from 'typeorm';
import { validate as uuidValidate, v4 } from 'uuid';
import { Plan } from 'src/accesscode/access-code.type';
import { PLANS } from 'src/utils/plans';
import { isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { ProductImageEntity } from './entities/product-image.entity';
import { ProductEntity, ProductStatus } from './entities/product.entity';
import { TourTrashEntity, TourTrashStatus } from './entities/tour-trash.entity';
import { UserTourTrashEntity } from './entities/user-tour-trash.entity';
import { DEFAULT_DELAY_TIME, TOUR_TRASH_MESSAGE_ERROR } from './tour-trash.constants';
import {
  EnteredEmailTourTrash,
  GroupTypeDto,
  InternalError,
  PickTourTrashWinner,
  ProductDto,
  ProductImageDto,
  SaveTourTrashResponse,
  SelectRandomDto,
  SelectRandomWinner,
  TourTrashActiveAndSoonResponse,
  TourTrashArchiveFromDateDto,
  TourTrashDto,
  TourTrashListDto,
  TourTrashListResponse,
  TourTrashRecord,
  TourTrashUpdateStatusDto,
  TourTrashWinnerDto,
  UpdateStatusTourTrash,
} from './tour-trash.type';

@Injectable()
export class TourTrashService {
  private readonly logger = new Logger(TourTrashService.name);
  constructor(
    private readonly cdmService: CdmService,
    private readonly config: ConfigService,
    @InjectRepository(TourTrashEntity)
    private readonly tourTrashRepo: Repository<TourTrashEntity>,
    @InjectRepository(UserTourTrashEntity)
    private readonly userTourTrashRepo: Repository<UserTourTrashEntity>,
    @InjectRepository(ProductEntity)
    private readonly productRepo: Repository<ProductEntity>,
    @InjectRepository(ProductImageEntity)
    private readonly productImageRepo: Repository<ProductImageEntity>,
    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
    private readonly klaviyoService: KlaviyoService,
    private readonly apiVersionsService: ApiVersionsService
  ) {
    this.config = config;
  }

  async getActiveTourTrash(
    userId: string,
    take = 10,
    page = 1,
    status: string = TourTrashStatus.ACTIVE,
    country?: string
  ): Promise<TourTrashActiveAndSoonResponse> {
    const NTake = Number(take);
    const NPage = Number(page);
    const filterStatus = status.split(',');
    let conditionCountry = '1=1';
    if (country) {
      if (isUSCountry(country)) {
        conditionCountry = `(countries LIKE '%${country}%' OR countries IS NULL)`;
      } else {
        conditionCountry = `(countries LIKE '%${country}%')`;
      }
    } else {
      conditionCountry = `(countries LIKE '%US%' OR countries IS NULL)`;
    }
    const [tourTrashList, total] = await this.tourTrashRepo
      .createQueryBuilder('t')
      .where('t.status IN (:...filterStatus)')
      .andWhere(conditionCountry)
      .leftJoinAndSelect('t.product', 'product')
      .leftJoinAndSelect('product.images', 'images')
      .orderBy('t.status')
      .addOrderBy('t.startDate')
      .setParameters({
        filterStatus,
        active: TourTrashStatus.ACTIVE,
      })
      .take(NTake)
      .offset((NPage - 1) * NTake)
      .getManyAndCount();

    if (total === 0) {
      return {
        total: 0,
        data: [],
      };
    }

    const activeTourTrashList = tourTrashList.filter((item) => item.status === TourTrashStatus.ACTIVE);
    const joinedTourTrash = {};

    if (activeTourTrashList.length > 0) {
      for (const tourTrash of activeTourTrashList) {
        joinedTourTrash[tourTrash.uuid] =
          (await this.userTourTrashRepo.count({
            where: { tourTrashUuid: tourTrash.uuid, userId },
          })) > 0;
      }
    }

    return {
      total,
      take: NTake,
      page: NPage,
      data: tourTrashList.map((item) => {
        let isJoined: boolean | undefined;
        if (item.status === TourTrashStatus.ACTIVE) {
          isJoined = joinedTourTrash[item.uuid];
        }

        if (item.participationLevel) {
          item.participationLevel = this.forceMapParticipationLevel(item.participationLevel).join(',');
        }
        return this.toRecord(item, item.product, isJoined);
      }),
    };
  }

  private forceMapParticipationLevel(participationLevel) {
    try {
      const levels = participationLevel.split(',').map((v) => Number(v));
      if (levels.includes(Plan.CHAMPION) && !levels.includes(Plan.LEGEND)) {
        levels.push(Plan.LEGEND);
      }
      if (levels.includes(Plan.LEGEND) && !levels.includes(Plan.CHAMPION)) {
        levels.push(Plan.CHAMPION);
      }
      return levels;
    } catch (error) {
      return [];
    }
  }

  async joinTourTrash(userId: string, tourTrashId: string) {
    const user = await this.getUserById(userId);
    const tourTrash = await this.getTourTrashActiveById(tourTrashId, ['product', 'userTourTrashes']);
    if (!tourTrash) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_ACTIVATED_YET,
      };
    }

    const nowDate = moment();
    const startDate = moment(new Date(tourTrash.startDate));
    const endDate = moment(new Date(tourTrash.endDate));
    if (nowDate.isBefore(startDate)) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_TIME_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.NOW_BEFORE_START_DATE,
      };
    }
    if (nowDate.isAfter(endDate)) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_TIME_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.NOW_AFTER_END_DATE,
      };
    }
    const permission = await this.cdmService.getPermission(user.email);
    const myTMSubscriptionLevel = permission.myTMSubscriptionLevel ?? 0;
    let participationLevels = [];
    if (tourTrash.participationLevel) {
      const levels = this.forceMapParticipationLevel(tourTrash.participationLevel);
      participationLevels = levels;
    }

    if (!tourTrash.participationLevel || participationLevels.includes(myTMSubscriptionLevel)) {
      if (
        tourTrash.userTourTrashes.length > 0 &&
        tourTrash.userTourTrashes.findIndex((x) => x.userId && x.userId.toUpperCase() === userId.toUpperCase()) > -1
      ) {
        return {
          internalErrorCode: ERROR_CODES.ALREADY_JOINED_TOUR_TRASH,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.ALREADY_JOINED_TOUR_TRASH,
        };
      }
      const userTourTrashEntity = new UserTourTrashEntity();
      userTourTrashEntity.userId = user.id;
      userTourTrashEntity.tourTrashUuid = tourTrash.uuid;
      userTourTrashEntity.firstName = permission.firstName;
      userTourTrashEntity.lastName = permission.lastName;
      userTourTrashEntity.subscriptionLevel = !_.isNil(myTMSubscriptionLevel) ? Number(myTMSubscriptionLevel) : 0;
      // tourTrash.userTourTrashes.push(userTourTrashEntity);
      await this.userTourTrashRepo.save(userTourTrashEntity);
    } else {
      return {
        internalErrorCode: ERROR_CODES.SUBSCRIPTION_DENIED,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.SUBSCRIPTION_DENIED,
      };
    }

    // await this.tourTrashRepo.save(tourTrash);

    this.klaviyoService.track(user.email, KlaviyoTrackEvents.ENTERED_TOUR_TRASH, {
      start_date: startDate,
      end_date: endDate,
      mytm_tour_trash_title: tourTrash.title,
    });

    return { success: true };
  }

  async unJoinTourTrash(userId: string) {
    const userTourTrash = await this.userTourTrashRepo.findOne({
      where: {
        userId,
      },
    });
    if (!userTourTrash) {
      return { success: false, msg: 'Can not found user Tourtrash!' };
    }

    await this.userTourTrashRepo.delete({
      userId,
    });

    return { success: true };
  }

  async getTourtrashList(take = 20, page = 1, optionsSearch?: TourTrashListDto): Promise<TourTrashListResponse> {
    if (take >= 500) {
      take = 500;
    }
    const NTake = Number(take);
    const NPage = Number(page);
    const queryDto = new TourTrashListDto();
    queryDto.status = optionsSearch?.status;
    queryDto.startDate = optionsSearch?.startDate;
    queryDto.endDate = optionsSearch?.endDate;

    const error = await validate(queryDto);

    if (error.length > 0) {
      let messErr = [];
      error.forEach((value) => {
        messErr = messErr.concat(Object.values(value.constraints));
      });
      throw new BadRequestException(messErr);
    }

    const query = this.tourTrashRepo
      .createQueryBuilder('t')
      .leftJoinAndSelect('t.product', 'product')
      .leftJoinAndSelect('product.images', 'images')
      .leftJoinAndSelect('t.winnerUser', 'winnerUser')
      .leftJoinAndSelect('t.enteredUsers', 'enteredUsers');
    if (!queryDto.status) {
      query.andWhere('t.status IN (:...activeStatus)', {
        activeStatus: [TourTrashStatus.ACTIVE, TourTrashStatus.CLOSED, TourTrashStatus.COMING_SOON],
      });
    } else {
      query.andWhere('t.status NOT IN (:...deleted)', { deleted: [TourTrashStatus.DELETED] });
    }
    const queryOptionalOptionsSearch = optionsSearch?.status
      ? query.andWhere('t.status = :status', { status: optionsSearch.status })
      : query;
    const queryOptionalOptionsSearchStartDate = optionsSearch?.startDate
      ? queryOptionalOptionsSearch.andWhere('t.startDate >= :startDate', { startDate: optionsSearch.startDate })
      : queryOptionalOptionsSearch;
    let queryOptionalOptionsSearchEndDate = optionsSearch?.endDate
      ? queryOptionalOptionsSearchStartDate.andWhere('t.endDate <= :endDate', { endDate: optionsSearch.endDate })
      : queryOptionalOptionsSearchStartDate;

    let conditionCountry = ` 1 = 1 `;
    const country = optionsSearch?.country;
    if (country) {
      if (isUSCountry(country)) {
        conditionCountry += ` AND (countries LIKE '%${country}%' OR countries IS NULL) `;
      } else {
        conditionCountry += ` AND countries LIKE '%${country}%' `;
      }
    } else {
      conditionCountry = `(countries LIKE '%US%' OR countries IS NULL) `;
    }
    queryOptionalOptionsSearchEndDate = queryOptionalOptionsSearchEndDate.andWhere(conditionCountry);

    const [tourTrashList, total] = await queryOptionalOptionsSearchEndDate
      .select([
        't',
        'product',
        'images',
        'winnerUser.email',
        'winnerUser.cdmUID',
        'winnerUser.id',
        'enteredUsers.id',
        'countries',
        'options',
      ])
      .orderBy('t.status')
      .addOrderBy('t.createdAt', 'DESC')
      .setParameter('active', TourTrashStatus.ACTIVE)
      .take(NTake)
      .skip((NPage - 1) * NTake)
      .getManyAndCount();

    if (total === 0) {
      return {
        total: 0,
        data: [],
      };
    }

    const emails = tourTrashList.map((val) => {
      if (val.winnerUser) {
        return val.winnerUser.email;
      }
    });

    const users = await this.cdmService.getConsumerByEmails(emails);

    return {
      total,
      take: NTake,
      page: NPage,
      data: tourTrashList.map((item) => {
        let winner = null;
        if (item.winnerUser) {
          const user =
            users &&
            users.length > 0 &&
            users.find((x: any) => x.id.toUpperCase() === (item?.winnerUser.cdmUID || '').toUpperCase());
          winner = {
            id: item.winnerUser.id,
            email: item.winnerUser.email,
            fullName: this.getFullNameFromUser(user),
          };
        }

        return {
          ...this.toRecord(item, item.product),
          ...{ winner },
          pushNotificationTo: item?.pushNotificationTo,
          sendEmailTo: item?.sendEmailTo,
          sendEmailAtLaunch: item.sendEmailAtLaunch,
          pushNotificationAtLaunch: item.pushNotificationAtLaunch,
          remindPushNotificationFrequency: item.remindPushNotificationFrequency,
          timeToSendEmailBeforeEndDate: item.timeToSendEmailBeforeEndDate,
          timeToAutoCloseAfterEndDate: item.timeToAutoCloseAfterEndDate,
          timeToPushNotificationBeforeEndDate: item.timeToPushNotificationBeforeEndDate,
          enteredUsers: item.enteredUsers && item.enteredUsers.length > 0,
        };
      }),
    };
  }

  async pickTourTrashWinner(data: TourTrashWinnerDto, userId: string): Promise<PickTourTrashWinner> {
    const tourTrash = await this.getTourTrashActiveById(data.tourTrashId, []);
    if (!tourTrash) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_ACTIVATED_YET,
      };
    }
    const totalTourTrashJoinedUser = await this.userTourTrashRepo.count({ where: { tourTrashUuid: tourTrash.uuid } });
    if (totalTourTrashJoinedUser === 0) {
      return {
        internalErrorCode: ERROR_CODES.NO_USER_JOINED,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.NO_USER_JOINED,
      };
    }
    if (moment().isBefore(moment(tourTrash.endDate))) {
      return {
        internalErrorCode: ERROR_CODES.TIME_TO_SELECT_WINNER_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TIME_TO_SELECT_WINNER_INVALID,
      };
    }
    let userWinner: UserTourTrashEntity;
    if (!data?.userId) {
      const randomPick = Math.floor(Math.random() * totalTourTrashJoinedUser);
      const userWinners = await this.userTourTrashRepo.find({
        where: { tourTrashUuid: tourTrash.uuid },
        skip: randomPick,
        take: 1,
      });
      userWinner = userWinners[0];
      tourTrash.winnerUserId = userWinner.userId;
    } else {
      const userWinners = await this.userTourTrashRepo.find({
        where: { tourTrashUuid: tourTrash.uuid, userId: data.userId },
      });
      if (userWinners.length === 0) {
        return {
          internalErrorCode: ERROR_CODES.NOT_PARTICIPATE,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.NOT_PARTICIPATE,
        };
      }
      userWinner = userWinners[0];
      tourTrash.winnerUserId = data.userId;
    }
    const winner = await this.getUserById(tourTrash.winnerUserId);
    tourTrash.updatedBy = userId;
    await this.tourTrashRepo.save(tourTrash);
    return {
      winner: {
        id: winner.id,
        email: winner.email,
        fullName: this.getFullNameFromUser(userWinner),
      },
    };
  }

  async selectRandomWinner(data: SelectRandomDto): Promise<SelectRandomWinner> {
    const { perPage, tourTrashId } = data;
    const tourTrash = await this.getTourTrashActiveById(data.tourTrashId, []);
    if (!tourTrash) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_ACTIVATED_YET,
      };
    }
    const totalTourTrashJoinedUser = await this.userTourTrashRepo.count({ where: { tourTrashUuid: tourTrash.uuid } });
    if (totalTourTrashJoinedUser === 0) {
      return {
        internalErrorCode: ERROR_CODES.NO_USER_JOINED,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.NO_USER_JOINED,
      };
    }
    const randomPick = Math.floor(Math.random() * totalTourTrashJoinedUser);
    const userWinners = await this.userTourTrashRepo.find({
      where: { tourTrashUuid: tourTrash.uuid },
      skip: randomPick,
      take: 1,
    });
    const userRandom = userWinners[0];
    const existOnPage = Math.ceil((randomPick + 1) / (perPage || 10));
    const user = await this.getUserById(userRandom.userId);
    return {
      tourTrashId,
      winner: {
        id: user.id,
        email: user.email,
        fullName: this.getFullNameFromUser(userRandom),
      },
      existOnPage,
      perPage: perPage || 10,
    };
  }

  async getEnteredEmailTourTrash(
    tourTrashId: string,
    take = 10,
    page = 1,
    searchOptions?: { emailOrName?: string }
  ): Promise<EnteredEmailTourTrash> {
    if (!uuidValidate(tourTrashId)) {
      throw new BadRequestException('tourTrashId must be an UUID');
    }

    const query = this.tourTrashRepo
      .createQueryBuilder('t')
      .leftJoinAndSelect('t.userTourTrashes', 'userTourTrashes')
      .leftJoinAndSelect('userTourTrashes.user', 'user')
      .where('uuid = :tourTrashId', { tourTrashId })
      .orderBy('userTourTrashes.createdAt', 'DESC');

    let tourTrash = await query.getOne();

    if (!tourTrash) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
      };
    }

    if (searchOptions?.emailOrName) {
      const strSearch = `%${searchOptions.emailOrName || ''}%`;
      tourTrash = await query
        .andWhere(
          new Brackets((qb) => {
            qb.where('user.email like :strSearch', { strSearch })
              .orWhere('userTourTrashes.firstName like :strSearch', { strSearch })
              .orWhere('userTourTrashes.lastName like :strSearch', { strSearch })
              .orWhere(`CONCAT(userTourTrashes.firstName,' ',userTourTrashes.lastName) like :strSearch`, { strSearch })
              .orWhere(`CONCAT(userTourTrashes.lastName,' ',userTourTrashes.firstName) like :strSearch`, { strSearch });
          })
        )
        .getOne();
    }

    if (!tourTrash || tourTrash.userTourTrashes.length === 0) {
      return {
        total: 0,
        data: [],
      };
    }
    let userTourTrashes = tourTrash.userTourTrashes;
    if (tourTrash?.winnerUserId) {
      const indexOfWinner = userTourTrashes.findIndex((x) => x.userId === tourTrash.winnerUserId);
      if (indexOfWinner >= 0) {
        const deepUsers = [...userTourTrashes];
        deepUsers.splice(indexOfWinner, 1);
        userTourTrashes = [...[userTourTrashes[indexOfWinner]], ...deepUsers];
      }
    }
    const enteredUsers = userTourTrashes.slice((page - 1) * take, page * take).filter((r) => r);
    if (!enteredUsers || enteredUsers.length === 0) {
      return {
        total: 0,
        data: [],
      };
    }
    const emails = enteredUsers.map((user) => user?.user?.email);
    const users = await this.cdmService.getConsumerByEmails(emails);
    return {
      total: tourTrash.userTourTrashes.length,
      take: Number(take),
      page: Number(page),
      data: enteredUsers.map((item) => {
        const user =
          users &&
          users.length > 0 &&
          users.find((x: any) => x.id.toUpperCase() === (item?.user.cdmUID || '').toUpperCase());
        return {
          id: item.user.id,
          email: item.user.email,
          fullName: this.getFullNameFromUser(item),
          myTMSubscriptionLevel: _.isNumber(item?.subscriptionLevel)
            ? item?.subscriptionLevel
            : _.isNumber(user?.myTMSubscriptionLevel)
            ? user?.myTMSubscriptionLevel
            : null,
          enteredDate: item.createdAt,
        };
      }),
    };
  }

  async activateTourTrash(payload: TourTrashUpdateStatusDto, userId: string): Promise<UpdateStatusTourTrash> {
    const tourTrashNeedActivate = await this.getTourTrashById(payload.id, ['product', 'product.images']);
    if (!tourTrashNeedActivate) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
      };
    }

    const validation = this.validationActivateTourTrash(tourTrashNeedActivate);
    if (validation) {
      return validation;
    }

    tourTrashNeedActivate.status = TourTrashStatus.ACTIVE;
    tourTrashNeedActivate.updatedBy = userId;
    await this.tourTrashRepo.save(tourTrashNeedActivate);
    await this.apiVersionsService.updateVersion(
      FEATURE_KEY_VERSION.REWARD_TOUR_TRASH,
      tourTrashNeedActivate?.countries,
      userId
    );

    return {
      success: true,
      tourTrash: tourTrashNeedActivate,
    };
  }

  async closeTourTrash(payload: TourTrashUpdateStatusDto, userId: string): Promise<UpdateStatusTourTrash> {
    const tourTrashNeedClose = await this.getTourTrashById(payload.id, [
      'enteredUsers',
      'winnerUser',
      'product',
      'product.images',
    ]);
    if (!tourTrashNeedClose) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
      };
    }

    const validation = this.validationCloseTourTrash(tourTrashNeedClose);
    if (validation) {
      return validation;
    }

    tourTrashNeedClose.status = TourTrashStatus.CLOSED;
    tourTrashNeedClose.updatedBy = userId;
    await this.tourTrashRepo.update(
      { uuid: tourTrashNeedClose.uuid },
      { status: TourTrashStatus.CLOSED, updatedBy: userId }
    );
    await this.apiVersionsService.updateVersion(
      FEATURE_KEY_VERSION.REWARD_TOUR_TRASH,
      tourTrashNeedClose?.countries,
      userId
    );

    return {
      success: true,
      tourTrash: tourTrashNeedClose,
    };
  }

  async cancelTourTrash(payload: TourTrashUpdateStatusDto, userId: string): Promise<UpdateStatusTourTrash> {
    const tourTrashNeedCancel = await this.getTourTrashById(payload.id, ['enteredUsers', 'product', 'product.images']);

    if (!tourTrashNeedCancel) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
      };
    }

    if (tourTrashNeedCancel.status !== TourTrashStatus.ACTIVE) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_STATUS_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.CAN_NOT_UPDATE_STATUS('cancel'),
      };
    }

    tourTrashNeedCancel.enteredUsers = [];
    tourTrashNeedCancel.winnerUserId = null;
    tourTrashNeedCancel.isCheckOpen = false;
    tourTrashNeedCancel.isCheckReminderPushNotification = false;
    tourTrashNeedCancel.isCheckReminderSendMail = false;
    tourTrashNeedCancel.isCheckSelectWinner = false;

    tourTrashNeedCancel.status = TourTrashStatus.DRAFT;
    tourTrashNeedCancel.updatedBy = userId;
    const startDate = moment(new Date(tourTrashNeedCancel.startDate));
    await this.tourTrashRepo.save(tourTrashNeedCancel);
    await this.apiVersionsService.updateVersion(
      FEATURE_KEY_VERSION.REWARD_TOUR_TRASH,
      tourTrashNeedCancel?.countries,
      userId
    );

    return {
      success: true,
      isSendPush: moment().isAfter(startDate),
      tourTrash: tourTrashNeedCancel,
    };
  }

  async deleteTourTrash(payload: TourTrashUpdateStatusDto, userId: string): Promise<UpdateStatusTourTrash> {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const tourTrashNeedDelete = await this.getTourTrashById(payload.id, []);

      if (!tourTrashNeedDelete) {
        return {
          internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
        };
      }

      tourTrashNeedDelete.status = TourTrashStatus.DELETED;
      tourTrashNeedDelete.deletedBy = userId;
      await this.tourTrashRepo.softRemove(tourTrashNeedDelete);

      // ADD ACTIVITY LOGS
      await CommonSubscriber.activityLogCommandDelete(
        {
          entity: {
            uuid: payload.id,
            deletedBy: userId,
          },
          manager: transactionalEntityManager,
          databaseEntity: tourTrashNeedDelete,
        },
        TourTrashEntity.name
      );
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.REWARD_TOUR_TRASH,
        tourTrashNeedDelete?.countries,
        userId
      );

      return { success: true };
    });

    return res;
  }

  async archiveTourTrash(payload: TourTrashUpdateStatusDto, userId: string): Promise<UpdateStatusTourTrash> {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const tourTrashNeedArchive = await this.getTourTrashById(payload.id, []);

      if (!tourTrashNeedArchive) {
        return {
          internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
        };
      }
      if (tourTrashNeedArchive.status !== TourTrashStatus.CLOSED) {
        return {
          internalErrorCode: ERROR_CODES.TOUR_TRASH_STATUS_INVALID,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.CAN_NOT_UPDATE_STATUS('archive'),
        };
      }
      tourTrashNeedArchive.status = TourTrashStatus.ARCHIVED;
      tourTrashNeedArchive.updatedBy = userId;
      await this.tourTrashRepo.update(
        { uuid: tourTrashNeedArchive.uuid },
        { status: TourTrashStatus.ARCHIVED, updatedBy: userId }
      );

      // ADD ACTIVITY LOGS
      await CommonSubscriber.activityLogCommandDelete(
        {
          entity: {
            uuid: payload.id,
            updateBy: userId,
          },
          manager: transactionalEntityManager,
          databaseEntity: tourTrashNeedArchive,
        },
        TourTrashEntity.name
      );
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.REWARD_TOUR_TRASH,
        tourTrashNeedArchive?.countries,
        userId
      );
      return { success: true };
    });

    return res;
  }

  async archiveTourTrashToDate(payload: TourTrashArchiveFromDateDto, userId: string): Promise<UpdateStatusTourTrash> {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const status = payload.isRollback ? TourTrashStatus.ARCHIVED : TourTrashStatus.CLOSED;
      const tourTrashesNeedArchive = await this.tourTrashRepo.find({
        where: { status: status, startDate: LessThanOrEqual(payload.date) },
        select: ['uuid', 'status', 'title'],
      });

      if (!tourTrashesNeedArchive) {
        return {
          internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
        };
      }
      const listUpdateSuccess = [];
      const listUpdateFail = [];
      const statusUpdate = payload.isRollback ? TourTrashStatus.CLOSED : TourTrashStatus.ARCHIVED;
      for (const tourTrash of tourTrashesNeedArchive) {
        try {
          await this.tourTrashRepo.update({ uuid: tourTrash.uuid }, { status: statusUpdate, updatedBy: userId });

          // ADD ACTIVITY LOGS
          await CommonSubscriber.activityLogCommandDelete(
            {
              entity: {
                uuid: tourTrash.uuid,
                updateBy: userId,
              },
              manager: transactionalEntityManager,
              databaseEntity: tourTrash,
            },
            TourTrashEntity.name
          );
          listUpdateSuccess.push({ title: tourTrash.title, uuid: tourTrash.uuid });
        } catch (error) {
          console.error(error);
          listUpdateFail.push({ title: tourTrash.title, uuid: tourTrash.uuid });
        }
      }

      return { success: true, listUpdateSuccess, listUpdateFail };
    });

    return res;
  }

  private validationActivateTourTrash(recordTourTrash: TourTrashEntity): InternalError {
    if (moment().isAfter(moment(new Date(recordTourTrash.startDate)))) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_TIME_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.NOW_AFTER_START_DATE,
      };
    }

    if (recordTourTrash.status !== TourTrashStatus.DRAFT) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_STATUS_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.CAN_NOT_UPDATE_STATUS('activate'),
      };
    }

    if (!recordTourTrash.product) {
      return {
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.PRODUCT_NOT_EXISTS,
      };
    }

    return null;
  }

  private validationCloseTourTrash(recordTourTrash: TourTrashEntity): InternalError {
    if (recordTourTrash.status !== TourTrashStatus.ACTIVE) {
      return {
        internalErrorCode: ERROR_CODES.TOUR_TRASH_STATUS_INVALID,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.CAN_NOT_UPDATE_STATUS('close'),
      };
    }
    if (!recordTourTrash.winnerUserId) {
      if (recordTourTrash.enteredUsers.length > 0) {
        return {
          internalErrorCode: ERROR_CODES.NO_WINNER_ASSIGNED_YET,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.WINNER_NOT_ASSIGNED,
        };
      }

      const endDate = moment(new Date(recordTourTrash.endDate));
      if (moment().isBefore(endDate)) {
        return {
          internalErrorCode: ERROR_CODES.TOUR_TRASH_TIME_INVALID,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.INVALID_CLOSE,
        };
      }
    }

    return null;
  }

  async saveTourTrash(record: TourTrashDto, userId: string): Promise<SaveTourTrashResponse> {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      let tourTrashNeedUpdate: TourTrashEntity;
      let groups: string[] = [];
      if (record?.id) {
        tourTrashNeedUpdate = await this.getTourTrashById(record.id, ['product', 'product.images']);
        if (!tourTrashNeedUpdate) {
          return {
            internalErrorCode: ERROR_CODES.TOUR_TRASH_NOT_FOUND,
            errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_NOT_FOUND,
          };
        }
        if (this.isComingSoon(tourTrashNeedUpdate) || tourTrashNeedUpdate.status === TourTrashStatus.DRAFT) {
          await this.validateTourTrash(
            {
              ...record,
              startDate: new Date(record?.startDate || tourTrashNeedUpdate.startDate).toISOString(),
              endDate: new Date(record?.endDate || tourTrashNeedUpdate.endDate).toISOString(),
            },
            [GroupTypeDto.CAN_UPDATE_ALL]
          );
          groups = [GroupTypeDto.CAN_UPDATE_ALL];
        } else if (tourTrashNeedUpdate.status === TourTrashStatus.ACTIVE) {
          await this.validateTourTrash(record, [GroupTypeDto.UPDATE_FOR_ACTIVE]);
          groups = [GroupTypeDto.UPDATE_FOR_ACTIVE];
        } else {
          return {
            internalErrorCode: ERROR_CODES.TOUR_TRASH_STATUS_INVALID,
            errorMessage: TOUR_TRASH_MESSAGE_ERROR.CAN_NOT_UPDATE_TOURTRASH(tourTrashNeedUpdate.status),
          };
        }
      } else {
        await this.validateTourTrash(record, [GroupTypeDto.CREATE_NEW]);
        groups = [GroupTypeDto.CREATE_NEW];
      }
      if (record?.product && record.product?.id) {
        await this.checkUpdateProduct(record.product.id, record?.id);
      }

      const tourTrashRecordEntity = this.createTourTrashEntity(record, tourTrashNeedUpdate, userId);

      const savedTourTrashRecord = await transactionalEntityManager.save(TourTrashEntity, tourTrashRecordEntity);

      let savedProduct: ProductEntity;
      if (savedTourTrashRecord) {
        if (record?.product) {
          await this.validateProduct(record?.product, groups);
          const recordProductEntity = await this.createProductEntity(
            savedTourTrashRecord.uuid,
            record?.product,
            tourTrashNeedUpdate && tourTrashNeedUpdate.product
          );
          savedProduct = await transactionalEntityManager.save(ProductEntity, recordProductEntity);
        } else {
          savedProduct = tourTrashNeedUpdate.product;
        }
      }

      if (record?.id) {
        const oldRecord = this.toRawRecord(tourTrashNeedUpdate, tourTrashNeedUpdate.product);
        const newRecord = this.toRawRecord(savedTourTrashRecord, savedProduct);
        if (!_.isEqual(oldRecord, newRecord)) {
          await CommonSubscriber.activityLogCommandUpdate(
            {
              entity: {
                uuid: record?.id,
                updatedBy: userId,
              },
              manager: transactionalEntityManager,
            },
            TourTrashEntity.name,
            oldRecord,
            newRecord
          );
        }
        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.REWARD_TOUR_TRASH, record?.countries, userId);
      }
      return {
        record: this.toRecord(savedTourTrashRecord, savedProduct),
        tourTrash: savedTourTrashRecord,
        product: savedProduct,
      };
    });

    return res;
  }

  private createTourTrashEntity(
    record: TourTrashDto,
    tourTrashNeedUpdate: TourTrashEntity,
    userId: string
  ): TourTrashEntity {
    const recordTourTrashSave = new TourTrashEntity();
    recordTourTrashSave.uuid = (record?.id || v4()).toUpperCase();
    recordTourTrashSave.status = record?.id ? tourTrashNeedUpdate.status : TourTrashStatus.DRAFT;
    recordTourTrashSave.title = record?.title || tourTrashNeedUpdate.title;
    recordTourTrashSave.description = record?.description || tourTrashNeedUpdate.description;
    recordTourTrashSave.startDate = new Date(record?.startDate || tourTrashNeedUpdate.startDate);
    recordTourTrashSave.endDate = new Date(record?.endDate || tourTrashNeedUpdate.endDate);
    recordTourTrashSave.sendEmailTo = _.isNumber(record?.sendEmailTo)
      ? record?.sendEmailTo
      : tourTrashNeedUpdate && tourTrashNeedUpdate.sendEmailTo;
    recordTourTrashSave.pushNotificationTo = _.isNumber(record?.pushNotificationTo)
      ? record?.pushNotificationTo
      : tourTrashNeedUpdate && tourTrashNeedUpdate.pushNotificationTo;
    recordTourTrashSave.participationLevel =
      record?.participationLevel || (tourTrashNeedUpdate && tourTrashNeedUpdate.participationLevel);
    recordTourTrashSave.remindPushNotificationFrequency = _.isNumber(record?.remindPushNotificationFrequency)
      ? record.remindPushNotificationFrequency
      : tourTrashNeedUpdate && tourTrashNeedUpdate.remindPushNotificationFrequency;

    recordTourTrashSave.sendEmailAtLaunch = !_.isNil(record?.sendEmailAtLaunch)
      ? record?.sendEmailAtLaunch
      : tourTrashNeedUpdate && tourTrashNeedUpdate.sendEmailAtLaunch;

    recordTourTrashSave.pushNotificationAtLaunch = !_.isNil(record?.pushNotificationAtLaunch)
      ? record?.pushNotificationAtLaunch
      : tourTrashNeedUpdate && tourTrashNeedUpdate.pushNotificationAtLaunch;

    recordTourTrashSave.timeToSendEmailBeforeEndDate = _.isNumber(record?.timeToSendEmailBeforeEndDate)
      ? record?.timeToSendEmailBeforeEndDate
      : tourTrashNeedUpdate
      ? tourTrashNeedUpdate.timeToSendEmailBeforeEndDate
      : DEFAULT_DELAY_TIME.SEND_EMAIL;
    recordTourTrashSave.timeToPushNotificationBeforeEndDate = _.isNumber(record?.timeToPushNotificationBeforeEndDate)
      ? record?.timeToPushNotificationBeforeEndDate
      : tourTrashNeedUpdate
      ? tourTrashNeedUpdate.timeToPushNotificationBeforeEndDate
      : DEFAULT_DELAY_TIME.PUSH_NOTI;
    recordTourTrashSave.timeToAutoCloseAfterEndDate = _.isNumber(record?.timeToAutoCloseAfterEndDate)
      ? record?.timeToAutoCloseAfterEndDate
      : tourTrashNeedUpdate
      ? tourTrashNeedUpdate.timeToAutoCloseAfterEndDate
      : DEFAULT_DELAY_TIME.AUTO_SELECT_WINNER;

    if (!tourTrashNeedUpdate) {
      recordTourTrashSave.createdBy = userId;
    } else {
      recordTourTrashSave.updatedBy = userId;
    }
    if (record?.countries) {
      recordTourTrashSave.countries = record?.countries;
    }

    if (record.options) {
      recordTourTrashSave.options = JSON.stringify(record.options);
    }

    return recordTourTrashSave;
  }

  private async validateTourTrash(record: TourTrashDto, groupsValidate: string[]) {
    const recordDto = new TourTrashDto();
    recordDto.id = record?.id;
    recordDto.title = record?.title;
    recordDto.description = record?.description;
    recordDto.startDate = record?.startDate;
    recordDto.endDate = record?.endDate;
    recordDto.pushNotificationTo = record?.pushNotificationTo;
    recordDto.sendEmailTo = record?.sendEmailTo;
    recordDto.participationLevel = record?.participationLevel;
    recordDto.remindPushNotificationFrequency = record?.remindPushNotificationFrequency;
    recordDto.sendEmailAtLaunch = record?.sendEmailAtLaunch;
    recordDto.pushNotificationAtLaunch = record?.pushNotificationAtLaunch;
    recordDto.timeToSendEmailBeforeEndDate = record?.timeToSendEmailBeforeEndDate;
    recordDto.timeToPushNotificationBeforeEndDate = record?.timeToPushNotificationBeforeEndDate;
    recordDto.timeToAutoCloseAfterEndDate = record?.timeToAutoCloseAfterEndDate;
    recordDto.product = record?.product;

    const error = await validate(recordDto, {
      groups: groupsValidate,
    });

    if (error.length > 0) {
      let messErr = [];
      error.forEach((value) => {
        messErr = messErr.concat(Object.values(value.constraints));
      });
      throw new BadRequestException(messErr);
    }
  }

  private async createProductEntity(tourTrashUuid: string, product: ProductDto, productNeedUpdate: ProductEntity) {
    const recordProductEntity = new ProductEntity();
    let uuid: string;
    if (product?.id) {
      uuid = product.id;
    } else if (productNeedUpdate) {
      uuid = productNeedUpdate.uuid;
    }

    recordProductEntity.uuid = (uuid || v4()).toUpperCase();
    recordProductEntity.tourTrashUuid = (tourTrashUuid || v4()).toUpperCase();
    recordProductEntity.name = product.name;
    recordProductEntity.status = product.status || ProductStatus.ENABLE;
    recordProductEntity.detail = product.detail;
    recordProductEntity.legal = product.legal;
    recordProductEntity.videoType = product?.videoType;
    recordProductEntity.videoId = product?.videoId;
    recordProductEntity.images = product?.images
      ? await Promise.all(
          product?.images.map(async (item) => {
            await this.validateProductImage(item);
            const productImageEntityRecord = new ProductImageEntity();
            if (item?.id) {
              await this.getProductImage(item.id, recordProductEntity.uuid);
              productImageEntityRecord.uuid = item?.id.toUpperCase();
            } else {
              if (productNeedUpdate && productNeedUpdate.images.find((x) => x.imageUrl === item.imageUrl)) {
                productImageEntityRecord.uuid = productNeedUpdate.images.find((x) => x.imageUrl === item.imageUrl).uuid;
              } else {
                productImageEntityRecord.uuid = v4().toUpperCase();
              }
            }
            productImageEntityRecord.productUuid = recordProductEntity.uuid;
            productImageEntityRecord.imageUrl = item.imageUrl;
            return productImageEntityRecord;
          })
        )
      : productNeedUpdate && productNeedUpdate.images;

    return recordProductEntity;
  }

  private async validateProductImage(record: ProductImageDto) {
    const recordDto = new ProductImageDto();
    recordDto.id = record?.id;
    recordDto.imageUrl = record?.imageUrl;

    const error = await validate(recordDto);

    if (error.length > 0) {
      let messErr = [];
      error.forEach((value) => {
        messErr = messErr.concat(Object.values(value.constraints));
      });
      throw new BadRequestException(messErr);
    }
  }

  private async validateProduct(record: ProductDto, groupsValidate: string[]) {
    const recordDto = new ProductDto();
    recordDto.id = record?.id;
    recordDto.name = record?.name;
    recordDto.status = record?.status;
    recordDto.detail = record?.detail;
    recordDto.videoType = record?.videoType;
    recordDto.videoId = record?.videoId;
    recordDto.images = record?.images;

    const error = await validate(recordDto, {
      groups: groupsValidate,
    });

    if (error.length > 0) {
      let messErr = [];
      error.forEach((value) => {
        messErr = messErr.concat(Object.values(value.constraints));
      });
      throw new BadRequestException(messErr);
    }
  }

  private async checkUpdateProduct(uuid: string, tourTrashUuid?: string): Promise<void> {
    if (tourTrashUuid) {
      const recordProductOfTourTrash = await this.productRepo.findOne({
        tourTrashUuid: tourTrashUuid.toUpperCase(),
        uuid: uuid.toUpperCase(),
      });
      if (!recordProductOfTourTrash) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.TOUR_TRASH_PRODUCT_ALREADY,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.OTHER_TOUR_TRASH_PRODUCT_ALREADY,
        });
      }
    } else {
      const recordProduct = await this.productRepo.findOne({
        uuid: uuid.toUpperCase(),
      });
      if (recordProduct) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.TOUR_TRASH_PRODUCT_ALREADY,
          errorMessage: TOUR_TRASH_MESSAGE_ERROR.TOUR_TRASH_PRODUCT_ALREADY,
        });
      }
    }
  }

  private toRecord(result: TourTrashEntity, resultProduct?: ProductEntity, isJoined?: boolean): TourTrashRecord {
    const record: TourTrashRecord = {
      id: result.uuid,
      title: result.title,
      status: this.isComingSoon(result) ? TourTrashStatus.COMING_SOON : result.status,
      description: result.description,
      startDate: result.startDate,
      endDate: result.endDate,
      joined: isJoined !== undefined ? isJoined : undefined,
      participationLevel: result.participationLevel,
      countries: result?.countries,
      options: result?.options,
    };
    if (resultProduct) {
      record.product = {
        id: resultProduct.uuid,
        name: resultProduct.name,
        detail: resultProduct.detail,
        legal: resultProduct.legal,
        images:
          resultProduct.images && resultProduct.images.length > 0
            ? resultProduct.images.map((image) => ({
                id: image.uuid,
                imageUrl: image.imageUrl,
              }))
            : [],
        videoType: resultProduct.videoType,
        videoId: resultProduct.videoId,
      };
    }

    return record;
  }

  private toRawRecord(result: TourTrashEntity, resultProduct?: ProductEntity) {
    const record: any = {
      uuid: result.uuid,
      title: result.title,
      status: result.status,
      description: result.description,
      startDate: result.startDate,
      endDate: result.endDate,
      countries: result?.countries,
      pushNotificationTo: result.pushNotificationTo,
      sendEmailTo: result.sendEmailTo,
      participationLevel: result.participationLevel,
      remindPushNotificationFrequency: result.remindPushNotificationFrequency,
      sendEmailAtLaunch: result.sendEmailAtLaunch,
      pushNotificationAtLaunch: result.pushNotificationAtLaunch,
      timeToSendEmailBeforeEndDate: Number(result.timeToSendEmailBeforeEndDate),
      timeToPushNotificationBeforeEndDate: Number(result.timeToPushNotificationBeforeEndDate),
      timeToAutoCloseAfterEndDate: Number(result.timeToAutoCloseAfterEndDate),
      winnerUserId: result.winnerUserId,
    };
    if (resultProduct) {
      record.product = {
        id: resultProduct.uuid,
        name: resultProduct.name,
        detail: resultProduct.detail,
        images:
          resultProduct.images && resultProduct.images.length > 0
            ? resultProduct.images.map((image) => ({
                id: image.uuid,
                imageUrl: image.imageUrl,
              }))
            : [],
        videoType: resultProduct.videoType,
        videoId: resultProduct.videoId,
      };
    }

    return record;
  }

  private isComingSoon(tourTrash: TourTrashEntity): boolean {
    return tourTrash.status === TourTrashStatus.ACTIVE && moment().isBefore(tourTrash.startDate);
  }

  async getUserById(userId: string, isThrow = true) {
    const user = await this.userRepo.findOne({
      where: {
        id: In([userId.toUpperCase(), userId.toLowerCase()]),
      },
    });

    if (!user && isThrow) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.USER_NOT_FOUND,
      });
    }

    return user;
  }

  async getTourTrashActiveById(id: string, relations: string[]) {
    return await this.tourTrashRepo.findOne({
      where: {
        status: TourTrashStatus.ACTIVE,
        uuid: In([id.toUpperCase(), id.toLowerCase()]),
      },
      relations,
    });
  }

  async getTourTrashById(id: string, relations: string[]) {
    return await this.tourTrashRepo.findOne({
      where: {
        uuid: In([id.toUpperCase(), id.toLowerCase()]),
      },
      relations,
    });
  }

  private async getProductImage(id: string, productId: string): Promise<ProductImageEntity> {
    const image: ProductImageEntity = await this.productImageRepo.findOne({
      where: {
        uuid: In([id.toUpperCase(), id.toLowerCase()]),
        productUuid: In([productId.toUpperCase(), productId.toLowerCase()]),
      },
    });

    if (!image) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_IMAGE_NOT_FOUND,
        errorMessage: TOUR_TRASH_MESSAGE_ERROR.PRODUCT_IMAGE_NOT_FOUND,
      });
    }

    return image;
  }

  private getFullNameFromUser(user: any) {
    let fullName = null;
    if (user && (!_.isNil(user?.firstName) || !_.isNil(user?.lastName))) {
      fullName = `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
    }

    return fullName;
  }
}

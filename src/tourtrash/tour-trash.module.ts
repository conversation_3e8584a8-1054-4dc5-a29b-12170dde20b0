import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { SYSTEM_TAG } from 'src/utils/constants';
import { UserTourTrashEntity } from './entities/user-tour-trash.entity';
import { TourTrashController } from './tour-trash.controller';
import { TourTrashCronService } from './tour-trash.cron.service';
import { TourTrashProcessor } from './tour-trash.processor';
import { TourTrashService } from './tour-trash.service';

let processors = [];

if (process.env.TAG !== SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, TourTrashProcessor];
}

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([UserTourTrashEntity])],
  controllers: [TourTrashController],
  providers: [...processors, TourTrashService, TourTrashCronService],
  exports: [TourTrashService, TourTrashCronService],
})
export class TourTrashModule {}

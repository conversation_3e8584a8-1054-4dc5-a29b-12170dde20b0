import { CACHE_MANAGER, Inject, Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';
import { Cache } from 'cache-manager';
import { ConfigService } from 'nestjs-config';
import { Connection } from 'typeorm';
import { CdmService } from 'src/cdm/cdm.service';
import { DeService } from 'src/content/de.service';
import { EbsService } from 'src/ebs/ebs.service';
import { EcomService } from 'src/ecom/ecom.service';
import { MfeService } from 'src/mfe/mfe.service';
import { MrpService } from 'src/mrp/mrp.service';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { LaunchMonitorDataDTO } from 'src/recommendation/recommendation.type';
import { ElevatedApiErrorsIncidentTemplate } from './health.constant';

const DEFAULT_PING_TIMEOUT = 30000;

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  constructor(
    private readonly config: ConfigService,
    private connection: Connection,
    private readonly deService: DeService,
    private readonly cdmService: CdmService,
    private readonly mfeService: MfeService,
    private readonly mrpService: MrpService,
    private readonly ecomService: EcomService,
    private readonly ebsService: EbsService,
    private readonly recommendationService: RecommendationService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {
    this.config = config;
  }

  async pingDatabaseConnection() {
    const queryRunner = this.connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.release();
    return true;
  }

  async pingRedisConnection() {
    try {
      await this.cacheManager.set('TEST_CONNECTION', 1);
      await this.cacheManager.get('TEST_CONNECTION');
      await this.cacheManager.del('TEST_CONNECTION');
      return true;
    } catch (e) {
      return false;
    }
  }

  getStatuspageRequestHeaders() {
    return {
      Authorization: `OAuth ${this.config.get('health.apiKey')}`,
    };
  }

  async pingService(service: string, axiosReq: any, componentId: string): Promise<Array<string>> {
    return new Promise(async (resolve) => {
      const pingTimeout = setTimeout(() => {
        resolve([componentId]);
        this.logger.log(`${service} unhealthy with timeout ${DEFAULT_PING_TIMEOUT}ms`);
      }, DEFAULT_PING_TIMEOUT);
      try {
        const { data } = await axiosReq;
        clearTimeout(pingTimeout);
        const healthy = data && data.healthy;
        if (!healthy) {
          resolve([componentId]);
        }
        this.logger.log(`${service} ${healthy ? 'healthy' : 'unhealthy'}`);
        resolve([]);
      } catch (error) {
        clearTimeout(pingTimeout);
        if (error.response && error.response.status !== 200) {
          this.logger.log(`${service} unhealthy`);
          resolve([componentId]);
        }
        resolve([]);
      }
    });
  }

  async cdmHealthyCheck() {
    const consumer = await this.cdmService.getConsumer(
      this.config.get('health.cdmConsumerEmail'),
      this.config.get('app.defaultRegionId'),
      false,
      false
    );
    const cdmHealthy = consumer && consumer.email === this.config.get('health.cdmConsumerEmail');
    return {
      data: {
        healthy: cdmHealthy,
      },
    };
  }

  async mfeHealthyCheck() {
    try {
      const mfeHealthy = (await this.mfeService.getHeadModels())?.length > 0;
      return {
        data: {
          healthy: mfeHealthy,
        },
      };
    } catch (e) {
      console.error(e);
      return {
        data: {
          healthy: false,
        },
      };
    }
  }

  async mrpHealthyCheck() {
    try {
      const avgScores = await this.mrpService.getAverageScores(parseInt(this.config.get('health.mrpUserId'), 10));
      return {
        data: {
          healthy: !!avgScores,
        },
      };
    } catch (e) {
      console.error(e);
      return {
        data: {
          healthy: false,
        },
      };
    }
  }

  async ecomHealthyCheck() {
    const products = await this.ecomService.getProductAll();
    return {
      data: {
        healthy: !!products,
      },
    };
  }

  async ebsHealthyCheck() {
    const { status } = await this.ebsService.configureInitialize(
      'init1_parentnode=Operating+Unit&init2_parentnode=Source+Application&model_name=TM_DRIVER_MODEL&init1_node=TM+US&init2_node=B2C&quantity=1&currency=USD'
    );
    return {
      data: {
        healthy: status === 200,
      },
    };
  }

  async insightHealthCheck() {
    try {
      const { status, data } = await axios.get(`${this.config.get('health.insightEndpoint')}/health`);
      return {
        data: {
          healthy: status === 200 && data?.status === 'UP',
        },
      };
    } catch (e) {
      console.error(e);
      return {
        data: {
          healthy: false,
        },
      };
    }
  }

  async clubRecHealthyCheck() {
    const launchMonitorData = {
      clubTypeId: 1,
      gender: 'male',
      age: '18',
      handed: 'right',
      handicap: '12.5',
      height: '72',
      typicalDriverData: 'no',
      carryYour7Iron: '220',
      driverBallFlight: 'mid',
      typicalDriverShotShape: 'draw',
      desiredDriverShotShape: 'draw',
    } as LaunchMonitorDataDTO;

    const res = await this.recommendationService.getClubRecommendationResults(launchMonitorData, true);
    return {
      data: {
        healthy: res?.status === 200,
      },
    };
  }

  async createAnIncident(unhealthyComponentIds: string[], payload: any) {
    const unhealthyComponents = {};
    unhealthyComponentIds.forEach((componentId) => (unhealthyComponents[componentId] = 'partial_outage'));
    const { data } = await axios.post(
      `https://api.statuspage.io/v1/pages/${this.config.get('health.pageId')}/incidents`,
      {
        incident: {
          ...payload,
          components: unhealthyComponents,
          component_ids: unhealthyComponentIds,
        },
      },
      {
        headers: this.getStatuspageRequestHeaders(),
      }
    );
    return data;
  }

  async updateAnIncident(incidentId: string, payload: any) {
    const { data } = await axios.put(
      `https://api.statuspage.io/v1/pages/${this.config.get('health.pageId')}/incidents/${incidentId}`,
      {
        incident: {
          ...payload,
        },
      },
      {
        headers: this.getStatuspageRequestHeaders(),
      }
    );
    return data;
  }

  async getUnresolvedIncidents(page = 1, perPage = 100) {
    const { data } = await axios.get(
      `https://api.statuspage.io/v1/pages/${this.config.get(
        'health.pageId'
      )}/incidents/unresolved?page=${page}&perPage=${perPage}`,
      {
        headers: this.getStatuspageRequestHeaders(),
      }
    );
    return data;
  }

  async getComponents(page = 1, perPage = 100) {
    const { data } = await axios.get(
      `https://api.statuspage.io/v1/pages/${this.config.get(
        'health.pageId'
      )}/components?page=${page}&perPage=${perPage}`,
      {
        headers: this.getStatuspageRequestHeaders(),
      }
    );
    return data;
  }

  async getIncidents(page = 1, perPage = 100) {
    const { data } = await axios.get(
      `https://api.statuspage.io/v1/pages/${this.config.get(
        'health.pageId'
      )}/incidents?page=${page}&perPage=${perPage}`,
      {
        headers: this.getStatuspageRequestHeaders(),
      }
    );
    return data;
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async healthCheckForAllServices() {
    if (this.config.get('app.tag') !== 'HEALTH_CHECK') {
      return;
    }
    let unhealthyComponentIds: string[] = [];

    const backendUnhealthyComponentIds = await this.pingService(
      'BACKEND',
      axios.get(this.config.get('health.backendEndpoint')),
      this.config.get('health.backendComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...backendUnhealthyComponentIds];

    const jobUnhealthyComponentIds = await this.pingService(
      'JOB',
      axios.get(this.config.get('health.jobEndpoint')),
      this.config.get('health.jobComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...jobUnhealthyComponentIds];

    const deUnhealthyComponentIds = await this.pingService(
      'DE',
      this.deService.healthy(),
      this.config.get('health.deComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...deUnhealthyComponentIds];

    const cdmUnhealthyComponentIds = await this.pingService(
      'CDM',
      this.cdmHealthyCheck(),
      this.config.get('health.cdmComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...cdmUnhealthyComponentIds];

    const mfeUnhealthyComponentIds = await this.pingService(
      'MFE',
      this.mfeHealthyCheck(),
      this.config.get('health.mfeComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...mfeUnhealthyComponentIds];

    const mrpUnhealthyComponentIds = await this.pingService(
      'MRP',
      this.mrpHealthyCheck(),
      this.config.get('health.mrpComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...mrpUnhealthyComponentIds];

    const ecomUnhealthyComponentIds = await this.pingService(
      'ECOM',
      this.ecomHealthyCheck(),
      this.config.get('health.ecomComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...ecomUnhealthyComponentIds];

    // const ebsUnhealthyComponentIds = await this.pingService(
    //   'EBS',
    //   this.ebsHealthyCheck(),
    //   this.config.get('health.ebsComponentId')
    // );
    // unhealthyComponentIds = [...unhealthyComponentIds, ...ebsUnhealthyComponentIds];

    // const clubRecUnhealthyComponentIds = await this.pingService(
    //   'CLUB_REC',
    //   this.clubRecHealthyCheck(),
    //   this.config.get('health.clubRecComponentId')
    // );
    // unhealthyComponentIds = [...unhealthyComponentIds, ...clubRecUnhealthyComponentIds];

    const insightUnhealthyComponentIds = await this.pingService(
      'INSIGHT',
      this.insightHealthCheck(),
      this.config.get('health.insightComponentId')
    );
    unhealthyComponentIds = [...unhealthyComponentIds, ...insightUnhealthyComponentIds];

    const unresolvedIncidents = await this.getUnresolvedIncidents();

    if (unhealthyComponentIds.length > 0 && unresolvedIncidents.length === 0) {
      await this.createAnIncident(unhealthyComponentIds, {
        ...ElevatedApiErrorsIncidentTemplate,
      });
    }

    if (unhealthyComponentIds.length === 0 && unresolvedIncidents.length > 0) {
      for (const unresolvedIncident of unresolvedIncidents) {
        const componentIds: string[] = unresolvedIncident.components.map((component) => component.id);
        const healthyComponents = {};
        componentIds.forEach((componentId) => (healthyComponents[componentId] = 'operational'));
        await this.updateAnIncident(unresolvedIncident.id, {
          status: 'resolved',
          component_ids: componentIds,
          components: healthyComponents,
        });
      }
    }
  }
}

import { Body, Controller, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { HealthService } from './health.service';

@Controller('health')
export class HealthController {
  constructor(private healthService: HealthService) {}

  @Get('status')
  async status(@Req() request: BaseRequest): Promise<any> {
    const databasePingStatus = await this.healthService.pingDatabaseConnection();
    const redisPingStatus = await this.healthService.pingRedisConnection();
    return {
      healthy: databasePingStatus && redisPingStatus,
      databaseEstablished: databasePingStatus,
      redisPingStatus: redisPingStatus,
    };
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('components')
  async components(@Query('page') page: number, @Query('perPage') perPage: number) {
    return this.healthService.getComponents(page, perPage);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('incidents')
  async incidents(@Query('page') page: number, @Query('perPage') perPage: number) {
    return this.healthService.getIncidents(page, perPage);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('unresolved-incidents')
  async unresolvedIncidents(@Query('page') page: number, @Query('perPage') perPage: number) {
    return this.healthService.getUnresolvedIncidents(page, perPage);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('incident')
  async createIncident(@Body() body: any) {
    return this.healthService.createAnIncident(body.unhealthyComponentIds, body.payload);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Put('incident/:incidentId')
  async updateIncident(@Body() body: any, @Param() params) {
    return this.healthService.updateAnIncident(params.incidentId, body.payload);
  }
}

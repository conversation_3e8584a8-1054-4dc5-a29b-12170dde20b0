import { Module } from '@nestjs/common';
import { EbsService } from 'src/ebs/ebs.service';
import { RecommendationService } from 'src/recommendation/recommendation.service';
import { SharedModule } from 'src/shared/shared.module';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';

@Module({
  imports: [SharedModule],
  controllers: [HealthController],
  providers: [HealthService, EbsService, RecommendationService],
})
export class HealthModule {}

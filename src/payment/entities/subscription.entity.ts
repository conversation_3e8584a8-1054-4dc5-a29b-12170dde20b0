import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('Subscriptions')
export class SubscriptionEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  quantity: string;

  @Column()
  productId: string;

  @Column()
  transactionId: string;

  @Column()
  originalTransactionId: string;

  @Column()
  purchaseDate: string;

  @Column()
  purchaseDateMs: string;

  @Column()
  purchaseDatePst: string;

  @Column()
  originalPurchaseDate: string;

  @Column()
  originalPurchaseDateMs: string;

  @Column()
  originalPurchaseDatePst: string;

  @Column()
  expiresDate: string;

  @Column()
  expiresDateMs: string;

  @Column()
  expiresDatePst: string;

  @Column()
  webOrderLineItemId: string;

  @Column()
  isTrialPeriod: string;

  @Column()
  isInIntroOfferPeriod: string;

  @Column()
  subscriptionGroupIdentifier: string;

  @Column()
  status: number;

  @Column()
  autoRenewing: boolean;

  @Column()
  purchaseToken: string;

  @Column()
  purchaseState: number;

  @Column('decimal', { precision: 6, scale: 2 })
  amount: number;

  @Column()
  notificationType: string;

  @Column()
  prevPlan: string;

  @Column()
  refSubscriptionId: string;

  @Column()
  offerType: number;

  @Column()
  offerIdentifier: string;

  @Column()
  subtype: string;

  @Column()
  revocationDate: Date;

  @Column()
  revocationReason: string;

  @Column()
  expirationDate: Date;

  @Column()
  platform: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column()
  isChecked: boolean;

  @Column()
  fromAccessCode: string;

  @Column()
  environment: string;
}

import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn } from 'typeorm';

@Entity('SubscriptionEventLogs')
export class SubscriptionEventLog {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  userId: string;

  @Column()
  eventName: string;

  @Column()
  subtype: string;

  @Column()
  notifyData: string;

  @Column()
  receiptData: string;

  @Column()
  platform: string;

  @Column()
  environment: string;

  @CreateDateColumn()
  createdAt: Date;
}

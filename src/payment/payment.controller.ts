import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { Queue } from 'bull';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { AppleDownloadType, GoogleDownloadType, PLATFORM, SUBSCRIPTION_QUEUE_NAME } from './payment.constants';
import { PaymentService } from './payment.service';
import { CanSubscribeDto, SubscribeDto, SyncToCDMDto } from './payment.type';
import { SubscriptionProcessorQueueName } from './subscription.processor';

@Controller()
@UseGuards(ClientGuard)
export class PaymentController {
  constructor(
    private authService: PaymentService,
    private paymentService: PaymentService,
    private readonly config: ConfigService,
    @InjectQueue(SUBSCRIPTION_QUEUE_NAME) private subscriptionQueue: Queue
  ) {
    this.config = config;
  }

  @Post('payments/subscribe')
  @UseGuards(AuthGuard)
  async subscribePlan(@Req() request: BaseRequest, @Body() body: SubscribeDto): Promise<any> {
    const result = await this.paymentService.subscribePlan(request.user.uid, request.user.email, body);
    if (!result.iapError) {
      return result;
    }
    if (result.iapError) {
      throw new BadRequestException({
        internalErrorCode: result.iapError.error,
        errorMessage: result.iapError.errorDescription,
      });
    }
  }

  @Post('payments/subscribe-from-log/:logId')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async subscribePlanFromLog(@Param('logId') logId: string): Promise<any> {
    return this.paymentService.subscribePlanFromLog(logId);
  }

  @Post('payments/can-subscribe')
  @UseGuards(AuthGuard)
  async canSubscribePlan(@Req() request: BaseRequest, @Body() body: CanSubscribeDto): Promise<any> {
    return this.paymentService.canSubscribe(request.user.uid, body.platform, body.originalTransactionId);
  }

  @Post('payments/sync-to-cdm')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async syncTheLatestPlanToCDM(@Req() request: BaseRequest, @Body() body: SyncToCDMDto): Promise<any> {
    const result = await this.paymentService.syncTheLatestPlanToCDM(body.emails);
    if (!result.error) {
      return result;
    }
    if (result.error) {
      throw new BadRequestException({
        internalErrorCode: result.error.error,
        errorMessage: result.error.errorDescription,
      });
    }
    return result;
  }

  @Get('payments/histories')
  @UseGuards(AuthGuard)
  async getPaymentHistories(@Req() request: BaseRequest): Promise<any> {
    return this.paymentService.getPaymentHistories(request.user.uid);
  }

  // @Get('payments/apple/download-reports')
  // @AccessedClients(CLIENTS.ADMIN_PORTAL)
  // @UseGuards(ClientGuard)
  // @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  // @UseGuards(AuthGuard)
  // async downloadAppleReports(@Query('date') date: string): Promise<any> {
  //   await this.subscriptionQueue
  //     .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //       downloadType: AppleDownloadType.SUBSCRIPTION,
  //       downloadReportDate: date,
  //       platform: PLATFORM.APPLE,
  //     })
  //     .catch((e) => console.error(e));
  //   await this.subscriptionQueue
  //     .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //       downloadType: AppleDownloadType.SUBSCRIPTION_EVENT,
  //       downloadReportDate: date,
  //       platform: PLATFORM.APPLE,
  //     })
  //     .catch((e) => console.error(e));
  //   await this.subscriptionQueue
  //     .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //       downloadType: AppleDownloadType.SUBSCRIBER,
  //       downloadReportDate: date,
  //       platform: PLATFORM.APPLE,
  //     })
  //     .catch((e) => console.error(e));
  //   return 'A queue is added. Please visit the Bull Dashboard to view more detail';
  // }

  @Get('payments/google/download-reports')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async googleSubscriptionReportSetup(@Query('date') date: Date, @Query('type') type): Promise<any> {
    return;
    // if (type === 'Earning') {
    //   await this.paymentService.downloadGoogleEarningsReport(date);
    // }
    //
    // if (type === 'Subscription') {
    //   await this.paymentService.downloadGoogleSalesReport(date);
    // }
    //
    // if (type === 'Installs') {
    //   await this.paymentService.downloadInstallsReport(date);
    // }
    // if (date) {
    //   await this.subscriptionQueue
    //     .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
    //       downloadType: type || GoogleDownloadType.SUBSCRIPTION,
    //       downloadReportDate: moment(date).format('YYYYMM'),
    //       platform: PLATFORM.GOOGLE,
    //       downloadedAt: moment().format('YYYY-MM-DD'),
    //     })
    //     .catch((e) => console.error(e));
    //   return;
    // }
    //
    // await this.subscriptionQueue
    //   .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
    //     downloadType: GoogleDownloadType.SUBSCRIPTION,
    //     downloadReportDate: moment().format('YYYYMM'),
    //     platform: PLATFORM.GOOGLE,
    //     downloadedAt: moment().format('YYYY-MM-DD'),
    //   })
    //   .catch((e) => console.error(e));
    //
    // return 'A queue is added. Please visit the Bull Dashboard to view more detail';
  }
}

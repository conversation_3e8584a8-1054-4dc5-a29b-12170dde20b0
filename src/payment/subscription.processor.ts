import { Process, Processor } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Logger } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import axios from 'axios';
import { Job } from 'bull';
import { Cache } from 'cache-manager';
import fs from 'fs';
import jwt from 'jsonwebtoken';
import { ConfigService } from 'nestjs-config';
import path from 'path';
import Client from 'ssh2-sftp-client';
import unzipper from 'unzipper';
import zlib from 'zlib';
import { convertSubscriptionDownloadPstDate } from '../utils/datetime';
import {
  APP_STORE_AUTH_TOKEN_CACHE_KEY,
  AppleDownloadType,
  DownloadMessage,
  GoogleDownloadType,
  PLATFORM,
  SUBSCRIPTION_QUEUE_NAME,
} from './payment.constants';

type JobDownloadReportAppleType = Job<{
  downloadType?: AppleDownloadType | GoogleDownloadType | any;
  downloadReportDate?: string;
  platform?: PLATFORM;
  downloadedAt?: string;
}>;

enum RetryType {
  SUBSCRIPTION = 'retriesCountDownloadSubscription',
  SUBSCRIPTION_EVENT = 'retriesCountDownloadSubscriptionEvent',
  SUBSCRIBER = 'retriesCountDownloadSubscriber',
}

export enum SubscriptionProcessorQueueName {
  DOWNLOAD_SUBSCRIPTION_REPORT = 'download-subscription-report',
}

@Processor(SUBSCRIPTION_QUEUE_NAME)
export class SubscriptionProcessor {
  private readonly logger = new Logger(SubscriptionProcessor.name);

  constructor(private readonly config: ConfigService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async generateJWTAppsStoreConnect() {
    const appleIssuerId = this.config.get('app.appleIssuerId');
    const appleKeyId = this.config.get('app.appleKeyId');
    const appleAuthKeyFileName = this.config.get('app.appleAuthKeyFileName');
    const pathKey = path.join(process.cwd(), `/${appleAuthKeyFileName}`);
    if (!fs.existsSync(pathKey)) {
      throw new Error(`Not Found path Auth_Key`);
    }
    const applePrivateKey = fs.readFileSync(pathKey);

    const now = Math.round(new Date().getTime() / 1000);
    const nowPlus20 = now + 1199;

    const payload = {
      iss: appleIssuerId,
      exp: nowPlus20,
      aud: 'appstoreconnect-v1',
    };

    const header = {
      alg: 'ES256',
      kid: appleKeyId,
      typ: 'JWT',
    };

    const signOptions: jwt.SignOptions = {
      algorithm: 'ES256',
      header,
    };

    const token = jwt.sign(payload, applePrivateKey, signOptions);

    await this.cacheManager.set(APP_STORE_AUTH_TOKEN_CACHE_KEY, token, {
      ttl: 1200,
    });
    return token;
  }

  async getHeadersConfigAppStoreApi() {
    let token = await this.cacheManager.get(APP_STORE_AUTH_TOKEN_CACHE_KEY);
    if (!token) {
      token = await this.generateJWTAppsStoreConnect();
    }
    return {
      headers: {
        Accept: 'application/a-gzip, application/json',
        Authorization: `Bearer ${token}`,
      },
    };
  }

  @Process({
    name: SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT,
    concurrency: 1,
  })
  async downloadReportProcessor(job: JobDownloadReportAppleType) {
    const { downloadType, downloadReportDate, platform } = job.data;
    if (platform === PLATFORM.APPLE) {
      return await this.downloadAppleReports(downloadType, downloadReportDate);
    }
    if (platform === PLATFORM.GOOGLE) {
      return await this.downloadGoogleReport(downloadReportDate, downloadType);
    }
    return null;
  }

  transformDownloadEndpointAndRetryType(downloadType: AppleDownloadType, reportDate: string) {
    switch (downloadType) {
      case AppleDownloadType.SUBSCRIPTION:
        return {
          endpoint: `${this.config.get(
            'app.appStoreEndpoint'
          )}/v1/salesReports?filter[frequency]=DAILY&filter[reportDate]=${reportDate}&filter[reportSubType]=SUMMARY&filter[reportType]=SUBSCRIPTION&filter[vendorNumber]=${this.config.get(
            'app.appleVendorNumber'
          )}&filter[version]=1_3`,
          retryType: RetryType.SUBSCRIPTION,
        };
      case AppleDownloadType.SUBSCRIPTION_EVENT:
        return {
          endpoint: `${this.config.get(
            'app.appStoreEndpoint'
          )}/v1/salesReports?filter[frequency]=DAILY&filter[reportDate]=${reportDate}&filter[reportSubType]=SUMMARY&filter[reportType]=SUBSCRIPTION_EVENT&filter[vendorNumber]=${this.config.get(
            'app.appleVendorNumber'
          )}&filter[version]=1_3`,
          retryType: RetryType.SUBSCRIPTION_EVENT,
        };
      case AppleDownloadType.SUBSCRIBER:
        return {
          endpoint: `${this.config.get(
            'app.appStoreEndpoint'
          )}/v1/salesReports?filter[frequency]=DAILY&filter[reportDate]=${reportDate}&filter[reportSubType]=DETAILED&filter[reportType]=SUBSCRIBER&filter[vendorNumber]=${this.config.get(
            'app.appleVendorNumber'
          )}&filter[version]=1_3`,
          retryType: RetryType.SUBSCRIBER,
        };
    }
  }

  async downloadAppleReports(downloadType: AppleDownloadType, downloadReportDate?: string) {
    const { localReportFile, localFileGZIP, localReportFolder, fileName, reportDate } =
      this.getInfoLocalFileForDownloadAppleReport(downloadType, downloadReportDate);
    if (await this.scanSFTPAndFindReportFile(fileName, 'Apple')) {
      return true;
    }
    if (!fs.existsSync(localReportFolder)) fs.mkdirSync(localReportFolder, { recursive: true });
    this.logger.log(`Begin: ${DownloadMessage[downloadType]} ${reportDate}`);
    const { endpoint, retryType } = this.transformDownloadEndpointAndRetryType(downloadType, reportDate);
    if (!endpoint) {
      this.logger.error(`Couldn't find the endpoint ${DownloadMessage[downloadType]}`);
      throw new Error(`Couldn't find the endpoint ${DownloadMessage[downloadType]}`);
    }
    const response = await this.requestDownloadAppleReport(endpoint);
    if (!response?.data) {
      this.logger.error(`Can't ${DownloadMessage[downloadType]} ${reportDate}`);
      throw new Error(`Can't ${DownloadMessage[downloadType]} ${reportDate}`);
    }
    await response.data.pipe(fs.createWriteStream(localFileGZIP)).on('finish', () => {
      fs.createReadStream(localFileGZIP)
        .pipe(zlib.createGunzip())
        .on('error', async (error) => {
          this.logger.error(`Can't execute createGunzip - ${DownloadMessage[downloadType]} ${reportDate}`);
          throw new Error(`Can't execute createGunzip - ${DownloadMessage[downloadType]} ${reportDate}`);
        })
        .pipe(fs.createWriteStream(localReportFile))
        .on('finish', async () => {
          this[retryType] = 0;
          await this.uploadReportToSFTP(localReportFile, fileName, 'Apple');
        })
        .on('error', async () => {
          this.logger.error(`Can't execute createWriteStream ${DownloadMessage[downloadType]} ${reportDate}`);
          throw new Error(`Can't execute createWriteStream ${DownloadMessage[downloadType]} ${reportDate}`);
        });
    });
  }

  async downloadGoogleReport(downloadReportDate: string, downloadType: GoogleDownloadType) {
    try {
      const { storage, bucketName } = this.setupGoogleCloudStorage();
      const { prefix, destFileNameZip, destFileName, reportFileName, destFileFolder } =
        this.getInfoDownloadGoogleReport(downloadType, downloadReportDate);
      const [files] = await storage.bucket(bucketName).getFiles({
        prefix,
      });

      if (files.length > 0) {
        if (downloadType === GoogleDownloadType.INSTALLS) {
          await Promise.all(
            files.map(async (file) => {
              await this.downloadReportFileCsv(storage, bucketName, file.name, reportFileName, destFileFolder);
            })
          );
        } else {
          await Promise.all(
            files.map(async (file) => {
              await this.downloadAndUnzipFile(
                storage,
                bucketName,
                file.name,
                reportFileName,
                destFileNameZip,
                destFileFolder,
                destFileName
              );
            })
          );
        }
      } else {
        this.logger.error(`Couldn't find the Google ${downloadType} (${downloadReportDate})`);
        if (downloadType !== GoogleDownloadType.EARNINGS) {
          throw new Error(`Couldn't find the Google ${downloadType} (${downloadReportDate})`);
        }
        return true;
      }
    } catch (error) {
      this.logger.error(`Download Google ${downloadType} (${downloadReportDate}) failed!`);
      throw new Error(error);
    }
  }

  getInfoDownloadGoogleReport(downloadType: string, downloadReportDate: string) {
    let prefix = '';
    let destFileNameZip = '';
    let destFileName = '';
    let reportFileName = '';

    const destFileFolder = path.join(process.cwd(), `/public/SalesAndTrends/Google`);
    if (!fs.existsSync(destFileFolder)) fs.mkdirSync(destFileFolder, { recursive: true });

    if (downloadType === GoogleDownloadType.EARNINGS) {
      prefix = `earnings/earnings_${downloadReportDate}`;
      destFileNameZip = `${destFileFolder}/earnings_${downloadReportDate}.zip`;
      destFileName = `PlayApps_${downloadReportDate}.csv`;
      reportFileName = `Google_Earnings_Report_${downloadReportDate}.csv`;
    }

    if (downloadType === GoogleDownloadType.SUBSCRIPTION) {
      prefix = `sales/salesreport_${downloadReportDate}`;
      destFileNameZip = `${destFileFolder}/salesreport_${downloadReportDate}.zip`;
      destFileName = `salesreport_${downloadReportDate}.csv`;
      reportFileName = `Google_Subscriptions_Report_${downloadReportDate}.csv`;
    }

    if (downloadType === GoogleDownloadType.INSTALLS) {
      prefix = `stats/installs/installs_${this.config.get('app.googlePackageName')}_${downloadReportDate}_overview`;
      reportFileName = `Google_Installs_Overview_Report_${downloadReportDate}.csv`;
    }

    return { prefix, destFileNameZip, destFileName, reportFileName, destFileFolder };
  }

  setupGoogleCloudStorage(): { storage: Storage; bucketName: string } {
    const keyFilename = path.join(process.cwd(), `/google-subscription-key.json`);
    const storage = new Storage({ keyFilename });
    const bucketName = this.config.get('app.googleBucketName');
    return { storage, bucketName };
  }

  async downloadAndUnzipFile(
    storage: Storage,
    bucketName: string,
    downloadFileName: string,
    reportFileName: string,
    destFileNameZip: string,
    destFileFolder: string,
    destFileName: string
  ) {
    this.logger.log(`Downloaded file ${downloadFileName} from bucket name ${bucketName} to ${destFileNameZip}`);
    await storage.bucket(bucketName).file(downloadFileName).download({
      destination: destFileNameZip,
    });
    const zip = fs.createReadStream(destFileNameZip).pipe(unzipper.Parse({ forceStream: true }));
    for await (const entry of zip) {
      const fileName = entry.path;
      if (fileName === destFileName) {
        const destFileCSV = `${destFileFolder}/${reportFileName}`;
        entry
          .pipe(fs.createWriteStream(destFileCSV))
          .on('finish', async () => {
            await this.uploadReportToSFTP(destFileCSV, reportFileName, 'Google');
          })
          .on('error', async () => {
            this.logger.error(`Can't execute createWriteStream when download ${downloadFileName}`);
            throw new Error(`Can't execute createWriteStream when download ${downloadFileName}`);
          });
      } else {
        entry.autodrain();
      }
    }
  }

  async downloadReportFileCsv(
    storage: Storage,
    bucketName: string,
    downloadFileName: string,
    reportFileName: string,
    destFileFolder: string
  ) {
    this.logger.log(`Downloaded file ${downloadFileName} from bucket name ${bucketName}`);
    const destination = `${destFileFolder}/${reportFileName}`;
    await storage.bucket(bucketName).file(downloadFileName).download({
      destination,
    });
    await this.uploadReportToSFTP(destination, reportFileName, 'Google');
  }

  async requestDownloadAppleReport(downloadEndpoint: string) {
    try {
      const header = await this.getHeadersConfigAppStoreApi();
      this.logger.log(downloadEndpoint);
      return await axios.get(downloadEndpoint, {
        ...header,
        responseType: 'stream',
      });
    } catch (e) {
      console.log(e.message);
      return null;
    }
  }

  getInfoLocalFileForDownloadAppleReport(subStrFileName: string, downloadReportDate?: string) {
    const reportDate = convertSubscriptionDownloadPstDate(this.config.get('app.applePreviousNumberDaysToDownload'));
    const localReportFolder = path.join(process.cwd(), `/public/SalesAndTrends/Apple`);
    const fileName = `${subStrFileName}_${(downloadReportDate || reportDate).replace(/-/g, '')}.txt`;
    const fileNameGZIP = `${fileName}.gz`;
    const localReportFile = path.join(process.cwd(), `/public/SalesAndTrends/Apple/${fileName}`);
    const localFileGZIP = path.join(process.cwd(), `/public/SalesAndTrends/Apple/${fileNameGZIP}`);

    return {
      localReportFile,
      localFileGZIP,
      localReportFolder,
      reportDate: downloadReportDate || reportDate,
      fileName,
    };
  }

  async uploadReportToSFTP(localFile: string, reportFileName: string, folderType: string) {
    const remoteFilePath = `/${this.config.get('app.ttbFTPRootPath')}/SalesAndTrends/${folderType}/${reportFileName}`;
    const client = await this.getFTPClient();

    if (folderType === 'Google' && reportFileName.indexOf('Google_Earnings_Report') > -1) {
      const status = await client.exists(remoteFilePath);
      if (status) {
        this.logger.log(`Skipped ${reportFileName} for upload to sftp...`);
        return client.end();
      }
    }

    this.logger.log(`Started to upload ${reportFileName} to sftp...`);
    await client.put(localFile, remoteFilePath);
    this.logger.log(`Uploaded ${reportFileName} to sftp...`);
    return client.end();
  }

  async scanSFTPAndFindReportFile(reportFileName: string, folderType: string) {
    const remoteFilePath = `/${this.config.get('app.ttbFTPRootPath')}/SalesAndTrends/${folderType}/${reportFileName}`;
    const client = await this.getFTPClient();

    const status = await client.exists(remoteFilePath);
    if (!status) {
      await client.end();
      return false;
    }

    await client.end();
    return true;
  }

  async getFTPClient() {
    const client = new Client();
    try {
      await client.connect({
        host: this.config.get('app.ttbFTPHost'),
        user: this.config.get('app.ttbFTPUser'),
        password: this.config.get('app.ttbFTPPassword'),
        port: 22,
        algorithms: {
          cipher: ['3des-cbc'],
        },
      });
    } catch (error) {
      this.logger.error(error.message);
      this.logger.log(`Delay 3s...`);
      await new Promise((resolve) => setTimeout(resolve, 3000));
      this.logger.log('Retry connect SFTP...');
      return this.getFTPClient();
    }
    return client;
  }
}

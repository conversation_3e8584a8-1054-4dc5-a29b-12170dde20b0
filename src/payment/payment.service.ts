import { InjectQueue } from '@nestjs/bull';
import { CACHE_MANAGER, Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { Cache } from 'cache-manager';
import camelcaseKeys from 'camelcase-keys';
import IAP from 'in-app-purchase';
import jws from 'jws';
import _ from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { EntityManager, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { AuthService } from 'src/auth/auth.service';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { AccessCodeStatus } from '../accesscode/access-code.type';
import { AccessCodeEntity } from '../accesscode/entities/access-code.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { KlaviyoService } from '../klaviyo/klaviyo.service';
import { LoggingService } from '../logging/logging.service';
import { EVERY_2_MINUTES, isCronJobHandlersEnabled, isProduction } from '../utils/cron';
import { ERROR_CODES } from '../utils/errors';
import { PLANS } from '../utils/plans';
import { PLATFORM_ANDROID, PLATFORM_IOS } from './constants';
import { SubscriptionEventLog } from './entities/subscription-event-log.entity';
import { SubscriptionEntity } from './entities/subscription.entity';
import { JobSubExpired, PaymentSubExpiredProcessorQueueName } from './payment-check-sub-expired.processor';
import {
  APPLE_EVENT_TEXT,
  AppleDownloadType,
  CHARGE_BE_EVENT,
  COMMON_SUBSCRIPTION_EVENT_TEXT,
  DownloadMessage,
  GOOGLE_EVENT_TEXT,
  GoogleDownloadType,
  PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME,
  PAYMENT_QUEUE_NAME,
  PLATFORM,
  SUBSCRIPTION_QUEUE_NAME,
  SUBSCRIPTION_SERVICES,
} from './payment.constants';
import { JobType, PaymentProcessorQueueName } from './payment.processor';
import { SubscribeDto } from './payment.type';
import { SubscriptionProcessorQueueName } from './subscription.processor';

const objKey = require('../../google-subscription-key.json');
const Verifier = require('google-play-billing-validator');
const EVERY_DAY_AT_3_30_PM = '30 15 * * *';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);
  constructor(
    private readonly config: ConfigService,
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    @Inject(forwardRef(() => AuthService)) private authService: AuthService,
    private readonly loggingService: LoggingService,
    private readonly klaviyoService: KlaviyoService,
    @InjectQueue(PAYMENT_QUEUE_NAME) private paymentQueue: Queue,
    @InjectQueue(PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME) private paymentSubExpiredQueue: Queue,
    @InjectQueue(SUBSCRIPTION_QUEUE_NAME) private subscriptionQueue: Queue,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepo: Repository<SubscriptionEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(SubscriptionEventLog) private readonly subscriptionEventLogRepo: Repository<SubscriptionEventLog>,
    @InjectRepository(AccessCodeEntity) private readonly accessCodeRepo: Repository<AccessCodeEntity>,
    @InjectEntityManager()
    private readonly entityManager: EntityManager,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {
    this.config = config;
  }

  async setupIAPForValidateReceiptOnAndroid() {
    IAP.reset();
    IAP.config({
      googlePublicKeyStrLive: this.config.get('app.googleIAPPublicKeyStrLive'),
      test: false,
      verbose: false,
    });
    await IAP.setup();
  }

  async setupIAPForValidateSubscriptionOnAndroid() {
    return new Verifier({
      email: objKey.client_email,
      key: objKey.private_key,
      projectId: objKey.project_id,
    });
  }

  async setupIAPForValidateReceiptOnIOS() {
    IAP.reset();
    IAP.config({
      appleExcludeOldTransactions: true,
      applePassword: this.config.get('app.appleIAPPassword'),
      test: false,
      verbose: false,
    });
    await IAP.setup();
  }

  async subscribePlan(uid: string, email: string, payload: SubscribeDto) {
    if (payload.platform === PLATFORM_IOS) {
      await this.setupIAPForValidateReceiptOnIOS();
      return this.subscribePlanIOS(uid, email, payload);
    }
    await this.setupIAPForValidateReceiptOnAndroid();
    return this.subscribePlanAndroid(uid, email, payload);
  }

  async subscribePlanFromLog(logId: string) {
    const log = await this.subscriptionEventLogRepo.findOne({ where: { id: parseInt(logId, 10) } });
    if (!log) {
      return;
    }
    if (log.platform === PLATFORM.APPLE) {
      return this.subscribePlanIOSFromLog(log);
    }
  }

  async syncTheLatestPlanToCDM(emails: string[]) {
    for (const email of emails) {
      const user = await this.userRepo.findOne({ where: { email } });
      if (!user) {
        continue;
      }
      const latestSubscription = await this.subscriptionRepo.findOne(
        { userId: user.id },
        { order: { createdAt: 'DESC' } }
      );
      if (!latestSubscription) {
        continue;
      }
      if (
        ![
          COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED,
          COMMON_SUBSCRIPTION_EVENT_TEXT.UPGRADED,
          COMMON_SUBSCRIPTION_EVENT_TEXT.REFUNDED,
          COMMON_SUBSCRIPTION_EVENT_TEXT.EXPIRED,
          COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_UPGRADED,
        ].includes(latestSubscription.notificationType as COMMON_SUBSCRIPTION_EVENT_TEXT)
      ) {
        continue;
      }
      console.log(`Start syncing for email: ${email}`);
      const plan = PLANS[latestSubscription.productId];
      const permissionList = this.cdmService.getPermissionListByPlan(plan.level);
      if (latestSubscription.notificationType === COMMON_SUBSCRIPTION_EVENT_TEXT.REFUNDED) {
        await this.resetToFreePlan(user.email, true);
        continue;
      }
      if (
        latestSubscription.notificationType === COMMON_SUBSCRIPTION_EVENT_TEXT.EXPIRED &&
        new Date().getTime() > new Date(latestSubscription.expirationDate).getTime()
      ) {
        await this.resetToFreePlan(user.email, true);
        continue;
      }
      await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
        myTMSubscriptionLevel: plan.level,
        subscriptionLength: plan.duration,
        tmAccessCode: false,
        subscriptionExpirationDate: new Date(latestSubscription.expirationDate).toISOString(),
        ...permissionList,
      });
    }
    return {
      success: true,
      error: null,
    };
  }

  async subscribePlanAndroid(uid: string, email: string, payload: SubscribeDto): Promise<any> {
    try {
      let verifiedReceipt;
      const data = JSON.parse(payload.receipt);
      this.logger.log({
        event: 'Android:Payments:Subscribe:Receipt',
        receipt: data,
      });
      try {
        const verifier = await this.setupIAPForValidateSubscriptionOnAndroid();
        const result = await verifier.verifySub({
          packageName: data.packageName,
          productId: data.productId,
          purchaseToken: data.purchaseToken,
          subscription: true,
        });
        if (!result.isSuccessful) {
          this.logger.log({
            event: 'Android:Payments:Subscribe:ReceiptVerificationFailed',
            result,
            ...data,
          });
          return PaymentService.receiptInvalidErrors();
        }
        verifiedReceipt = result.payload;
        this.logger.log({
          event: 'Android:Payments:Subscribe:ReceiptVerified',
          verifiedReceipt,
        });
        await this.subscriptionEventLogRepo.save({
          eventName: 'DIRECT_SUBSCRIBE_FROM_APP',
          receiptData: JSON.stringify(verifiedReceipt),
          platform: PLATFORM.GOOGLE,
          userId: uid,
          environment: 'Unknown',
        });
      } catch (e) {
        this.logger.log({
          event: 'Android:Payments:Subscribe:ReceiptVerificationFailed',
          error: e,
        });
        return PaymentService.receiptInvalidErrors();
      }
      const orderId = verifiedReceipt.orderId;
      const originalOrderId = verifiedReceipt.orderId.split('..')[0];
      const used = await this.isReceiptAlreadyUsed(orderId);
      if (used) {
        this.logger.log({
          event: 'Android:Payments:Subscribe:ReceiptUsed',
          receiptTransactionId: verifiedReceipt.orderId,
        });
        return this.cdmService.getConsumer(email, this.config.get('app.defaultRegionId'));
      }
      const { canSubscribe, errorCode, hint } = await this.canSubscribe(uid, PLATFORM_ANDROID, originalOrderId);
      if (!canSubscribe) {
        return PaymentService.cannotSubscribeErrors(errorCode, hint);
      }
      await this.cacheProcessingTransactionId(orderId);
      const queuePayload: JobType['data'] = {
        userId: uid,
        email,
        productId: data.productId,
        orderId,
        platform: PLATFORM.GOOGLE,
        receipt: verifiedReceipt,
        fromWebhook: false,
        subscriptionService: SUBSCRIPTION_SERVICES.GOOGLE,
      };
      const plan = PLANS[data.productId];
      const expirationDate = moment(parseInt(verifiedReceipt.expiryTimeMillis, 10)).toDate();
      await this.paymentQueue.add(PaymentProcessorQueueName.HANDLE_PAYMENT, queuePayload);
      const consumer = await this.cdmService.getConsumer(email, this.config.get('app.defaultRegionId'));
      const permissionList = this.cdmService.getPermissionListByPlan(plan.level || 0);
      return {
        ...consumer,
        myTMSubscriptionLevel: plan.level,
        subscriptionService: SUBSCRIPTION_SERVICES.GOOGLE,
        myTMPermission: {
          ...consumer.myTMPermission,
          subscriptionExpirationDate: expirationDate,
          subscriptionLength: plan.duration,
          ...permissionList,
        },
      };
    } catch (e) {
      this.logger.log({
        event: 'Android:Payments:Subscribe:Failed',
        error: e,
      });
      return PaymentService.receiptInvalidErrors();
    }
  }

  async subscribePlanIOS(uid: string, email: string, payload: SubscribeDto): Promise<any> {
    try {
      const response = await IAP.validate(payload.receipt);
      if (!response.latest_receipt_info || response.latest_receipt_info.length === 0) {
        return PaymentService.receiptInvalidErrors();
      }
      const receipt = response.latest_receipt_info[0];
      const environment = response.environment;
      this.logger.log({
        event: 'iOS:Payments:Subscribe:Receipt',
        receipt,
        environment,
      });
      await this.subscriptionEventLogRepo.save({
        eventName: 'DIRECT_SUBSCRIBE_FROM_APP',
        receiptData: JSON.stringify(receipt),
        platform: PLATFORM.APPLE,
        userId: uid,
        environment,
      });
      const used = await this.isReceiptAlreadyUsed(receipt.transaction_id);
      if (used) {
        this.logger.log({
          event: 'iOS:Payments:Subscribe:ReceiptUsed',
          receiptTransactionId: receipt.transaction_id,
        });
        const consumer = await this.cdmService.getConsumer(email, this.config.get('app.defaultRegionId'));
        return {
          ...consumer,
          receiptProcessed: true,
        };
      }
      const { canSubscribe } = await this.canSubscribe(uid, PLATFORM_IOS, receipt.original_transaction_id);
      if (!canSubscribe) {
        const consumer = await this.cdmService.getConsumer(email, this.config.get('app.defaultRegionId'));
        return {
          ...consumer,
          receiptProcessed: true,
        };
      }
      const queuePayload: JobType['data'] = {
        userId: uid,
        email,
        receipt,
        finalData: null,
        platform: PLATFORM.APPLE,
        fromWebhook: false,
        environment,
        subscriptionService: SUBSCRIPTION_SERVICES.APPLE,
      };
      await this.paymentQueue.add(PaymentProcessorQueueName.HANDLE_PAYMENT, queuePayload);
      await this.cacheProcessingTransactionId(receipt.transaction_id);
      const plan = PLANS[receipt.product_id];
      const expirationDate = moment(parseInt(receipt.expires_date_ms, 10)).toISOString();
      const consumer = await this.cdmService.getConsumer(email, this.config.get('app.defaultRegionId'));
      const permissionList = this.cdmService.getPermissionListByPlan(plan.level || 0);
      const updatedPermissions = {
        ...consumer.myTMPermission,
        subscriptionExpirationDate: expirationDate,
        subscriptionLength: plan.duration,
        ...permissionList,
      };
      await this.cdmService.cacheUserPermissionsWhenPurchase(uid, plan.level, updatedPermissions);
      return {
        ...consumer,
        myTMSubscriptionLevel: plan.level,
        subscriptionService: SUBSCRIPTION_SERVICES.APPLE,
        myTMPermission: updatedPermissions,
      };
    } catch (e) {
      this.logger.log({
        event: 'iOS:Payments:Subscribe:Failed',
        error: e,
      });
      return PaymentService.receiptInvalidErrors();
    }
  }

  async subscribePlanIOSFromLog(log: SubscriptionEventLog): Promise<any> {
    try {
      const receipt = JSON.parse(log.receiptData);
      const user = await this.userRepo.findOne({ where: { id: log.userId } });
      const queuePayload: JobType['data'] = {
        userId: user.id,
        email: user.email,
        receipt,
        finalData: null,
        platform: PLATFORM.APPLE,
        fromWebhook: false,
        environment: log.environment,
        subscriptionService: SUBSCRIPTION_SERVICES.APPLE,
      };
      await this.paymentQueue.add(PaymentProcessorQueueName.HANDLE_PAYMENT, queuePayload);
      const plan = PLANS[receipt.product_id];
      const expirationDate = moment(parseInt(receipt.expires_date_ms, 10)).toISOString();
      const consumer = await this.cdmService.getConsumer(user.email, this.config.get('app.defaultRegionId'));
      const permissionList = this.cdmService.getPermissionListByPlan(plan.level || 0);
      const updatedPermissions = {
        ...consumer.myTMPermission,
        subscriptionExpirationDate: expirationDate,
        subscriptionLength: plan.duration,
        ...permissionList,
      };
      await this.cdmService.cacheUserPermissionsWhenPurchase(user.id, plan.level, updatedPermissions);
      return {
        ...consumer,
        myTMSubscriptionLevel: plan.level,
        subscriptionService: SUBSCRIPTION_SERVICES.APPLE,
        myTMPermission: updatedPermissions,
      };
    } catch (e) {
      this.logger.log({
        event: 'iOS:Payments:Subscribe:Failed',
        error: e,
      });
      return PaymentService.receiptInvalidErrors();
    }
  }

  async isReceiptAlreadyUsed(transactionId) {
    const cachedProcessingTransactionId = await this.getCachedProcessingTransactionId(transactionId);
    if (cachedProcessingTransactionId && cachedProcessingTransactionId === transactionId) {
      return true;
    }
    const count = await this.subscriptionRepo.count({
      transactionId,
    });
    return count > 0;
  }

  async getPaymentHistories(userId: string) {
    return this.subscriptionRepo
      .createQueryBuilder('sub')
      .where('sub.userId = :userId', { userId })
      .orderBy({
        'sub.createdAt': 'DESC',
      })
      .getMany();
  }

  async canSubscribe(uid: string, platform: string, originalTransactionId: string) {
    if (await this.isUsingAccessCode(uid)) {
      return {
        canSubscribe: false,
        errorCode: ERROR_CODES.USER_IS_USING_ACCESS_CODE,
        hint: `This user is using access code`,
      };
    }
    const prevSubscription = await this.subscriptionRepo.findOne({
      where: { userId: uid },
      order: { createdAt: 'DESC' },
    });
    if (prevSubscription && prevSubscription.platform !== platform) {
      if (moment(new Date(prevSubscription.expirationDate)).isBefore(moment())) {
        return {
          canSubscribe: true,
        };
      }
      try {
        await this.loggingService.save({
          event: 'CAN-SUBSCRIBER-FAIL',
          data: {
            uid,
            platform,
            originalTransactionId,
            hint: `Already subscribed on ${prevSubscription.platform}`,
          },
        });
      } catch (error) {}

      return {
        canSubscribe: false,
        errorCode: ERROR_CODES.USER_HAS_SUBSCRIBE_A_SUBSCRIPTION_ON_ANOTHER_PLATFORM,
        hint: `Already subscribed on ${prevSubscription.platform}`,
        platform: prevSubscription.platform,
      };
    }
    if (!originalTransactionId) {
      return {
        canSubscribe: true,
      };
    }
    const prevOriginalSubscription = await this.subscriptionRepo.findOne({
      where: { originalTransactionId: originalTransactionId.split('..')[0] },
      order: { createdAt: 'DESC' },
    });
    if (
      prevOriginalSubscription &&
      prevOriginalSubscription.userId?.toString().toLowerCase() !== uid?.toString()?.toLowerCase()
    ) {
      const user = await this.userRepo.findOne({ where: { id: prevOriginalSubscription.userId } });
      try {
        await this.loggingService.save({
          event: 'CAN-SUBSCRIBER-FAIL',
          data: {
            uid,
            platform,
            originalTransactionId,
            email: user?.email,
            prevUserId: prevOriginalSubscription?.userId,
            hint: `Already subscribed for email ${user?.email}`,
          },
        });
      } catch (error) {}
      return {
        canSubscribe: false,
        errorCode: ERROR_CODES.THE_SUBSCRIPTION_ALREADY_USED_BY_ANOTHER_ACCOUNT,
        hint: `Already subscribed for email ${user.email}`,
        email: user.email,
      };
    }
    return {
      canSubscribe: true,
    };
  }

  async handleAppleNotification(data: any): Promise<any> {
    if (!data || !data.signedPayload) {
      return false;
    }
    try {
      const decoded = jws.decode(data.signedPayload);
      const payload = JSON.parse(decoded.payload);
      const notifyType = payload.notificationType;
      const subtype = payload.subtype;
      const environment = payload.data.environment;
      const signedTransactionInfo = JSON.parse(jws.decode(payload.data.signedTransactionInfo).payload);
      const signedRenewalInfo = JSON.parse(jws.decode(payload.data.signedRenewalInfo).payload);
      const finalData = await this.extractSubFromReceiptData(signedTransactionInfo);
      this.logger.log({
        event: 'iOS:Payments:Webhook:NotificationPayload',
        notifyType,
        subtype,
        environment,
        signedTransactionInfo,
        signedRenewalInfo,
        finalData,
      });
      if (!finalData) {
        return false;
      }
      const user = await this.userRepo.findOne(finalData.userId);
      await this.subscriptionEventLogRepo.save({
        eventName: notifyType,
        subtype,
        notifyData: JSON.stringify(payload),
        receiptData: JSON.stringify(signedTransactionInfo),
        platform: PLATFORM.APPLE,
        userId: user.id,
        environment,
      });
      const acceptedNotifyTypes = [
        APPLE_EVENT_TEXT.DID_RENEW,
        APPLE_EVENT_TEXT.SUBSCRIBED,
        APPLE_EVENT_TEXT.DID_RECOVER,
        APPLE_EVENT_TEXT.INTERACTIVE_RENEWAL,
        APPLE_EVENT_TEXT.CANCEL,
        APPLE_EVENT_TEXT.REFUND,
        APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_PREF,
        APPLE_EVENT_TEXT.OFFER_REDEEMED,
        APPLE_EVENT_TEXT.REFUND_DECLINED,
        APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_STATUS,
      ];
      if (!acceptedNotifyTypes.includes(notifyType)) {
        this.logger.log({
          event: 'iOS:Payments:Webhook:SkippedNotificationType',
          notifyType,
        });
        return false;
      }
      const queuePayload: JobType['data'] = {
        notifyType,
        userId: user.id,
        email: user.email,
        signedTransactionInfo,
        signedRenewalInfo,
        subtype,
        finalData,
        platform: PLATFORM.APPLE,
        fromWebhook: true,
        environment,
        subscriptionService: SUBSCRIPTION_SERVICES.APPLE,
      };
      await this.paymentQueue.add(PaymentProcessorQueueName.HANDLE_PAYMENT, queuePayload);
      return true;
    } catch (error) {
      this.logger.log({
        event: 'iOS:Payments:Webhook:Failed',
        error,
      });
      return true;
    }
  }

  async extractSubFromReceiptData(receiptData: any): Promise<any> {
    try {
      const originalTransactionId = receiptData.originalTransactionId;
      const userSubscriptions = await this.subscriptionRepo.find({ originalTransactionId });
      if (!userSubscriptions || userSubscriptions.length === 0) {
        return false;
      }

      const userId = userSubscriptions[0].userId;
      return {
        id: v4(),
        userId,
        platform: PLATFORM_IOS,
        createdBy: userId,
        ...camelcaseKeys(receiptData),
      };
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async isUsingAccessCode(userId: string) {
    return (
      (await this.subscriptionRepo
        .createQueryBuilder('sub')
        .where('sub.userId = :userId', { userId })
        .andWhere('sub.fromAccessCode IS NOT NULL')
        .andWhere('sub.expirationDate > :date', {
          date: new Date(),
        })
        .getCount()) > 0
    );
  }
  async downgradeToChampionPlan(email: string, subId: string) {
    const user = await this.userRepo.findOne({ where: { email } });
    let shouldResetToChampionPlan = true;
    const myTMPermission = await this.cdmService.getMyTMPermission(user.id, email);
    const subscriptionExpirationDate = myTMPermission?.subscriptionExpirationDate;
    if (
      user.myTMSubscriptionLevel > 0 &&
      subscriptionExpirationDate &&
      new Date(subscriptionExpirationDate).getTime() > new Date().getTime()
    ) {
      shouldResetToChampionPlan = false;
    }
    if (!shouldResetToChampionPlan) {
      return false;
    }
    try {
      const subscriptionChampionAnnual = await this.subscriptionRepo.findOne({ refSubscriptionId: subId });
      if (!subscriptionChampionAnnual) {
        console.log(`NotFound subscription Champion Annual for ${email}`);
        return false;
      }
      const permissionList = this.cdmService.getPermissionListByPlan(1);
      const user = await this.userRepo.findOne({ where: { email } });
      await this.userRepo.update({ email }, { myTMSubscriptionLevel: 1, tmAccessCode: false });
      await this.userPermissionRepo.update(
        { userId: user.id },
        {
          subscriptionLength: 12,
          subscriptionExpirationDate: subscriptionChampionAnnual.expirationDate?.toISOString(),
          isSyncCDM: false,
          ...permissionList,
        }
      );
      await this.cdmService.clearCacheUserPermissionsWhenPurchase(user.id);
      const consumer = await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
        myTMSubscriptionLevel: 1,
        subscriptionLength: 12,
        tmAccessCode: false,
        subscriptionExpirationDate: subscriptionChampionAnnual.expirationDate?.toISOString(),
        ...permissionList,
      });
      return consumer.myTMSubscriptionLevel === 1;
    } catch (error) {
      console.log(error);
      return false;
    }
  }
  async resetToFreePlan(email: string, force = false) {
    const user = await this.userRepo.findOne({ where: { email } });
    let shouldResetToFreePlan = true;
    const myTMPermission = await this.cdmService.getMyTMPermission(user.id, email);
    const subscriptionExpirationDate = myTMPermission?.subscriptionExpirationDate;
    if (
      !force &&
      user.myTMSubscriptionLevel > 0 &&
      subscriptionExpirationDate &&
      new Date(subscriptionExpirationDate).getTime() > new Date().getTime()
    ) {
      shouldResetToFreePlan = false;
    }
    if (!shouldResetToFreePlan) {
      return false;
    }
    try {
      const permissionList = this.cdmService.getPermissionListByPlan(0);
      const user = await this.userRepo.findOne({ where: { email } });
      await this.userRepo.update({ email }, { myTMSubscriptionLevel: 0, tmAccessCode: false });
      await this.userPermissionRepo.update(
        { userId: user.id },
        {
          subscriptionLength: 0,
          subscriptionExpirationDate: new Date().toISOString(),
          isSyncCDM: false,
          ...permissionList,
        }
      );
      await this.cdmService.clearCacheUserPermissionsWhenPurchase(user.id);
      if (user.cdmUID === 'NULL') {
        this.logger.log(`Err Func resetToFreePlan cdmUID is null - userId ${user?.id}`);
        return;
      }
      const consumer = await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
        myTMSubscriptionLevel: 0,
        subscriptionLength: 0,
        tmAccessCode: false,
        subscriptionExpirationDate: new Date().toISOString(),
        ...permissionList,
      });
      return consumer.myTMSubscriptionLevel === 0;
    } catch (error) {
      console.log(error);
      this.logger.error(`Err Func resetToFreePlan:`);
      this.logger.log(error);
      return false;
    }
  }

  async handleGooglePlayNotification(notificationData: any) {
    try {
      if (!notificationData.message) {
        return;
      }
      await this.loggingService.save(notificationData);
      const encodedString = notificationData.message.data.toString();
      const bufferObj = Buffer.from(encodedString, 'base64');

      const decodedString = bufferObj.toString('utf8');
      const data = JSON.parse(decodedString);

      const subscriptionNotification = data.subscriptionNotification;
      const notificationType = subscriptionNotification.notificationType;
      const notificationEvent = GOOGLE_EVENT_TEXT[notificationType];
      const packageName = data.packageName;
      const productId = subscriptionNotification.subscriptionId;
      const purchaseToken = subscriptionNotification.purchaseToken;

      this.logger.log({
        event: 'Android:Payments:Webhook:NotificationPayload',
        data,
      });

      if (!notificationEvent) {
        this.logger.log({
          event: 'Android:Payments:Webhook:SkippedNotification',
          data,
        });
        return true;
      }

      const receipt = await this.validateGooglePlayReceipt(packageName, productId, purchaseToken);

      const originalTransactionId = receipt.orderId.split('..')[0];
      const userSubscription = await this.subscriptionRepo.findOne({ originalTransactionId });
      const userId = userSubscription?.userId || receipt.obfuscatedExternalAccountId;

      await this.subscriptionEventLogRepo.save({
        eventName: notificationEvent,
        notifyData: decodedString,
        platform: PLATFORM.GOOGLE,
        receiptData: receipt ? JSON.stringify(receipt) : 'false',
        userId,
        environment: 'Unknown',
      });

      if (!receipt.productId) {
        return false;
      }
      const acceptedNotifyTypes = [
        GOOGLE_EVENT_TEXT.SUBSCRIPTION_RENEWED,
        GOOGLE_EVENT_TEXT.SUBSCRIPTION_RESTARTED,
        GOOGLE_EVENT_TEXT.SUBSCRIPTION_RECOVERED,
        GOOGLE_EVENT_TEXT.SUBSCRIPTION_REVOKED,
        GOOGLE_EVENT_TEXT.SUBSCRIPTION_CANCELED,
      ];
      if (!acceptedNotifyTypes.includes(notificationType)) {
        this.logger.log({
          event: 'Android:Payments:Webhook:SkippedNotificationType',
          notifyType: notificationType,
        });
        return false;
      }
      const queuePayload: JobType['data'] = {
        notifyType: notificationType,
        receipt,
        userId,
        platform: PLATFORM.GOOGLE,
        fromWebhook: true,
        environment: 'Unknown',
        subscriptionService: SUBSCRIPTION_SERVICES.GOOGLE,
      };
      await this.paymentQueue.add(PaymentProcessorQueueName.HANDLE_PAYMENT, queuePayload);
      return true;
    } catch (error) {
      console.log(error);
      return true;
    }
  }
  async handleChargeBeeNotification(notificationData: any) {
    try {
      if (!notificationData.content || !notificationData.event_type) {
        return;
      }
      this.logger.log({
        event: 'ChargeBee:Payments:Webhook:NotificationPayload',
        content: notificationData,
      });
      await this.loggingService.save(notificationData);

      const { event_type, content, id } = notificationData;
      const subscriptionNotification = content.subscription;
      const notificationType = event_type.toUpperCase();

      const acceptedNotifyTypes = [
        CHARGE_BE_EVENT.SUBSCRIPTION_RENEWED,
        CHARGE_BE_EVENT.SUBSCRIPTION_CREATED,
        CHARGE_BE_EVENT.SUBSCRIPTION_CANCELED,
        CHARGE_BE_EVENT.SUBSCRIPTION_PAUSED,
        CHARGE_BE_EVENT.SUBSCRIPTION_RESUMED,
        CHARGE_BE_EVENT.SUBSCRIPTION_REACTIVATED,
        CHARGE_BE_EVENT.SUBSCRIPTION_CHANGED,
        CHARGE_BE_EVENT.SUBSCRIPTION_CHANGES_SCHEDULED,
      ];
      const packageName = subscriptionNotification?.plan_id.replace('-', '_') || '';
      if (!acceptedNotifyTypes.includes(notificationType) || !PLANS[packageName]) {
        this.logger.log({
          event: 'ChargeBee:Payments:Webhook:SkippedNotificationType',
          notifyType: notificationType,
          packageId: packageName,
        });
        return false;
      }

      content['productId'] = packageName;
      content['id'] = id;
      const originalTransactionId = id;
      let user = await this.userRepo.findOne({ email: content.customer.email });
      if (!user) {
        // create user if not exits in MyTM
        this.logger.debug(`Force create new account for ChargeBee Subscription`);
        user = await this.authService.forceCreateUser(content.customer.email);
      }

      const userId = user.id;

      const { canSubscribe, errorCode, hint } = await this.canSubscribe(
        userId,
        PLATFORM.CHARGE_BEE,
        originalTransactionId
      );
      if (!canSubscribe) {
        return PaymentService.cannotSubscribeErrors(errorCode, hint);
      }

      await this.subscriptionEventLogRepo.save({
        eventName: notificationType,
        receiptData: JSON.stringify(notificationData),
        platform: PLATFORM.CHARGE_BEE,
        userId,
        environment: 'Unknown',
      });

      if (!packageName) {
        return false;
      }

      const queuePayload: JobType['data'] = {
        notifyType: notificationType,
        receipt: content,
        userId,
        platform: PLATFORM.CHARGE_BEE,
        fromWebhook: true,
        environment: 'Unknown',
        subscriptionService: SUBSCRIPTION_SERVICES.CHARGE_BEE,
      };
      await this.paymentQueue.add(PaymentProcessorQueueName.HANDLE_PAYMENT, queuePayload);
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  }
  async validateGooglePlayReceipt(packageName: string, productId: string, purchaseToken: string) {
    let receipt;
    try {
      const verifier = await this.setupIAPForValidateSubscriptionOnAndroid();
      const result = await verifier.verifySub({
        packageName,
        productId,
        purchaseToken,
        subscription: true,
      });
      if (!result.isSuccessful) {
        this.logger.log({
          event: 'Android:Payments:Subscribe:ReceiptVerificationFailed',
          result,
          purchaseToken,
          productId,
          packageName,
        });
        return PaymentService.receiptInvalidErrors();
      }
      receipt = result.payload;
      this.logger.log({
        event: 'Android:Payments:Subscribe:ReceiptVerified',
        verifiedReceipt: receipt,
      });
    } catch (e) {
      this.logger.log({
        event: 'Android:Payments:Subscribe:ReceiptVerificationFailed',
        error: e,
      });
      return PaymentService.receiptInvalidErrors();
    }
    receipt.productId = productId;
    return receipt;
  }

  async cacheProcessingTransactionId(transactionId: string) {
    return this.cacheManager.set(`PAYMENTS:PROCESSING:${transactionId.toString()}`, transactionId.toString(), {
      ttl: 0,
    });
  }

  async getCachedProcessingTransactionId(transactionId: string) {
    return this.cacheManager.get(`PAYMENTS:PROCESSING:${transactionId.toString()}`);
  }

  static receiptInvalidErrors() {
    return {
      iapError: {
        error: ERROR_CODES.RECEIPT_IS_INVALID,
        errorDescription: 'Your receipt is invalid!',
      },
    };
  }

  static cannotSubscribeErrors(code: string, errorDescription: string) {
    return {
      iapError: {
        error: code,
        errorDescription: errorDescription || 'Your do not able to subscribe!',
      },
    };
  }

  // @Cron(isProduction ? CronExpression.EVERY_HOUR : CronExpression.EVERY_5_MINUTES)
  // async checkExpiredSubscription() {
  //   if (!isCronJobHandlersEnabled(this.config)) {
  //     return;
  //   }
  //   this.logger.debug(`Checking expired subscriptions`);
  //   const sqlQuery = `
  //     SELECT t1.* FROM Subscriptions AS t1
  //     JOIN (SELECT userId , MAX(createdAt) createdAt FROM Subscriptions GROUP BY userId) AS t2
  //     ON t1.userId = t2.userId AND t1.createdAt = t2.createdAt
  //     JOIN Users as t3 ON t1.userId = t3.id
  //     WHERE t1.isChecked = 0 AND t1.expirationDate < DATEADD(mi,-4,GETDATE()) AND (t1.notificationType is null OR t1.notificationType <> 'EXPIRED') AND t3.myTMSubscriptionLevel > 0
  //   `;
  //   const expiredSubs = await this.entityManager.query(sqlQuery);
  //   this.logger.debug(`Found ${expiredSubs.length} expired subscriptions`);
  //   const chunkSubs = _.chunk(expiredSubs, 20);
  //   for (const chunkSub of chunkSubs) {
  //     const queuePayload: JobSubExpired['data'] = {
  //       subs: chunkSub,
  //     };
  //     await this.paymentSubExpiredQueue.add(
  //       PaymentSubExpiredProcessorQueueName.HANDLE_PAYMENT_CHECK_SUB_EXPIRED,
  //       queuePayload,
  //       {
  //         delay: 10000,
  //       }
  //     );
  //   }
  //   this.logger.debug(`End of checking expired subscriptions`);
  // }

  async handleCheckExpiredUser(sub) {
    try {
      const user = await this.userRepo.findOne(sub?.userId);
      if (!user) {
        console.log(`Not found userId ${sub?.userId}`);
        return;
      }
      const success = await this.resetToFreePlan(user.email, false);
      if (!success) {
        console.log('success', success, sub?.userId);
        this.logger.log(`Err Func handleCheckExpiredUser success = false - userId ${sub?.userId}`);
        return;
      }
      await this.subscriptionRepo.update(sub?.id, { isChecked: true });
      if (sub.fromAccessCode) {
        await this.accessCodeRepo.update(
          { code: sub.fromAccessCode, userId: sub?.userId },
          { status: AccessCodeStatus.EXPIRED }
        );
      }
      const expiredSub = {
        ...sub,
        notificationType: COMMON_SUBSCRIPTION_EVENT_TEXT.EXPIRED,
        refSubscriptionId: sub?.id,
        id: v4(),
        createdAt: new Date(),
      };
      await this.subscriptionRepo.save(expiredSub);
      return true;
    } catch (error) {
      console.log(error);
      this.logger.error(`Err PAYMENT Func handleCheckExpiredUser`);
      this.logger.log(error);
      await this.loggingService.save({
        event: 'Err_Payment_Func_handleCheckExpiredUser',
        data: {
          error,
          userId: sub?.userId,
        },
      });
    }
  }

  @Cron(isProduction ? CronExpression.EVERY_10_MINUTES : CronExpression.EVERY_5_MINUTES)
  async checkExpiredDownGradeChargeBeeSubscription() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    this.logger.debug(`Checking expired charge bee downgrade subscriptions`);
    const sqlQuery = `
      SELECT t1.* FROM Subscriptions AS t1
      WHERE isChecked = 0 AND 
      subtype = 'DOWNGRADED' AND 
      platform = 'CHARGE_BEE' AND 
      expirationDate < DATEADD(mi,-4,GETDATE()) AND
      (notificationType is null OR notificationType <> 'EXPIRED')
    `;
    const expiredSubs = await this.entityManager.query(sqlQuery);
    this.logger.debug(`Found ${expiredSubs.length} expired charge bee subscriptions`);
    for (const sub of expiredSubs) {
      try {
        const user = await this.userRepo.findOne(sub.userId);
        const success = await this.downgradeToChampionPlan(user.email, sub.id);
        if (!success) {
          continue;
        }
        await this.subscriptionRepo.update(sub.id, {
          isChecked: true,
          notificationType: COMMON_SUBSCRIPTION_EVENT_TEXT.EXPIRED,
        });
      } catch (error) {
        console.log(error);
      }
    }
    this.logger.debug(`End of checking expired subscriptions`);
  }
  // @Cron(EVERY_DAY_AT_3_30_PM)
  // async downloadSubscriptionReports(reportDate?: string) {
  //   if (isCronJobHandlersEnabled(this.config) || reportDate) {
  //     const downloadReportDate = this.getAppleReportDate();
  //     this.logger.log(`Cron Job ${DownloadMessage[AppleDownloadType.SUBSCRIPTION]} run...`);
  //     await this.subscriptionQueue
  //       .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //         downloadType: AppleDownloadType.SUBSCRIPTION,
  //         downloadReportDate: reportDate || downloadReportDate,
  //         platform: PLATFORM.APPLE,
  //       })
  //       .catch((e) => console.error(e));
  //     await this.subscriptionQueue
  //       .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //         downloadType: AppleDownloadType.SUBSCRIPTION_EVENT,
  //         downloadReportDate: reportDate || downloadReportDate,
  //         platform: PLATFORM.APPLE,
  //       })
  //       .catch((e) => console.error(e));
  //     await this.subscriptionQueue
  //       .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //         downloadType: AppleDownloadType.SUBSCRIBER,
  //         downloadReportDate: reportDate || downloadReportDate,
  //         platform: PLATFORM.APPLE,
  //       })
  //       .catch((e) => console.error(e));
  //
  //     const googleDownloadReportDate = this.getGoogleReportDate();
  //     await this.subscriptionQueue
  //       .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //         downloadType: GoogleDownloadType.EARNINGS,
  //         downloadReportDate: reportDate || googleDownloadReportDate,
  //         platform: PLATFORM.GOOGLE,
  //         downloadedAt: moment().format('YYYY-MM-DD'),
  //       })
  //       .catch((e) => console.error(e));
  //     const today = moment().date();
  //     const dayInMonth = moment().daysInMonth();
  //     await this.subscriptionQueue
  //       .add(
  //         SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT,
  //         {
  //           downloadType: GoogleDownloadType.SUBSCRIPTION,
  //           downloadReportDate: reportDate || moment().format('YYYYMM'),
  //           platform: PLATFORM.GOOGLE,
  //           downloadedAt: moment().format('YYYY-MM-DD'),
  //         },
  //         { delay: today === dayInMonth ? 1000 * 60 * 60 * 24 : 0 }
  //       )
  //       .catch((e) => console.error(e));
  //     await this.subscriptionQueue
  //       .add(SubscriptionProcessorQueueName.DOWNLOAD_SUBSCRIPTION_REPORT, {
  //         downloadType: GoogleDownloadType.INSTALLS,
  //         downloadReportDate: reportDate || moment().format('YYYYMM'),
  //         platform: PLATFORM.GOOGLE,
  //         downloadedAt: moment().format('YYYY-MM-DD'),
  //       })
  //       .catch((e) => console.error(e));
  //   }
  // }

  getAppleReportDate() {
    const applePreviousNumberDaysToDownload = parseInt(this.config.get('app.applePreviousNumberDaysToDownload'), 10);
    return moment().subtract(applePreviousNumberDaysToDownload, 'days').format('YYYY-MM-DD');
  }

  getGoogleReportDate() {
    return moment().subtract(1, 'month').format('YYYYMM');
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Inject, Logger, forwardRef } from '@nestjs/common';
import { Job } from 'bull';
import { ConfigService } from 'nestjs-config';
import { PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME } from './payment.constants';
import { PaymentService } from './payment.service';

export type JobSubExpired = Job<{
  subs: any;
}>;

export enum PaymentSubExpiredProcessorQueueName {
  HANDLE_PAYMENT_CHECK_SUB_EXPIRED = 'handle-payment-check-sub-expired',
}

@Processor(PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME)
export class PaymentCheckSubExpiredProcessor {
  private readonly logger = new Logger(PaymentCheckSubExpiredProcessor.name);
  constructor(
    private readonly config: ConfigService,
    @Inject(forwardRef(() => PaymentService)) private paymentService: PaymentService
  ) {}

  @Process({
    name: PaymentSubExpiredProcessorQueueName.HANDLE_PAYMENT_CHECK_SUB_EXPIRED,
  })
  async handlePaymentCheckSubExpired(job: JobSubExpired): Promise<any> {
    const { subs } = job.data;

    this.logger.debug(`Run Job handle-payment-check-sub-expired running...`);
    for (const sub of subs) {
      try {
        await this.paymentService.handleCheckExpiredUser(sub);
      } catch (error) {
        this.logger.error(`Err Payment Processor handlePaymentCheckSubExpired`);
        this.logger.log(error);
      }
    }
  }
}

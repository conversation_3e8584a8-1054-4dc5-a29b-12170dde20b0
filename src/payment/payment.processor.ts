import { Process, Processor } from '@nestjs/bull';
import { Inject, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import camelcaseKeys from 'camelcase-keys';
import IAP from 'in-app-purchase';
import _ from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import { IsNull, Not, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { LoggingService } from '../logging/logging.service';
import { UserReferralService } from '../user-referral/user-referral.service';
import { convertSubscriptionDates } from '../utils/datetime';
import { PLANS, PlanType } from '../utils/plans';
import { PLATFORM_ANDROID, PLATFORM_IOS } from './constants';
import { SubscriptionEventLog } from './entities/subscription-event-log.entity';
import { SubscriptionEntity } from './entities/subscription.entity';
import {
  APPLE_EVENT_TEXT,
  APPLE_SUBTYPE,
  CHARGE_BE_EVENT,
  COMMON_SUBSCRIPTION_EVENT_TEXT,
  GOOGLE_EVENT_TEXT,
  PAYMENT_QUEUE_NAME,
  PLATFORM,
} from './payment.constants';
import { PaymentService } from './payment.service';

export type JobType = Job<{
  receipt?: any;
  userId: string;
  email?: string;
  fromWebhook: boolean;
  finalData?: any;
  notifyType?: string | number;
  productId?: string;
  orderId?: string;
  platform: string;
  autoRenewStatus?: boolean;
  signedTransactionInfo?: any;
  signedRenewalInfo?: any;
  subtype?: string;
  environment?: string;
  subscriptionService?: string;
}>;

export enum PaymentProcessorQueueName {
  HANDLE_PAYMENT = 'handle-payment',
}

@Processor(PAYMENT_QUEUE_NAME)
export class PaymentProcessor {
  private readonly logger = new Logger(PaymentProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private cdmService: CdmService,
    @Inject(forwardRef(() => PaymentService)) private paymentService: PaymentService,
    private readonly klaviyoService: KlaviyoService,
    private readonly loggingService: LoggingService,
    private readonly userReferralService: UserReferralService,
    @InjectRepository(SubscriptionEntity) private readonly subscriptionRepo: Repository<SubscriptionEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectRepository(UserPermissionsEntity) private readonly userPermissionRepo: Repository<UserPermissionsEntity>,
    @InjectRepository(SubscriptionEventLog) private readonly subscriptionEventLogRepo: Repository<SubscriptionEventLog>
  ) {}

  async setupIAPForValidateReceiptOnIOS() {
    IAP.reset();
    IAP.config({
      appleExcludeOldTransactions: true,
      applePassword: this.config.get('app.appleIAPPassword'),
      test: false,
      verbose: false,
    });
    await IAP.setup();
  }

  @Process({
    name: PaymentProcessorQueueName.HANDLE_PAYMENT,
    concurrency: 1,
  })
  async handlePayment(job: JobType) {
    const { platform } = job.data;
    if (platform === PLATFORM.APPLE) {
      return this.appleIAP(job);
    }
    if (platform === PLATFORM.CHARGE_BEE) {
      return this.handleChargeBeeIAPWebhook(job);
    }
    return this.googleIAP(job);
  }

  async appleIAP(job: JobType): Promise<any> {
    const { receipt, userId, email, fromWebhook } = job.data;
    try {
      await this.setupIAPForValidateReceiptOnIOS();
      if (fromWebhook) {
        this.logger.log({
          event: `iOS:Payments:Webhook:Processor`,
          jobData: job.data,
        });
      } else {
        this.logger.log({
          event: `iOS:Payments:AppDirect:Processor`,
          jobData: job.data,
        });
      }
      const hasSubscribedBefore = (await this.subscriptionRepo.count({ where: { userId, fromAccessCode: null } })) > 0;
      if (!fromWebhook) {
        if (hasSubscribedBefore && !receipt.is_trial_period) {
          this.logger.log({
            event: `iOS:Payments:AppDirect:Processor:HasSubscribedBefore`,
            jobData: job.data,
          });
          setTimeout(() => {
            this.cdmService.clearCacheUserPermissionsWhenPurchase(userId);
          }, 10000);
          return;
        }
        const plan = PLANS[receipt.product_id];
        const expirationDate = moment(parseInt(receipt.expires_date_ms, 10)).toDate();
        const subscriptionPayload = {
          id: v4(),
          userId,
          platform: PLATFORM_IOS,
          createdBy: userId,
          amount: plan.price,
          expirationDate,
          notificationType: COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED,
          ...camelcaseKeys(receipt),
        };
        await this.subscriptionRepo.save(subscriptionPayload);
        await this.cdmService.clearCacheUserPermissionsWhenPurchase(userId);
        return this.savePlanToCDM(
          userId,
          email,
          plan,
          expirationDate,
          subscriptionPayload.notificationType,
          PLATFORM_IOS,
          job.data.subscriptionService
        );
      }
      return this.handleAppleIAPWebhook(job);
    } catch (err) {
      console.log('ERR Payment Func appleIAP - userId - err', userId, err);
      this.logger.error(`ERR Payment Func appleIAP`);
      this.logger.log(err);
      await this.loggingService.save({
        event: 'ERR_PAYMENT_FUNC_appleIAP',
        data: {
          error: err,
          userId,
        },
      });
      return null;
    }
  }

  async handleAppleIAPWebhook(job: JobType) {
    const { signedTransactionInfo, email, notifyType, subtype, finalData, signedRenewalInfo, environment } = job.data;
    const user = await this.userRepo.findOne(finalData.userId);
    const plan = PLANS[signedTransactionInfo.productId];
    const expirationDate = moment(signedTransactionInfo.expiresDate).toDate();
    const duplicatedSubscription = await this.isDuplicatedSubscription(
      user.id,
      expirationDate.toISOString(),
      notifyType as string
    );
    if (duplicatedSubscription) {
      this.logger.log({
        event: `iOS:Payments:Webhook:Processor:DuplicatedNotificationType`,
        userId: user.id,
        expirationDate,
        notifyType,
      });
      await this.cdmService.clearCacheUserPermissionsWhenPurchase(user.id);
      return false;
    }
    let commonNotifyType =
      notifyType === APPLE_EVENT_TEXT.DID_RECOVER
        ? COMMON_SUBSCRIPTION_EVENT_TEXT.RECOVERED
        : COMMON_SUBSCRIPTION_EVENT_TEXT.RENEWED;
    let prevPlan;

    if (
      notifyType === APPLE_EVENT_TEXT.CANCEL ||
      (notifyType === APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_STATUS && !signedRenewalInfo.autoRenewStatus)
    ) {
      commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.CANCELED;
    }
    if (notifyType === APPLE_EVENT_TEXT.REFUND) {
      commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.REFUNDED;
    }
    if (
      notifyType === APPLE_EVENT_TEXT.INTERACTIVE_RENEWAL ||
      notifyType === APPLE_EVENT_TEXT.SUBSCRIBED ||
      notifyType === APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_PREF
    ) {
      if (notifyType === APPLE_EVENT_TEXT.SUBSCRIBED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED;
      }
      const prevSubscription = await this.subscriptionRepo.findOne(
        { userId: user.id },
        { order: { createdAt: 'DESC' } }
      );
      if (prevSubscription && subtype !== COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED) {
        const upgradeDowngradeResults = this.handleUpgradeDowngrade(prevSubscription, plan);
        if (upgradeDowngradeResults.notificationEvent && upgradeDowngradeResults.prevPlan) {
          commonNotifyType = upgradeDowngradeResults.notificationEvent;
          prevPlan = upgradeDowngradeResults.prevPlan;
        }
      }
    }

    const purchaseDates = convertSubscriptionDates(signedTransactionInfo.purchaseDate);
    const expiresDates = convertSubscriptionDates(signedTransactionInfo.expiresDate);
    const originalPurchaseDates = convertSubscriptionDates(signedTransactionInfo.originalPurchaseDate);

    let subscriptionPayload: SubscriptionEntity = {
      ...finalData,
      amount: plan.price,
      expirationDate,
      subtype,
      purchaseDatePst: purchaseDates.pst,
      purchaseDateMs: purchaseDates.ms,
      purchaseDate: purchaseDates.ect,
      expiresDatePst: expiresDates.pst,
      expiresDateMs: expiresDates.ms,
      expiresDate: expiresDates.ect,
      originalPurchaseDatePst: originalPurchaseDates.pst,
      originalPurchaseDate: originalPurchaseDates.ect,
      originalPurchaseDateMs: originalPurchaseDates.ms,
      notificationType: commonNotifyType,
      environment,
      isChecked: commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.REFUNDED,
    };

    if (notifyType === APPLE_EVENT_TEXT.REFUND) {
      const prevSubscription = await this.subscriptionRepo.findOne(
        { userId: user.id },
        { order: { createdAt: 'DESC' } }
      );
      const isRefundedTheCurrentPlan = this.isRefundedTheCurrentPlan(signedRenewalInfo, prevSubscription);
      const isUsingAccessCode = await this.paymentService.isUsingAccessCode(user.id);
      if (isRefundedTheCurrentPlan && !isUsingAccessCode) {
        await this.resetToFreePlan(email, true);
      } else {
        const prevRefundSubscription = await this.subscriptionRepo.findOne(
          { userId: user.id, productId: signedRenewalInfo.productId },
          { order: { createdAt: 'DESC' } }
        );
        if (prevRefundSubscription) {
          delete prevRefundSubscription.id;
          subscriptionPayload = {
            ...subscriptionPayload,
            ...prevRefundSubscription,
            revocationReason: signedTransactionInfo.revocationReason,
            notificationType: COMMON_SUBSCRIPTION_EVENT_TEXT.REFUNDED,
          };
        }
      }
    }

    if (subtype === APPLE_SUBTYPE.DOWNGRADE) {
      subscriptionPayload.notificationType = COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED;
      subscriptionPayload.prevPlan = signedRenewalInfo.productId;
      subscriptionPayload.productId = signedRenewalInfo.autoRenewProductId;
      subscriptionPayload.amount = PLANS[signedRenewalInfo.autoRenewProductId].price;
      if (this.isDurationDowngraded(signedRenewalInfo.productId, PLANS[signedRenewalInfo.autoRenewProductId])) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_DOWNGRADED;
        subscriptionPayload.prevPlan = signedRenewalInfo.productId;
        subscriptionPayload.productId = signedRenewalInfo.autoRenewProductId;
        subscriptionPayload.amount = PLANS[signedRenewalInfo.autoRenewProductId].price;
      }
      if (this.isDurationUpgraded(signedRenewalInfo.productId, PLANS[signedRenewalInfo.autoRenewProductId])) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_UPGRADED;
        subscriptionPayload.prevPlan = signedRenewalInfo.productId;
        subscriptionPayload.productId = signedRenewalInfo.autoRenewProductId;
        subscriptionPayload.amount = PLANS[signedRenewalInfo.autoRenewProductId].price;
      }
    }

    if (signedTransactionInfo.revocationDate) {
      subscriptionPayload.revocationDate = moment(signedTransactionInfo.revocationDate).toDate();
    }

    if (prevPlan) {
      subscriptionPayload.prevPlan = prevPlan;
    }

    await this.subscriptionRepo.save(subscriptionPayload);
    if (commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.RENEWED) {
      const subTrial = await this.subscriptionRepo
        .createQueryBuilder('s')
        .where('s.userId = :userId', { userId: user?.id })
        .andWhere('s.expirationDate >= getdate()')
        .andWhere("s.isTrialPeriod = 'true'")
        .getOne();

      if (subTrial && subTrial.id) {
        await this.subscriptionRepo.update({ id: subTrial.id }, { isTrialPeriod: 'false' });
      }
    }
    this.trackingUpgradeDowngradeToKlaviyo(email, commonNotifyType);

    if (this.shouldUpdateConsumer(notifyType as string, subtype)) {
      await this.savePlanToCDM(
        user.id,
        email,
        plan,
        expirationDate,
        commonNotifyType,
        'iOS',
        job.data.subscriptionService
      );
    }
    if (notifyType === APPLE_EVENT_TEXT.CANCEL) {
      await this.resetToFreePlan(email, true);
    }
    if (this.shouldTrackingCanceledSubscriptions(commonNotifyType, notifyType, subtype)) {
      this.klaviyoService.track(email, KlaviyoTrackEvents.CANCELED_SUBSCRIPTION);
    }
    await this.cdmService.clearCacheUserPermissionsWhenPurchase(finalData.userId);
    return true;
  }

  async googleIAP(job: JobType): Promise<any> {
    const { receipt, productId, orderId, userId, email, fromWebhook, environment } = job.data;
    try {
      this.logger.log(`Handle Google IAP for product: ${productId}`);
      if (!fromWebhook) {
        const plan = PLANS[productId];
        const expirationDate = moment(parseInt(receipt.expiryTimeMillis, 10)).toDate();
        let commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED;
        let prevPlan;
        const prevSubscription = await this.subscriptionRepo.findOne({ userId }, { order: { createdAt: 'DESC' } });
        const upgradeDowngradeResults = this.handleUpgradeDowngrade(prevSubscription, plan);
        if (upgradeDowngradeResults.notificationEvent && upgradeDowngradeResults.prevPlan) {
          commonNotifyType = upgradeDowngradeResults.notificationEvent;
          prevPlan = upgradeDowngradeResults.prevPlan;
        }
        const purchaseDates = convertSubscriptionDates(parseInt(receipt.startTimeMillis, 10));
        const expiresDates = convertSubscriptionDates(parseInt(receipt.expiryTimeMillis, 10));
        const originalPurchaseDates = convertSubscriptionDates(parseInt(receipt.startTimeMillis, 10));
        const subscriptionPayload: SubscriptionEntity = {
          id: v4(),
          userId,
          ...camelcaseKeys(receipt),
          transactionId: orderId,
          originalTransactionId: orderId.split('..')[0],
          productId,
          quantity: '1',
          amount: plan.price,
          expirationDate,
          purchaseDatePst: purchaseDates.pst,
          purchaseDateMs: purchaseDates.ms,
          purchaseDate: purchaseDates.ect,
          expiresDatePst: expiresDates.pst,
          expiresDateMs: expiresDates.ms,
          expiresDate: expiresDates.ect,
          originalPurchaseDatePst: originalPurchaseDates.pst,
          originalPurchaseDate: originalPurchaseDates.ect,
          originalPurchaseDateMs: originalPurchaseDates.ms,
          notificationType: commonNotifyType,
          platform: PLATFORM_ANDROID,
          createdBy: userId,
          environment,
        };
        if (prevPlan) {
          subscriptionPayload.prevPlan = prevPlan;
        }
        await this.subscriptionRepo.save(subscriptionPayload);
        this.trackingUpgradeDowngradeToKlaviyo(email, commonNotifyType);
        await this.cdmService.clearCacheUserPermissionsWhenPurchase(userId);
        return this.savePlanToCDM(
          userId,
          email,
          plan,
          expirationDate,
          commonNotifyType,
          'Android',
          job.data.subscriptionService
        );
      }
      return this.handleGoogleIAPWebhook(job);
    } catch (err) {
      console.log('ERR Payment Func googleIAP - userId - err', userId, err);
      this.logger.error(`ERR Payment Func googleIAP`);
      this.logger.log(err);
      await this.loggingService.save({
        event: 'ERR_PAYMENT_FUNC_googleIAP',
        data: {
          error: err,
          userId,
        },
      });
      return null;
    }
  }
  async handleGoogleIAPWebhook(job: JobType) {
    const { receipt, notifyType, userId, environment } = job.data;
    const expirationDate = moment(parseInt(receipt.expiryTimeMillis, 10)).toDate();
    const plan = PLANS[receipt.productId];
    let notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.RENEWED;
    if (notifyType === GOOGLE_EVENT_TEXT.SUBSCRIPTION_RECOVERED) {
      notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.RECOVERED;
    }
    if (
      notifyType === GOOGLE_EVENT_TEXT.SUBSCRIPTION_REVOKED ||
      notifyType === GOOGLE_EVENT_TEXT.SUBSCRIPTION_CANCELED
    ) {
      notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.CANCELED;
    }
    let prevPlan;
    const prevSubscription = await this.subscriptionRepo.findOne({ userId }, { order: { createdAt: 'DESC' } });
    const upgradeDowngradeResults = this.handleUpgradeDowngrade(prevSubscription, plan);
    if (upgradeDowngradeResults.notificationEvent && upgradeDowngradeResults.prevPlan) {
      notificationEvent = upgradeDowngradeResults.notificationEvent;
      prevPlan = upgradeDowngradeResults.prevPlan;
    }
    const subscription = await this.updateGooglePlaySub(receipt, notificationEvent, environment, prevPlan);
    if (!subscription) {
      return true;
    }
    const user = await this.userRepo.findOne(subscription.userId);
    const email = user.email;
    this.trackingUpgradeDowngradeToKlaviyo(email, notificationEvent);
    const shouldRenew = [
      GOOGLE_EVENT_TEXT.SUBSCRIPTION_RENEWED,
      GOOGLE_EVENT_TEXT.SUBSCRIPTION_RESTARTED,
      GOOGLE_EVENT_TEXT.SUBSCRIPTION_RECOVERED,
    ].includes(notifyType as number);
    if (shouldRenew) {
      await this.savePlanToCDM(
        userId,
        email,
        plan,
        expirationDate,
        notificationEvent,
        'Android',
        job.data.subscriptionService
      );
    }
    if (
      notifyType === GOOGLE_EVENT_TEXT.SUBSCRIPTION_REVOKED ||
      notifyType === GOOGLE_EVENT_TEXT.SUBSCRIPTION_CANCELED
    ) {
      const isUsingAccessCode = await this.paymentService.isUsingAccessCode(user.id);
      if (!isUsingAccessCode) {
        await this.resetToFreePlan(email);
      }
      this.klaviyoService.track(email, KlaviyoTrackEvents.CANCELED_SUBSCRIPTION);
    }
    await this.cdmService.clearCacheUserPermissionsWhenPurchase(userId);
    return true;
  }
  async handleChargeBeeIAPWebhook(job: JobType) {
    const { receipt, notifyType, userId, environment } = job.data;
    try {
      const expirationDate = moment(parseInt(receipt.subscription.current_term_end, 10) * 1000).toDate();
      const plan = PLANS[receipt.productId];
      plan.price = receipt.subscription.plan_amount / 100 || plan.price;

      this.logger.log(`Handle ChargeBee IAP for product: ${receipt.productId}`);
      let commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED;
      let prevPlan;
      const user = await this.userRepo.findOne({ id: userId });

      if (notifyType === CHARGE_BE_EVENT.SUBSCRIPTION_CANCELED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.CANCELED;
      }
      if (notifyType === CHARGE_BE_EVENT.SUBSCRIPTION_RENEWED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.RENEWED;
      }
      if (notifyType === CHARGE_BE_EVENT.SUBSCRIPTION_PAUSED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.PAUSED;
      }
      if (notifyType === CHARGE_BE_EVENT.SUBSCRIPTION_RENEWED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.RENEWED;
      }
      if (notifyType === CHARGE_BE_EVENT.SUBSCRIPTION_RESUMED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.RESUMED;
      }
      if (notifyType === CHARGE_BE_EVENT.SUBSCRIPTION_REACTIVATED) {
        commonNotifyType = COMMON_SUBSCRIPTION_EVENT_TEXT.REACTIVATED;
      }

      const duplicatedSubscription = await this.isDuplicatedChargeBeeSubscription(
        receipt.subscription.id,
        parseInt(receipt.subscription.current_term_end, 10) * 1000,
        commonNotifyType as string,
        receipt.productId
      );
      if (duplicatedSubscription) {
        this.logger.log({
          event: `ChargeBee:Payments:Webhook:Processor:DuplicatedNotificationType`,
          userId: user.id,
          expirationDate,
          notifyType,
          subscriptionId: receipt.subscription.id,
        });
        await this.cdmService.clearCacheUserPermissionsWhenPurchase(user.id);
        return false;
      }
      // if (commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED) {
      //   this.triggerKlaviyoMyTMSubscription(receipt, user.email);
      // }

      const prevSubscription = await this.subscriptionRepo.findOne({ userId }, { order: { createdAt: 'DESC' } });
      if (prevSubscription) {
        // ignore check old subscription
        await this.subscriptionRepo.update({ id: prevSubscription.id }, { isChecked: true });
      }
      const upgradeDowngradeResults = this.handleUpgradeDowngrade(prevSubscription, plan);
      if (upgradeDowngradeResults.notificationEvent && upgradeDowngradeResults.prevPlan) {
        commonNotifyType = upgradeDowngradeResults.notificationEvent;
        prevPlan = upgradeDowngradeResults.prevPlan;
      }
      this.triggerKlaviyoMyTMSubscription(receipt, user.email, notifyType, commonNotifyType);
      const purchaseDates = convertSubscriptionDates(parseInt(receipt.subscription.current_term_start, 10) * 1000);
      const expiresDates = convertSubscriptionDates(parseInt(receipt.subscription.current_term_end, 10) * 1000);
      const originalPurchaseDates = convertSubscriptionDates(
        parseInt(receipt.subscription.current_term_start, 10) * 1000
      );

      const autoRenewing = receipt.subscription.status.toUpperCase() === 'ACTIVE';
      const webOrderLineItemId = receipt.subscription.id;
      const subscriptionPayload = {
        id: v4(),
        userId,
        autoRenewing,
        webOrderLineItemId,
        transactionId: receipt.id,
        originalTransactionId: receipt.id,
        productId: receipt.productId,
        quantity: '1',
        amount: plan.price,
        expirationDate,
        purchaseDatePst: purchaseDates.pst,
        purchaseDateMs: purchaseDates.ms,
        purchaseDate: purchaseDates.ect,
        expiresDatePst: expiresDates.pst,
        expiresDateMs: expiresDates.ms,
        expiresDate: expiresDates.ect,
        originalPurchaseDatePst: originalPurchaseDates.pst,
        originalPurchaseDate: originalPurchaseDates.ect,
        originalPurchaseDateMs: originalPurchaseDates.ms,
        notificationType: commonNotifyType,
        platform: PLATFORM.CHARGE_BEE,
        createdBy: userId,
        isChecked: false,
        environment,
      };
      if (prevPlan) {
        subscriptionPayload['prevPlan'] = prevPlan;
        // Handle case downgrade from Legend Monthly to Champion Annual
        if (['legend_monthly', 'legend_daily'].includes(prevPlan) && receipt.productId === 'champion_annual') {
          subscriptionPayload['refSubscriptionId'] = prevSubscription.id;
          await this.subscriptionRepo.update({ id: prevSubscription.id }, { isChecked: false, subtype: 'DOWNGRADED' });
        }
      }
      this.logger.debug({ subscriptionPayload });
      await this.subscriptionRepo.save(subscriptionPayload);
      await this.cdmService.clearCacheUserPermissionsWhenPurchase(userId);
      if (this.shouldUpdateConsumerChargeBee(commonNotifyType)) {
        return this.savePlanToCDM(
          userId,
          user.email,
          plan,
          expirationDate,
          commonNotifyType,
          PLATFORM.CHARGE_BEE,
          job.data.subscriptionService
        );
      }
    } catch (err) {
      console.log('ERR Payment Func handleChargeBeeIAPWebhook - userId - err', userId, err);
      this.logger.error(`ERR Payment Func handleChargeBeeIAPWebhook`);
      this.logger.log(err);
      await this.loggingService.save({
        event: 'ERR_PAYMENT_FUNC_handleChargeBeeIAPWebhook',
        data: {
          error: err,
          userId,
        },
      });
      return null;
    }
  }

  async updateGooglePlaySub(receipt: any, notificationEvent: string, environment, prevPlan?: string) {
    const originalTransactionId = receipt.orderId.split('..')[0];
    const userSubscription = await this.subscriptionRepo.findOne({ originalTransactionId });
    const userId = userSubscription?.userId || receipt.obfuscatedExternalAccountId;
    if (!userId) {
      return;
    }
    const isUserHasSubscriptions = (await this.subscriptionRepo.count({ userId })) > 0;
    if (!isUserHasSubscriptions) {
      return;
    }
    const expirationDate = moment(parseInt(receipt.expiryTimeMillis, 10)).toDate();
    const plan = PLANS[receipt.productId];

    await this.subscriptionRepo.update({ userId }, { isChecked: true });

    const purchaseDates = convertSubscriptionDates(parseInt(receipt.startTimeMillis, 10));
    const expiresDates = convertSubscriptionDates(parseInt(receipt.expiryTimeMillis, 10));
    const originalPurchaseDates = convertSubscriptionDates(parseInt(receipt.startTimeMillis, 10));

    const subscriptionPayload: SubscriptionEntity = {
      id: v4(),
      userId,
      ...camelcaseKeys(receipt),
      transactionId: receipt.orderId,
      originalTransactionId,
      productId: receipt.productId,
      purchaseDatePst: purchaseDates.pst,
      purchaseDateMs: purchaseDates.ms,
      purchaseDate: purchaseDates.ect,
      expiresDatePst: expiresDates.pst,
      expiresDateMs: expiresDates.ms,
      expiresDate: expiresDates.ect,
      originalPurchaseDatePst: originalPurchaseDates.pst,
      originalPurchaseDate: originalPurchaseDates.ect,
      originalPurchaseDateMs: originalPurchaseDates.ms,
      quantity: '1',
      platform: PLATFORM_ANDROID,
      createdBy: userId,
      notificationType: notificationEvent,
      amount: plan.price,
      expirationDate,
      isChecked: false,
      environment,
    };
    if (prevPlan) {
      subscriptionPayload.prevPlan = prevPlan;
    }
    return await this.subscriptionRepo.save(subscriptionPayload);
  }

  async resetToFreePlan(email: string, force = false) {
    const user = await this.userRepo.findOne({ where: { email } });
    let shouldResetToFreePlan = true;
    const myTMPermission = await this.cdmService.getMyTMPermission(user.id, email);
    const subscriptionExpirationDate = myTMPermission?.subscriptionExpirationDate;
    if (
      !force &&
      user.myTMSubscriptionLevel > 0 &&
      subscriptionExpirationDate &&
      new Date(subscriptionExpirationDate).getTime() > new Date().getTime()
    ) {
      shouldResetToFreePlan = false;
    }
    if (!shouldResetToFreePlan) {
      return false;
    }
    const permissionList = this.cdmService.getPermissionListByPlan(0);
    await this.userRepo.update({ email }, { myTMSubscriptionLevel: 0, tmAccessCode: false });
    await this.userPermissionRepo.update(
      { userId: user.id },
      {
        subscriptionLength: 0,
        subscriptionExpirationDate: new Date().toISOString(),
        isSyncCDM: false,
        ...permissionList,
      }
    );
    await this.cdmService.clearCacheUserPermissionsWhenPurchase(user.id);
    await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), {
      myTMSubscriptionLevel: 0,
      subscriptionLength: 0,
      tmAccessCode: false,
      subscriptionExpirationDate: new Date().toISOString(),
      ...permissionList,
    });
    return true;
  }

  async isDuplicatedSubscription(userId: string, expirationDate: string, notifyType: string) {
    if (
      [
        APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_PREF,
        APPLE_EVENT_TEXT.REFUND,
        APPLE_EVENT_TEXT.OFFER_REDEEMED,
        APPLE_EVENT_TEXT.CANCEL,
        APPLE_EVENT_TEXT.REFUND_DECLINED,
        APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_STATUS,
      ].includes(notifyType as APPLE_EVENT_TEXT)
    ) {
      return false;
    }
    return (await this.subscriptionRepo.count({ userId, expirationDate: new Date(expirationDate) })) > 0;
  }
  async isDuplicatedChargeBeeSubscription(
    subscriptionId: string,
    expirationDate: any,
    notifyType: string,
    productId: string
  ) {
    return (
      (await this.subscriptionRepo.count({
        webOrderLineItemId: subscriptionId,
        notificationType: notifyType,
        expirationDate: new Date(expirationDate),
        productId,
      })) > 0
    );
  }

  isChangePlanDuration(currentProductId: string, nextPlan: PlanType) {
    const currentPlan = PLANS[currentProductId];
    return currentPlan?.level === nextPlan?.level;
  }

  isUpgraded(currentProductId: string, nextPlan: PlanType) {
    const currentPlan = PLANS[currentProductId];
    return (currentPlan?.level || 0) < nextPlan?.level;
  }

  isDowngraded(currentProductId: string, nextPlan: PlanType) {
    const currentPlan = PLANS[currentProductId];
    return currentPlan?.level > nextPlan?.level;
  }

  isDurationUpgraded(currentProductId: string, nextPlan: PlanType) {
    const currentPlan = PLANS[currentProductId];
    return (currentPlan?.duration || 0) < nextPlan?.duration;
  }

  isDurationDowngraded(currentProductId: string, nextPlan: PlanType) {
    const currentPlan = PLANS[currentProductId];
    return currentPlan?.duration > nextPlan?.duration;
  }

  handleUpgradeDowngrade(prevSubscription: SubscriptionEntity, plan: PlanType) {
    let notificationEvent;
    let prevPlan;
    if (prevSubscription) {
      if (this.isChangePlanDuration(prevSubscription.productId, plan)) {
        if (this.isDurationUpgraded(prevSubscription.productId, plan)) {
          notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_UPGRADED;
          prevPlan = prevSubscription.productId;
        }
        if (this.isDurationDowngraded(prevSubscription.productId, plan)) {
          notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_DOWNGRADED;
          prevPlan = prevSubscription.productId;
        }
      } else {
        if (this.isUpgraded(prevSubscription.productId, plan)) {
          notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.UPGRADED;
          prevPlan = prevSubscription.productId;
        }
        if (this.isDowngraded(prevSubscription.productId, plan)) {
          notificationEvent = COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED;
          prevPlan = prevSubscription.productId;
        }
      }
    }
    return { notificationEvent, prevPlan };
  }

  trackingUpgradeDowngradeToKlaviyo(email, commonNotifyType) {
    if (
      ![
        COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED,
        COMMON_SUBSCRIPTION_EVENT_TEXT.UPGRADED,
        COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_DOWNGRADED,
        COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_UPGRADED,
      ].includes(commonNotifyType)
    ) {
      return;
    }
    if (commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED) {
      this.klaviyoService.track(email, KlaviyoTrackEvents.DOWNGRADE_TIER);
    }
    if (commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.UPGRADED) {
      this.klaviyoService.track(email, KlaviyoTrackEvents.UPGRADE_TIER);
    }
  }

  shouldUpdateConsumer(notifyType: string, subtype: string) {
    if (notifyType === APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_PREF && subtype === APPLE_SUBTYPE.UPGRADE) {
      return true;
    }
    return [APPLE_EVENT_TEXT.DID_RENEW, APPLE_EVENT_TEXT.SUBSCRIBED, APPLE_EVENT_TEXT.INTERACTIVE_RENEWAL].includes(
      notifyType as APPLE_EVENT_TEXT
    );
  }
  shouldUpdateConsumerChargeBee(notificationType: string) {
    const listNoUpdate = [
      COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED,
      COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_DOWNGRADED,
      COMMON_SUBSCRIPTION_EVENT_TEXT.CANCELED,
      COMMON_SUBSCRIPTION_EVENT_TEXT.PAUSED,
    ];
    return !listNoUpdate.includes(notificationType as COMMON_SUBSCRIPTION_EVENT_TEXT);
  }

  shouldTrackingCanceledSubscriptions(commonNotifyType, notifyType, subtype) {
    return (
      commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.CANCELED ||
      (notifyType === APPLE_EVENT_TEXT.DID_CHANGE_RENEWAL_STATUS && subtype === APPLE_SUBTYPE.AUTO_RENEW_DISABLED)
    );
  }

  async isRefundedTheCurrentPlan(signedRenewalInfo: any, prevSubscription: SubscriptionEntity) {
    const { productId } = signedRenewalInfo;
    return productId === prevSubscription.productId;
  }

  async savePlanToCDM(
    userId,
    email,
    plan,
    expirationDate,
    commonNotifyType: COMMON_SUBSCRIPTION_EVENT_TEXT = null,
    platform = 'iOS',
    subscriptionService = null
  ) {
    const permissionList = this.cdmService.getPermissionListByPlan(plan.level || 0);
    const user = await this.userRepo.findOne(userId);

    const payload: any = {
      myTMSubscriptionLevel: plan.level,
      subscriptionLength: plan.duration,
      subscriptionExpirationDate: expirationDate,
      subscriptionService,
      ...permissionList,
    };
    const subscriptionStartDate = new Date().toISOString();
    await this.userRepo.update(
      { email },
      { subscriptionService, myTMSubscriptionLevel: payload.myTMSubscriptionLevel }
    );
    await this.userPermissionRepo.update(
      { userId },
      {
        subscriptionLength: plan.duration,
        subscriptionExpirationDate: expirationDate,
        subscriptionStartDate,
        isSyncCDM: false,
        ...permissionList,
      }
    );
    if (commonNotifyType === COMMON_SUBSCRIPTION_EVENT_TEXT.PURCHASED) {
      payload.subscriptionStartDate = subscriptionStartDate;
    }

    if (subscriptionService === PLATFORM.CHARGE_BEE) {
      payload.subscriptionStartDate = subscriptionStartDate;
    }
    const account = await this.cdmService.createOrUpdateAccount(email, this.config.get('app.defaultRegionId'), payload);
    this.logger.log({
      event: `${platform}:Payments:Webhook:Processor:SavedConsumerSubscriptions`,
      email,
      payload: {
        myTMSubscriptionLevel: plan.level,
        subscriptionLength: plan.duration,
        subscriptionExpirationDate: expirationDate,
        subscriptionService,
        ...permissionList,
      },
    });
    return this.cdmService.transformConsumerData(account);
  }

  async triggerKlaviyoMyTMSubscriptionDownGradeLegendToChampion(
    email: string,
    commonNotifyType: any,
    prevPlan: any,
    receipt: any
  ) {
    if (prevPlan && commonNotifyType == COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED && prevPlan == 'legend_annual') {
      const expirationDate = moment(parseInt(receipt.subscription.current_term_end, 10) * 1000).format('MM/DD/yyyy');
      const subStartDate = moment(parseInt(receipt.subscription.current_term_start, 10) * 1000).format('MM/DD/yyyy');
      const startDate = moment().format('MM/DD/yyyy');
      const properties = {
        mytm_subscriber_email: email,
        mytm_subscriber_firstname: receipt.customer?.first_name || '',
        mytm_subscriber_lastname: receipt.customer?.last_name || '',
        mytm_subscription_date: startDate,
        mytm_subscription_start_date: subStartDate,
        mytm_subscription_expiration_date: expirationDate,
      };
      this.klaviyoService.track(
        this.config.get('app.emailReceivedEcomDowngrade'),
        KlaviyoTrackEvents.MYTM_SUBSCRIPTION_LEGEND_TO_CHAMPION,
        properties
      );
    }
  }

  async triggerKlaviyoMyTMSubscription(receipt: any, email: string, notifyType: any, commonNotifyType: any) {
    const listEventsNoTrigger = [
      COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_DOWNGRADED,
      COMMON_SUBSCRIPTION_EVENT_TEXT.DURATION_UPGRADED,
    ];
    if (listEventsNoTrigger.includes(commonNotifyType)) {
      return;
    }
    const productType = receipt.productId.split('_');
    const tierLevel = _.capitalize(productType[0]) || '';
    let tierDuration = _.capitalize(productType[productType.length - 1]) || 'Monthly';
    if (tierDuration === 'Annual') {
      tierDuration = 'Yearly';
    }
    let event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION;
    switch (commonNotifyType) {
      case COMMON_SUBSCRIPTION_EVENT_TEXT.DOWNGRADED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_DOWN_GRADE;
        break;
      case COMMON_SUBSCRIPTION_EVENT_TEXT.UPGRADED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_UP_GRADE;
        break;
      case COMMON_SUBSCRIPTION_EVENT_TEXT.CANCELED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_CANCEL;
        break;
      case COMMON_SUBSCRIPTION_EVENT_TEXT.PAUSED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_PAUSE;
        break;
      case COMMON_SUBSCRIPTION_EVENT_TEXT.REACTIVATED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_REACTIVE;
        break;
      case COMMON_SUBSCRIPTION_EVENT_TEXT.RESUMED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_RESUME;
        break;
      case COMMON_SUBSCRIPTION_EVENT_TEXT.RENEWED:
        event = KlaviyoTrackEvents.MYTM_SUBSCRIPTION_RENEW;
        break;
    }
    const expirationDate = moment(parseInt(receipt.subscription.current_term_end, 10) * 1000).format('MM/DD/yyyy');
    const subStartDate = moment(parseInt(receipt.subscription.current_term_start, 10) * 1000).format('MM/DD/yyyy');
    const startDate = moment().format('MM/DD/yyyy');
    let mytm_subscriber_current_tier = tierLevel;
    const user = await this.userRepo.findOne({ email });
    if (
      user.myTMSubscriptionLevel === 0 ||
      user.myTMSubscriptionLevel === null ||
      user.myTMSubscriptionLevel === undefined
    ) {
      mytm_subscriber_current_tier = 'Free';
    }
    const properties = {
      mytm_subscriber_firstname: receipt.customer?.first_name || '',
      mytm_subscriber_lastname: receipt.customer?.last_name || '',
      mytm_subscriber_current_tier,
      mytm_subscriber_current_subscription: tierDuration,
      mytm_subscriber_id: 'True',
      mytm_subscription_date: startDate,
      mytm_subscription_start_date: subStartDate,
      mytm_subscription_expiration_date: expirationDate,
    };
    await this.klaviyoService.track(email, event, properties, properties);
  }
}

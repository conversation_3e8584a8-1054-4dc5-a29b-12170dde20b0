import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { SharedModule } from 'src/shared/shared.module';
import { SYSTEM_TAG } from 'src/utils/constants';
import { PaymentCheckSubExpiredProcessor } from './payment-check-sub-expired.processor';
import { PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME, PAYMENT_QUEUE_NAME, SUBSCRIPTION_QUEUE_NAME } from './payment.constants';
import { PaymentController } from './payment.controller';
import { PaymentProcessor } from './payment.processor';
import { PaymentService } from './payment.service';
import { SubscriptionProcessor } from './subscription.processor';
import { WebhookController } from './webhook.controller';

let processors = [];

if (process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [...processors, PaymentProcessor, SubscriptionProcessor, PaymentCheckSubExpiredProcessor];
}

@Module({
  imports: [
    SharedModule,
    BullModule.registerQueue({
      name: PAYMENT_QUEUE_NAME,
      defaultJobOptions: {
        removeOnComplete: false,
        removeOnFail: false,
        attempts: 100,
        backoff: 300000,
      },
      settings: {
        lockDuration: 300000,
      },
    }),
    BullModule.registerQueue({
      name: PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 100,
        backoff: 300000,
      },
      settings: {
        lockDuration: 300000,
      },
    }),
    BullModule.registerQueue({
      name: SUBSCRIPTION_QUEUE_NAME,
      defaultJobOptions: {
        removeOnComplete: true,
        removeOnFail: false,
        attempts: 1000,
        backoff: 300000 * 6,
      },
      settings: {
        lockDuration: 300000 * 6,
      },
    }),
  ],
  controllers: [PaymentController, WebhookController],
  providers: [...processors, PaymentService],
})
export class PaymentModule {}

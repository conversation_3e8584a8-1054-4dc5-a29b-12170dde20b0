import { Body, Controller, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ConfigService } from 'nestjs-config';
import { PaymentService } from './payment.service';

@Controller('webhook')
export class WebhookController {
  constructor(
    private authService: PaymentService,
    private paymentService: PaymentService,
    private readonly config: ConfigService
  ) {
    this.config = config;
  }

  @Post('ios')
  async handleAppleNotification(@Body() data: any): Promise<any> {
    await this.paymentService.handleAppleNotification(data);
  }

  @Post('android')
  async handleGooglePlayNotification(@Body() data: any): Promise<any> {
    await this.paymentService.handleGooglePlayNotification(data);
  }
  @Post('charge-bee')
  @UseGuards(AuthGuard('basic'))
  async handleChargeBeeNotification(@Body() data: any): Promise<any> {
    return await this.paymentService.handleChargeBeeNotification(data);
  }
}

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>al, <PERSON>String, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class SubscribeDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  platform: string;

  @IsString()
  @IsNotEmpty()
  receipt: any;

  @IsString()
  @IsOptional()
  signature: string;
}

export class CanSubscribeDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  platform: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  originalTransactionId: string;

  @IsString()
  @IsOptional()
  @MaxLength(255)
  testUserId: string;
}

export class SyncToCDMDto {
  @IsNotEmpty()
  @IsArray()
  emails: string[];
}

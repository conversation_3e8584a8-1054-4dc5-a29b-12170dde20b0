export enum GOOGLE_EVENT_TEXT {
  SUBSCRIPTION_RECOVERED = 1,
  SUBSCRIPTION_RENEWED = 2,
  SUBSCRIPTION_CANCELED = 3,
  SUBSCRIPTION_PURCHASED = 4,
  SUBSCRIPTION_RESTARTED = 7,
  SUBSCRIPTION_REVOKED = 12,
  SUBSCRIPTION_ON_HOLD = 5,
  SUBSCRIPTION_IN_GRACE_PERIOD = 6,
  SUBSCRIPTION_PRICE_CHANGE_CONFIRMED = 8,
  SUBSCRIPTION_DEFERRED = 9,
  SUBSCRIPTION_PAUSED = 10,
  SUBSCRIPTION_PAUSE_SCHEDULE_CHANGED = 11,
  SUBSCRIPTION_EXPIRED = 13,
}
export enum CHARGE_BE_EVENT {
  SUBSCRIPTION_RENEWED = 'SUBSCRIPTION_RENEWED',
  SUBSCRIPTION_CREATED = 'SUBSCRIPTION_CREATED',
  SUBSCRIPTION_CANCELED = 'SUBSCRIPTION_CANCELLATION_SCHEDULED',
  SUBSCRIPTION_CHANGED = 'SUBSCRIPTION_CHANGED',
  SUBSCRIPTION_PAUSED = 'SUBSCRIPTION_PAUSED',
  SUBSCRIPTION_RESUMED = 'SUBSCRIPTION_RESUMED',
  SUBSCRIPTION_REACTIVATED = 'SUBSCRIPTION_REACTIVATED',
  SUBSCRIPTION_CHANGES_SCHEDULED = 'SUBSCRIPTION_CHANGES_SCHEDULED',
}
export enum APPLE_EVENT_TEXT {
  DID_RENEW = 'DID_RENEW',
  SUBSCRIBED = 'SUBSCRIBED',
  DID_RECOVER = 'DID_RECOVER',
  CANCEL = 'CANCEL',
  INTERACTIVE_RENEWAL = 'INTERACTIVE_RENEWAL',
  DID_CHANGE_RENEWAL_PREF = 'DID_CHANGE_RENEWAL_PREF',
  REFUND = 'REFUND',
  REFUND_DECLINED = 'REFUND_DECLINED',
  DID_CHANGE_RENEWAL_STATUS = 'DID_CHANGE_RENEWAL_STATUS',
  OFFER_REDEEMED = 'OFFER_REDEEMED',
}

export enum COMMON_SUBSCRIPTION_EVENT_TEXT {
  RENEWED = 'RENEWED',
  RESUMED = 'RESUMED',
  RECOVERED = 'RECOVERED',
  PURCHASED = 'PURCHASED',
  UPGRADED = 'UPGRADED',
  DURATION_UPGRADED = 'DURATION_UPGRADED',
  DOWNGRADED = 'DOWNGRADED',
  DURATION_DOWNGRADED = 'DURATION_DOWNGRADED',
  CANCELED = 'CANCELED',
  PAUSED = 'PAUSED',
  EXPIRED = 'EXPIRED',
  REFUNDED = 'REFUNDED',
  OFFER_REDEEMED = 'OFFER_REDEEMED',
  REFUND_DECLINED = 'REFUND_DECLINED',
  REACTIVATED = 'REACTIVATED',
}

export enum PLATFORM {
  GOOGLE = 'GOOGLE',
  APPLE = 'APPLE',
  CHARGE_BEE = 'CHARGE_BEE',
}
export enum APPLE_SUBTYPE {
  UPGRADE = 'UPGRADE',
  DOWNGRADE = 'DOWNGRADE',
  AUTO_RENEW_DISABLED = 'AUTO_RENEW_DISABLED',
}

export const PAYMENT_QUEUE_NAME = 'payment';
export const PAYMENT_CHECK_SUB_EXPIRED_QUEUE_NAME = 'payment-check-sub-expired';
export const SUBSCRIPTION_QUEUE_NAME = 'subscription-report';

export enum AppleDownloadType {
  SUBSCRIPTION = 'Subscription',
  SUBSCRIPTION_EVENT = 'SubscriptionEvent',
  SUBSCRIBER = 'Subscriber',
}
export enum SUBSCRIPTION_SERVICES {
  APPLE = 'APPLE',
  GOOGLE = 'GOOGLE',
  MyTaylorMade = 'MyTaylorMade',
  CHARGE_BEE = 'CHARGE_BEE',
}
export const DownloadMessage = {
  [AppleDownloadType.SUBSCRIPTION]: 'Download Subscription report',
  [AppleDownloadType.SUBSCRIPTION_EVENT]: 'Download Subscription Event report',
  [AppleDownloadType.SUBSCRIBER]: 'Download Subscribe report',
};

export enum GoogleDownloadType {
  EARNINGS = 'Earnings Report',
  SUBSCRIPTION = 'Sales Report',
  INSTALLS = 'Installs Report',
}

export const MAX_RETRIES = 3;

export const APP_STORE_AUTH_TOKEN_CACHE_KEY = 'APP_STORE_AUTH_TOKEN';

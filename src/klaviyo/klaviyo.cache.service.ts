import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { Cache } from 'cache-manager';
import { parseInt } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { TOTAL_SYNC_KLAVIYO_CACHE_KEY } from 'src/admin/admin.constants';
import { CURRENT_SYNCED_KLAVIYO_CACHE_KEY } from './../admin/admin.constants';

@Injectable()
export class AdminForceSyncKlaviyoCacheService {
  constructor(private readonly config: ConfigService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {}

  async cacheTotalUserWhenForceSync(value: number) {
    return this.cacheManager.set(TOTAL_SYNC_KLAVIYO_CACHE_KEY, value.toString(), {
      ttl: 1000 * 60 * 60 * 24 * 7,
    });
  }

  async removeTotalUserWhenForceSync() {
    return this.cacheManager.del(TOTAL_SYNC_KLAVIYO_CACHE_KEY);
  }

  async getTotalUserWhenForceSync() {
    const total = await this.cacheManager.get(TOTAL_SYNC_KLAVIYO_CACHE_KEY);
    return parseInt(total + '', 10) || 0;
  }

  async cacheCurrentUserWhenForceSync(value: number) {
    return this.cacheManager.set(CURRENT_SYNCED_KLAVIYO_CACHE_KEY, value.toString(), {
      ttl: 1000 * 60 * 60 * 24 * 7,
    });
  }

  async removeCurrentUserWhenForceSync() {
    return this.cacheManager.del(CURRENT_SYNCED_KLAVIYO_CACHE_KEY);
  }

  async getCurrentUserWhenForceSync(): Promise<number> {
    const currentUser = await this.cacheManager.get(CURRENT_SYNCED_KLAVIYO_CACHE_KEY);
    return parseInt(currentUser + '', 10) || 0;
  }
}

import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { SYSTEM_TAG } from 'src/utils/constants';
import { SharedModule } from '../shared/shared.module';
import { KlaviyoSyncProcessor } from './klaviyo-sync.processor';
import { KlaviyoTrackProcessor } from './klaviyo-track.processor';
import { KlaviyoService } from './klaviyo.service';

let processors = [];

if (process.env.TAG === SYSTEM_TAG.APIS) {
  processors = [...processors, KlaviyoSyncProcessor];
}

if (process.env.TAG === SYSTEM_TAG.APIS || process.env.TAG === SYSTEM_TAG.JOBS) {
  processors = [...processors, KlaviyoTrackProcessor];
}

@Module({
  imports: [forwardRef(() => SharedModule), TypeOrmModule.forFeature([UserEntity])],
  providers: [KlaviyoService, ...processors],
  exports: [KlaviyoService],
})
export class KlaviyoModule {}

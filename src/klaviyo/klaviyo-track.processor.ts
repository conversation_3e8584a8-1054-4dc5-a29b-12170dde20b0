import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Job } from 'bull';
import { ApiKeySession, EventCreateQueryV2, EventsApi } from 'klaviyo-api';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { isCanOrUsaCountry, isCanadaCountry } from '../utils/transform';

export enum KlaviyoTrackProcessorQueueName {
  TRACK = 'track',
}

export type klaviyoTrackJob = Job<{
  event: string;
  email: string;
  properties: any;
  customerProperties: any;
}>;

@Processor('klaviyo-track')
export class KlaviyoTrackProcessor {
  private readonly logger = new Logger(KlaviyoTrackProcessor.name);
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}

  @Process({
    name: KlaviyoTrackProcessorQueueName.TRACK,
    concurrency: 1,
  })
  async track(job: klaviyoTrackJob): Promise<any> {
    try {
      const { data } = job;
      const { email } = data;
      const user = await this.userRepo.findOne({ email });
      const country = (user && user?.userCountry) || null;
      if (!isCanOrUsaCountry(country)) {
        return;
      }
      const privateKey = isCanadaCountry(country)
        ? this.config.get('app.klaviyoCAPrivateToken')
        : this.config.get('app.klaviyoPrivateToken');
      const session = new ApiKeySession(privateKey);
      const eventsApi = new EventsApi(session);
      const customerProperties = data?.customerProperties || {};
      const body: EventCreateQueryV2 = {
        data: {
          type: 'event',
          attributes: {
            properties: { ...data?.properties },
            metric: { data: { type: 'metric', attributes: { name: data.event } } },
            profile: {
              data: {
                type: 'profile',
                attributes: {
                  email: data.email,
                  ...customerProperties,
                },
              },
            },
          },
        },
      };

      await eventsApi.createEvent(body);
      return true;
    } catch (err) {
      this.logger.log(`Error Klaviyo Track Event ${JSON.stringify(err)}`);
      console.log('Err Klaviyo', err);
      return false;
    }
  }
}

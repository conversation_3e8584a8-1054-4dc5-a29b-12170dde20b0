import { Process, Processor } from '@nestjs/bull';
import { InjectRepository } from '@nestjs/typeorm';
import { ApiKeySession, EventCreateQueryV2, EventsApi } from 'klaviyo-api';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { isCanOrUsaCountry, isCanadaCountry } from '../utils/transform';
import { KlaviyoService, KlaviyoTrackEvents } from './klaviyo.service';
import { SyncKlaviyoJob } from './types/consumer.type';

export enum KlaviyoSyncProcessorQueueName {
  FORCE_SYNC_KLAVIYO = 'force-sync-klaviyo',
}

@Processor('klaviyo-sync')
export class KlaviyoSyncProcessor {
  constructor(
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    private readonly klaviyoService: KlaviyoService,
    private readonly config: ConfigService
  ) {}

  @Process(KlaviyoSyncProcessorQueueName.FORCE_SYNC_KLAVIYO)
  async forceSyncKlaviyo(job: SyncKlaviyoJob): Promise<any> {
    const { email } = job.data;
    const user = await this.userRepo.findOne({ email });
    const country = (user && user?.userCountry) || null;
    if (!isCanOrUsaCountry(country)) {
      return;
    }
    const privateKey = isCanadaCountry(country)
      ? this.config.get('app.klaviyoCAPrivateToken')
      : this.config.get('app.klaviyoPrivateToken');
    if (user) {
      if (
        user.myTMSubscriptionLevel === 0 ||
        user.myTMSubscriptionLevel === null ||
        user.myTMSubscriptionLevel === undefined
      ) {
        const properties = {
          mytm_subscriber_current_tier: 'Free',
        };
        const customerProperties = {
          $email: email,
          mytm_subscriber_current_tier: 'Free',
        };

        const session = new ApiKeySession(privateKey);
        const eventsApi = new EventsApi(session);
        const body: EventCreateQueryV2 = {
          data: {
            type: 'event',
            attributes: {
              properties: { ...properties },
              metric: { data: { type: 'metric', attributes: { name: KlaviyoTrackEvents.MYTM_SUBSCRIPTION_SYNC } } },
              profile: {
                data: {
                  type: 'profile',
                  attributes: {
                    email: email,
                    ...customerProperties,
                  },
                },
              },
            },
          },
        };

        await eventsApi.createEvent(body);

        await new Promise((resolve) => setTimeout(resolve, 1000));
        return true;
      }
    }
  }
}

import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { CronExpression, SchedulerRegistry } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { CronJob } from 'cron';
import _ from 'lodash';
import { Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { isProduction } from 'src/utils/cron';
import { SYNC_KLAVIYO_CRON_JOB } from '../admin/admin.constants';
import { KlaviyoSyncProcessorQueueName } from './klaviyo-sync.processor';
import { AdminForceSyncKlaviyoCacheService } from './klaviyo.cache.service';

@Injectable()
export class KlaviyoCronService {
  private readonly logger = new Logger(KlaviyoCronService.name);
  constructor(
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectQueue('klaviyo-sync') private syncKlaviyoQueue: Queue,
    private readonly adminForceSyncKlaviyoCacheService: AdminForceSyncKlaviyoCacheService,
    private schedulerRegistry: SchedulerRegistry
  ) {}

  async startForceSyncKlaviyoCronJob(numberUserPerJob = 500, isHadSubscription = false) {
    try {
      await this.clearWaitingJobs();
      this.stopForceSyncKlaviyoCronJob();
      this.logger.debug('Job force sync Klaviyo run...');
      const forceSyncJob = new CronJob(
        isProduction ? CronExpression.EVERY_HOUR : CronExpression.EVERY_MINUTE,
        async () => {
          const currentUserSynced = (await this.adminForceSyncKlaviyoCacheService.getCurrentUserWhenForceSync()) || 0;
          const total = (await this.adminForceSyncKlaviyoCacheService.getTotalUserWhenForceSync()) || 0;
          this.logger.debug(`currentUserSynced: ${currentUserSynced} on Total User Permissions: ${total}`);
          if (currentUserSynced >= total) {
            this.stopForceSyncKlaviyoCronJob();
            return;
          }
          const querystring = 'u.myTMSubscriptionLevel IN (:...levels)';
          let levels = [0, null];
          if (isHadSubscription) {
            levels = [1, 2];
          }
          const users = await this.userRepo
            .createQueryBuilder('u')
            .where(querystring, {
              levels,
            })
            .andWhere({ isNewAccount: false })
            .orderBy('u.updatedAt', 'ASC')
            .limit(numberUserPerJob)
            .getMany();
          await this.adminForceSyncKlaviyoCacheService.cacheCurrentUserWhenForceSync(
            currentUserSynced + numberUserPerJob
          );
          const chunkTargetUsers = _.chunk(users, 10);
          for (const chunkUsers of chunkTargetUsers) {
            const emails = chunkUsers.map((user) => user.email);
            await this.syncKlaviyoQueue.add(
              KlaviyoSyncProcessorQueueName.FORCE_SYNC_KLAVIYO,
              {
                userEmails: emails,
              },
              { priority: 2 }
            );
          }
        }
      );

      this.schedulerRegistry.addCronJob(SYNC_KLAVIYO_CRON_JOB, forceSyncJob);
      forceSyncJob.start();
    } catch (error) {
      this.logger.error(error);
    }
  }

  async clearWaitingJobs() {
    await this.syncKlaviyoQueue.empty();
  }

  async addSyncKlaviyoQueue(email: string) {
    return this.syncKlaviyoQueue.add(KlaviyoSyncProcessorQueueName.FORCE_SYNC_KLAVIYO, {
      email,
    });
  }

  stopForceSyncKlaviyoCronJob() {
    try {
      if (this.isExistForceSyncKlaviyoCronJob()) {
        const job = this.schedulerRegistry.getCronJob(SYNC_KLAVIYO_CRON_JOB);
        job?.stop();
        this.logger.debug(`Job force sync user permission stop at ${job.lastDate()}`);
      }
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  isExistForceSyncKlaviyoCronJob() {
    return this.schedulerRegistry.doesExists('cron', SYNC_KLAVIYO_CRON_JOB);
  }
}

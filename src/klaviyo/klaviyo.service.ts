import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { ApiKeySession, EventCreateQueryV2, EventsApi, ProfileCreateQuery, ProfilesApi } from 'klaviyo-api';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { isCanOrUsaCountry, isCanadaCountry } from '../utils/transform';
import { KlaviyoTrackProcessorQueueName } from './klaviyo-track.processor';

export enum KlaviyoTrackEvents {
  COMPLETE_ONBOARDING = 'Completed Onboarding',
  UPGRADE_TIER = 'Upgraded Tier',
  DOWNGRADE_TIER = 'Downgraded Tier',
  UPGRADE_DURATION = 'Upgraded Duration',
  DOWNGRADE_DURATION = 'Downgraded Duration',
  CANCELED_SUBSCRIPTION = 'Canceled Subscription',
  RENEWED_SUBSCRIPTION = 'Renewed Subscription',
  ENTERED_TOUR_TRASH = 'Entered Tour Trash',
  LAST_OPEN_APP = 'Last Open App',
  VIEWED_INSIGHTS = 'Viewed Insights',
  TTB_ORDER_CONFIRMATION = 'TTB Order Confirmation',
  TTB_SHIP_CONFIRMATION = 'TTB Ship Confirmation',
  TTB_DELIVERY_CONFIRMATION = 'TTB Delivery Confirmation',
  TTB_ORDER_ABNORMAL_DAILY_REPORT = 'TTB Order Abnormal Daily Report',
  TTB_HALFWAY_OVER = 'TTB Halfway Over',
  TTB_TWO_DAYS_LEFT = 'TTB Two Days Left',
  TTB_TRIAL_ENDED = 'TTB Trial Ended',
  TTB_DECISION_TIME = 'TTB Decision Time',
  TTB_LAST_CHANCE = 'TTB Last Chance',
  TTB_RETURN_CONFIRMATION = 'TTB Return Confirmation',
  TTB_CHARGE_CONFIRMATION = 'TTB Charge Confirmation',
  TTB_RETURN_INITIATED = 'TTB Return Initiated',
  TTB_CHARGE_INITIATED = 'TTB Charge Initiated',
  TTB_RETURN_REMINDER = 'TTB Return Reminder',
  TTB_CANCELATION = 'TTB Order Cancelation',
  TTB_FAILED_INSPECTION = 'TTB Failed Inspection',
  MYTM_MEMBER_SHOP_ORDER_CONFIRMATION = 'MyTM+_Member Shop Order Confirmation',
  MYTM_MEMBER_SHOP_SHIP_CONFIRMATION = 'MyTM+_Member Shop Ship Confirmation',
  MYTM_MEMBER_SHOP_DELIVERY_CONFIRMATION = 'MyTM+_Member Shop Delivery Confirmation',
  MYTM_MEMBER_SHOP_CANCELLED = 'MyTM+_Member Shop Order Cancelation',
  MYTMOC_CREATE_USER_SUCCESSFUL = 'Welcome to MyTMOC',
  MYTMOC_PLAYED_ROUND = 'MyTMOC_Played Round',
  TOUR_TRASH_OPEN = 'Tour Trash Open',
  TOUR_TRASH_REMINDER = 'Tour Trash Reminder',
  TOUR_TRASH_CANCELLED = 'Tour Trash Cancelled',
  TOUR_TRASH_CLOSED = 'Tour Trash Closed',
  TOUR_TRASH_READY_TO_CLOSE = 'Tour Trash Ready To Close',
  SHORT_PUTT_SCORED = 'Short Putt Scored',
  LONG_PUTT_SCORED = 'Long Putt Scored',
  SINGLE_SWING_SHOT_RETURNED_SCORED = 'Swing Shot Returned_Scored',
  SINGLE_SWING_SHOT_SCORED = 'Swing Shot Scored',
  SINGLE_LESSON_FEED_BACK = 'Single Lesson Feedback',
  RETURNED_SWING_SHOT_PASSED_ALL = 'Swing Shot Passed_All',
  RETURNED_SWING_SHOT_PASSED = 'Swing Shot Passed',
  RETURNED_SWING_SHOT_SKIPPED_LAST = 'Swing Shot Skipped_Last',
  RETURNED_SWING_SHOT_SKIPPED = 'Swing Shot Skipped',
  RETURNED_SWING_SHOT_FAILED = 'Swing Shot Failed',
  ROADMAP_GENERATED = 'Roadmap Generated',
  DEEPER_INSIGHT_COMPLETED_ONBOARDING = 'Deeper Insight Completed Onboarding',
  MYTM_SUBSCRIPTION = 'MyTM+ .com Subscription',
  MYTM_SUBSCRIPTION_UP_GRADE = 'MyTM+ .com Upgrade',
  MYTM_SUBSCRIPTION_DOWN_GRADE = 'MyTM+ .com Downgrade',
  MYTM_SUBSCRIPTION_PAUSE = 'MyTM+ .com Pause',
  MYTM_SUBSCRIPTION_RENEW = 'MyTM+ .com Renew',
  MYTM_SUBSCRIPTION_CANCEL = 'MyTM+ .com Cancel',
  MYTM_SUBSCRIPTION_REACTIVE = 'MyTM+ .com Reactivate',
  MYTM_SUBSCRIPTION_RESUME = 'MyTM+ .com Resume',
  MYTM_SUBSCRIPTION_SYNC = 'MyTM+ .com Sync',
  MYTM_SUBSCRIPTION_TTBFRAUD_ADMIN = 'TTB Fraud Admin',
  MYTM_SUBSCRIPTION_TTBTHRESHOLD_ADMIN = 'TTB Threshold Admin',
  MYTM_SUBSCRIPTION_LEGEND_TO_CHAMPION = 'MyTM+ .com Downgrade Legend Annual to Champion Monthly',
  MYTM_REFERRAL = 'MyTM+ Referral',
  MYTM_REFERRAL_REWARD_SENT = 'MyTM+ Referral Reward Sent',
  MYTM_VIEWED_PRODUCT = 'Viewed Product',
  MYTM_VIEWED_CATEGORY = 'Viewed Category',
  MYTM_ACTIVE_ON_SITE = 'Active on Site',
  MYTM_SPAM_REQUEST_COURSE = 'Spam Request Courses',
  OTP_VERIFIED = 'OTP Verified',
  ORDER_PUSH_NOTIFICATION = 'Order Push Notification Event',
  CHECK_BULL_DASHBOARD_DAILY = 'Check Bull Dashboard Daily',
}
@Injectable()
export class KlaviyoService {
  private readonly logger = new Logger(KlaviyoService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectQueue('klaviyo-track') private klaviyoTrackQueue: Queue
  ) {}

  async track(email: string, event: KlaviyoTrackEvents, properties: any = {}, customerProperties: any = {}) {
    try {
      // const delay = await this.getDelayedTrackTime();
      return this.klaviyoTrackQueue.add(KlaviyoTrackProcessorQueueName.TRACK, {
        event,
        email,
        properties,
        customerProperties: {
          $email: email,
          ...customerProperties,
        },
      });
    } catch (e) {
      this.logger.error(e);
    }
  }

  async triggerEvent(email, event: string, payload) {
    try {
      return this.klaviyoTrackQueue.add(KlaviyoTrackProcessorQueueName.TRACK, {
        event,
        email,
        properties: payload,
      });
    } catch (e) {
      this.logger.error(e);
    }
  }

  async getDelayedTrackTime() {
    const momentTz = require('moment-timezone');
    const klaviyoPushNotificationAt = this.config.get('app.klaviyoPushNotificationAt');
    const [fromDateHour, fromDateTimezone] = klaviyoPushNotificationAt.split('<->')[0].split(':');
    const [toDateHour] = klaviyoPushNotificationAt.split('<->')[1].split(':');
    momentTz.tz.setDefault(fromDateTimezone);
    const fromDate = momentTz().set({ hour: fromDateHour, minute: 0, second: 0, millisecond: 0 });
    const toDate = momentTz().set({ hour: toDateHour, minute: 0, second: 0, millisecond: 0 });
    const now = momentTz();
    let delayed = 0;
    if (!now.isBetween(fromDate, toDate)) {
      if (now.isBefore(fromDate)) {
        delayed = fromDate.diff(now);
      }
      if (now.isAfter(toDate)) {
        delayed = fromDate.add(1, 'd').diff(now);
      }
    }
    momentTz.tz.setDefault();
    return delayed;
  }

  async identify(email: string, properties: any = {}) {
    this.logger.log(`Track identify for email ${email}`);
    const user = await this.userRepo.findOne({
      where: { email },
    });
    const country = (user && user?.userCountry) || null;
    if (!isCanOrUsaCountry(country)) {
      return;
    }
    const privateKey = this.getSecretKeyKlaviyo(country);
    const session = new ApiKeySession(privateKey);
    const profilesApi = new ProfilesApi(session);
    const body: ProfileCreateQuery = {
      data: {
        type: 'profile',
        attributes: {
          email: email,
          properties: { ...properties },
        },
      },
    };

    await profilesApi.createOrUpdateProfile(body);
    return true;
  }

  async trackOnlyEvent(event: KlaviyoTrackEvents, properties: any = {}) {
    try {
      // only use admin. Don't need to change country.
      const session = new ApiKeySession(this.config.get('app.klaviyoPrivateToken'));
      const eventsApi = new EventsApi(session);
      const adminEmail = this.config.get('app.adminEmail');
      const body: EventCreateQueryV2 = {
        data: {
          type: 'event',
          attributes: {
            properties: { ...properties },
            metric: { data: { type: 'metric', attributes: { name: event } } },
            profile: {
              data: {
                type: 'profile',
                attributes: {
                  email: adminEmail,
                },
              },
            },
          },
        },
      };

      await eventsApi.createEvent(body);
      return true;
    } catch (e) {
      this.logger.error(e);
    }
  }

  getSecretKeyKlaviyo(country: any) {
    if (country && isCanadaCountry(country)) {
      return this.config.get('app.klaviyoCAPrivateToken');
    }
    return this.config.get('app.klaviyoPrivateToken');
  }
}

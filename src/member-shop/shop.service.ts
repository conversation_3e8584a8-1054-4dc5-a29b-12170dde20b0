import { BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, In, Repository, getManager } from 'typeorm';
import { Plan } from 'src/accesscode/access-code.type';
import { EcomService } from 'src/ecom/ecom.service';
import { ERROR_CODES } from 'src/utils/errors';
import { isUSCountry } from 'src/utils/transform';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ProductOrderEntity } from './entities/product-order.entity';
import { MEMBER_SHOP_LIMIT } from './shop.constants';
import { ProductSoldOutDto, SortOrderProductDto, UpdateProductOrderDto } from './shop.type';

export class ShopService {
  constructor(
    private readonly config: ConfigService,
    private readonly ecomService: EcomService,
    @InjectRepository(ProductOrderEntity) private readonly productOrderRepo: Repository<ProductOrderEntity>
  ) {
    this.config = config;
  }

  async getOrderProducts(country?: string) {
    try {
      const conditionCountry = this.getConditionCountry({ country });
      const results = await this.productOrderRepo
        .createQueryBuilder('por')
        .where(conditionCountry)
        .orderBy({
          'por.sortOrder': 'ASC',
        })
        .getManyAndCount();
      return {
        count: results[1],
        overwrite_products: results[0],
      };
    } catch (e) {
      return null;
    }
  }
  async getProductsRewards(country?: string) {
    try {
      const data = await this.ecomService.getTeamTMProductRewards(country);
      return data?.hits;
    } catch (e) {
      return null;
    }
  }

  async getAllProducts(country?: string, userId?: string) {
    try {
      const payload = await this.getProductsFromEcom(country);
      const newProductIds = [];
      const conditionCountry = this.getConditionCountry({ country });
      await Promise.all(
        payload.map(async (product) => {
          newProductIds.push(product.productId);
          const productQuery = this.productOrderRepo.createQueryBuilder();
          // const isExist = await this.productOrderRepo.findOne({ product_id: product.productId });
          const isExist = await productQuery
            .select(['id', 'product_id'])
            .where({ product_id: product.productId })
            .andWhere(conditionCountry)
            .getRawOne();
          if (!isExist) {
            const poe = new ProductOrderEntity();
            poe.image = product.image?.link;
            poe.product_id = product.productId;
            poe.productName = product.productName;
            poe.sold_out = !product.orderable;
            poe.sortOrder = 1;
            poe.maxQty = 1;
            poe.backgroundOpacity = '0.4'; // set default value background
            poe.participationLevel = Plan.CHAMPION.toString();
            poe.country = country?.toUpperCase().trim() || null;
            return await this.productOrderRepo.save(poe);
          } else {
            console.log(`------------------------------------------`);
            console.log(`COUNTRY: ${country}`);
            console.log(`PRODUCT ID: ${isExist.product_id}`);
            console.log(`UUID: ${isExist.id}`);
            console.log(`SOLD_OUT: ${!product.orderable}`);
            console.log(`------------------------------------------`);
            return await this.productOrderRepo.update(
              { id: isExist.id },
              { image: product.image?.link, sold_out: !product.orderable, updatedBy: userId }
            );
          }
        })
      );

      const productPrefetch = await this.productOrderRepo
        .createQueryBuilder('po')
        .where(conditionCountry)
        .select(['po.product_id', 'po.id'])
        .limit(MEMBER_SHOP_LIMIT)
        .getMany();
      for (const product of productPrefetch) {
        if (newProductIds.indexOf(product.product_id) < 0) {
          await this.productOrderRepo.delete({ id: product.id });
        }
      }
      await this.randomSortOrderProduct(payload);
      return await this.getOrderProducts(country);
    } catch (e) {
      return [];
    }
  }

  async randomSortOrderProduct(payload: any) {
    const sortOrders = _.shuffle(
      Array(payload.length)
        .fill(null)
        .map((_, index) => index + 1)
    );
    let i = 0;
    for (const product of payload) {
      await this.productOrderRepo.update({ product_id: product.productId }, { sortOrder: sortOrders[i] });
      i += 1;
    }
  }

  async getProductsFromEcom(country?: string) {
    try {
      const data = await this.ecomService.getProductAll(country);
      return data?.hits;
    } catch (e) {
      return null;
    }
  }

  async postUpdateProduct(productId, payload: UpdateProductOrderDto, updatedById) {
    const product = await this.getProductById(productId, payload.country);
    if (product) {
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        delete product.country;
        await transactionalEntityManager.update(ProductOrderEntity, { id: product.id }, payload);
        await CommonSubscriber.activityLogCommandUpdate(
          {
            entity: {
              uuid: product.id,
              updatedBy: updatedById,
            },
            manager: transactionalEntityManager,
          },
          ProductOrderEntity.name,
          product,
          payload
        );
      });
      return this.productOrderRepo.findOne({ id: product.id });
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: `Product ${productId} not found`,
      });
    }
  }

  async postUpdateProductSoldOut(payload: ProductSoldOutDto, userId?: string): Promise<any> {
    return await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const id = payload.productId;
      const productNeedUpdate = await this.getProductById(id, payload.country);
      if (!productNeedUpdate) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
          errorMessage: `Product ${id} not found`,
        });
      }
      const productUpdate = {
        ...productNeedUpdate,
        sold_out: payload.soldOut !== undefined ? payload.soldOut : productNeedUpdate.sold_out,
        participationLevel:
          payload.participationLevel !== undefined ? payload.participationLevel : productNeedUpdate.participationLevel,
        maxQty: payload.maxQty !== undefined ? payload.maxQty : productNeedUpdate.maxQty,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      await transactionalEntityManager.update(ProductOrderEntity, { id: productUpdate.id }, productUpdate);
      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: productUpdate.id,
            updatedBy: userId,
          },
          manager: transactionalEntityManager,
        },
        ProductOrderEntity.name,
        productNeedUpdate,
        productUpdate
      );
      return { success: true };
    });
  }

  async postUpdateSortOrderProduct(payload: SortOrderProductDto, userId: string): Promise<any> {
    try {
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listProduct = payload.sortOrderList;
          await Promise.all(
            listProduct.map(async (product) => {
              const checkConstantSortOrder = listProduct.filter(
                (otherProduct) => otherProduct.sortOrder === product.sortOrder
              );
              if (checkConstantSortOrder.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }
              const checkExistProduct = listProduct.filter(
                (otherProduct) => otherProduct.productId === product.productId
              );
              if (checkExistProduct.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_EXISTED_MORE_THAN_ONE,
                  errorMessage: `ProductId exist more than one record`,
                });
              }

              const id = product.productId;
              const productNeedUpdate = await this.getProductById(id, payload.country);
              if (!productNeedUpdate) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
                  errorMessage: `Product ${id} not found`,
                });
              }
              await transactionalEntityManager.update(
                ProductOrderEntity,
                { id: productNeedUpdate.id },
                { sortOrder: product.sortOrder, updatedBy: userId }
              );
            })
          );
        })
        .then(async () => {
          const conditionCountry = this.getConditionCountry(payload);
          return await this.productOrderRepo
            .createQueryBuilder('por')
            .where(conditionCountry)
            .orderBy({
              'por.sortOrder': 'ASC',
            })
            .getMany();
        });
    } catch (e) {
      return e;
    }
  }

  async getProductById(id: string, country?: string) {
    const conditionCountry = this.getConditionCountry({ country });
    return await this.productOrderRepo
      .createQueryBuilder('po')
      .where(conditionCountry)
      .andWhere({ product_id: In([id.toUpperCase(), id.toLowerCase()]) })
      .getOne();
    // return await this.productOrderRepo.findOne({
    //   where: {
    //     product_id: In([id.toUpperCase(), id.toLowerCase()]),
    //   },
    // });
  }

  async deleteProduct(product_id: string, country?: string, userId?: string) {
    const product = await this.getProductById(product_id, country);
    if (product) {
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        await this.productOrderRepo.delete({
          id: product.id,
        });

        await transactionalEntityManager.delete(ProductOrderEntity, { id: product.id });
        await CommonSubscriber.activityLogCommandDelete(
          {
            entity: {
              uuid: product.id,
              deletedBy: userId,
            },
            manager: transactionalEntityManager,
            databaseEntity: product,
          },
          ProductOrderEntity.name
        );
      });
      return { success: true };
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: `Product ${product_id} not found`,
      });
    }
  }

  private getConditionCountry(payload: any) {
    let conditionCountry = '1=1';
    let countryCode = payload.country;
    if (countryCode) {
      countryCode = countryCode.trim().toUpperCase();
      if (isUSCountry(countryCode)) {
        conditionCountry = ` (country IS NULL OR country = '${countryCode}') `;
      } else {
        conditionCountry = ` country = '${countryCode}' `;
      }
    } else {
      conditionCountry = ` (country IS NULL OR country = 'USA' OR country = 'US') `;
    }
    return conditionCountry;
  }
}

import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { ShopService } from './shop.service';
import { ProductSoldOutDto, SortOrderProductDto, UpdateProductOrderDto } from './shop.type';

@Controller('shop')
export class ShopController {
  constructor(private shopService: ShopService) {}
  @Get('products/overwriteEcom')
  async getOrderProducts(@Request() req) {
    return this.shopService.getOrderProducts(req?.headers?.country);
  }
  @Get('products/rewards')
  async getProductRewards(@Request() req) {
    return await this.shopService.getProductsRewards(req?.headers?.country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('products/adminPortal')
  async getAllProducts(@Req() request: BaseRequest, @Query('country') country?: string) {
    return this.shopService.getAllProducts(country, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('products/:id')
  async deleteProductById(@Req() request: BaseRequest, @Param('id') id: string, @Query('country') country?: string) {
    return this.shopService.deleteProduct(id, country, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('products/sort')
  async postUpdateSortOrderProductMemberShop(@Req() request: BaseRequest, @Body() payload: SortOrderProductDto) {
    return this.shopService.postUpdateSortOrderProduct(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('products/soldOut')
  async postUpdateSoldOutProductMemberShop(@Req() request: BaseRequest, @Body() payload: ProductSoldOutDto) {
    return this.shopService.postUpdateProductSoldOut(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Put('products/:id')
  async putUpdateProduct(@Param('id') id: string, @Body() payload: UpdateProductOrderDto, @Req() request: BaseRequest) {
    return this.shopService.postUpdateProduct(id, payload, request.user.uid);
  }
}

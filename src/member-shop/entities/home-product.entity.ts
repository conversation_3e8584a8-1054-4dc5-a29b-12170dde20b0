import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('HomeProducts')
export class HomeProductEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  product_id: string;

  @Column()
  image: string;

  @Column()
  brandLogo: string;

  @Column()
  tag: string;

  @Column()
  link: string;

  @Column()
  sortOrder: number;

  @Column()
  productName: string;

  @Column()
  description: string;

  @Column()
  options: string;

  @Column()
  price: string;

  @Column()
  priceDiscount: string;

  @Column()
  country: string;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  updatedBy: string;

  @Column()
  createdBy: string;

  @Column()
  deletedBy: string;
}

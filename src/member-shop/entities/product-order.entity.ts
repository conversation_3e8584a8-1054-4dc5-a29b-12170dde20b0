import { Column, <PERSON>tity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('ProductOrders')
export class ProductOrderEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  product_id: string;

  @Column({ default: false })
  sold_out: boolean;

  @Column()
  image: string;

  @Column()
  sortOrder: number;

  @Column()
  productName: string;

  @Column()
  priceColor: string;

  @Column()
  textColor: string;

  @Column()
  buttonText: string;

  @Column()
  buttonPosition: string;

  @Column()
  buttonBackgroundColor: string;

  @Column()
  buttonTextColor: string;

  @Column({ default: '0.4' })
  backgroundOpacity: string;

  @Column({ default: 1 })
  maxQty: number;

  @Column()
  participationLevel: string;

  @Column()
  country: string;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column()
  updatedBy: string;
}

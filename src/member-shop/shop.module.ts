import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { HomeProductEntity } from './entities/home-product.entity';
import { ProductDiscountEntity } from './entities/product-discount.entity';
import { ProductOrderEntity } from './entities/product-order.entity';
import { ShopController } from './shop.controller';
import { ShopDiscountController } from './shop.discount.controller';
import { ShopDiscountService } from './shop.discount.service';
import { ShopHomeProductController } from './shop.home.product.controller';
import { ShopHomeProductService } from './shop.home.product.service';
import { ShopService } from './shop.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([ProductOrderEntity, ProductDiscountEntity, HomeProductEntity])],
  controllers: [ShopController, ShopDiscountController, ShopHomeProductController],
  providers: [ShopService, ShopDiscountService, ShopHomeProductService],
})
export class ShopModule {}

import { IsNotEmpty, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';

export const PRODUCT_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};
export class CreateHomeProductDto {
  @IsNotEmpty()
  @IsString()
  productName: string;

  @IsString()
  @IsOptional()
  product_id: string;

  @IsString()
  @IsOptional()
  brandLogo: string;

  @IsString()
  @IsNotEmpty()
  tag: string;

  @IsOptional()
  options: string;

  @IsString()
  @IsNotEmpty()
  country?: string;

  @IsString()
  @IsNotEmpty()
  image?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsNotEmpty()
  link: string;

  @IsString()
  @IsOptional()
  createdBy: string;

  @IsNumber()
  @IsOptional()
  sortOrder: number;

  @IsOptional()
  updatedAt: Date;
}
export class UpdateHomeProductDto {
  @IsString()
  @IsOptional()
  productName: string;

  @IsString()
  @IsOptional()
  product_id: string;

  @IsString()
  @IsOptional()
  brandLogo: string;

  @IsString()
  @IsOptional()
  tag: string;

  @IsOptional()
  options: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  link?: string;

  @IsString()
  @IsOptional()
  image?: string;

  @IsNumber()
  @IsOptional()
  sortOrder: number;
}

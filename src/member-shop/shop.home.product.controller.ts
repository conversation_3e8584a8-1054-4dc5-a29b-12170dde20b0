import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { ShopHomeProductService } from './shop.home.product.service';
import { CreateHomeProductDto, UpdateHomeProductDto } from './shop.home.product.type';
import { SortOrderProductDto } from './shop.type';

@Controller('home-product')
export class ShopHomeProductController {
  constructor(private shopService: ShopHomeProductService) {}
  @Get('products')
  async getOrderProductsActive(@Request() req) {
    return await this.shopService.getOrderProducts(req?.headers?.country, true);
  }
  @Get('products/overwriteEcom')
  async getOrderProducts(@Request() req) {
    return await this.shopService.getOrderProducts(req?.headers?.country ?? 'all', false);
  }

  @UseGuards(AuthGuard)
  @Get('orders')
  async getOrdersCustomerByEmail(@Request() req, @Query('email') email?: string, @Query('orderIds') orderIds?: string) {
    const customerEmail = email ?? req?.user.email;
    return await this.shopService.getOrdersCustomerByEmail(customerEmail, req?.headers?.country, orderIds);
  }

  @UseGuards(AuthGuard)
  @Get('orders/:orderId')
  async getOrderDetail(@Request() req, @Param('orderId') orderId: string) {
    return await this.shopService.getOrderTeamTMSite(orderId, req?.headers?.country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('products/adminPortal')
  async getAllProducts(@Req() request: BaseRequest, @Query('country') country?: string) {
    return await this.shopService.getAllHomeProducts(country, request.user.uid);
  }
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('products/save')
  async createHomeProduct(@Req() request: BaseRequest, @Body() payload: CreateHomeProductDto) {
    return await this.shopService.createHomeProduct(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete('products/:id')
  async deleteProductById(@Req() request: BaseRequest, @Param('id') id: string, @Query('country') country?: string) {
    return this.shopService.deleteProduct(id, country, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('products/sort')
  async postUpdateSortOrderProductMemberShop(@Req() request: BaseRequest, @Body() payload: SortOrderProductDto) {
    return this.shopService.postUpdateSortOrderProduct(payload, request.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Put('products/:id')
  async putUpdateProduct(@Param('id') id: string, @Body() payload: UpdateHomeProductDto, @Req() request: BaseRequest) {
    return this.shopService.postUpdateProduct(id, payload, request.user.uid);
  }
}

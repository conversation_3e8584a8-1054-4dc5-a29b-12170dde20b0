import { BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, In, Repository, getManager } from 'typeorm';
import { EcomService } from 'src/ecom/ecom.service';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import { ERROR_CODES } from 'src/utils/errors';
import { isAllCountry, isCanadaCountry, isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { HomeProductEntity } from './entities/home-product.entity';
import { MEMBER_SHOP_LIMIT } from './shop.constants';
import { PRODUCT_STATUS } from './shop.discount.type';
import { CreateHomeProductDto, UpdateHomeProductDto } from './shop.home.product.type';
import { SortOrderProductDto } from './shop.type';

export class ShopHomeProductService {
  constructor(
    private readonly config: ConfigService,
    private readonly ecomService: EcomService,
    private readonly apiVersionsService: ApiVersionsService,
    @InjectRepository(HomeProductEntity) private readonly homeProductRepo: Repository<HomeProductEntity>
  ) {
    this.config = config;
  }

  async getOrderProducts(country?: string, isCheckStatus?: boolean) {
    try {
      let conditionCountry = ' 1 = 1 ';
      conditionCountry = this.getQueryCountryCondition(country, conditionCountry);

      const queryBuilder = this.homeProductRepo.createQueryBuilder('por');

      queryBuilder.where(conditionCountry).orderBy({
        'por.sortOrder': 'ASC',
        'por.updatedAt': 'DESC',
      });

      if (isCheckStatus) {
        queryBuilder.andWhere({ status: PRODUCT_STATUS.ACTIVE });
      }
      const results = await queryBuilder.getManyAndCount();
      return {
        count: results[1],
        overwrite_products: results[0],
      };
    } catch (e) {
      return null;
    }
  }
  async createHomeProduct(payload: CreateHomeProductDto, userId: string) {
    try {
      payload.createdBy = userId;
      payload.updatedAt = new Date();
      payload.status = payload?.status ? payload.status?.toUpperCase() : PRODUCT_STATUS.ACTIVE;
      payload.sortOrder = payload?.sortOrder ? payload?.sortOrder : 1;
      if (payload.options) {
        payload.options = JSON.stringify(payload.options);
      }
      const homeProduct = await this.homeProductRepo.save(this.homeProductRepo.create(payload));
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_PRODUCT, payload.country, payload.createdBy);
      return homeProduct;
    } catch (error) {
      console.error(error);
      throw new BadRequestException(error.message);
    }
  }
  async getAllHomeProducts(country?: string, userId?: string) {
    return true;
    try {
      const payload = await this.getProductsFromEcom(country);
      const newProductIds = [];
      const conditionCountry = this.getConditionCountry({ country });
      let urlECom = this.config.get('app.ecomEndpoint');
      if (isCanadaCountry(country)) {
        urlECom = this.config.get('app.ecomCANEndpoint');
      }
      await Promise.all(
        payload.map(async (product) => {
          newProductIds.push(product.productId);
          const productQuery = this.homeProductRepo.createQueryBuilder();
          const isExist = await productQuery
            .select(['id', 'product_id'])
            .where({ product_id: product.productId })
            .andWhere(conditionCountry)
            .getRawOne();
          if (!isExist) {
            const poe = new HomeProductEntity();
            poe.image = product.image?.link;
            poe.product_id = product.productId;
            poe.price = product.price;
            poe.priceDiscount = product.pricePerUnit;
            poe.productName = product.productName;
            poe.sortOrder = 1;
            poe.createdBy = userId;
            poe.link = `${urlECom}/${_.kebabCase(product?.productName)}/${product.productId}.html`;
            poe.status = PRODUCT_STATUS.ACTIVE;
            poe.country = country?.toUpperCase().trim() || null;
            return await this.homeProductRepo.save(poe);
          } else {
            console.log(`------------------------------------------`);
            console.log(`COUNTRY: ${country}`);
            console.log(`PRODUCT ID: ${isExist.product_id}`);
            console.log(`UUID: ${isExist.id}`);
            console.log(`------------------------------------------`);
            return await this.homeProductRepo.update(
              { id: isExist.id },
              {
                image: product.image?.link,
                price: product.price,
                // link: product.link,
                priceDiscount: product.pricePerUnit,
                updatedBy: userId,
              }
            );
          }
        })
      );

      const productPrefetch = await this.homeProductRepo
        .createQueryBuilder('po')
        .where(conditionCountry)
        .select(['po.product_id', 'po.id'])
        .limit(MEMBER_SHOP_LIMIT)
        .getMany();
      for (const product of productPrefetch) {
        if (newProductIds.indexOf(product.product_id) < 0) {
          await this.homeProductRepo.delete({ id: product.id });
        }
      }
      await this.randomSortOrderProduct(payload);
      return await this.getOrderProducts(country, false);
    } catch (e) {
      console.log(e);

      return [];
    }
  }

  async cronUpdateHomeProduct() {
    return;
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    console.log(`START cronUpdateHomeProduct ${new Date().toISOString()}`);
    try {
      await this.getAllHomeProducts('USA', null);
    } catch (error) {}

    try {
      await this.getAllHomeProducts('CAN', null);
    } catch (error) {}
    console.log(`FINISH cronUpdateHomeProduct ${new Date().toISOString()}`);
  }
  async randomSortOrderProduct(payload: any) {
    const sortOrders = _.shuffle(
      Array(payload.length)
        .fill(null)
        .map((_, index) => index + 1)
    );
    let i = 0;
    for (const product of payload) {
      await this.homeProductRepo.update({ product_id: product.productId }, { sortOrder: sortOrders[i] });
      i += 1;
    }
  }

  async getProductsFromEcom(country?: string) {
    try {
      const data = await this.ecomService.getHomeProductWithPrices(country);
      return data?.hits;
    } catch (e) {
      return null;
    }
  }
  async getOrdersCustomerByEmail(customerEmail: string, country?: string, orderIds?: string) {
    try {
      const data = await this.ecomService.getOrdersCustomerByEmail(customerEmail, country, orderIds);
      return data?.hits;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getOrderTeamTMSite(orderId: string, country?: string) {
    try {
      const data = await this.ecomService.getTeamTMSiteOrder(orderId, country);
      return data;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async postUpdateProduct(productId, payload: UpdateHomeProductDto, updatedById) {
    const product = await this.getHomeProductById(productId);
    if (product) {
      if (payload.options) {
        payload.options = JSON.stringify(payload.options);
      }
      if (payload.status) {
        payload.status = payload.status.toUpperCase();
      }
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        delete product.country;
        await transactionalEntityManager.update(HomeProductEntity, { id: product.id }, payload);
        await CommonSubscriber.activityLogCommandUpdate(
          {
            entity: {
              uuid: product.id,
              updatedBy: updatedById,
            },
            manager: transactionalEntityManager,
          },
          HomeProductEntity.name,
          product,
          payload
        );
      });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_PRODUCT, product.country, product.createdBy);
      return this.homeProductRepo.findOne({ id: product.id });
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: `Product ${productId} not found`,
      });
    }
  }

  async postUpdateSortOrderProduct(payload: SortOrderProductDto, userId: string): Promise<any> {
    try {
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listProduct = payload.sortOrderList;
          await Promise.all(
            listProduct.map(async (product) => {
              const checkConstantSortOrder = listProduct.filter(
                (otherProduct) => otherProduct.sortOrder === product.sortOrder
              );
              if (checkConstantSortOrder.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }
              const checkExistProduct = listProduct.filter((otherProduct) => otherProduct.id === product.id);
              if (checkExistProduct.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_EXISTED_MORE_THAN_ONE,
                  errorMessage: `ProductId exist more than one record`,
                });
              }

              const id = product.id;
              const productNeedUpdate = await this.getHomeProductById(id);
              if (!productNeedUpdate) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
                  errorMessage: `Product ${id} not found`,
                });
              }
              await transactionalEntityManager.update(
                HomeProductEntity,
                { id: productNeedUpdate.id },
                { sortOrder: product.sortOrder, updatedBy: userId }
              );
            })
          );
        })
        .then(async () => {
          let conditionCountry = ' 1 = 1 ';
          conditionCountry = this.getQueryCountryCondition(payload?.country, conditionCountry);
          const homeProducts = await this.homeProductRepo
            .createQueryBuilder('por')
            .where(conditionCountry)
            .orderBy({
              'por.sortOrder': 'ASC',
            })
            .getMany();
          if (homeProducts && homeProducts.length > 0) {
            const homeProduct = homeProducts[0];
            await this.apiVersionsService.updateVersion(
              FEATURE_KEY_VERSION.HOME_PRODUCT,
              homeProduct.country,
              homeProduct.updatedBy
            );
          }
        });
    } catch (e) {
      return e;
    }
  }

  async getProductById(id: string, country?: string) {
    let conditionCountry = ' 1 = 1 ';
    conditionCountry = this.getQueryCountryCondition(country, conditionCountry);
    return await this.homeProductRepo
      .createQueryBuilder('po')
      .where(conditionCountry)
      .andWhere([
        { product_id: In([id.toUpperCase(), id.toLowerCase()]) },
        { id: In([id.toUpperCase(), id.toLowerCase()]) },
      ])
      .getOne();
  }
  async getHomeProductById(id: string) {
    return await this.homeProductRepo.createQueryBuilder('po').where({ id }).getOne();
  }

  async deleteProduct(product_id: string, country?: string, userId?: string) {
    const product = await this.getHomeProductById(product_id);
    if (product) {
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        await this.homeProductRepo.delete({
          id: product.id,
        });

        await transactionalEntityManager.delete(HomeProductEntity, { id: product.id });
        await CommonSubscriber.activityLogCommandDelete(
          {
            entity: {
              uuid: product.id,
              deletedBy: userId,
            },
            manager: transactionalEntityManager,
            databaseEntity: product,
          },
          HomeProductEntity.name
        );
      });
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.HOME_PRODUCT, product.country, product.createdBy);
      return { success: true };
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: `Product ${product_id} not found`,
      });
    }
  }

  getQueryCountryCondition(country: any, conditionCountry: string) {
    if (country) {
      if (isAllCountry(country)) {
        return conditionCountry;
      }
      if (isUSCountry(country)) {
        conditionCountry += ` AND (country LIKE '%${country}%' OR country IS NULL) `;
        return conditionCountry;
      }
      conditionCountry += ` AND country LIKE '%${country}%' `;
      return conditionCountry;
    }
    conditionCountry = `(country LIKE '%US%'  OR country IS NULL) `;
    return conditionCountry;
  }

  private getConditionCountry(payload: any) {
    let conditionCountry = '1=1';
    let countryCode = payload.country;
    if (countryCode) {
      countryCode = countryCode.trim().toUpperCase();
      if (isUSCountry(countryCode)) {
        conditionCountry = ` (country IS NULL OR country = '${countryCode}') `;
      } else {
        conditionCountry = ` country = '${countryCode}' `;
      }
    } else {
      conditionCountry = ` (country IS NULL OR country = 'USA' OR country = 'US') `;
    }
    return conditionCountry;
  }
}

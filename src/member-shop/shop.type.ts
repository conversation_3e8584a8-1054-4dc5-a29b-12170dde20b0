import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class SortOrderProductDto {
  @IsNotEmpty()
  @IsArray()
  sortOrderList: SortOrderProduct[];

  @IsOptional()
  country: string;
}

export class SortOrderProduct {
  @IsUUID()
  id: string;

  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;

  @IsString()
  @IsOptional()
  name: string;

  @IsBoolean()
  @IsOptional()
  soldOut: boolean;

  @IsOptional()
  country: string;
}

export class ProductSoldOutDto {
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsBoolean()
  @IsOptional()
  soldOut: boolean;

  @IsBoolean()
  @IsOptional()
  isHidden: boolean;

  @IsString()
  @IsOptional()
  participationLevel: string;

  @IsNumber()
  @IsOptional()
  maxQty: number;

  @IsString()
  @IsOptional()
  country: string;
}

export class UpdateProductOrderDto {
  @IsString()
  @IsOptional()
  productName: string;

  @IsString()
  @IsOptional()
  priceColor: string;

  @IsString()
  @IsOptional()
  textColor: string;

  @IsString()
  @IsOptional()
  buttonText: string;

  @IsString()
  @IsOptional()
  buttonPosition: string;

  @IsString()
  @IsOptional()
  buttonBackgroundColor: string;

  @IsString()
  @IsOptional()
  buttonTextColor: string;

  @IsString()
  @IsOptional()
  backgroundOpacity: string;

  @IsNumber()
  @IsOptional()
  maxQty: number;

  @IsBoolean()
  @IsOptional()
  isHidden: boolean;

  @IsString()
  @IsOptional()
  participationLevel: string;

  @IsBoolean()
  @IsOptional()
  isFree: boolean;

  @IsString()
  @IsOptional()
  country?: string;
}

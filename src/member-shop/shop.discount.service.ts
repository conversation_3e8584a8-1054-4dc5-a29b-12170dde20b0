import { BadRequestException } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import { isEmpty } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, In, Repository, getManager } from 'typeorm';
import { EcomService } from 'src/ecom/ecom.service';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import { ERROR_CODES } from 'src/utils/errors';
import { isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ProductDiscountEntity } from './entities/product-discount.entity';
import { MEMBER_SHOP_LIMIT } from './shop.constants';
import { PRODUCT_STATUS, UpdateProductDiscountDto } from './shop.discount.type';
import { SortOrderProductDto } from './shop.type';

export class ShopDiscountService {
  constructor(
    private readonly config: ConfigService,
    private readonly ecomService: EcomService,
    private readonly apiVersionsService: ApiVersionsService,
    @InjectRepository(ProductDiscountEntity) private readonly productDiscountRepo: Repository<ProductDiscountEntity>
  ) {
    this.config = config;
  }

  async getOrderProducts(country?: string, isCheckStatus?: boolean) {
    try {
      const conditionCountry = this.getConditionCountry({ country });
      const queryBuilder = this.productDiscountRepo.createQueryBuilder('por');

      queryBuilder.where(conditionCountry).orderBy({
        'por.sortOrder': 'ASC',
      });

      if (isCheckStatus) {
        queryBuilder.andWhere({ status: PRODUCT_STATUS.ACTIVE });
      }
      const results = await queryBuilder.getManyAndCount();
      return {
        count: results[1],
        overwrite_products: results[0],
      };
    } catch (e) {
      return null;
    }
  }

  async getAllProducts(country?: string, userId?: string) {
    try {
      const payload = await this.getProductsFromEcom(country);
      if (!payload) {
        console.log(`${country} - Product TeamTM not found!`);
        return null;
      }
      const newProductIds = [];
      const conditionCountry = this.getConditionCountry({ country });
      await Promise.all(
        payload.map(async (product) => {
          newProductIds.push(product.productId);
          const productQuery = this.productDiscountRepo.createQueryBuilder();
          const isExist = await productQuery
            .select(['id', 'product_id'])
            .where({ product_id: product.productId })
            .andWhere(conditionCountry)
            .getRawOne();
          const productRefId = product?.representedProduct?.id;
          console.log(`ProductID: ${product.productId}, ProductRefId: ${productRefId}`);
          const productRefDetail = await this.ecomService.getTeamTMProductDetail(productRefId, country);
          const productDetail = await this.ecomService.getTeamTMProductDetail(product.productId, country);

          const price = productRefDetail?.prices?.taylormadeUsdRetailPriceList ?? product?.price;
          const priceDiscount = productRefDetail?.prices?.tmagUsClearance ?? 0;
          const productImageUrl = product.image?.link ?? productRefDetail?.imageGroups[0]['images'][0]['link'];
          const brandLogo = productDetail?.cTmCollaborationLabel ?? '';
          if (!isExist) {
            const poe = new ProductDiscountEntity();
            poe.image = productImageUrl;
            poe.orderable = product?.orderable == true;
            poe.product_id = product.productId;
            poe.price = price;
            poe.priceDiscount = priceDiscount;
            poe.productName = product.productName;
            poe.sortOrder = 1;
            poe.createdBy = userId;
            poe.link = product?.link;
            poe.productRefId = productRefId;
            poe.brandLogo = brandLogo;
            poe.status = PRODUCT_STATUS.ACTIVE;
            poe.country = country?.toUpperCase().trim() || null;
            return await this.productDiscountRepo.save(poe);
          } else {
            console.log(`------------------------------------------`);
            console.log(`COUNTRY: ${country}`);
            console.log(`PRODUCT ID: ${isExist.product_id}`);
            console.log(`UUID: ${isExist.id}`);
            console.log(`------------------------------------------`);

            return await this.productDiscountRepo.update(
              { id: isExist.id },
              {
                image: productImageUrl,
                price,
                brandLogo: isExist.brandLogo ?? brandLogo,
                priceDiscount,
                productRefId,
                orderable: product?.orderable == true,
                updatedBy: userId,
              }
            );
          }
        })
      );

      const productPrefetch = await this.productDiscountRepo
        .createQueryBuilder('po')
        .where(conditionCountry)
        .select(['po.product_id', 'po.id'])
        .limit(MEMBER_SHOP_LIMIT)
        .getMany();
      for (const product of productPrefetch) {
        if (newProductIds.indexOf(product.product_id) < 0) {
          await this.productDiscountRepo.delete({ id: product.id });
        }
      }
      await this.randomSortOrderProduct(payload);
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.SHOP_PRODUCT_CATALOG, country, userId);
      return await this.getOrderProducts(country, false);
    } catch (e) {
      console.log(e);

      return [];
    }
  }

  @Cron(CronExpression.EVERY_HOUR)
  async cronUpdateProductDiscounts() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }
    console.log(`START cronUpdateProductDiscount ${new Date().toISOString()}`);
    try {
      await this.getAllProducts('USA', null);
    } catch (error) {}

    try {
      await this.getAllProducts('CAN', null);
    } catch (error) {}
    console.log(`FINISH cronUpdateProductDiscount ${new Date().toISOString()}`);
  }
  async randomSortOrderProduct(payload: any) {
    const sortOrders = _.shuffle(
      Array(payload.length)
        .fill(null)
        .map((_, index) => index + 1)
    );
    let i = 0;
    for (const product of payload) {
      await this.productDiscountRepo.update({ product_id: product.productId }, { sortOrder: sortOrders[i] });
      i += 1;
    }
  }

  async getProductsFromEcom(country?: string) {
    try {
      const data = await this.ecomService.getProductAllWithPrices(country);
      return data?.hits;
    } catch (e) {
      return null;
    }
  }

  async postUpdateProduct(productId, payload: UpdateProductDiscountDto, updatedById) {
    const product = await this.getProductById(productId, payload.country);
    if (product) {
      if (payload.options) {
        payload.options = JSON.stringify(payload.options);
      }
      if (payload.status) {
        payload.status = payload.status.toUpperCase();
      }
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        delete product.country;
        await transactionalEntityManager.update(ProductDiscountEntity, { id: product.id }, payload);
        await CommonSubscriber.activityLogCommandUpdate(
          {
            entity: {
              uuid: product.id,
              updatedBy: updatedById,
            },
            manager: transactionalEntityManager,
          },
          ProductDiscountEntity.name,
          product,
          payload
        );
      });
      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.SHOP_PRODUCT_CATALOG,
        payload.country,
        updatedById
      );
      return this.productDiscountRepo.findOne({ id: product.id });
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: `Product ${productId} not found`,
      });
    }
  }

  async postUpdateSortOrderProduct(payload: SortOrderProductDto, userId: string): Promise<any> {
    try {
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listProduct = payload.sortOrderList;
          await Promise.all(
            listProduct.map(async (product) => {
              const checkConstantSortOrder = listProduct.filter(
                (otherProduct) => otherProduct.sortOrder === product.sortOrder
              );
              if (checkConstantSortOrder.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }
              const checkExistProduct = listProduct.filter(
                (otherProduct) => otherProduct.productId === product.productId
              );
              if (checkExistProduct.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_EXISTED_MORE_THAN_ONE,
                  errorMessage: `ProductId exist more than one record`,
                });
              }

              const id = product.productId;
              const productNeedUpdate = await this.getProductById(id, payload.country);
              if (!productNeedUpdate) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
                  errorMessage: `Product ${id} not found`,
                });
              }
              await transactionalEntityManager.update(
                ProductDiscountEntity,
                { id: productNeedUpdate.id },
                { sortOrder: product.sortOrder, updatedBy: userId }
              );
            })
          );
        })
        .then(async () => {
          const conditionCountry = this.getConditionCountry(payload);
          const productDiscounts = await this.productDiscountRepo
            .createQueryBuilder('por')
            .where(conditionCountry)
            .orderBy({
              'por.sortOrder': 'ASC',
            })
            .getMany();
          if (!isEmpty(productDiscounts)) {
            const productDiscount = productDiscounts[0];
            await this.apiVersionsService.updateVersion(
              FEATURE_KEY_VERSION.SHOP_PRODUCT_CATALOG,
              productDiscount.country,
              userId
            );
          }
          return productDiscounts;
        });
    } catch (e) {
      return e;
    }
  }

  async getProductById(id: string, country?: string) {
    const conditionCountry = this.getConditionCountry({ country });
    return await this.productDiscountRepo
      .createQueryBuilder('po')
      .where(conditionCountry)
      .andWhere({ product_id: In([id.toUpperCase(), id.toLowerCase()]) })
      .getOne();
  }

  async deleteProduct(product_id: string, country?: string, userId?: string) {
    const product = await this.getProductById(product_id, country);
    if (product) {
      await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        await this.productDiscountRepo.delete({
          id: product.id,
        });

        await transactionalEntityManager.delete(ProductDiscountEntity, { id: product.id });
        await CommonSubscriber.activityLogCommandDelete(
          {
            entity: {
              uuid: product.id,
              deletedBy: userId,
            },
            manager: transactionalEntityManager,
            databaseEntity: product,
          },
          ProductDiscountEntity.name
        );
      });
      return { success: true };
    } else {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
        errorMessage: `Product ${product_id} not found`,
      });
    }
  }

  private getConditionCountry(payload: any) {
    let conditionCountry = '1=1';
    let countryCode = payload.country;
    if (countryCode) {
      countryCode = countryCode.trim().toUpperCase();
      if (isUSCountry(countryCode)) {
        conditionCountry = ` (country IS NULL OR country = '${countryCode}') `;
      } else {
        conditionCountry = ` country = '${countryCode}' `;
      }
    } else {
      conditionCountry = ` (country IS NULL OR country = 'USA' OR country = 'US') `;
    }
    return conditionCountry;
  }
}

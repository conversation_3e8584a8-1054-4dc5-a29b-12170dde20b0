import { IsOptional, IsString } from 'class-validator';

export const PRODUCT_STATUS = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
};
export class UpdateProductDiscountDto {
  @IsString()
  @IsOptional()
  productName: string;

  @IsString()
  @IsOptional()
  brandLogo: string;

  @IsString()
  @IsOptional()
  tag: string;

  @IsOptional()
  options: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  link?: string;
}

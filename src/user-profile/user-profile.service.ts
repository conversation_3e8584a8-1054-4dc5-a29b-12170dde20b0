import { BadRequestException, CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import axios from 'axios';
import { Cache } from 'cache-manager';
import { ConfigService } from 'nestjs-config';
import { Entity<PERSON>anager, IsNull, Repository, getManager } from 'typeorm';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';
import { MrpService } from '../mrp/mrp.service';
import { ERROR_CODES } from '../utils/errors';
import { USER_PROFILE_MESSAGE_ERROR } from './user-profile.constants';

const CDM_AUTH_TOKEN_CACHE_KEY = 'CDM_AUTH_TOKEN';

@Injectable()
export class UserProfileService {
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(UserEntity) private readonly userRepository: Repository<UserEntity>,
    private readonly cdmService: CdmService,
    private readonly mrpService: MrpService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async getUserProfile(email) {
    if (!email) {
      return false;
    }
    const user = await this.userRepository.findOne({
      where: { email },
      withDeleted: true,
      relations: ['userPermission', 'userBlackList'],
    });
    if (!user) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
        errorMessage: USER_PROFILE_MESSAGE_ERROR.USER_NOT_FOUND,
      });
    }
    const userCDM = await this.getUserFromCDM(user);
    const witbs = await this.cdmService.getWITB(user.email);
    const onCourse = await this.mrpService.getRoundsPlayed(user?.mrpUID);
    const result = await this.handleUserProfile(user, userCDM, witbs, onCourse);
    return result;
  }

  handleUserProfile(user, userCDM, witbs, onCourse) {
    const golferProfile = userCDM?.golferProfile;
    if (userCDM.golferProfile) {
      delete userCDM.golferProfile;
    }

    return {
      ...user,
      golferProfile,
      witbs,
      userCDM,
      onCourse: {
        strokesGainedBaseline: onCourse?.strokesGainedBaseline,
        roundsPlayed: onCourse?.roundsPlayed,
        deleteData: onCourse?.deleteData,
      },
    };
  }

  async getUserFromCDM(user: UserEntity) {
    const response = await axios.get(
      `${this.config.get('app.cdmEndpoint')}/api/consumer/query?email=${encodeURIComponent(user.email)}&regionId=${
        user.regionId
      }&omitWITB=true`,
      await this.getRequestHeaderConfigs()
    );

    return response?.data;
  }

  async getRequestHeaderConfigs() {
    let token = await this.cacheManager.get(CDM_AUTH_TOKEN_CACHE_KEY);
    if (!token) {
      token = await this.cdmService.handleRefreshCdmAuthToken(true);
    }
    return {
      headers: { Authorization: `bearer ${token}` },
    };
  }
}

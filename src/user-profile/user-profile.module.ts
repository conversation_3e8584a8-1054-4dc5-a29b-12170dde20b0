import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { UserEntity } from '../auth/entities/user.entity';
import { UserProfileController } from './user-profile.controller';
import { UserProfileService } from './user-profile.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([UserEntity])],
  controllers: [UserProfileController],
  providers: [UserProfileService],
})
export class UserProfileModule {}

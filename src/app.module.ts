import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FirebaseAdminModule } from '@aginix/nestjs-firebase-admin';
import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { SentryModule } from '@ntegral/nestjs-sentry';
import { LogLevel } from '@sentry/types';
import 'dotenv/config';
import * as admin from 'firebase-admin';
import { ConfigModule, ConfigService } from 'nestjs-config';
import path from 'path';
import { AccessCodeModule } from './accesscode/access-code.module';
import { AdminModule } from './admin/admin.module';
import { AnswerModule } from './answer/answer.module';
import { ApiVersionsModule } from './api-versions/api-version.module';
import { AppController } from './app.controller';
import { ArccosModule } from './arccos/arccos.module';
import { AuthBasicModule } from './auth/auth.bassic.module';
import { AuthModule } from './auth/auth.module';
import { BagModule } from './bag/bag.module';
import { BenefitsModule } from './benefits/benefits.module';
import { BullDashboardModule } from './bull-dashboard/bull-dashboard.module';
import { CdmModule } from './cdm/cdm.module';
import { ClientModule } from './client/client.module';
import { CmsModule } from './cms/cms.module';
import { CoachProfileModule } from './coach-profiles/coach-profile.module';
import FirebaseServiceAccount from './config/firebase-service-account.json';
import { ContentModule } from './content/content.module';
import { EbsModule } from './ebs/ebs.module';
import { EcomModule } from './ecom/ecom.module';
import { EventModule } from './event/event.module';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { GameProfileModule } from './game-profile/game-profile.module';
import { HealthModule } from './health/health.module';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { PostInterceptor } from './interceptors/post.interceptor';
import { SentryInterceptor } from './interceptors/sentry.interceptor';
import { KlaviyoModule } from './klaviyo/klaviyo.module';
import { LoggingModule } from './logging/logging.module';
import { LoyaltyModule } from './loyalty/loyalty.module';
import { MailModule } from './mail/mail.module';
import { ShopModule } from './member-shop/shop.module';
import { MfeModule } from './mfe/mfe.module';
import { MiscModule } from './misc/misc.module';
import { MrpModule } from './mrp/mrp.module';
import { NotificationModule } from './notification/notification.module';
import { PartnersModule } from './partners/partners.module';
import { PaymentModule } from './payment/payment.module';
import { PermissionModule } from './permission/permission.module';
import { PlayModule } from './play/play.module';
import { PromotionModule } from './promotion/promotion.module';
import { QuestionModule } from './question/question.module';
import { RecommendationModule } from './recommendation/recommendation.module';
import { ShopCategoryModule } from './shop-category/shop-category.module';
import { SwingIndexModule } from './si/si.module';
import { SurveyModule } from './survey/survey.module';
import { TilesWidgetModule } from './tiles-widget/tiles-widget.module';
import { TosModule } from './tos/tos.module';
import { TourStoryModule } from './tour-story/tour-story.module';
import { TourTrashModule } from './tourtrash/tour-trash.module';
import { TranslationModule } from './translation/translation.module';
import { TTBModule } from './ttb/ttb.module';
import { UserAnswerModule } from './user-answer/user-answer.module';
import { UserBlackListModule } from './user-black-list/user-black-list.module';
import { UserProfileModule } from './user-profile/user-profile.module';
import { UserReferralModule } from './user-referral/user-referral.module';
import { redisConfig } from './utils/redis';
import { VersionModule } from './version/version.module';

@Module({
  imports: [
    AuthModule,
    ClientModule,
    MrpModule,
    CdmModule,
    MiscModule,
    PlayModule,
    BagModule,
    ContentModule,
    PaymentModule,
    LoggingModule,
    EventModule,
    CmsModule,
    MfeModule,
    TTBModule,
    EcomModule,
    ShopModule,
    AdminModule,
    SwingIndexModule,
    TourTrashModule,
    TranslationModule,
    RecommendationModule,
    SurveyModule,
    AccessCodeModule,
    EbsModule,
    TosModule,
    ArccosModule,
    HealthModule,
    KlaviyoModule,
    AuthBasicModule,
    TilesWidgetModule,
    CoachProfileModule,
    GameProfileModule,
    TourStoryModule,
    ShopCategoryModule,
    ScheduleModule.forRoot(),
    ConfigModule.load(path.resolve(__dirname, 'config', '**/!(*.d).{ts,js}')),
    TypeOrmModule.forRootAsync({
      useFactory: (config: ConfigService) => config.get('database'),
      inject: [ConfigService],
    }),
    BullModule.forRootAsync({
      useFactory: () => ({
        redis: redisConfig,
      }),
      inject: [ConfigService],
    }),
    MailerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        transport: {
          host: config.get('mail.host'),
          port: config.get('mail.port'),
          secure: false,
          pool: true,
          auth: {
            user: config.get('mail.user'),
            pass: config.get('mail.pass'),
          },
        },
        defaults: {
          from: config.get('mail.fromEmail'),
        },
        template: {
          dir: path.join(process.env.PWD || '', 'src', 'templates', 'emails'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
    }),
    MailModule,
    FirebaseAdminModule.forRootAsync({
      useFactory: () =>
        ({
          credential: admin.credential.cert(FirebaseServiceAccount as any),
        } as any),
    }),
    SentryModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        dsn: config.get('app.sentryDNS'),
        debug: true,
        environment: config.get('app.sentryEnv'),
        release: config.get('app.sentryRelease'),
        logLevel: LogLevel.Debug,
      }),
      inject: [ConfigService],
    }),
    PromotionModule,
    NotificationModule,
    PermissionModule,
    BenefitsModule,
    PartnersModule,
    UserBlackListModule,
    UserProfileModule,
    UserReferralModule,
    LoyaltyModule,
    QuestionModule,
    AnswerModule,
    UserAnswerModule,
    VersionModule,
    BullDashboardModule,
    ApiVersionsModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: PostInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: SentryInterceptor,
    },
  ],
})
export class AppModule {}

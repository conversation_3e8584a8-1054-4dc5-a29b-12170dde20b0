import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { validate } from 'class-validator';
import { ConfigService } from 'nestjs-config';
import { EntityManager, In, Repository, getManager } from 'typeorm';
import { v4 } from 'uuid';
import { ERROR_CODES } from '../utils/errors';
import { AnswerEntity } from './entities/answer.entity';
import { QuestionEntity } from './entities/question.entity';
import { ResponseEntity } from './entities/response.entity';
import { ResponseItemEntity } from './entities/responseItem.entity';
import { SurveyEntity, SurveyStatus } from './entities/survey.entity';
import { SURVEY_ERROR } from './survey.constants';
import {
  ActivateSurveyDto,
  AnswerDto,
  DeleteDto,
  QuestionDto,
  RecordSurvey,
  ResponseItemDto,
  SaveSurveyDto,
  StatusDto,
  SubmitSurveyDto,
  SubmitSurveyInterface,
  SuccessResponse,
  SurveyRecordInterface,
  UpdateStatusAnswerQuestionDto,
  UpdateStatusQuestionSurveyDto,
} from './survey.type';

@Injectable()
export class SurveyService {
  private readonly logger = new Logger(SurveyService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(SurveyEntity) private readonly surveyRepo: Repository<SurveyEntity>,
    @InjectRepository(QuestionEntity) private readonly questionRepo: Repository<QuestionEntity>,
    @InjectRepository(AnswerEntity) private readonly answerRepo: Repository<AnswerEntity>,
    @InjectRepository(ResponseEntity) private readonly responseRepo: Repository<ResponseEntity>,
    @InjectRepository(ResponseItemEntity) private readonly responseItemRepo: Repository<ResponseItemEntity>
  ) {
    this.config = config;
  }

  async getSurveyActive(feature: string): Promise<SurveyRecordInterface> {
    const survey = await this.surveyRepo
      .createQueryBuilder('s')
      .leftJoinAndSelect('s.questions', 'questions')
      .leftJoinAndSelect('questions.answers', 'answers')
      .where('s.status = :status', { status: !!SurveyStatus.ACTIVE })
      .andWhere('s.feature = :feature', { feature })
      .orderBy({
        'questions.orderIndex': 'ASC',
      })
      .addOrderBy('answers.orderIndex', 'ASC')
      .getOne();

    if (!survey) {
      return {
        error: {
          internalErrorCode: ERROR_CODES.SURVEY_NOT_FOUND,
          errorMessage: SURVEY_ERROR.NO_SURVEY_ACTIVE,
        },
      };
    }
    return {
      record: this.toRecordSurvey(survey),
    };
  }

  async submitSurvey(payload: SubmitSurveyDto, userId: string): Promise<SubmitSurveyInterface> {
    return await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const { responses, comment, feature, refId, surveyId } = payload;
      const responseItems = await Promise.all(
        responses.map(async (item) => {
          await this.validateSubmitSurvey(item);
          const question = await this.getQuestionById(item.questionId, ['answers']);
          const answer = question.answers.find((x) => x.id.toUpperCase() === item.answerId.toUpperCase());
          if (!answer) {
            throw new BadRequestException({
              internalErrorCode: ERROR_CODES.ANSWER_NOT_FOUND,
              errorMessage: SURVEY_ERROR.ANSWER_NOT_FOUND,
            });
          }

          const responseItem = new ResponseItemEntity();
          responseItem.questionId = question.id;
          responseItem.questionValue = question.question;
          responseItem.answerId = answer.id;
          responseItem.answerValue = answer.answer;

          return responseItem;
        })
      );

      const surveyActive = await this.getSurveyActiveByFeature(feature);

      if (!surveyActive || surveyActive.id.toUpperCase() !== surveyId.toUpperCase()) {
        return {
          error: {
            internalErrorCode: ERROR_CODES.SURVEY_NOT_FOUND,
            errorMessage: SURVEY_ERROR.NO_SURVEY_ACTIVE,
          },
        };
      }

      const responseRecord = new ResponseEntity();
      responseRecord.id = v4();
      responseRecord.refId = refId;
      responseRecord.surveyId = surveyId;
      responseRecord.userId = userId;
      responseRecord.additionalComment = comment;
      responseRecord.responseItems = responseItems;
      const responseSaved = new Promise((resolve: (value: ResponseEntity) => void, reject) => {
        transactionalEntityManager.save(ResponseEntity, responseRecord).then(async (responseRecordSaved) => {
          responseRecordSaved.responseItems = await Promise.all(
            responseItems.map(async (item) => {
              item.responseId = responseRecordSaved.id;
              return await transactionalEntityManager.save(ResponseItemEntity, item);
            })
          );
          resolve(responseRecordSaved);
        });
      });
      return {
        record: await responseSaved,
      };
    });
  }

  async saveSurvey(payload: SaveSurveyDto, userId?: string): Promise<SurveyRecordInterface> {
    return await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const surveyNeedUpdate = payload.id ? await this.getSurveyById(payload.id, ['questions']) : null;

      const surveyEntity: SurveyEntity = this.createSurveyEntity(payload, surveyNeedUpdate, userId);

      const surveySaved = await transactionalEntityManager.save(SurveyEntity, surveyEntity);

      const questions = await Promise.all(
        payload.questions.map((questionItem) => {
          return new Promise(async (resolve: (value: QuestionEntity) => void, reject) => {
            try {
              await this.validateSaveQuestion(questionItem, payload.questions, surveySaved && surveySaved.questions);
              const questionNeedUpdate = questionItem.id
                ? await this.getQuestionById(questionItem.id, ['answers'])
                : null;
              const questionEntity = this.createQuestionEntity(
                questionItem,
                questionNeedUpdate,
                surveySaved.id,
                userId
              );
              transactionalEntityManager.save(QuestionEntity, questionEntity).then(async (questionSaved) => {
                questionSaved.answers = await Promise.all(
                  questionItem.answers.map(async (answersItem) => {
                    await this.validateSaveAnswer(
                      answersItem,
                      questionItem.answers,
                      questionNeedUpdate && questionNeedUpdate.answers
                    );
                    const answerNeedUpdate = answersItem.id ? await this.getAnswerById(answersItem.id) : null;
                    const answerEntity = this.createAnswerEntity(
                      answersItem,
                      answerNeedUpdate,
                      questionSaved.id,
                      userId
                    );
                    return await transactionalEntityManager.save(AnswerEntity, answerEntity);
                  })
                );
                if (questionNeedUpdate && questionNeedUpdate.answers.length > 0) {
                  const answerDelete = questionNeedUpdate.answers.filter(
                    (x) => !questionItem.answers.filter((y) => !y.id).includes(x)
                  );
                  await Promise.all(
                    answerDelete.map(async (answerItemDeleted) => {
                      answerItemDeleted.deletedBy = userId;
                      await transactionalEntityManager.softRemove(AnswerEntity, answerItemDeleted);
                    })
                  );
                }
                resolve(questionSaved);
              });
            } catch (error) {
              reject(error);
            }
          });
        })
      );

      if (surveyNeedUpdate && surveyNeedUpdate.questions.length > 0) {
        const questionsDeleted = surveyNeedUpdate.questions.filter(
          (x) => !payload.questions.filter((y) => !y.id).includes(x)
        );
        await Promise.all(
          questionsDeleted.map(async (questionItemDeleted) => {
            questionItemDeleted.deletedBy = userId;
            await transactionalEntityManager.softRemove(QuestionEntity, questionItemDeleted);
          })
        );
      }
      surveySaved.questions = questions;
      return {
        record: this.toRecordSurvey(surveySaved),
      };
    });
  }

  async deleteSurveyById(payload: DeleteDto, userId: string): Promise<SuccessResponse> {
    const surveyNeedUpdate = await this.getSurveyById(payload.id);
    surveyNeedUpdate.deletedBy = userId;
    await this.surveyRepo.softRemove(surveyNeedUpdate);

    return {
      success: true,
    };
  }

  async deleteQuestionById(payload: DeleteDto, userId: string): Promise<SuccessResponse> {
    const questionNeedUpdate = await this.getQuestionById(payload.id);
    questionNeedUpdate.deletedBy = userId;
    await this.questionRepo.softRemove(questionNeedUpdate);

    return {
      success: true,
    };
  }

  async deleteAnswerById(payload: DeleteDto, userId: string): Promise<SuccessResponse> {
    const answerNeedUpdate = await this.getAnswerById(payload.id);
    answerNeedUpdate.deletedBy = userId;
    await this.answerRepo.softRemove(answerNeedUpdate);

    return {
      success: true,
    };
  }

  async activateSurvey(payload: ActivateSurveyDto, userId: string): Promise<SuccessResponse> {
    const surveyNeedUpdate = await this.getSurveyById(payload.id);
    const surveyActive = await this.getSurveyActiveByFeature(payload.feature);
    if (surveyActive) {
      surveyActive.status = !!SurveyStatus.DRAFT;
      surveyActive.updatedBy = userId;
    }
    surveyNeedUpdate.status = !!SurveyStatus.ACTIVE;
    surveyNeedUpdate.updatedBy = userId;
    const surveyUpdate = surveyActive ? [surveyNeedUpdate, surveyActive] : [surveyNeedUpdate];
    await this.surveyRepo.save(surveyUpdate);

    return {
      success: true,
    };
  }

  async updateStatusSurveyQuestion(payload: UpdateStatusQuestionSurveyDto, userId: string): Promise<SuccessResponse> {
    const survey = await this.getSurveyById(payload.surveyId, ['questions']);
    await Promise.all(payload.questions.map(async (item) => await this.validateUpdateStatusQuestionAnswer(item)));
    survey.questions.map(async (item) => {
      const questionUpdate = payload.questions.find((x) => x.id && x.id.toUpperCase() === item.id.toUpperCase());
      const statusUpdate = questionUpdate ? !!questionUpdate.status : item.status;
      return {
        ...item,
        status: statusUpdate,
        updatedBy: userId,
      };
    });
    await this.surveyRepo.save(survey);
    return {
      success: true,
    };
  }

  async updateStatusAnswerQuestion(payload: UpdateStatusAnswerQuestionDto, userId: string): Promise<SuccessResponse> {
    const question = await this.getQuestionById(payload.questionId, ['questions']);
    await Promise.all(payload.answers.map(async (item) => await this.validateUpdateStatusQuestionAnswer(item)));
    question.answers.map((item) => {
      const questionUpdate = payload.answers.find((x) => x.id && x.id.toUpperCase() === item.id.toUpperCase());
      const statusUpdate = questionUpdate ? !!questionUpdate.status : item.status;
      return {
        ...item,
        status: statusUpdate,
        updatedBy: userId,
      };
    });
    await this.questionRepo.save(question);
    return {
      success: true,
    };
  }

  private async validateUpdateStatusQuestionAnswer(payload: StatusDto) {
    const queryDto = new StatusDto();
    queryDto.status = payload?.status;
    queryDto.id = payload?.id;

    await this.validatePayload(queryDto);
  }

  private async validateSubmitSurvey(payload: ResponseItemDto) {
    const queryDto = new ResponseItemDto();
    queryDto.questionId = payload?.questionId;
    queryDto.answerId = payload?.answerId;

    await this.validatePayload(queryDto);
  }

  private async validateSaveQuestion(
    payload: QuestionDto,
    questions: QuestionDto[],
    questionsInDb: QuestionEntity[] = []
  ) {
    const queryDto = new QuestionDto();
    queryDto.id = payload?.id;
    queryDto.answers = payload?.answers;
    queryDto.question = payload?.question;
    queryDto.orderIndex = payload?.orderIndex;

    await this.validatePayload(queryDto);

    const resultFilter = questions && questions.filter((item, index) => item.orderIndex === payload?.orderIndex);
    const resultFilterAnswerInDB =
      questionsInDb && questionsInDb.filter((item, index) => item.orderIndex === payload?.orderIndex);
    if ((resultFilter && resultFilter.length > 1) || (resultFilterAnswerInDB && resultFilterAnswerInDB.length > 1)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
        errorMessage: SURVEY_ERROR.ORDER_INDEX_EXISTED,
      });
    }
  }

  private async validateSaveAnswer(
    payload: AnswerDto,
    answers: AnswerDto[],
    answersInDb: AnswerEntity[] = []
  ): Promise<void> {
    const queryDto = new AnswerDto();
    queryDto.id = payload?.id;
    queryDto.answer = payload?.answer;
    queryDto.orderIndex = payload?.orderIndex;
    await this.validatePayload(queryDto);
    const resultFilter = answers && answers.filter((item, index) => item.orderIndex === payload?.orderIndex);
    const resultFilterAnswerInDB =
      answersInDb && answersInDb.filter((item, index) => item.orderIndex === payload?.orderIndex);
    if ((resultFilter && resultFilter.length > 1) || (resultFilterAnswerInDB && resultFilterAnswerInDB.length > 1)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
        errorMessage: SURVEY_ERROR.ORDER_INDEX_EXISTED,
      });
    }
  }

  private async validatePayload(queryDto: any, groups?: string[]) {
    const error = await validate(queryDto, {
      groups,
    });

    if (error.length > 0) {
      let messErr = [];
      error.forEach((value) => {
        messErr = messErr.concat(Object.values(value.constraints));
      });
      throw new BadRequestException(messErr);
    }
  }

  private createSurveyEntity(payload: SaveSurveyDto, surveyNeedUpdate: SurveyEntity, userId: string): SurveyEntity {
    const surveyEntity = new SurveyEntity();
    surveyEntity.id = (payload.id || v4()).toUpperCase();
    surveyEntity.feature = payload.feature || (surveyNeedUpdate && surveyNeedUpdate.feature);
    surveyEntity.name = payload.name || (surveyNeedUpdate && surveyNeedUpdate.name);
    surveyEntity.status = payload.id && surveyNeedUpdate.status;
    if (payload.id) {
      surveyEntity.updatedBy = userId;
    } else {
      surveyEntity.createdBy = userId;
    }

    return surveyEntity;
  }

  private createQuestionEntity(
    payload: QuestionDto,
    questionNeedUpdate: QuestionEntity,
    surveyId: string,
    userId: string
  ): QuestionEntity {
    const questionEntity = new QuestionEntity();
    questionEntity.id = (payload.id || v4()).toUpperCase();
    questionEntity.surveyId = surveyId;
    questionEntity.question = payload.question || (questionNeedUpdate && questionNeedUpdate.question);
    questionEntity.status = payload.id && questionNeedUpdate.status;
    questionEntity.orderIndex = payload.orderIndex || (questionNeedUpdate && questionNeedUpdate.orderIndex);
    if (payload.id) {
      questionEntity.updatedBy = userId;
    } else {
      questionEntity.createdBy = userId;
    }

    return questionEntity;
  }

  private createAnswerEntity(
    payload: AnswerDto,
    answerNeedUpdate: AnswerEntity,
    questionId: string,
    userId: string
  ): AnswerEntity {
    const answerEntity = new AnswerEntity();
    answerEntity.id = (payload.id || v4()).toUpperCase();
    answerEntity.questionId = questionId;
    answerEntity.answer = payload.answer || (answerNeedUpdate && answerNeedUpdate.answer);
    answerEntity.status = payload.id && answerNeedUpdate.status;
    answerEntity.orderIndex = payload.orderIndex || (answerNeedUpdate && answerNeedUpdate.orderIndex);
    if (payload.id) {
      answerEntity.updatedBy = userId;
    } else {
      answerEntity.createdBy = userId;
    }

    return answerEntity;
  }

  private async getSurveyActiveByFeature(feature: string, relations: string[] = []): Promise<SurveyEntity> {
    return await this.surveyRepo.findOne({
      where: {
        status: !!SurveyStatus.ACTIVE,
        feature,
      },
      relations,
    });
  }

  private async getSurveyById(id: string, relations: string[] = [], throwError = true) {
    const survey: SurveyEntity = await this.surveyRepo.findOne({
      where: {
        id: In([id.toUpperCase(), id.toLowerCase()]),
      },
      relations,
    });

    if (throwError) {
      if (!survey) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.SURVEY_NOT_FOUND,
          errorMessage: SURVEY_ERROR.SURVEY_NOT_FOUND,
        });
      }
    }

    return survey;
  }

  private async getQuestionById(id: string, relations: string[] = [], throwError = true) {
    const question: QuestionEntity = await this.questionRepo.findOne({
      where: {
        id: In([id.toUpperCase(), id.toLowerCase()]),
      },
      relations,
    });

    if (throwError) {
      if (!question) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.QUESTION_NOT_FOUND,
          errorMessage: SURVEY_ERROR.QUESTION_NOT_FOUND,
        });
      }
    }

    return question;
  }

  private async getAnswerById(id: string, relations: string[] = [], throwError = true) {
    const answer: AnswerEntity = await this.answerRepo.findOne({
      where: {
        id: In([id.toUpperCase(), id.toLowerCase()]),
      },
      relations,
    });

    if (throwError) {
      if (!answer) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.ANSWER_NOT_FOUND,
          errorMessage: SURVEY_ERROR.ANSWER_NOT_FOUND,
        });
      }
    }

    return answer;
  }

  private toRecordSurvey(record: SurveyEntity): RecordSurvey {
    return {
      id: record.id,
      feature: record.feature,
      name: record.name,
      questions: record.questions.map((question) => ({
        id: question.id,
        question: question.question,
        answers: question.answers.map((answer) => ({
          id: answer.id,
          answer: answer.answer,
        })),
      })),
    };
  }
}

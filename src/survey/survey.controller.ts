import { BadRequestException, Body, Controller, Get, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from '../auth/roles.decorator';
import { AccessedClients } from '../client/clients.decorator';
import { AuthGuard } from '../guards/auth.guard';
import { CLIENTS, ClientGuard } from '../guards/client.guard';
import { ResponseEntity } from './entities/response.entity';
import { SurveyService } from './survey.service';
import {
  ActivateSurveyDto,
  DeleteDto,
  RecordSurvey,
  SaveSurveyDto,
  SubmitSurveyDto,
  SuccessResponse,
  UpdateStatusAnswerQuestionDto,
  UpdateStatusQuestionSurveyDto,
} from './survey.type';

@Controller()
export class SurveyController {
  constructor(private surveyService: SurveyService) {}

  @Get('get-survey')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async getSurveyActiveList(@Query('feature') feature: string): Promise<RecordSurvey> {
    const result = await this.surveyService.getSurveyActive(feature);
    if (result?.error) {
      throw new BadRequestException(result.error);
    }
    return result.record;
  }

  @Post('submit-survey')
  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  async submitSurvey(
    @Req() request: Request & { user: any },
    @Body() payload: SubmitSurveyDto
  ): Promise<ResponseEntity> {
    const result = await this.surveyService.submitSurvey(payload, request.user.uid);
    if (result.error) {
      throw new BadRequestException(result.error);
    }
    return result.record;
  }

  @Post('survey/save')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async saveSurvey(@Req() request: Request & { user: any }, @Body() payload: SaveSurveyDto): Promise<RecordSurvey> {
    const result = await this.surveyService.saveSurvey(payload, request.user.uid);
    return result.record;
  }

  @Post('survey/delete')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async deleteSurvey(@Req() request: Request & { user: any }, @Body() payload: DeleteDto): Promise<SuccessResponse> {
    const result = await this.surveyService.deleteSurveyById(payload, request.user.uid);
    return result;
  }

  @Post('survey/question/delete')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async deleteQuestion(@Req() request: Request & { user: any }, @Body() payload: DeleteDto): Promise<SuccessResponse> {
    const result = await this.surveyService.deleteQuestionById(payload, request.user.uid);
    return result;
  }

  @Post('survey/answer/delete')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async deleteAnswer(@Req() request: Request & { user: any }, @Body() payload: DeleteDto): Promise<SuccessResponse> {
    const result = await this.surveyService.deleteAnswerById(payload, request.user.uid);
    return result;
  }

  @Post('survey/activate')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async activateSurvey(
    @Req() request: Request & { user: any },
    @Body() payload: ActivateSurveyDto
  ): Promise<SuccessResponse> {
    const result = await this.surveyService.activateSurvey(payload, request.user.uid);
    return result;
  }

  @Post('survey/question/update-status')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async updateStatusSurveyQuestion(
    @Req() request: Request & { user: any },
    @Body() payload: UpdateStatusQuestionSurveyDto
  ): Promise<SuccessResponse> {
    const result = await this.surveyService.updateStatusSurveyQuestion(payload, request.user.uid);
    return result;
  }

  @Post('survey/answer/update-status')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async updateStatusSurveyAnswer(
    @Req() request: Request & { user: any },
    @Body() payload: UpdateStatusAnswerQuestionDto
  ): Promise<SuccessResponse> {
    const result = await this.surveyService.updateStatusAnswerQuestion(payload, request.user.uid);
    return result;
  }
}

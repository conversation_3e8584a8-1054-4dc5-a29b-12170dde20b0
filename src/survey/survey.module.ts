import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from '../shared/shared.module';
import { AnswerEntity } from './entities/answer.entity';
import { QuestionEntity } from './entities/question.entity';
import { ResponseEntity } from './entities/response.entity';
import { ResponseItemEntity } from './entities/responseItem.entity';
import { SurveyEntity } from './entities/survey.entity';
import { SurveyController } from './survey.controller';
import { SurveyService } from './survey.service';

@Module({
  imports: [
    SharedModule,
    TypeOrmModule.forFeature([SurveyEntity, QuestionEntity, AnswerEntity, ResponseEntity, ResponseItemEntity]),
  ],
  controllers: [SurveyController],
  providers: [SurveyService],
  exports: [SurveyService],
})
export class SurveyModule {}

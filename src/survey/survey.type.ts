import { Is<PERSON>rray, IsBoolean, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';
import { ResponseEntity } from './entities/response.entity';

export interface InternalError {
  internalErrorCode?: string;
  errorMessage?: string;
}

export interface AnswerRecord {
  id: string;
  answer: string;
}

export interface QuestionRecord {
  id: string;
  question: string;
  answers: AnswerRecord[];
}

export interface RecordSurvey {
  id: string;
  feature: string;
  name: string;
  questions: QuestionRecord[];
}

export interface SurveyRecordInterface {
  error?: InternalError;
  record?: RecordSurvey;
}

export interface SubmitSurveyInterface {
  error?: InternalError;
  record?: ResponseEntity;
}

export interface SuccessResponse {
  success: boolean;
}

export class AnswerDto {
  @IsOptional()
  @IsUUID('4')
  id: string;

  @IsNotEmpty()
  @IsString()
  answer: string;

  @IsNotEmpty()
  @IsNumber()
  orderIndex: number;
}

export class QuestionDto {
  @IsOptional()
  @IsUUID('4')
  id: string;

  @IsNotEmpty()
  @IsString()
  question: string;

  @IsNotEmpty()
  @IsNumber()
  orderIndex: number;

  @IsNotEmpty()
  @IsArray()
  answers: AnswerDto[];
}

export class SaveSurveyDto {
  @IsOptional()
  @IsUUID('4')
  id: string;

  @IsNotEmpty()
  @IsString()
  feature: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsArray()
  questions: QuestionDto[];
}

export class ResponseItemDto {
  @IsNotEmpty()
  @IsUUID()
  questionId: string;

  @IsNotEmpty()
  @IsUUID()
  answerId: string;
}

export class SubmitSurveyDto {
  @IsNotEmpty()
  @IsUUID()
  surveyId: string;

  @IsNotEmpty()
  @IsString()
  feature: string;

  @IsNotEmpty()
  @IsArray()
  responses: ResponseItemDto[];

  @IsOptional()
  @IsString()
  comment: string;

  @IsOptional()
  @IsUUID()
  refId: string;
}

export class DeleteDto {
  @IsNotEmpty()
  @IsUUID()
  id: string;
}

export class ActivateSurveyDto {
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @IsNotEmpty()
  @IsString()
  feature: string;
}

export class StatusDto {
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @IsNotEmpty()
  @IsBoolean()
  status: boolean;
}

export class UpdateStatusQuestionSurveyDto {
  @IsNotEmpty()
  @IsUUID()
  surveyId: string;

  @IsNotEmpty()
  @IsArray()
  questions: StatusDto[];
}

export class UpdateStatusAnswerQuestionDto {
  @IsNotEmpty()
  @IsUUID()
  questionId: string;

  @IsNotEmpty()
  @IsArray()
  answers: StatusDto[];
}

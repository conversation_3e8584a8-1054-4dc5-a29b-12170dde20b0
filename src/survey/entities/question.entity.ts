import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AnswerEntity } from './answer.entity';
import { SurveyEntity } from './survey.entity';

export enum QuestionStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}

@Entity('Questions')
export class QuestionEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column('uuid')
  surveyId: string;

  @Column()
  question: string;

  @Column()
  status: boolean;

  @Column()
  orderIndex: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @ManyToOne(() => SurveyEntity, (survey) => survey.questions)
  survey: SurveyEntity;

  @OneToMany((type) => AnswerEntity, (answer) => answer.question)
  answers: AnswerEntity[];
}

import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ResponseEntity } from './response.entity';

@Entity('ResponseItems')
export class ResponseItemEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column('uuid')
  responseId: string;

  @Column('uuid')
  questionId: string;

  @Column('uuid')
  answerId: string;

  @Column()
  questionValue: string;

  @Column()
  answerValue: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @ManyToOne(() => ResponseEntity, (response) => response.responseItems)
  response: ResponseEntity;
}

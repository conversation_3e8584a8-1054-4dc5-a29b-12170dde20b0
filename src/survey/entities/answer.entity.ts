import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { QuestionEntity } from './question.entity';

export enum AnswerStatus {
  ACTIVE = 1,
  INACTIVE = 0,
}

@Entity('Answers')
export class AnswerEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column('uuid')
  questionId: string;

  @Column()
  answer: string;

  @Column()
  status: boolean;

  @Column()
  orderIndex: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @ManyToOne(() => QuestionEntity, (question) => question.answers)
  question: QuestionEntity;
}

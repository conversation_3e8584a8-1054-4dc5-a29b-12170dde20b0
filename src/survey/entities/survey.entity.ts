import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { QuestionEntity } from './question.entity';

export enum SurveyStatus {
  ACTIVE = 1,
  DRAFT = 0,
}

@Entity('Surveys')
export class SurveyEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  feature: string;

  @Column()
  name: string;

  @Column()
  status: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @OneToMany(() => QuestionEntity, (question) => question.survey, { cascade: true })
  questions: QuestionEntity[];
}

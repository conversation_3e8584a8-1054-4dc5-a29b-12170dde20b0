import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ResponseItemEntity } from './responseItem.entity';
import { SurveyEntity } from './survey.entity';

@Entity('Responses')
export class ResponseEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column('uuid')
  userId: string;

  @Column('uuid')
  surveyId: string;

  @Column('uuid')
  refId: string;

  @Column()
  additionalComment: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @OneToOne((type) => SurveyEntity)
  survey: SurveyEntity;

  @OneToMany((type) => ResponseItemEntity, (responseItem) => responseItem.response)
  responseItems: ResponseItemEntity[];
}

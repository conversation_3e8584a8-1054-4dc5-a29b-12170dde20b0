import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import { AccessedClients } from 'src/client/clients.decorator';
import { DeService } from 'src/content/de.service';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { MfeService } from 'src/mfe/mfe.service';
import { CompleteFittingDTO, NewRoundDto } from 'src/mfe/mfe.type';
import { EventService } from './event.service';
import { DEPushNotificationDto, SwingIndexPushNotificationDto } from './event.type';

@Controller('events')
export class EventController {
  constructor(
    private readonly config: ConfigService,
    private readonly eventService: EventService,
    private deService: DeService,
    private mfeService: MfeService
  ) {
    this.config = config;
  }

  @Post('swing-index/notifications/push')
  @AccessedClients(CLIENTS.SWING_INDEX)
  @UseGuards(ClientGuard)
  async postPostScore(@Body() body: SwingIndexPushNotificationDto): Promise<any> {
    await this.eventService.pushSwingIndexNotification(body);
    return {
      success: true,
    };
  }

  @Post('decision-engine/notifications/push')
  @AccessedClients(CLIENTS.DE)
  @UseGuards(ClientGuard)
  async postDENotificationPush(@Body() body: DEPushNotificationDto): Promise<any> {
    await this.eventService.pushDENotification(body);
    return {
      success: true,
    };
  }

  @Post('mrp/start-round')
  @AccessedClients(CLIENTS.MRP)
  @UseGuards(ClientGuard)
  async triggerRoundStartedEvent(@Body() payload: NewRoundDto): Promise<any> {
    return await this.deService.triggerRoundStartedEvent(payload.cdmUID, payload.roundId, payload.courseId);
  }

  @Post('fitting/complete')
  @AccessedClients(CLIENTS.MFE)
  @UseGuards(ClientGuard)
  async postCompleteFitting(@Body() payload: CompleteFittingDTO): Promise<any> {
    await this.mfeService.postCompleteFitting(payload);
    return {
      success: true,
    };
  }
}

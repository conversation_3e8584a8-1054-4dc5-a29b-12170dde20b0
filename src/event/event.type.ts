import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON><PERSON>, IsString, Max<PERSON>eng<PERSON> } from 'class-validator';

export enum SWING_INDEX_CODE {
  ROADMAP_GENERATED = 'ROADMAP_GENERATED',
  RETURNED_SWING_SHOT_FAILED = 'RETURNED_SWING_SHOT_FAILED',
  RETURNED_SWING_SHOT_SKIPPED = 'RETURNED_SWING_SHOT_SKIPPED',
  RETURNED_SWING_SHOT_SKIPPED_LAST = 'RETURNED_SWING_SHOT_SKIPPED_LAST',
  RETURNED_SWING_SHOT_PASSED = 'RETURNED_SWING_SHOT_PASSED',
  RETURNED_SWING_SHOT_PASSED_ALL = 'RETURNED_SWING_SHOT_PASSED_ALL',
  SINGLE_SWING_SHOT_SCORED = 'SINGLE_SWING_SHOT_SCORED',
  SINGLE_SWING_SHOT_RETURNED_SCORED = 'SINGLE_SWING_SHOT_RETURNED_SCORED',
  LONG_PUTT_SCORED = 'LONG_PUTT_SCORED',
  SHORT_PUTT_SCORED = 'SHORT_PUTT_SCORED',
}
export class SwingIndexPushNotificationDto {
  @IsEmail()
  @IsNotEmpty()
  @MaxLength(255)
  email: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  message: string;

  @IsString()
  @IsNotEmpty()
  deepLink: string;

  @IsOptional()
  code: string;
}

export class DEPushNotificationDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  CDMID: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  message: string;

  @IsString()
  @IsNotEmpty()
  deepLink: string;
}

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { messaging } from 'firebase-admin';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { KlaviyoService, KlaviyoTrackEvents } from 'src/klaviyo/klaviyo.service';
import { LoggingService } from 'src/logging/logging.service';
import { NotificationService } from 'src/notification/notification.service';
import { ERROR_CODES } from 'src/utils/errors';
import { DE_NOTIFICATION, SWING_INDEX_NOTIFICATION } from './event.constants';
import { DEPushNotificationDto, SwingIndexPushNotificationDto } from './event.type';

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(
    private readonly config: ConfigService,
    private readonly klaviyoService: KlaviyoService,
    private firebaseMessaging: FirebaseMessagingService,
    private readonly loggingService: LoggingService,
    private readonly notificationService: NotificationService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {
    this.config = config;
  }

  async pushSwingIndexNotification(data: SwingIndexPushNotificationDto) {
    try {
      const user = await this.userRepo.findOne({ email: data.email });
      if (!user) {
        return;
      }
      const ctaLink = data.deepLink;
      const userNotificationId = await this.saveNotificationToMyTMDB(
        user.id,
        SWING_INDEX_NOTIFICATION.TITLE,
        data.message,
        ctaLink,
        SWING_INDEX_NOTIFICATION.SERVICE
      );
      const message: messaging.Message = {
        data: {
          deepLink: data.deepLink,
          userNotificationId,
        },
        token: user.fcmToken,
        notification: {
          title: 'TaylorMade',
          body: data.message,
        },
      };
      if (data?.code && KlaviyoTrackEvents[data.code] && KlaviyoTrackEvents[data.code] !== '') {
        await this.klaviyoService.track(data.email, KlaviyoTrackEvents[data.code]);
      }
      this.trackSingleLessonFeedback(data);
      await this.loggingService.save({
        event: `SWING_INDEX_PUSH_NOTIFICATION`,
        data,
        service: `SWING_INDEX`,
      });
      if (user.fcmToken) {
        await this.firebaseMessaging.send(message);
      }
      this.logger.log(`Pushed notification Swing Index to ${user.email}`);
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.CAN_NOT_PUSH_NOTIFICATION_FOR_THIS_USER,
        errorMessage: 'Can not push notification to this user!',
      });
    }
  }

  trackSingleLessonFeedback(data: any) {
    try {
      if (
        data?.code &&
        [
          'SWING_MAINT_SCORED',
          'SWING_MAINT_RETURNED_SCORED',
          // 'SINGLE_SWING_SHOT_SCORED',
          // 'SINGLE_SWING_SHOT_RETURNED_SCORED',
        ].includes(data.code)
      ) {
        this.klaviyoService.track(data.email, KlaviyoTrackEvents.SINGLE_LESSON_FEED_BACK);
      }
    } catch (error) {
      console.log(error);
    }
  }

  async pushDENotification(data: DEPushNotificationDto) {
    try {
      const user = await this.userRepo.findOne({ cdmUID: data.CDMID });
      if (!user || !user.fcmToken) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.CAN_NOT_PUSH_NOTIFICATION_FOR_THIS_USER,
          errorMessage: 'Can not push notification to this user!',
        });
      }
      const ctaLink = data.deepLink;
      const userNotificationId = await this.saveNotificationToMyTMDB(
        user.id,
        DE_NOTIFICATION.TITLE,
        data.message,
        ctaLink,
        DE_NOTIFICATION.SERVICE
      );
      const message: messaging.Message = {
        data: {
          deepLink: data.deepLink,
          userNotificationId,
        },
        token: user.fcmToken,
        notification: {
          title: 'TaylorMade',
          body: data.message,
        },
      };
      await this.loggingService.save({
        event: `DE_PUSH_NOTIFICATION`,
        data,
        service: `DE`,
      });
      await this.firebaseMessaging.send(message);
      this.logger.log(`Pushed notification DE to ${user.email}`);
    } catch (error) {
      console.log(error);
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.CAN_NOT_PUSH_NOTIFICATION_FOR_THIS_USER,
        errorMessage: 'Can not push notification to this user!',
      });
    }
  }

  private async saveNotificationToMyTMDB(
    userId: string,
    title: string,
    message: string,
    ctaLink: string,
    service: string
  ) {
    const variables = {
      title,
      message,
      service,
    };
    this.logger.log(`Pushing notification ${process} to MyTM BE`);
    const save = await this.notificationService.saveNotificationFromOtherService(userId, ctaLink, variables);
    if (!save) {
      this.logger.error(`Fail to save notification ${process} to MyTM BE`);
      return null;
    } else {
      return save.id;
    }
  }
}

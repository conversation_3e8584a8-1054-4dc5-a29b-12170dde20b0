import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import querystring from 'querystring';
import { CdmService } from '../cdm/cdm.service';
import { AuthGuard } from '../guards/auth.guard';
import { ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { BagService } from './bag.service';
import { WITBDto, WITBUpdateDto } from './bag.type';

@Controller()
@UseGuards(ClientGuard)
export class BagController {
  constructor(private bagService: BagService, private cdmService: CdmService, private readonly config: ConfigService) {
    this.config = config;
  }

  @Get('witb')
  @UseGuards(AuthGuard)
  async getWITB(@Req() request: BaseRequest, @Query() query): Promise<any> {
    return this.bagService.getWITB(
      request.user.email,
      request.user.regionId || this.config.get('app.defaultRegionId'),
      query
    );
  }

  @Get('witb/brands')
  @UseGuards(AuthGuard)
  async getBrands(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getBrands(take, skip);
  }

  @Post('witb/create')
  @UseGuards(AuthGuard)
  async postCreateWITB(@Req() request: BaseRequest, @Body() data: WITBDto): Promise<any> {
    return await this.cdmService.createWITB(request.user, data);
  }

  @Post('witb/update')
  @UseGuards(AuthGuard)
  async postUpdateWITB(@Req() request: BaseRequest, @Body() data: WITBUpdateDto): Promise<any> {
    return await this.cdmService.updateWITB(request.user.cdmUID, request.user.email, data);
  }

  @Get('witb/brands-by-club-category/:categoryId')
  async getBrandsByClubCategory(@Param() params): Promise<any> {
    return await this.cdmService.getBrandsByClubCategory(params.categoryId);
  }

  @Get('witb/models')
  async getModels(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getModels(take, skip);
  }

  @Get('witb/club/category-types/query')
  @UseGuards(AuthGuard)
  async getClubCategoriesTypesQuery(@Query() query): Promise<any> {
    return await this.cdmService.getClubCategoriesTypesQuery(querystring.stringify(query));
  }

  @Get('witb/club/category-types')
  @UseGuards(AuthGuard)
  async getClubCategoriesTypes(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubCategoriesTypes(take, skip);
  }

  @Get('witb/club/category-types-by-category/:categoryId')
  @UseGuards(AuthGuard)
  async getClubCategoriesTypesByCategory(@Param() params): Promise<any> {
    return await this.cdmService.getClubCategoriesTypesByCategory(params.categoryId);
  }

  @Get('witb/club/categories')
  @UseGuards(AuthGuard)
  async getClubCategories(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubCategories(take, skip);
  }

  @Get('witb/models-by-brand/:brandId')
  async getModelsByBrand(@Param() params): Promise<any> {
    return await this.cdmService.getModelsByBrand(params.brandId);
  }

  @Get('witb/club/hands')
  @UseGuards(AuthGuard)
  async getClubHands(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubHands(take, skip);
  }

  @Get('witb/club/lofts')
  @UseGuards(AuthGuard)
  async getClubLofts(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubLofts(take, skip);
  }

  @Get('witb/club/lofts-by-category/:categoryId')
  @UseGuards(AuthGuard)
  async getClubLoftsByCategory(@Param() params): Promise<any> {
    return await this.cdmService.getClubLoftsByCategory(params.categoryId);
  }

  @Get('witb/club/shaft-flex')
  @UseGuards(AuthGuard)
  async getClubShaftFlex(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubShaftFlex(take, skip);
  }

  @Get('witb/club/shaft-flex-by-category/:categoryId')
  @UseGuards(AuthGuard)
  async getClubShaftFlexByCategory(@Param() params): Promise<any> {
    return await this.cdmService.getClubShaftFlexByCategory(params.categoryId);
  }

  @Get('witb/club/loft-adjustment')
  @UseGuards(AuthGuard)
  async getClubLoftAdjustment(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubLoftAdjustment(take, skip);
  }

  @Get('witb/club/shaft-length')
  @UseGuards(AuthGuard)
  async getClubShaftLength(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubShaftLength(take, skip);
  }

  @Get('witb/club/shaft-length-by-club-category/:clubCategoryId')
  @UseGuards(AuthGuard)
  async getClubShaftLengthByClubCategoryId(@Query('take') take, @Param() params, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubShaftLengthByClubCategoryId(params.clubCategoryId, take, skip);
  }

  @Get('witb/club/lies')
  @UseGuards(AuthGuard)
  async getClubLies(@Query('take') take, @Query('skip') skip): Promise<any> {
    return await this.cdmService.getClubLies(take, skip);
  }
}

import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class WITBDto {
  @IsString()
  @IsOptional()
  categoryId: string;

  @IsString()
  @IsOptional()
  categoryTypeId: string;

  @IsString()
  @IsOptional()
  brandId: string;

  @IsString()
  @IsOptional()
  modelId: string;

  @IsOptional()
  @IsBoolean()
  inBag: boolean;
}

export class WITBUpdateDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsOptional()
  categoryId: string;

  @IsString()
  @IsOptional()
  categoryTypeId: string;

  @IsString()
  @IsOptional()
  brandId: string;

  @IsString()
  @IsOptional()
  modelId: string;

  @IsOptional()
  inBag: boolean;

  @IsString()
  @IsOptional()
  clubLoftId: string;

  @IsString()
  @IsOptional()
  clubShaftFlexId: string;

  @IsString()
  @IsOptional()
  clubShaftLengthId: string;

  @IsString()
  @IsOptional()
  faceLieAdjustmentId: string;

  @IsBoolean()
  @IsOptional()
  deleted: boolean;

  @IsBoolean()
  @IsOptional()
  disable: boolean;

  @IsString()
  @IsOptional()
  serialNumber: string;

  @IsString()
  @IsOptional()
  purchaseDate: string;

  @IsString()
  @IsOptional()
  buyFrom: string;
}

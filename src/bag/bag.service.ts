import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { isArccosService } from 'src/utils/service.preference';
import { ArccosService } from '../arccos/arccos.service';
import { UserEntity } from '../auth/entities/user.entity';
import { CdmService } from '../cdm/cdm.service';

@Injectable()
export class BagService {
  constructor(
    private readonly config: ConfigService,
    private readonly arccosService: ArccosService,
    private readonly cdmService: CdmService,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {
    this.config = config;
  }

  async getWITB(email: string, regionId?: string, query?: any) {
    if (isArccosService(query)) {
      const rounds = await this.arccosService.getArccosClubs(email);
      if (rounds.userStatus.isMember) {
        return (
          rounds?.clubs?.map((club) => ({
            inBag: club?.isInBag,
            disable: false,
            manufacturer: {
              name: club?.brand,
            },
            modelName: {
              name: club?.model,
            },
            clubFamily: {
              name: this.transformClubCategory(club?.club),
            },
            clubType: {
              type: this.transformClubType(club?.club),
            },
            shaftLength: {
              value: null,
            },
            shaftFlex: {
              value: null,
            },
            faceLieAdjustment: {
              value: null,
            },
            faceLoftAdjustment: {
              value: null,
            },
            loft: {
              value: null,
            },
            serialNumber: null,
            purchaseDate: null,
            buyFrom: null,
            imageThumb: null,
            imageSmall: null,
            imageReg: null,
            imageLarge: null,
          })) || []
        );
      }
      return [];
    }
    return await this.cdmService.getWITB(email);
  }

  transformClubCategory(club: string) {
    if (club === 'Dr') {
      return 'Driver';
    }
    const firstChar = club.substring(0, 1);
    const secondChar = club.substring(1, 2);
    if (this.isNumber(firstChar) && secondChar === 'w') {
      return 'Fairway';
    }
    if ((this.isNumber(firstChar) && secondChar === 'i') || ['X'].includes(firstChar)) {
      return 'Irons';
    }
    if (this.isNumber(firstChar) && secondChar === 'h') {
      return 'Rescue';
    }
    if (
      !this.isNumber(firstChar) &&
      ['P'].includes(firstChar) &&
      (secondChar === 'u' || secondChar === '' || !secondChar)
    ) {
      return 'Putter';
    }
    const wedgeList = ['P', 'G', 'S', 'L'];
    if (
      (!this.isNumber(firstChar) &&
        wedgeList.includes(firstChar) &&
        (secondChar === 'w' || secondChar === '' || !secondChar)) ||
      (this.isNumber(firstChar) && club.includes('°'))
    ) {
      return 'Wedge';
    }
    return 'Others';
  }

  transformClubType(club: string) {
    const firstChar = club.substring(0, 1);
    const secondChar = club.substring(1, 2);
    if ((this.isNumber(firstChar) && secondChar === 'i') || ['X'].includes(firstChar)) {
      return club.toUpperCase().replace('I', '');
    }
    return club.toUpperCase();
  }

  isNumber(nTest: any) {
    const regexTextNumber = /^\d+$/;
    const regex = new RegExp(regexTextNumber);

    return regex.test(nTest);
  }
}

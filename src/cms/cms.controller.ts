import { Controller, Get, Param, Query, Req, UseGuards } from '@nestjs/common';
import querystring from 'querystring';
import { BaseRequest } from 'src/types/core';
import { AuthGuard } from '../guards/auth.guard';
import { ClientGuard } from '../guards/client.guard';
import { CmsService } from './cms.service';

@Controller('cms')
@UseGuards(ClientGuard)
export class CmsController {
  constructor(private cmsService: CmsService) {}

  @Get('tour-players')
  @UseGuards(AuthGuard)
  async getTourPlayers(@Query('lang') lang): Promise<any> {
    return await this.cmsService.getTourPlayers(lang);
  }

  @Get('coach-tag/:tag')
  @UseGuards(AuthGuard)
  async getCoachTag(@Param('tag') tag: string, @Query('count') count: number): Promise<any> {
    return await this.cmsService.getCoachTag(tag, count);
  }

  @Get('plans/features')
  @UseGuards(AuthGuard)
  async getPlanFeatures(@Query('lang') lang, @Query('newVersion') newVersion): Promise<any> {
    return await this.cmsService.getPlanFeatures(lang, newVersion);
  }
  @Get('single/:contentType/:contentId')
  @UseGuards(AuthGuard)
  async getSingleContent(
    @Param('contentType') contentType,
    @Param('contentId') contentId,
    @Req() request: BaseRequest
  ): Promise<any> {
    return await this.cmsService.getCmsContent(contentId, contentType, request?.country);
  }
  @Get('list')
  @UseGuards(AuthGuard)
  async getCmsListContent(@Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsListContent(querystring.stringify(query), request?.country);
  }

  @Get('list-video')
  @UseGuards(AuthGuard)
  async getCmsListVideo(@Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsListVideo(querystring.stringify(query), request?.country);
  }

  @Get('drill-video')
  @UseGuards(AuthGuard)
  async getCmsDrillVideo(@Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsDrillVideo(querystring.stringify(query), request?.country);
  }

  @Get('clubhouse')
  @UseGuards(AuthGuard)
  async getCmsListClubHouse(@Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsListClubHouse(querystring.stringify(query), request?.country);
  }

  @Get('tags')
  @UseGuards(AuthGuard)
  async getCmsTags(@Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsTags(querystring.stringify(query), request?.country);
  }

  @Get('articles/:contentType')
  @UseGuards(AuthGuard)
  async getListArticles(@Param('contentType') contentType, @Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getListArticles(contentType, querystring.stringify(query), request?.country);
  }

  @Get('collections')
  @UseGuards(AuthGuard)
  async getCmsCollections(@Query() query, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsCollections(querystring.stringify(query), request?.country);
  }

  @Get('collections/:collectionId')
  @UseGuards(AuthGuard)
  async getCmsCollectionDetail(@Param('collectionId') collectionId, @Req() request: BaseRequest): Promise<any> {
    return await this.cmsService.getCmsCollectionDetail(collectionId, request?.country);
  }
}

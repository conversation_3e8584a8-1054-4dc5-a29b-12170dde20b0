import { CACHE_MANAGER, Inject, Injectable } from '@nestjs/common';
import axios from 'axios';
import { Cache } from 'cache-manager';
import camelcaseKeys from 'camelcase-keys';
import { lowerCase } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { isCanadaCountry, isUSCountry } from '../utils/transform';
import SubscriptionSetting from './config/subscription_setting.json';
import SubscriptionSettingNew2 from './config/subscription_setting_new2.json';
import SubscriptionSettingNew from './config/subscription_setting_new.json';
import SubscriptionSettingProd from './config/subscription_setting_prod.json';
import SubscriptionSettingProdNew2 from './config/subscription_setting_prod_new2.json';
import SubscriptionSettingProdNew from './config/subscription_setting_prod_new.json';

const DEFAULT_LANG = 'us';
const LANG_CAN = 'ca';
const TIME_CACHE_TTL = 7200; // 2 hours

@Injectable()
export class CmsService {
  constructor(private readonly config: ConfigService, @Inject(CACHE_MANAGER) private cacheManager: Cache) {
    this.config = config;
  }

  getRequestHeaderConfigs() {
    return {
      headers: { Authorization: `Basic ${this.config.get('app.cmsToken')}` },
    };
  }

  async getTourPlayers(lang: string) {
    let country = DEFAULT_LANG;
    if (lang && isCanadaCountry(lang)) {
      country = LANG_CAN;
    }
    const response = await axios.get(
      `${this.config.get('app.cmsEndpoint')}/${country}/_api/v1/tourplayers`,
      this.getRequestHeaderConfigs()
    );
    if (response.data) {
      const data = response.data.data.map((item) => {
        delete item.jsonUrl;
        return item;
      });
      return camelcaseKeys(data, { deep: true });
    }
    return [];
  }

  async getCoachTag(tag: string, count = 30) {
    try {
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/_api/v2/mytm/content?mytmInstructor=${tag}&count=${count}`,
        this.getRequestHeaderConfigs()
      );
      if (response.data) {
        return response.data;
      }
      return {};
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  async isValidContent(contentId: number, contentType: string) {
    try {
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${DEFAULT_LANG}/_api/v1/${contentType}/${contentId}`,
        this.getRequestHeaderConfigs()
      );
      return response.status === 200 && response.data?.data?.id === contentId;
    } catch (e) {
      console.log(e);
      return false;
    }
  }

  async getPlanFeatures(lang: string, newVersion: any) {
    if (this.config.get('app.stage') === 'production') {
      if (+newVersion === 2) {
        return SubscriptionSettingProdNew2;
      }
      return +newVersion === 1 ? SubscriptionSettingProdNew : SubscriptionSettingProd;
    }
    if (+newVersion === 2) {
      return SubscriptionSettingNew2;
    }
    return +newVersion === 1 ? SubscriptionSettingNew : SubscriptionSetting;
  }

  async getListArticles(contentType: string, query?: string, lang?: string) {
    let country = DEFAULT_LANG;
    if (lang && isCanadaCountry(lang)) {
      country = LANG_CAN;
    }

    const cacheKey = `DRILL:CMS:ARTICLES_${contentType}_${query}_${country}`;
    const cachedData = await this.cacheManager.get(cacheKey);

    if (cachedData) {
      return cachedData;
    }

    try {
      let version = 'v1';
      if (['videos', 'clubhousearticles'].includes(contentType?.toLocaleLowerCase())) {
        version = 'v2';
      }
      const url = `${this.config.get('app.cmsEndpoint')}/${country}/_api/${version}/${contentType}?${query}`;
      console.log(`URL getListArticles: ${url}`);
      const response = await axios.get(url, this.getRequestHeaderConfigs());
      if (response.status === 200) {
        const data = response.data;
        await this.cacheManager.set(cacheKey, data, { ttl: TIME_CACHE_TTL });
        return data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }
  async getCmsContent(contentId: number, contentType: string, lang?: string) {
    try {
      let country = DEFAULT_LANG;
      if (lang && isCanadaCountry(lang)) {
        country = LANG_CAN;
      }
      let version = 'v1';
      if (['videos', 'clubhousearticles'].includes(contentType?.toLocaleLowerCase())) {
        version = 'v2';
      }
      console.log({ version });

      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/${version}/${contentType}/${contentId}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getCmsListContent(query: string, lang?: string) {
    try {
      let country = DEFAULT_LANG;
      if (lang && isCanadaCountry(lang)) {
        country = LANG_CAN;
      }
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v2/mytm/content?${query}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getCmsListVideo(query: string, lang?: string) {
    try {
      let country = DEFAULT_LANG;
      if (lang && isCanadaCountry(lang)) {
        country = LANG_CAN;
      }
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v2/clubhouseVideos?${query}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async getCmsDrillVideo(query: string, lang?: string) {
    try {
      let country = DEFAULT_LANG;
      if (lang && isCanadaCountry(lang)) {
        country = LANG_CAN;
      }
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v2/mytm/videos?${query}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200 && response.data && response?.data?.data.length) {
        let data = response?.data?.data;
        const meta = response?.data?.meta;
        data = data.map(this.buildVideoWithUrl);
        return {
          data,
          meta,
        };
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  buildVideoWithUrl(video) {
    const videoWithUrl = video;
    if (video.video_type === 'youtube' && video.video_id) {
      videoWithUrl.video_url = `https://www.youtube.com/watch?v=${video.video_id}`;
    }
    return videoWithUrl;
  }

  async getCmsListClubHouse(query: string, lang?: string) {
    let country = DEFAULT_LANG;
    if (lang && isCanadaCountry(lang)) {
      country = LANG_CAN;
    }

    const cacheKey = `DRILL:CMS:CLUBHOUSE_${query}_${country}`;
    const cachedData = await this.cacheManager.get(cacheKey);

    if (cachedData) {
      return cachedData;
    }

    try {
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v2/clubhouse?${query}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        const data = response.data;
        await this.cacheManager.set(cacheKey, data, { ttl: TIME_CACHE_TTL });
        return data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }
  async getCmsTags(query: string, lang?: string) {
    let country = DEFAULT_LANG;
    if (lang && isCanadaCountry(lang)) {
      country = LANG_CAN;
    }

    const cacheKey = `DRILL:CMS:TAG_${query}_${country}`;
    const cachedData = await this.cacheManager.get(cacheKey);

    if (cachedData) {
      return cachedData;
    }

    try {
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v1/tags?${query}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        const data = response.data;
        await this.cacheManager.set(cacheKey, data, { ttl: TIME_CACHE_TTL });

        return data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }
  async getCmsCollections(query: string, lang?: string) {
    const cacheKey = `DRILL:CMS:COLLECTIONS`;
    const cachedData = await this.cacheManager.get(cacheKey);

    if (cachedData) {
      return cachedData;
    }

    try {
      let country = DEFAULT_LANG;
      if (lang && isCanadaCountry(lang)) {
        country = LANG_CAN;
      }
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v2/collections?${query}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        const data = response.data;

        await this.cacheManager.set(cacheKey, data, { ttl: TIME_CACHE_TTL });

        return data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }
  async getCmsCollectionDetail(collectionId: string, lang?: string) {
    try {
      let country = DEFAULT_LANG;
      if (lang && isCanadaCountry(lang)) {
        country = LANG_CAN;
      }
      const response = await axios.get(
        `${this.config.get('app.cmsEndpoint')}/${country}/_api/v2/collections/${collectionId}`,
        this.getRequestHeaderConfigs()
      );
      if (response.status === 200) {
        return response.data;
      }
      return null;
    } catch (e) {
      console.log(e);
      return null;
    }
  }
}

import { Body, Controller, Get, Post } from '@nestjs/common';
import _ from 'lodash';
import { OtpConfig } from 'src/auth/entities/user-otp.entity';
import { MailService } from './mail.service';

@Controller('mail')
export class MailController {
  constructor(private mailService: MailService) {}

  @Get('testEmail')
  async testEmail() {
    return await this.mailService.sendConfirmationEmail('<EMAIL>', '123123', 'USA');
  }
  @Post('testSMS')
  async testOtpSMS(@Body() payload: any) {
    const otpCode = _.padStart(_.random(0, Math.pow(10, OtpConfig.LENGTH) - 1).toString(), OtpConfig.LENGTH, '0');
    return await this.mailService.sendConfirmationEmail(payload.email, otpCode, 'USA', payload.phoneNumber);
  }
}

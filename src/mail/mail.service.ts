import { InjectQueue } from '@nestjs/bull';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';

@Injectable()
export class MailService {
  constructor(
    @InjectQueue('mail-otp')
    private mailQueue: Queue
  ) {}

  async sendConfirmationEmail(
    userEmail: string,
    code: string,
    country?: string,
    phoneNumber?: string
  ): Promise<boolean> {
    try {
      country = country || 'USA';
      await this.mailQueue.add('confirmation', {
        userEmail,
        code,
        phoneNumber,
        country,
      });
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  }
}

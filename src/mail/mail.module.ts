import { Module, forwardRef } from '@nestjs/common';
import { SharedModule } from '../shared/shared.module';
import { SYSTEM_TAG } from '../utils/constants';
import { MailController } from './mail.controller';
import { MailProcessor } from './mail.processor';
import { MailService } from './mail.service';

let processors = [];

if (process.env.TAG !== SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, MailProcessor];
}

@Module({
  imports: [forwardRef(() => SharedModule)],
  controllers: [MailController],
  providers: [...processors, MailService],
  exports: [MailService],
})
export class MailModule {}

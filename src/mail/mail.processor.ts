import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { Job } from 'bull';
import { ApiKeySession, EventCreateQueryV2, EventsApi } from 'klaviyo-api';
import { ConfigService } from 'nestjs-config';
import { isProduction } from 'src/utils/cron';
import { isCanOrUsaCountry, isCanadaCountry } from '../utils/transform';

@Processor('mail-otp')
export class MailProcessor {
  private readonly logger = new Logger(MailProcessor.name);
  constructor(private readonly mailerService: MailerService, private readonly config: ConfigService) {}

  @Process('confirmation')
  async sendWelcomeEmail(
    job: Job<{ userEmail: string; code: string; phoneNumber: string; country: string }>
  ): Promise<any> {
    try {
      const country = job.data?.country || null;
      if (!isCanOrUsaCountry(country)) {
        return;
      }
      if (job.data.phoneNumber) {
        this.logger.log(`Sending SMS OTP confirmation email to '${job.data.userEmail}'`);
        return await this.sendOtpSMSByTwilio(job.data.code, job.data.userEmail, job.data.phoneNumber, country);
      }
      if (this.config.get('app.phoneSmsOtp') && !isProduction) {
        await this.sendOtpSMSByTwilio(job.data.code, job.data.userEmail, this.config.get('app.phoneSmsOtp'), country);
      }
      this.logger.log(`Sending confirmation email to '${job.data.userEmail}'`);
      return await this.sendOtpByKlaviyo(job.data.code, job.data.userEmail, country);
    } catch (error) {
      this.logger.error(`Failed to send confirmation email to '${job.data.userEmail}'`, error.stack);
      throw error;
    }
  }

  async sendOtpByKlaviyo(otp: string, email: string, country: string) {
    const configKeyKlaviyo = isCanadaCountry(country)
      ? this.config.get('app.klaviyoCAPrivateToken')
      : this.config.get('app.klaviyoPrivateToken');
    const session = new ApiKeySession(configKeyKlaviyo);
    const eventsApi = new EventsApi(session);
    const properties = {
      otp,
    };
    const customerProperties = {
      mytm_subscriber_id: 'False',
    };
    const body: EventCreateQueryV2 = {
      data: {
        type: 'event',
        attributes: {
          metric: { data: { type: 'metric', attributes: { name: this.config.get('app.klaviyoOtpEvent') } } },
          properties: properties,
          profile: {
            data: {
              type: 'profile',
              attributes: {
                email: email,
                ...customerProperties,
              },
            },
          },
        },
      },
    };

    await eventsApi.createEvent(body);

    return true;
  }

  async sendOtpSMSByTwilio(otp: string, email: string, phoneNumber: string, country: string) {
    const configKeyKlaviyo = isCanadaCountry(country)
      ? this.config.get('app.klaviyoCAPrivateToken')
      : this.config.get('app.klaviyoPrivateToken');
    const accountSid = this.config.get('app.twilioAccountSID');
    const authToken = this.config.get('app.twilioToken');
    const messagingServiceSid = this.config.get('app.twilioMessagigServiceId');

    const clientTwilio = require('twilio')(accountSid, authToken);
    clientTwilio.messages
      .create({
        body: `Your TaylorMade code is ${otp}.`,
        messagingServiceSid,
        to: phoneNumber,
      })
      .then((message) => console.log(`SMS ID: ${message.sid}`))
      .catch((e) => console.log(`ERROR SendSMS: ${e.message}`))
      .done();
    const session = new ApiKeySession(configKeyKlaviyo);
    const eventsApi = new EventsApi(session);
    const properties = {
      otp,
    };
    const customerProperties = {
      mytm_subscriber_id: 'False',
    };
    const body: EventCreateQueryV2 = {
      data: {
        type: 'event',
        attributes: {
          properties: properties,
          metric: { data: { type: 'metric', attributes: { name: this.config.get('app.klaviyoOtpSMSEvent') } } },
          profile: {
            data: {
              type: 'profile',
              attributes: {
                email: email,
                ...customerProperties,
              },
            },
          },
        },
      },
    };

    await eventsApi.createEvent(body);
    return true;
  }
}

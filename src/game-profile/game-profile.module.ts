import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiVersionsModule } from 'src/api-versions/api-version.module';
import { SharedModule } from 'src/shared/shared.module';
import { GameProfileAnswer } from './entities/game-profile-answer.entity';
import { GameProfileQuestion } from './entities/game-profile-question.entity';
import { GameProfileUserAnswer } from './entities/game-profile-user-answer.entity';
import { GameProfile } from './entities/game-profile.entity';
import { GameProfileQuestionController } from './game-profile-question.controller';
import { GameProfileQuestionService } from './game-profile-question.service';
import { GameProfileUserAnswerController } from './game-profile-user-answer.controller';
import { GameProfileUserAnswerService } from './game-profile-user-answer.service';
import { GameProfileController } from './game-profile.controller';
import { GameProfileCronService } from './game-profile.cron.service';
import { GameProfileService } from './game-profile.service';

@Module({
  imports: [
    SharedModule,
    ApiVersionsModule,
    TypeOrmModule.forFeature([GameProfile, GameProfileQuestion, GameProfileAnswer, GameProfileUserAnswer]),
  ],
  controllers: [GameProfileController, GameProfileQuestionController, GameProfileUserAnswerController],
  providers: [GameProfileService, GameProfileCronService, GameProfileQuestionService, GameProfileUserAnswerService],
  exports: [GameProfileService, GameProfileQuestionService, GameProfileUserAnswerService],
})
export class GameProfileModule {}

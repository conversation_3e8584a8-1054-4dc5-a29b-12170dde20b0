import { Body, Controller, Delete, Get, Param, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { CreateGameProfileQuestionDto } from './dto/create-game-profile-question.dto';
import { SortGameProfileQuestionDto } from './dto/sort-game-profile-question.dto';
import { UpdateGameProfileQuestionDto } from './dto/update-game-profile-question.dto';
import { UpdateGameProfileQuestionStatusDto } from './dto/update-question-status.dto';
import { GameProfileQuestionService } from './game-profile-question.service';

@Controller('game-profile-questions')
export class GameProfileQuestionController {
  constructor(private readonly gameProfileQuestionService: GameProfileQuestionService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  createQuestion(@Body() createQuestionDto: CreateGameProfileQuestionDto, @Req() req: BaseRequest) {
    return this.gameProfileQuestionService.createQuestion(createQuestionDto, req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('admin')
  getAllQuestionsForAdmin() {
    return this.gameProfileQuestionService.getAllQuestionsForAdmin();
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get()
  getQuestions(@Req() req: BaseRequest) {
    return this.gameProfileQuestionService.getQuestions(req.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('user/with-answers')
  getQuestionsWithUserAnswers(@Req() req: BaseRequest) {
    return this.gameProfileQuestionService.getQuestionsWithUserAnswers(req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('summary/:id')
  getQuestionSummary(@Param('id') id: string) {
    return this.gameProfileQuestionService.getQuestionSummary(id);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get(':id')
  getQuestionById(@Param('id') id: string, @Req() req: BaseRequest) {
    return this.gameProfileQuestionService.getQuestionById(id, req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Patch(':id')
  updateQuestion(
    @Param('id') id: string,
    @Body() updateQuestionDto: UpdateGameProfileQuestionDto,
    @Req() req: BaseRequest
  ) {
    return this.gameProfileQuestionService.updateQuestion(id, updateQuestionDto, req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post(':id/status')
  updateQuestionStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateGameProfileQuestionStatusDto,
    @Req() req: BaseRequest
  ) {
    return this.gameProfileQuestionService.updateQuestionStatus(id, updateStatusDto, req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  sortQuestions(@Body() sortDto: SortGameProfileQuestionDto, @Req() req: BaseRequest) {
    return this.gameProfileQuestionService.sortQuestions(sortDto, req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  deleteQuestion(@Param('id') id: string, @Req() req: BaseRequest) {
    return this.gameProfileQuestionService.deleteQuestion(id, req.user?.uid);
  }
}

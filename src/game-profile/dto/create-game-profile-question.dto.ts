import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import {
  GameProfileAnswerType,
  GameProfileQuestionCategory,
  GameProfileQuestionType,
} from '../entities/game-profile-question.entity';

export class CreateGameProfileAnswerDto {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  subTitle?: string;

  @IsOptional()
  @IsString()
  imageLink?: string;

  @IsOptional()
  @IsString()
  options?: string;
}

export class CreateGameProfileQuestionDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEnum(GameProfileQuestionCategory)
  category: GameProfileQuestionCategory;

  @IsOptional()
  @IsString()
  subCategory?: string;

  @IsNotEmpty()
  @IsString()
  sectionName: string;

  @IsNotEmpty()
  @IsEnum(GameProfileQuestionType)
  questionType: GameProfileQuestionType;

  @IsNotEmpty()
  @IsEnum(GameProfileAnswerType)
  answerType: GameProfileAnswerType;

  @IsOptional()
  options?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateGameProfileAnswerDto)
  answers: CreateGameProfileAnswerDto[];

  @IsOptional()
  disabled?: boolean;
}

import { <PERSON><PERSON><PERSON>y, IsBoolean, IsNotEmpty, IsO<PERSON>al, IsUUID, ValidateIf } from 'class-validator';

export class CreateGameProfileUserAnswerDto {
  @IsNotEmpty()
  @IsUUID()
  gameProfileQuestionId: string;

  // Support both single answer (string) and multiple answers (string[])
  @ValidateIf((o) => !Array.isArray(o.gameProfileAnswerId) && !o.hasNoAnswer)
  @IsNotEmpty()
  @IsUUID()
  gameProfileAnswerId?: string;

  @ValidateIf((o) => Array.isArray(o.gameProfileAnswerId) && !o.hasNoAnswer)
  @IsArray()
  @IsUUID('4', { each: true })
  gameProfileAnswerIds?: string[];

  @IsOptional()
  @IsBoolean()
  hasNoAnswer?: boolean;

  userId?: string; // Will be set from request
}

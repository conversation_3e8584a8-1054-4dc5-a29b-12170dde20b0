import { Is<PERSON>rray, IsNotEmpty, IsUUID, ValidateIf } from 'class-validator';

export class CreateGameProfileUserAnswerDto {
  @IsNotEmpty()
  @IsUUID()
  gameProfileQuestionId: string;

  // Support both single answer (string) and multiple answers (string[])
  @ValidateIf((o) => !Array.isArray(o.gameProfileAnswerId))
  @IsNotEmpty()
  @IsUUID()
  gameProfileAnswerId?: string;

  @ValidateIf((o) => Array.isArray(o.gameProfileAnswerId))
  @IsArray()
  @IsUUID('4', { each: true })
  gameProfileAnswerIds?: string[];

  userId?: string; // Will be set from request
}

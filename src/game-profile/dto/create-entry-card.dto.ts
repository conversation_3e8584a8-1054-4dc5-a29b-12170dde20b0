import { IsDateString, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateEntryCardDto {
  @IsNotEmpty()
  @IsString()
  header: string;

  @IsOptional()
  @IsString()
  subHeader?: string;

  @IsNotEmpty()
  @IsString()
  ctaButton: string;

  @IsOptional()
  @IsString()
  imageLink?: string;

  @IsOptional()
  @IsString()
  backgroundColor?: string;

  @IsOptional()
  @IsDateString()
  startTime?: Date;

  @IsOptional()
  @IsDateString()
  endTime?: Date;

  @IsOptional()
  @IsString()
  annexActionUsaId?: string;

  @IsOptional()
  @IsString()
  annexActionCanId?: string;

  @IsOptional()
  disabled?: boolean;

  @IsOptional()
  options?: string;
}

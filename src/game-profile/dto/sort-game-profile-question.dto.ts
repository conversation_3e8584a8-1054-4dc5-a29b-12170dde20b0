import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsUUID, ValidateNested } from 'class-validator';

export class SortGameProfileQuestionItemDto {
  @IsNotEmpty()
  @IsUUID()
  id: string;

  @IsNotEmpty()
  sortOrder: number;
}

export class SortGameProfileQuestionDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SortGameProfileQuestionItemDto)
  questions: SortGameProfileQuestionItemDto[];
}

import { Expose, Type } from 'class-transformer';

export class GameProfileAnswerResponseDto {
  @Expose()
  id: string;

  @Expose()
  title: string;

  @Expose()
  subTitle: string;

  @Expose()
  imageLink: string;

  @Expose()
  options: string;

  @Expose()
  createdAt: Date;
}

export class GameProfileQuestionResponseDto {
  @Expose()
  id: string;

  @Expose()
  name: string;

  @Expose()
  category: string;

  @Expose()
  subCategory: string;

  @Expose()
  sectionName: string;

  @Expose()
  questionType: string;

  @Expose()
  answerType: string;

  @Expose()
  disabled: boolean;

  @Expose()
  options: string;

  @Expose()
  @Type(() => GameProfileAnswerResponseDto)
  answers: GameProfileAnswerResponseDto[];

  @Expose()
  createdAt: Date;
}

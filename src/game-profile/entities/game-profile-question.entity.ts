import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { GameProfileAnswer } from './game-profile-answer.entity';

export enum GameProfileQuestionCategory {
  MY_GAME = 'MY_GAME',
  MY_EQUIPMENT = 'MY_EQUIPMENT',
  MY_TEAMS = 'MY_TEAMS',
}

export enum GameProfileQuestionType {
  SINGLE = 'SINGLE',
  MULTIPLE = 'MULTIPLE',
}

export enum GameProfileAnswerType {
  SINGLE_ONE_COLUMN = 'SINGLE_ONE_COLUMN',
  SINGLE_TWO_COLUMNS = 'SINGLE_TWO_COLUMNS',
  DROPDOWN_WITH_SEARCH = 'DROPDOWN_WITH_SEARCH',
  SEARCH = 'SEARCH',
  IMAGE_TILE = 'IMAGE_TILE',
}

@Entity('GameProfileQuestions')
export class GameProfileQuestion {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  category: string;

  @Column({ nullable: true })
  subCategory: string;

  @Column({ nullable: true })
  sectionName: string;

  @Column({ nullable: true })
  questionType: string;

  @Column({ nullable: true })
  answerType: string;

  @Column({ default: 1 })
  sortOrder: number;

  @Column({ type: 'bit', default: true })
  disabled: boolean;

  @Column({ type: 'text', nullable: true })
  options: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  updatedAt: Date;

  @DeleteDateColumn({ nullable: true })
  deletedAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;

  @Column({ nullable: true })
  deletedBy: string;

  @OneToMany(() => GameProfileAnswer, (answer) => answer.question, {
    cascade: true,
  })
  answers?: GameProfileAnswer[];
}

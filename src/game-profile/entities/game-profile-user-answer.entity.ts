import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { GameProfileAnswer } from './game-profile-answer.entity';
import { GameProfileQuestion } from './game-profile-question.entity';

@Entity('GameProfileUserAnswers')
export class GameProfileUserAnswer {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  gameProfileQuestionId: string;

  @Column()
  gameProfileAnswerId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  updatedAt: Date;

  @DeleteDateColumn({ nullable: true })
  deletedAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;

  @Column({ nullable: true })
  deletedBy: string;

  @ManyToOne(() => GameProfileQuestion)
  @JoinColumn({ name: 'gameProfileQuestionId' })
  question?: GameProfileQuestion;

  @ManyToOne(() => GameProfileAnswer)
  @JoinColumn({ name: 'gameProfileAnswerId' })
  answer?: GameProfileAnswer;
}

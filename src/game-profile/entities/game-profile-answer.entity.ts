import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { GameProfileQuestion } from './game-profile-question.entity';

@Entity('GameProfileAnswers')
export class GameProfileAnswer {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  gameProfileQuestionId: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  subTitle: string;

  @Column({ nullable: true })
  imageLink: string;

  @Column({ type: 'text', nullable: true })
  options: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  updatedAt: Date;

  @DeleteDateColumn({ nullable: true })
  deletedAt: Date;

  @Column({ nullable: true })
  createdBy: string;

  @Column({ nullable: true })
  updatedBy: string;

  @Column({ nullable: true })
  deletedBy: string;

  @ManyToOne(() => GameProfileQuestion, (question) => question.answers)
  @JoinColumn({ name: 'gameProfileQuestionId' })
  question?: GameProfileQuestion;
}

import { Column, CreateDate<PERSON><PERSON>umn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';

@Entity('GameProfiles')
export class GameProfile {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  header: string;

  @Column({ type: 'text', nullable: true })
  subHeader: string;

  @Column({ nullable: true })
  ctaButton: string;

  @Column({ nullable: true })
  options: string;

  @Column({ nullable: true })
  imageLink: string;

  @Column({ nullable: true })
  backgroundColor: string;

  @Column({ nullable: true })
  annexActionUsaId: string;

  @Column({ nullable: true })
  annexActionCanId: string;

  @Column({ nullable: true })
  type: string;

  @Column({ type: 'bit', default: 1 })
  disabled: boolean;

  @Column({ nullable: true })
  thankYouHeader: string;

  @Column({ nullable: true })
  thankYouMessage: string;

  @Column({ type: 'datetime', nullable: true })
  startTime: Date;

  @Column({ type: 'datetime', nullable: true })
  endTime: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  updatedAt: Date;

  @DeleteDateColumn({ nullable: true })
  deletedAt: Date;

  @Column({ nullable: true })
  createdBy: string;
}

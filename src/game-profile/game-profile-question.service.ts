import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { orderBy, result } from 'lodash';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { percentRound } from '../utils/transform';
import { CreateGameProfileQuestionDto } from './dto/create-game-profile-question.dto';
import { GameProfileQuestionResponseDto } from './dto/game-profile-question-response.dto';
import { SortGameProfileQuestionDto } from './dto/sort-game-profile-question.dto';
import { UpdateGameProfileQuestionDto } from './dto/update-game-profile-question.dto';
import { UpdateGameProfileQuestionStatusDto } from './dto/update-question-status.dto';
import { GameProfileAnswer } from './entities/game-profile-answer.entity';
import { GameProfileQuestion } from './entities/game-profile-question.entity';
import { GameProfileUserAnswer } from './entities/game-profile-user-answer.entity';

@Injectable()
export class GameProfileQuestionService {
  private readonly logger = new Logger(GameProfileQuestionService.name);

  constructor(
    @InjectRepository(GameProfileQuestion)
    private readonly gameProfileQuestionRepo: Repository<GameProfileQuestion>,
    @InjectRepository(GameProfileAnswer)
    private readonly gameProfileAnswerRepo: Repository<GameProfileAnswer>,
    @InjectRepository(GameProfileUserAnswer)
    private readonly gameProfileUserAnswerRepo: Repository<GameProfileUserAnswer>,
    private readonly apiVersionsService: ApiVersionsService
  ) {}

  async createQuestion(createDto: CreateGameProfileQuestionDto, userId: string) {
    try {
      // Check if question with same name already exists
      const existingQuestion = await this.gameProfileQuestionRepo.findOne({
        where: { name: createDto.name.trim() },
      });

      if (existingQuestion) {
        throw new BadRequestException(`Question with name "${createDto.name}" already exists`);
      }

      // Create question
      const questionId = v4();
      const questionData = {
        id: questionId,
        name: createDto.name.trim(),
        category: createDto.category,
        subCategory: createDto.subCategory,
        sectionName: createDto.sectionName,
        questionType: createDto.questionType,
        answerType: createDto.answerType,
        sortOrder: 1,
        options: createDto.options ? JSON.stringify(createDto.options) : null,
        disabled: createDto.disabled !== undefined ? createDto.disabled : true,
        createdBy: userId,
      };

      const question = await this.gameProfileQuestionRepo.save(this.gameProfileQuestionRepo.create(questionData));

      // Create answers
      const answers = [];
      for (const answerDto of createDto.answers) {
        if (answerDto.title && answerDto.title.trim()) {
          const answerData = {
            id: v4(),
            gameProfileQuestionId: questionId,
            title: answerDto.title.trim(),
            subTitle: answerDto.subTitle,
            imageLink: answerDto.imageLink,
            options: answerDto.options ? JSON.stringify(answerDto.options) : null,
            createdBy: userId,
          };

          const answer = await this.gameProfileAnswerRepo.save(this.gameProfileAnswerRepo.create(answerData));
          answers.push(answer);
        }
      }

      // Update API version
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE_QUESTIONS, 'USA', userId);

      return {
        ...question,
        answers,
      };
    } catch (error) {
      this.logger.error('Error creating game profile question:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to create question. Please try again.');
    }
  }

  async getQuestions(userId?: string) {
    try {
      const questions = await this.gameProfileQuestionRepo.find({
        where: { disabled: false },
        relations: ['answers'],
        order: { sortOrder: 'ASC', createdAt: 'DESC' },
      });

      // If userId is provided, check which questions user has answered
      if (userId) {
        const userAnswers = await this.gameProfileUserAnswerRepo.find({
          where: { userId },
          select: ['gameProfileQuestionId', 'gameProfileAnswerId'],
        });

        // Group answers by question
        const userAnswersByQuestion = userAnswers.reduce((acc, ua) => {
          if (!acc[ua.gameProfileQuestionId]) {
            acc[ua.gameProfileQuestionId] = [];
          }
          acc[ua.gameProfileQuestionId].push(ua.gameProfileAnswerId);
          return acc;
        }, {});

        const questionsWithAnswerStatus = questions.map((question) => ({
          ...question,
          isAnswered: !!userAnswersByQuestion[question.id],
          userAnswerIds: userAnswersByQuestion[question.id] || [],
          userAnswerCount: userAnswersByQuestion[question.id]?.length || 0,
        }));

        return { questions: questionsWithAnswerStatus };
      }

      return { questions };
    } catch (error) {
      this.logger.error('Error fetching game profile questions:', error);
      throw new BadRequestException('Failed to fetch questions');
    }
  }

  async getQuestionsWithUserAnswers(userId: string) {
    try {
      const questions = await this.gameProfileQuestionRepo.find({
        where: { disabled: false },
        relations: ['answers'],
        order: { sortOrder: 'ASC', createdAt: 'DESC' },
      });

      // Get all user answers for this user
      const userAnswers = await this.gameProfileUserAnswerRepo.find({
        where: { userId },
      });

      // Group answers by question
      const userAnswersByQuestion = userAnswers.reduce((acc, ua) => {
        if (!acc[ua.gameProfileQuestionId]) {
          acc[ua.gameProfileQuestionId] = [];
        }
        acc[ua.gameProfileQuestionId].push(ua.gameProfileAnswerId);
        return acc;
      }, {});

      const questionsWithAnswers = questions.map((question) => {
        const userAnswerIds = userAnswersByQuestion[question.id] || [];
        const userAnswers = userAnswerIds
          .map((answerId) => question.answers?.find((answer) => answer.id === answerId))
          .filter(Boolean);

        return {
          ...question,
          isAnswered: userAnswerIds.length > 0,
          userAnswerIds,
          userAnswerCount: userAnswerIds.length,
          userAnswers,
        };
      });

      return { questions: questionsWithAnswers };
    } catch (error) {
      this.logger.error('Error fetching questions with user answers:', error);
      throw new BadRequestException('Failed to fetch questions with user answers');
    }
  }

  async getQuestionById(id: string, userId?: string) {
    try {
      const question = await this.gameProfileQuestionRepo.findOne({
        where: { id },
        relations: ['answers'],
      });

      if (!question) {
        throw new NotFoundException('Question not found');
      }

      // If userId is provided, check if user has answered this question
      if (userId) {
        const userAnswers = await this.gameProfileUserAnswerRepo.find({
          where: { userId, gameProfileQuestionId: id },
        });

        const userAnswerIds = userAnswers.map((ua) => ua.gameProfileAnswerId);
        const userAnswerObjects = userAnswerIds
          .map((answerId) => question.answers?.find((answer) => answer.id === answerId))
          .filter(Boolean);

        return {
          ...question,
          isAnswered: userAnswers.length > 0,
          userAnswerIds,
          userAnswerCount: userAnswers.length,
          userAnswers: userAnswerObjects,
        };
      }

      return question;
    } catch (error) {
      this.logger.error('Error fetching game profile question by id:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to fetch question');
    }
  }

  async getAllQuestionsForAdmin() {
    try {
      const questions = await this.gameProfileQuestionRepo.find({
        relations: ['answers'],
        order: { sortOrder: 'ASC', createdAt: 'DESC' },
      });

      // Check which questions have user answers
      const questionsWithAnswerStatus = await Promise.all(
        questions.map(async (question) => {
          const userAnswerCount = await this.getQuestionAnswerCount(question.id);
          const hasAnswers = userAnswerCount > 0;

          return {
            ...question,
            isAnswered: hasAnswers,
            totalUserAnswers: userAnswerCount,
            canEdit: !hasAnswers,
          };
        })
      );

      return { questions: questionsWithAnswerStatus };
    } catch (error) {
      this.logger.error('Error fetching all game profile questions for admin:', error);
      throw new BadRequestException('Failed to fetch questions');
    }
  }

  async updateQuestion(id: string, updateDto: UpdateGameProfileQuestionDto, userId: string) {
    try {
      const existingQuestion = await this.gameProfileQuestionRepo.findOne({
        where: { id },
        relations: ['answers'],
      });

      if (!existingQuestion) {
        throw new NotFoundException('Question not found');
      }

      // Check if question has user answers
      const hasAnswers = await this.checkQuestionHasAnswers(id);
      if (hasAnswers) {
        throw new BadRequestException(
          'Cannot update question that has user answers. Please create a new question instead.'
        );
      }

      // Check if name is being updated and if it conflicts with another question
      if (updateDto.name && updateDto.name.trim() !== existingQuestion.name) {
        const conflictQuestion = await this.gameProfileQuestionRepo.findOne({
          where: { name: updateDto.name.trim() },
        });

        if (conflictQuestion && conflictQuestion.id !== id) {
          throw new BadRequestException(`Question with name "${updateDto.name}" already exists`);
        }
      }

      // Update question
      const updateQuestion = { ...updateDto };
      delete updateQuestion.answers;
      const questionData = {
        ...updateQuestion,
        name: updateDto.name ? updateDto.name.trim() : existingQuestion.name,
        options: updateDto.options ? JSON.stringify(updateDto.options) : existingQuestion.options,
        updatedBy: userId,
        updatedAt: new Date(),
      };

      await this.gameProfileQuestionRepo.update(id, questionData);

      // Update answers if provided
      if (updateDto.answers && updateDto.answers.length > 0) {
        // Delete existing answers
        await this.gameProfileAnswerRepo.delete({ gameProfileQuestionId: id });

        // Create new answers
        for (const answerDto of updateDto.answers) {
          if (answerDto.title && answerDto.title.trim()) {
            const answerData = {
              id: v4(),
              gameProfileQuestionId: id,
              title: answerDto.title.trim(),
              subTitle: answerDto.subTitle,
              imageLink: answerDto.imageLink,
              options: answerDto.options ? JSON.stringify(answerDto.options) : null,
              createdBy: userId,
            };

            await this.gameProfileAnswerRepo.save(this.gameProfileAnswerRepo.create(answerData));
          }
        }
      }

      // Update API version
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE_QUESTIONS, 'USA', userId);

      return await this.getQuestionById(id);
    } catch (error) {
      this.logger.error('Error updating game profile question:', error);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to update question. Please try again.');
    }
  }

  async updateQuestionStatus(id: string, updateDto: UpdateGameProfileQuestionStatusDto, userId: string) {
    try {
      const question = await this.gameProfileQuestionRepo.findOne({ where: { id } });

      if (!question) {
        throw new NotFoundException('Question not found');
      }

      await this.gameProfileQuestionRepo.update(id, {
        disabled: updateDto.disabled,
        updatedBy: userId,
        updatedAt: new Date(),
      });

      // Update API version
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE_QUESTIONS, 'USA', userId);

      return await this.getQuestionById(id);
    } catch (error) {
      this.logger.error('Error updating question status:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to update question status');
    }
  }

  async sortQuestions(sortDto: SortGameProfileQuestionDto, userId: string) {
    try {
      for (const item of sortDto.questions) {
        await this.gameProfileQuestionRepo.update(item.id, {
          sortOrder: item.sortOrder,
          updatedBy: userId,
          updatedAt: new Date(),
        });
      }

      // Update API version
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE_QUESTIONS, 'USA', userId);

      return { success: true };
    } catch (error) {
      this.logger.error('Error sorting questions:', error);
      throw new BadRequestException('Failed to sort questions');
    }
  }

  async deleteQuestion(id: string, userId: string) {
    try {
      const question = await this.gameProfileQuestionRepo.findOne({ where: { id } });

      if (!question) {
        throw new NotFoundException('Question not found');
      }

      // Soft delete answers first
      await this.gameProfileAnswerRepo.softDelete({ gameProfileQuestionId: id });

      // Soft delete question
      await this.gameProfileQuestionRepo.softDelete(id);

      // Update API version
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE_QUESTIONS, 'USA', userId);

      return { success: true };
    } catch (error) {
      this.logger.error('Error deleting question:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete question');
    }
  }

  async getQuestionSummary(id: string) {
    try {
      const questionDb = await this.gameProfileQuestionRepo.findOne({
        where: { id },
        relations: ['answers'],
      });

      if (!questionDb) {
        throw new NotFoundException(`Question ${id} not found`);
      }

      const answers = result(questionDb, 'answers', null);
      if (answers) {
        questionDb.answers = orderBy(answers, 'createdAt');
      }

      const question = plainToClass(GameProfileQuestionResponseDto, questionDb, {
        excludeExtraneousValues: true,
      });

      await this.calculatePercentAnswer(id, question);

      return { question };
    } catch (error) {
      this.logger.error('Error getting question summary:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to get question summary');
    }
  }

  private async calculatePercentAnswer(id: string, question: GameProfileQuestionResponseDto) {
    const queryUserAnswer = this.gameProfileUserAnswerRepo.createQueryBuilder('UAnswer');
    queryUserAnswer.where({ gameProfileQuestionId: id });
    queryUserAnswer.select(['gameProfileAnswerId', 'count(gameProfileAnswerId) count']);
    queryUserAnswer.groupBy('gameProfileAnswerId');
    const userAnswers = await queryUserAnswer.getRawMany();

    if (userAnswers?.length > 0) {
      let totalAnswer = 0;
      userAnswers.map((answer) => (totalAnswer += answer.count));

      let lstPercentAnswerForQuestions = [];
      lstPercentAnswerForQuestions = userAnswers.map((answer) => {
        return { ...answer, percent: +((+answer.count / +totalAnswer) * 100) };
      });

      const arrQuestionAnswerPercent = [];
      question.answers = question?.answers?.map((item) => {
        const percentAnswerForQuestion = lstPercentAnswerForQuestions.find(
          (percentAnswer) => percentAnswer.gameProfileAnswerId == item.id
        );
        item['percent'] = percentAnswerForQuestion ? percentAnswerForQuestion.percent : 0;
        item['count'] = percentAnswerForQuestion ? percentAnswerForQuestion.count : 0;
        arrQuestionAnswerPercent.push({ id: item.id, percent: item['percent'] });
        return item;
      });

      const roundPercents = percentRound(
        arrQuestionAnswerPercent.map((item) => item.percent),
        2
      );

      // Remap percent
      question.answers = question.answers.map((item, index) => {
        item['percent'] = roundPercents[index];
        return item;
      });

      question['totalAnswer'] = totalAnswer;
    } else {
      // No answers yet, set all to 0
      question.answers = question?.answers?.map((item) => {
        item['percent'] = 0;
        item['count'] = 0;
        return item;
      });
      question['totalAnswer'] = 0;
    }
  }

  async checkQuestionHasAnswers(questionId: string): Promise<boolean> {
    const userAnswerCount = await this.gameProfileUserAnswerRepo.count({
      where: { gameProfileQuestionId: questionId },
    });
    return userAnswerCount > 0;
  }

  async getQuestionAnswerCount(questionId: string): Promise<number> {
    return await this.gameProfileUserAnswerRepo.count({
      where: { gameProfileQuestionId: questionId },
    });
  }
}

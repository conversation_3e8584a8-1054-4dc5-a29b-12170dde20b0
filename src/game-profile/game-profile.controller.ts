import { Body, Controller, Delete, Get, Param, Patch, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { CreateEntryCardDto } from './dto/create-entry-card.dto';
import { CreateQuestionnaireDto } from './dto/create-questionnaire.dto';
import { GameProfileService } from './game-profile.service';
import { SortGameProfileDto } from './game-profile.types';

@Controller('game-profiles')
export class GameProfileController {
  constructor(private readonly gameProfileService: GameProfileService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get()
  findAll(@Query() query: SortGameProfileDto) {
    return this.gameProfileService.findAll(query);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('entry-card/status')
  updateStatus(@Body('disabled') disabled: boolean, @Req() req: BaseRequest) {
    return this.gameProfileService.updateEntryCardStatus(disabled, req.user?.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Req() req: BaseRequest) {
    return this.gameProfileService.remove(id, req.user?.uid);
  }

  // Specific endpoints for ENTRY_CARD
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('entry-card')
  createOrUpdateEntryCard(@Body() createEntryCardDto: CreateEntryCardDto, @Req() req: BaseRequest) {
    return this.gameProfileService.createOrUpdateEntryCard(createEntryCardDto, req.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('entry-card')
  getEntryCard() {
    return this.gameProfileService.getEntryCard();
  }

  // Specific endpoints for QUESTIONNAIRE
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('questionnaire')
  createOrUpdateQuestionnaire(@Body() createQuestionnaireDto: CreateQuestionnaireDto, @Req() req: BaseRequest) {
    return this.gameProfileService.createOrUpdateQuestionnaire(createQuestionnaireDto, req.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('questionnaire')
  getQuestionnaire() {
    return this.gameProfileService.getQuestionnaire();
  }

  // write a api call func updateDisabledStatusByTime
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('cronjob-status')
  updateDisabledStatusByTime() {
    return this.gameProfileService.updateDisabledStatusByTime();
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.gameProfileService.findOne(id);
  }
}

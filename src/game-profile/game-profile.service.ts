import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { IsNull, Not, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { CreateEntryCardDto } from './dto/create-entry-card.dto';
import { CreateQuestionnaireDto } from './dto/create-questionnaire.dto';
import { GameProfile } from './entities/game-profile.entity';
import { GAME_PROFILE_TYPE, SortGameProfileDto } from './game-profile.types';

@Injectable()
export class GameProfileService {
  private readonly logger = new Logger(GameProfileService.name);

  constructor(
    @InjectRepository(GameProfile)
    private gameProfileRepo: Repository<GameProfile>,
    private readonly apiVersionsService: ApiVersionsService,
    private readonly config: ConfigService
  ) {}

  async findAll(options: SortGameProfileDto = {}) {
    try {
      const nTake = options?.limit ? parseInt(options.limit.toString(), 10) : 10;
      const nPage = options?.page ? parseInt(options.page.toString(), 10) : 1;
      const skip = (nPage - 1) * nTake;

      const queryBuilder = this.gameProfileRepo.createQueryBuilder('gameProfile');

      if (options.type) {
        queryBuilder.andWhere('gameProfile.type = :type', { type: options.type });
      }

      // Add ordering
      if (options.createdAt) {
        queryBuilder.addOrderBy('gameProfile.createdAt', options.createdAt);
      } else {
        queryBuilder.orderBy('gameProfile.createdAt', 'DESC');
      }

      const [result, total] = await queryBuilder.take(nTake).skip(skip).getManyAndCount();

      return {
        gameProfiles: result,
        total,
        limit: nTake,
        page: nPage,
      };
    } catch (err) {
      this.logger.error('Error fetching game profiles:', err);
      throw new BadRequestException('Failed to fetch game profiles. Please try again!');
    }
  }

  async findOne(id: string) {
    try {
      const gameProfile = await this.gameProfileRepo.findOne({
        where: { id },
      });

      if (!gameProfile) {
        throw new NotFoundException(`Game profile with ID ${id} not found`);
      }

      return gameProfile;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Error finding game profile:', error);
      throw new BadRequestException(`Failed to find game profile ${id}`);
    }
  }

  async remove(id: string, userId?: string) {
    try {
      await this.findOne(id);
      await this.gameProfileRepo.update(id, {
        deletedAt: new Date(),
      });

      await this.apiVersionsService.updateVersion(
        FEATURE_KEY_VERSION.GAME_PROFILE, // Using existing feature key, you may want to create a new one
        'USA',
        userId || null
      );

      return { message: 'Game profile deleted successfully' };
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw err;
      }
      this.logger.error('Error deleting game profile:', err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async updateEntryCardStatus(disabled: boolean, userId?: string) {
    try {
      const entryCard = await this.gameProfileRepo.findOne({
        where: { type: GAME_PROFILE_TYPE.ENTRY_CARD },
      });
      if (!entryCard) {
        throw new NotFoundException(`Entry card not found`);
      }
      await this.gameProfileRepo.update(
        { type: GAME_PROFILE_TYPE.ENTRY_CARD },
        {
          disabled,
          updatedAt: new Date(),
        }
      );

      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE, 'USA', userId || null);

      return this.findOne(entryCard.id);
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw err;
      }
      this.logger.error('Error updating game profile status:', err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async createOrUpdateEntryCard(createDTO: CreateEntryCardDto, userId?: string) {
    try {
      // Find existing ENTRY_CARD
      const existingEntryCard = await this.gameProfileRepo.findOne({
        where: { type: GAME_PROFILE_TYPE.ENTRY_CARD },
      });

      const gameProfileData = {
        ...createDTO,
        type: GAME_PROFILE_TYPE.ENTRY_CARD,
        updatedAt: new Date(),
      };

      if (createDTO.options) {
        gameProfileData.options = JSON.stringify(createDTO.options);
      }

      if (createDTO.startTime && createDTO.endTime) {
        if (createDTO.startTime > createDTO.endTime) {
          throw new BadRequestException('startTime should be less than endTime');
        }
        if (createDTO.startTime < new Date()) {
          throw new BadRequestException('startTime should be greater than now');
        }
        if (createDTO.endTime < new Date()) {
          throw new BadRequestException('endTime should be greater than now');
        }
      }

      if (existingEntryCard) {
        // Update existing ENTRY_CARD
        await this.gameProfileRepo.update(existingEntryCard.id, gameProfileData);

        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE, 'USA', userId || null);

        return this.findOne(existingEntryCard.id);
      } else {
        if (createDTO.disabled === undefined) {
          gameProfileData.disabled = true; // Default to disabled
        }
        // Create new ENTRY_CARD
        const newGameProfileData = {
          ...gameProfileData,
          id: v4(),
          createdBy: userId,
        };

        const gameProfile = await this.gameProfileRepo.save(this.gameProfileRepo.create(newGameProfileData));

        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE, 'USA', userId || null);

        return gameProfile;
      }
    } catch (err) {
      this.logger.error('Error creating/updating entry card:', err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async createOrUpdateQuestionnaire(createDTO: CreateQuestionnaireDto, userId?: string) {
    try {
      // Find existing QUESTIONNAIRE
      const existingQuestionnaire = await this.gameProfileRepo.findOne({
        where: { type: GAME_PROFILE_TYPE.QUESTIONNAIRE },
      });

      const gameProfileData = {
        ...createDTO,
        type: GAME_PROFILE_TYPE.QUESTIONNAIRE,
        updatedAt: new Date(),
        disabled: true,
      };

      if (createDTO.options) {
        gameProfileData.options = JSON.stringify(createDTO.options);
      }

      if (existingQuestionnaire) {
        // Update existing QUESTIONNAIRE
        await this.gameProfileRepo.update(existingQuestionnaire.id, gameProfileData);

        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE, 'USA', userId || null);

        return this.findOne(existingQuestionnaire.id);
      } else {
        // Create new QUESTIONNAIRE
        const newGameProfileData = {
          ...gameProfileData,
          id: v4(),
          createdBy: userId,
        };

        const gameProfile = await this.gameProfileRepo.save(this.gameProfileRepo.create(newGameProfileData));

        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE, 'USA', userId || null);

        return gameProfile;
      }
    } catch (err) {
      this.logger.error('Error creating/updating questionnaire:', err);
      throw new BadRequestException({ errorMessage: err.message });
    }
  }

  async getEntryCard() {
    try {
      const entryCard = await this.gameProfileRepo.findOne({
        where: { type: GAME_PROFILE_TYPE.ENTRY_CARD, disabled: false },
      });

      return entryCard || {};
    } catch (error) {
      this.logger.error('Error fetching entry card:', error);
      throw new BadRequestException('Failed to fetch entry card');
    }
  }

  async updateDisabledStatusByTime() {
    try {
      const momentTz = require('moment-timezone');
      const currentDate = momentTz().utc();
      this.logger.log(`CURRENT TIME UTC: ${currentDate.toISOString()}`);

      // Get the ENTRY_CARD game profile that has startTime and endTime
      const entryCard = await this.gameProfileRepo.findOne({
        where: {
          type: GAME_PROFILE_TYPE.ENTRY_CARD,
          startTime: Not(IsNull()),
          endTime: Not(IsNull()),
        },
      });

      if (!entryCard) {
        this.logger.log('No ENTRY_CARD found with startTime and endTime');
        return;
      }

      // Convert startTime and endTime to moment objects for comparison
      const convertStartTime = momentTz(entryCard.startTime).format('YYYY-MM-DD HH:mm');
      const startDate = momentTz.tz(convertStartTime, 'UTC');

      const convertEndTime = momentTz(entryCard.endTime).format('YYYY-MM-DD HH:mm');
      const endDate = momentTz.tz(convertEndTime, 'UTC');

      this.logger.log(`ENTRY_CARD startTime: ${startDate.toISOString()}`);
      this.logger.log(`ENTRY_CARD endTime: ${endDate.toISOString()}`);

      let shouldDisable: boolean | null = null;

      // If current time is between startTime and endTime, enable (disabled = false)
      if (currentDate.isSameOrAfter(startDate) && currentDate.isSameOrBefore(endDate)) {
        shouldDisable = false;
      }
      // If current time is after endTime, disable (disabled = true)
      else if (currentDate.isAfter(endDate)) {
        shouldDisable = true;
      }
      // If current time is before startTime, keep disabled = true (no change needed)

      // Only update if there's a change needed
      if (shouldDisable !== null && entryCard.disabled !== shouldDisable) {
        await this.gameProfileRepo.update(entryCard.id, {
          disabled: shouldDisable,
          updatedAt: new Date(),
        });

        this.logger.log(`Updated ENTRY_CARD ${entryCard.id} disabled status to ${shouldDisable}`);

        // Update API version after status change
        await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.GAME_PROFILE, 'USA', null);
      } else {
        this.logger.log(`ENTRY_CARD ${entryCard.id} disabled status unchanged (${entryCard.disabled})`);
      }
      return true;
    } catch (error) {
      this.logger.error('Error updating ENTRY_CARD disabled status by time:', error);
      throw error;
    }
  }

  async getQuestionnaire() {
    try {
      const questionnaire = await this.gameProfileRepo.findOne({
        where: { type: GAME_PROFILE_TYPE.QUESTIONNAIRE },
      });

      return questionnaire || {};
    } catch (error) {
      this.logger.error('Error fetching questionnaire:', error);
      throw new BadRequestException('Failed to fetch questionnaire');
    }
  }
}

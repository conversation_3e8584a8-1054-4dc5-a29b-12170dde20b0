import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { ConfigService } from 'nestjs-config';
import { isCronJobHandlersEnabled } from 'src/utils/cron';
import { GameProfileService } from './game-profile.service';

@Injectable()
export class GameProfileCronService {
  private readonly logger = new Logger(GameProfileCronService.name);

  constructor(private readonly gameProfileService: GameProfileService, private readonly config: ConfigService) {}

  // Run every 10 minutes
  @Cron('0 */10 * * * *')
  async handleGameProfileStatusUpdateCron() {
    if (!isCronJobHandlersEnabled(this.config)) {
      return;
    }

    this.logger.log('Starting GameProfile status update cron job...');

    try {
      await this.gameProfileService.updateDisabledStatusByTime();
      this.logger.log('GameProfile status update cron job completed successfully');
    } catch (error) {
      this.logger.error('Error in GameProfile status update cron job:', error);
    }
  }
}

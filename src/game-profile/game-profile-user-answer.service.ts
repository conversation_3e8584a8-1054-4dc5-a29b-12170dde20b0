import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { CreateGameProfileUserAnswerDto } from './dto/create-game-profile-user-answer.dto';
import { GameProfileAnswer } from './entities/game-profile-answer.entity';
import { GameProfileQuestion } from './entities/game-profile-question.entity';
import { GameProfileUserAnswer } from './entities/game-profile-user-answer.entity';

@Injectable()
export class GameProfileUserAnswerService {
  private readonly logger = new Logger(GameProfileUserAnswerService.name);

  constructor(
    @InjectRepository(GameProfileUserAnswer)
    private readonly gameProfileUserAnswerRepo: Repository<GameProfileUserAnswer>,
    @InjectRepository(GameProfileQuestion)
    private readonly gameProfileQuestionRepo: Repository<GameProfileQuestion>,
    @InjectRepository(GameProfileAnswer)
    private readonly gameProfileAnswerRepo: Repository<GameProfileAnswer>
  ) {}

  async addUpdateAnswer(createDto: CreateGameProfileUserAnswerDto, userId: string) {
    try {
      const { gameProfileQuestionId } = createDto;

      // Support both single and multiple answers
      let answerIds: string[] = [];
      if (createDto.gameProfileAnswerId) {
        answerIds = [createDto.gameProfileAnswerId];
      } else if (createDto.gameProfileAnswerIds) {
        answerIds = createDto.gameProfileAnswerIds;
      } else {
        throw new BadRequestException('Either gameProfileAnswerId or gameProfileAnswerIds must be provided');
      }

      // Validate question exists and is active
      const question = await this.gameProfileQuestionRepo.findOne({
        where: { id: gameProfileQuestionId },
        relations: ['answers'],
      });

      if (!question) {
        throw new NotFoundException('Question not found');
      }

      if (question.disabled) {
        throw new BadRequestException('Question is no longer available');
      }

      // Validate all answers exist and belong to the question
      for (const answerId of answerIds) {
        const answer = await this.gameProfileAnswerRepo.findOne({
          where: {
            id: answerId,
            gameProfileQuestionId,
          },
        });

        if (!answer) {
          throw new NotFoundException(`Answer ${answerId} not found or does not belong to this question`);
        }
      }

      // Remove all existing answers for this user and question
      await this.gameProfileUserAnswerRepo.delete({
        userId,
        gameProfileQuestionId,
      });

      // Create new answers
      const userAnswers = [];
      for (const answerId of answerIds) {
        const userAnswerData = {
          id: v4(),
          userId,
          gameProfileQuestionId,
          gameProfileAnswerId: answerId,
          createdBy: userId,
        };
        userAnswers.push(this.gameProfileUserAnswerRepo.create(userAnswerData));
      }

      const savedAnswers = await this.gameProfileUserAnswerRepo.save(userAnswers);
      return { userAnswers: savedAnswers, count: savedAnswers.length };
    } catch (error) {
      this.logger.error('Error adding/updating game profile user answer:', error);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to save answer. Please try again.');
    }
  }

  async getUserAnswers(userId: string) {
    try {
      const userAnswers = await this.gameProfileUserAnswerRepo.find({
        where: { userId },
        relations: ['question', 'answer'],
        order: { createdAt: 'DESC' },
      });

      // Group answers by question
      const groupedAnswers = userAnswers.reduce((acc, userAnswer) => {
        const questionId = userAnswer.gameProfileQuestionId;
        if (!acc[questionId]) {
          acc[questionId] = {
            questionId,
            question: userAnswer.question,
            answers: [],
            answeredAt: userAnswer.createdAt,
          };
        }
        acc[questionId].answers.push({
          id: userAnswer.id,
          answerId: userAnswer.gameProfileAnswerId,
          answer: userAnswer.answer,
        });
        return acc;
      }, {});

      return {
        userAnswers: Object.values(groupedAnswers),
        totalQuestions: Object.keys(groupedAnswers).length,
        totalAnswers: userAnswers.length,
      };
    } catch (error) {
      this.logger.error('Error fetching user answers:', error);
      throw new BadRequestException('Failed to fetch user answers');
    }
  }

  async deleteUserAnswer(userId: string, gameProfileQuestionId: string) {
    try {
      const userAnswers = await this.gameProfileUserAnswerRepo.find({
        where: {
          userId,
          gameProfileQuestionId,
        },
      });

      if (!userAnswers || userAnswers.length === 0) {
        throw new NotFoundException('User answers not found for this question');
      }

      // Delete all answers for this question
      const deleteResult = await this.gameProfileUserAnswerRepo.delete({
        userId,
        gameProfileQuestionId,
      });

      return {
        success: true,
        deletedCount: deleteResult.affected || 0,
        message: `Deleted ${deleteResult.affected || 0} answer(s) for question ${gameProfileQuestionId}`,
      };
    } catch (error) {
      this.logger.error('Error deleting user answer:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException('Failed to delete answer');
    }
  }
}

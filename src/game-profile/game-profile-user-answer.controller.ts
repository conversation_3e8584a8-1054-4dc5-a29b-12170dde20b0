import { Body, Controller, Delete, Get, Param, Post, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { CreateGameProfileUserAnswerDto } from './dto/create-game-profile-user-answer.dto';
import { GameProfileUserAnswerService } from './game-profile-user-answer.service';

@Controller('game-profile-user-answers')
export class GameProfileUserAnswerController {
  constructor(private readonly gameProfileUserAnswerService: GameProfileUserAnswerService) {}

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post()
  addUpdateAnswer(@Body() createUserAnswerDto: CreateGameProfileUserAnswerDto, @Req() req: BaseRequest) {
    return this.gameProfileUserAnswerService.addUpdateAnswer(createUserAnswerDto, req.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('multiple')
  createMultipleUserAnswers(
    @Body() createDto: { gameProfileQuestionId: string; gameProfileAnswerIds: string[] },
    @Req() req: BaseRequest
  ) {
    const dto: CreateGameProfileUserAnswerDto = {
      gameProfileQuestionId: createDto.gameProfileQuestionId,
      gameProfileAnswerIds: createDto.gameProfileAnswerIds,
    };
    return this.gameProfileUserAnswerService.addUpdateAnswer(dto, req.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get()
  getUserAnswers(@Req() req: BaseRequest) {
    return this.gameProfileUserAnswerService.getUserAnswers(req.user?.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Delete('question/:questionId')
  deleteUserAnswer(@Req() req: BaseRequest, @Param('questionId') questionId: string) {
    return this.gameProfileUserAnswerService.deleteUserAnswer(req.user?.uid, questionId);
  }
}

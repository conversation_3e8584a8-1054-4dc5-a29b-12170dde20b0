import { createBullBoard } from '@bull-board/api';
import { BullAdapter } from '@bull-board/api/bullAdapter';
import { ExpressAdapter } from '@bull-board/express';
import Queue from 'bull';
import { ensureLoggedIn } from 'connect-ensure-login';
import cookieSession from 'cookie-session';
import ms from 'ms';
import passport from 'passport';
import { Strategy as LocalStrategy } from 'passport-local';
import { redisConfig } from './utils/redis';

passport.use(
  new LocalStrategy(function (username, password, cb) {
    if (username === process.env.BULL_BOARD_EMAIL && password === process.env.BULL_BOARD_PASSWORD) {
      return cb(null, { user: 'bull-board-admin' });
    }
    return cb(null, false);
  })
);

passport.serializeUser((user, cb) => {
  cb(null, user);
});

passport.deserializeUser((user, cb) => {
  cb(null, user);
});

export default function (app) {
  const serverAdapter = new ExpressAdapter();
  app.use(
    cookieSession({
      name: 'session',
      keys: [process.env.SESSION_SECRET],
      maxAge: ms('30 days'),
    })
  );
  app.use(passport.initialize({}));
  app.use(passport.session({}));
  createBullBoard({
    queues: [
      new BullAdapter(new Queue('payment', { redis: redisConfig })),
      // new BullAdapter(new Queue('subscription-report', { redis: redisConfig })),
      // new BullAdapter(new Queue('prefetch-contents', { redis: redisConfig })),
      // new BullAdapter(new Queue('prefetch-content-user', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-shipment', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-delivering', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-ebs', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-gvc', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-notify', { redis: redisConfig })),
      new BullAdapter(new Queue('member-shop-notify', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-fraud', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-notify-admin', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-gat-status', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-sync-gat-status', { redis: redisConfig })),
      // new BullAdapter(new Queue('ttb-threshold', { redis: redisConfig })),
      new BullAdapter(new Queue('ecom-shipment', { redis: redisConfig })),
      new BullAdapter(new Queue('ecom-shipment-overdue', { redis: redisConfig })),
      new BullAdapter(new Queue('ecom-delivering', { redis: redisConfig })),
      new BullAdapter(new Queue('ecom-alert', { redis: redisConfig })),
      new BullAdapter(new Queue('tourtrash', { redis: redisConfig })),
      // new BullAdapter(new Queue('play-arccos', { redis: redisConfig })),
      new BullAdapter(new Queue('klaviyo-track', { redis: redisConfig })),
      // new BullAdapter(new Queue('access-code', { redis: redisConfig })),
      new BullAdapter(new Queue('notification', { redis: redisConfig })),
      new BullAdapter(new Queue('klaviyo-sync', { redis: redisConfig })),
      // new BullAdapter(new Queue('salesforce-csv', { redis: redisConfig })),
      new BullAdapter(new Queue('upload-s3', { redis: redisConfig })),
      // new BullAdapter(new Queue('sync-user-sub-level-cdm', { redis: redisConfig })),
      // new BullAdapter(new Queue('payment-check-sub-expired', { redis: redisConfig })),
      new BullAdapter(new Queue('country-syncing', { redis: redisConfig })),
      // new BullAdapter(new Queue('user-referral-csv', { redis: redisConfig })),
      new BullAdapter(new Queue('witb-export-csv', { redis: redisConfig })),
      new BullAdapter(new Queue('mail-otp', { redis: redisConfig })),
    ],
    serverAdapter,
  });

  app.use('/admin/login', (req, res, next) => {
    if (req.method === 'POST') {
      return passport.authenticate('local', function (err, user) {
        if (err) {
          return next(err);
        }
        if (!user) {
          return res.redirect('/admin/login');
        }
        req.logIn(user, function (err) {
          if (err) {
            return next(err);
          }
          setTimeout(() => {
            res.redirect('/admin/queues');
          }, 500);
        });
      })(req, res, next);
    }
    res.sendFile(process.cwd() + '/src/views/login.html');
  });
  serverAdapter.setBasePath('/admin/queues');
  app.use('/admin/queues', ensureLoggedIn({ redirectTo: '/admin/login' }), serverAdapter.getRouter());
}

import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { ConfigService } from 'nestjs-config';
import { Entity<PERSON><PERSON><PERSON>, IsNull, Repository, getManager } from 'typeorm';
import * as uuid from 'uuid';
import { UserBlackListEntity } from '../auth/entities/user-black-list.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { CreateUserBlackListDto } from './dto/create-user-black-list.dto';
import { UpdateUserBlackListDto } from './dto/update-user-black-list.dto';
import { USER_BLACKLIST_MESSAGE_ERROR } from './user-black-list.constants';

@Injectable()
export class UserBlackListService {
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(UserBlackListEntity) private readonly userBlackListRepository: Repository<UserBlackListEntity>,
    @InjectRepository(UserEntity) private readonly userRepository: Repository<UserEntity>
  ) {}

  async create(createUserBlackListDto: CreateUserBlackListDto, createdBy: string) {
    try {
      const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
        const { email } = createUserBlackListDto;
        const user = await this.userRepository.findOne({ email: email });
        if (!user) {
          throw new BadRequestException({
            internalErrorCode: ERROR_CODES.USER_NOT_FOUND,
            errorMessage: USER_BLACKLIST_MESSAGE_ERROR.USER_NOT_FOUND,
          });
        }
        const userBlackList = await this.userBlackListRepository.findOne({
          where: { userId: user.id },
          withDeleted: true,
        });
        if (userBlackList && !userBlackList.deletedAt) {
          throw new BadRequestException({
            internalErrorCode: ERROR_CODES.USER_BLACKLIST_EXISTS,
            errorMessage: USER_BLACKLIST_MESSAGE_ERROR.USER_BLACKLIST_EXISTS,
          });
        }

        let newUserBlacklist: UserBlackListEntity = null;
        if (userBlackList && userBlackList.deletedAt) {
          newUserBlacklist = {
            ...userBlackList,
            deletedAt: null,
            deletedBy: null,
            updatedBy: createdBy,
          };
        } else {
          newUserBlacklist = plainToClass(UserBlackListEntity, {
            id: uuid.v4(),
            createdBy,
            userId: user.id,
            email: user.email,
          });
        }
        await transactionalEntityManager.save(UserBlackListEntity, newUserBlacklist);
        await CommonSubscriber.activityLogCommandInsert(
          {
            entity: {
              uuid: newUserBlacklist.id,
              createdBy: newUserBlacklist.createdBy,
              email: newUserBlacklist.email,
            },
            manager: transactionalEntityManager,
          },
          UserBlackListEntity.name
        );
        return newUserBlacklist.id;
      });
      return this.userBlackListRepository.findOne({ id: res });
    } catch (err) {
      console.error('ERR create UserBlackList', err);
      return { success: false };
    }
  }

  findAll() {
    return this.userBlackListRepository.find({
      where: { deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
    });
  }

  async getAllPaginate({ ...options }: any) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const skip = (nPage - 1) * nTake;
    const [UserBlackLists, total] = await this.userBlackListRepository.findAndCount({
      where: { deletedAt: IsNull() },
      order: { createdAt: 'DESC' },
      take: nTake,
      skip,
    });

    if (total === 0) {
      return {
        total: 0,
        UserBlackLists: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      UserBlackLists,
    };
  }

  findOne(id: string) {
    return this.userBlackListRepository.findOne({
      where: { id },
    });
  }

  async remove(id: string, deletedBy: string) {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const UserBlackList = await this.userBlackListRepository.findOne({ id });
      if (!UserBlackList) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.USER_BLACKLIST_NOT_FOUND,
          errorMessage: USER_BLACKLIST_MESSAGE_ERROR.USER_BLACKLIST_NOT_FOUND,
        });
      }
      await transactionalEntityManager.update(
        UserBlackListEntity,
        { id },
        { deletedAt: new Date(Date.now()), deletedBy }
      );
      await CommonSubscriber.activityLogCommandDelete(
        {
          entity: {
            uuid: id,
            deletedBy: deletedBy,
          },
          manager: transactionalEntityManager,
          databaseEntity: UserBlackList,
        },
        UserBlackListEntity.name
      );

      return { success: true, message: USER_BLACKLIST_MESSAGE_ERROR.USER_BLACKLIST_DELETED };
    });

    return res;
  }
}

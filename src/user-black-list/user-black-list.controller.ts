import { Body, Controller, Delete, Get, Param, Post, Put, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { CreateUserBlackListDto } from './dto/create-user-black-list.dto';
import { UpdateUserBlackListDto } from './dto/update-user-black-list.dto';
import { UserBlackListService } from './user-black-list.service';

@Controller('user-black-list')
export class UserBlackListController {
  constructor(private readonly userBlackListService: UserBlackListService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('get-all') // paginate
  async getAll(@Request() req: any) {
    return this.userBlackListService.getAllPaginate(req.query);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createUserBlackListDto: CreateUserBlackListDto, @Request() req: BaseRequest) {
    return this.userBlackListService.create(createUserBlackListDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req: BaseRequest) {
    return this.userBlackListService.remove(id, req.user.uid);
  }
}

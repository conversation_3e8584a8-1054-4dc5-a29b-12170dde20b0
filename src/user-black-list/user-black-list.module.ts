import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { UserBlackListEntity } from '../auth/entities/user-black-list.entity';
import { UserBlackListController } from './user-black-list.controller';
import { UserBlackListService } from './user-black-list.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([UserBlackListEntity])],
  controllers: [UserBlackListController],
  providers: [UserBlackListService],
})
export class UserBlackListModule {}

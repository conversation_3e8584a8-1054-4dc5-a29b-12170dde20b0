import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import sgMail from '@sendgrid/mail';
import fs from 'fs';
import * as _ from 'lodash';
import moment from 'moment';
import { ConfigService } from 'nestjs-config';
import xlsx from 'node-xlsx';
import path from 'path';
import { Brackets, In, Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { KlaviyoService, KlaviyoTrackEvents } from '../klaviyo/klaviyo.service';
import { LoggingService } from '../logging/logging.service';
import { NotificationService } from '../notification/notification.service';
import { UserProfileService } from '../user-profile/user-profile.service';
import { USER_REFERRAL_MESSAGE_ERROR } from '../user-referral/user-referral.constants';
import { ERROR_CODES } from '../utils/errors';
import { CreateUserReferralDto, CreateUserReferralsDto } from './dto/create-user-referrals.dto';
import { UserReferralEntity } from './entity/user-referral.entity';

@Injectable()
export class UserReferralService {
  private readonly logger = new Logger(UserReferralService.name);
  constructor(
    private readonly config: ConfigService,
    private readonly klaviyoService: KlaviyoService,
    private readonly loggingService: LoggingService,
    private readonly userProfileService: UserProfileService,
    private readonly notificationService: NotificationService,
    private firebaseMessaging: FirebaseMessagingService,
    @InjectRepository(UserEntity) private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(UserReferralEntity) private readonly userReferralRepository: Repository<UserReferralEntity>,
    @InjectRepository(UserNotifyAdminEntity) private readonly userNotifyAdminRepo: Repository<UserNotifyAdminEntity>
  ) {}

  async postEmailsReferral(createUserReferralsDto: CreateUserReferralsDto, createdBy: string) {
    try {
      const { emails } = createUserReferralsDto;
      const { validEmail } = await this.validateEmailsReferral(emails);
      if (!validEmail || !validEmail.length) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.USER_REFERRAL_INVALID,
          errorMessage: USER_REFERRAL_MESSAGE_ERROR.EMAIL_REFERRAL_INVALID,
        });
      }

      const results = await Promise.all(
        validEmail.map(async (email) => {
          return await this.createUserReferral(email, createdBy);
        })
      );
      return { success: true, userReferrals: results };
    } catch (err) {
      this.logger.error('ERR UserReferral func postEmailsReferral');
      this.logger.log(JSON.stringify(err));
      console.log('error', err);
      return { success: false, message: err?.response?.data?.message || err?.message };
    }
  }

  async postEmailReferral(createUserReferralDto: CreateUserReferralDto, createdBy: string) {
    try {
      const { email } = createUserReferralDto;
      const { isEmail } = await this.validateEmailReferral(email);
      if (!isEmail) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.USER_REFERRAL_INVALID,
          errorMessage: USER_REFERRAL_MESSAGE_ERROR.EMAIL_REFERRAL_INVALID,
        });
      }

      const userReferral = await this.createUserReferral(email, createdBy);
      return { success: true, userReferral: userReferral };
    } catch (err) {
      this.logger.error('ERR UserReferral func postEmailsReferral');
      this.logger.log(JSON.stringify(err));
      console.log('error', err);
      return { success: false, message: err?.response?.data?.message || err?.message };
    }
  }

  async createUserReferral(email, createdBy) {
    const newEmail = email.toLowerCase();
    const userReferralDto = new UserReferralEntity();
    userReferralDto.id = v4();
    userReferralDto.email = newEmail;
    userReferralDto.userId = createdBy;
    userReferralDto.createdBy = createdBy;
    const newUserReferral = await this.userReferralRepository.save(userReferralDto);
    return newUserReferral;
  }

  async validateEmailsReferral(emails) {
    const newEmails = _.compact(emails);
    const invalidEmail = [];
    const validEmail = [];
    for (const email of newEmails) {
      const { isEmail } = await this.validateEmailReferral(email);
      if (isEmail) {
        validEmail.push(email);
      } else {
        invalidEmail.push(email);
      }
    }

    return { validEmail, invalidEmail };
  }

  async validateEmailReferral(email) {
    let isEmail = true;
    if (!this.validateEmail(email)) {
      isEmail = false;
    }
    const user = await this.userRepository.findOne({
      where: { email },
    });
    const userReferral = await this.userReferralRepository.findOne({
      where: { email },
    });
    if (user || userReferral) {
      isEmail = false;
    }

    return { isEmail };
  }

  async getReferrerByEmail(email) {
    try {
      const userReferral = await this.userReferralRepository.findOne({
        where: { email },
      });
      if (userReferral) {
        return { userReferral };
      }
      return { userReferral: null };
    } catch (err) {
      this.logger.log(JSON.stringify(err));
      console.log('error', err);
      return null;
    }
  }

  async checkEmailReferral(emailReferee, platform = 'API') {
    try {
      const userReferral = await this.userReferralRepository.findOne({
        where: {
          email: emailReferee,
        },
      });
      if (!userReferral) {
        console.log('UserReferral is not found!');
        return { success: false };
      }
      const userIdReferrer = userReferral?.userId;
      const userReferrer = await this.userRepository.findOne(userIdReferrer);
      if (userReferrer && userReferrer.is_receive_gift) {
        console.log('EmailReferrer exists gift');
        return { success: false };
      }

      const emailSubcribe = await this.userRepository.findOne({
        where: {
          email: emailReferee,
        },
      });

      if (!emailSubcribe) {
        console.log('EmailSubcribe do not have conditions');
        return { success: false };
      }
      // sent email to klaviyo
      const klaviyoPayload = {
        referredUsers: emailReferee,
      };
      await this.klaviyoService.track(userReferrer.email, KlaviyoTrackEvents.MYTM_REFERRAL, klaviyoPayload);
      await this.pushNotification(userReferrer);
      const payloadUser = {
        is_receive_gift: true,
        total_receive_gift: userReferrer?.total_receive_gift + 1,
      };
      await this.userRepository.update({ id: userReferrer.id }, payloadUser);
      await this.userReferralRepository.update({ email: emailReferee }, { is_active: true });
      return { success: true };
    } catch (err) {
      console.log('ERR checkEmailReferral', err, platform);
      this.logger.error(`ERR UserReferral func checkEmailReferral ${platform}`);
      this.logger.log(JSON.stringify(err));
      await this.loggingService.save({
        event: 'ERR_USER_REFERRAL_FUNC_checkEmailReferral',
        data: {
          error: err,
          platform,
          email: emailReferee,
        },
      });
    }
  }

  async pushNotification(user: UserEntity) {
    try {
      const fcmTokens = _.result(user, 'fcmToken');
      const title = 'TaylorMade';
      const body = `Congratulations! You're eligible to earn your Referral reward.`;
      const ctaLink = `${this.config.get('app.deeplinkMyTMEndpoint')}/mytaylormadeplus/address`;
      const variables = {
        title,
        message: body,
        service: 'USER_REFERRAL',
      };
      const message: any = {
        data: {
          link: ctaLink,
        },
        token: fcmTokens,
        notification: {
          title,
          body,
        },
        webpush: {
          fcm_options: {
            link: ctaLink,
          },
        },
      };
      await this.firebaseMessaging.send(message).then((r) => r);
      await this.notificationService.saveNotificationFromOtherService(user?.id, ctaLink, variables);
    } catch (e) {
      console.log('pushNotification Referral', e);
      return null;
    }
  }

  async exportCsvReferrer() {
    try {
      const outputDir = path.join(process.cwd(), `/public/Referral/`);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      const suffixName = moment().format('YYYYMMDD');
      const buildFiles = `userReferrer_${suffixName}`;
      const userReferrers = await this.getUserReferrer();
      if (!userReferrers || !userReferrers.length) {
        return { success: true, msg: 'Can not found User Referrer receive gift.' };
      }
      const rows = [];

      const emailErrs = [];
      for (const user of userReferrers) {
        const userCdm = await this.getUserCDM(user);
        if (!userCdm) {
          emailErrs.push(user?.email);
          continue;
        }
        const address = JSON.parse(user?.address);
        const newData = [
          user?.id,
          user?.email,
          userCdm?.firstName,
          userCdm?.lastName,
          address?.phoneNumber,
          address?.address,
          address?.address2,
          address?.city,
          address?.state,
          address?.zipCode,
        ];
        rows.push(newData);
      }
      // create file export if not exists
      let emails = _.map(userReferrers, 'email');
      if (emailErrs && emailErrs.length) {
        emails = _.difference(emails, emailErrs);
      }
      if (emails && emails.length) {
        this.createCSVFile(buildFiles, rows);
        await this.sentEmailGridToAdmin(`${buildFiles}.csv`);
        await this.updateUserReferrer(emails);
        return { success: true, msg: 'Export csv User Referral successfully' };
      } else {
        return { success: false, msg: 'Can not found user Cdm' };
      }
    } catch (err) {
      console.log('Err UserReferral func exportCsvReferrer', err);
      this.logger.error(`ERR UserReferral func exportCsvReferrer`);
      this.logger.log(JSON.stringify(err));
      await this.loggingService.save({
        event: 'ERR_USER_REFERRAL_FUNC_exportCsvReferrer',
        data: {
          error: err,
        },
      });
      return err;
    }
  }

  async getUserReferrer() {
    const userReferrers = await this.userRepository
      .createQueryBuilder('u')
      .where('is_receive_gift = 1 AND total_receive_gift > 0 AND address is not null')
      .select('id,email,address,is_receive_gift,total_receive_gift,regionId')
      .getRawMany();
    if (!userReferrers || !userReferrers.length) {
      return null;
    }
    return userReferrers;
  }

  async updateUserReferrer(emails) {
    return await this.userRepository.update({ email: In(emails) }, { total_receive_gift: 0 });
  }

  async getUserCDM(user) {
    try {
      return this.userProfileService.getUserFromCDM(user);
    } catch (err) {
      this.logger.error(`ERR UserReferral func getUserCDM`);
      this.logger.log(JSON.stringify(err));
      return null;
    }
  }

  async sentEmailGridToAdmin(fileName) {
    const outputDir = path.join(process.cwd(), `/public/Referral/`);
    sgMail.setApiKey(this.config.get('app.sendGridApiKey'));
    const pathToAttachment = `${outputDir}${fileName}`;
    const attachment = fs.readFileSync(pathToAttachment).toString('base64');
    const users = await this.userNotifyAdminRepo.find({
      where: { is_referral: true },
      relations: ['user'],
    });
    if (!users || !users.length) {
      return false;
    }
    const emailTos = _.compact(
      _.map(users, (value) => {
        return _.result(value, 'user.email');
      })
    );
    const msg: any = {
      to: emailTos,
      from: this.config.get('app.sendGridFrom'),
      subject: 'MyTM+ Referral - Referrers eligible to receive reward',
      text: "We have exported the list as a daily CSV file which includes each user's email along with their shipping addresses. This CSV file can be utilized to ensure that each eligible referer is sent their reward in a timely manner.",
      attachments: [
        {
          content: attachment,
          filename: fileName,
          type: 'application/csv',
          disposition: 'attachment',
        },
      ],
    };
    return sgMail.send(msg);
  }

  createCSVFile(name, rows) {
    const dataHeading = [
      'UserId',
      'Email',
      'FirstName',
      'LastName',
      'PhoneNumber',
      'Address 1',
      'Address 2',
      'City',
      'State/Province',
      'Zipcode',
    ];
    const buffer: any = xlsx.build(
      [
        {
          name: 'user',
          data: [dataHeading, ...rows],
        },
      ],
      { bookType: 'csv' }
    );

    const csvPath = path.join(process.cwd(), `/public/Referral/${name}.csv`);
    fs.writeFileSync(csvPath, buffer);
  }

  validateEmail(email) {
    return String(email)
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      );
  }
}

import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { UserReferralService } from './user-referral.service';

export enum UserReferralCSVProcessorQueueName {
  PROCESS_USER_REFERRAL_CSV = 'process_user-referral-csv',
}

@Processor('user-referral-csv')
export class UserReferralCSVProcessor {
  private readonly logger = new Logger(UserReferralCSVProcessor.name);
  constructor(private readonly userReferralService: UserReferralService) {}

  @Process(UserReferralCSVProcessorQueueName.PROCESS_USER_REFERRAL_CSV)
  async exportCSVUserReferrer(): Promise<any> {
    this.logger.log(`process_user-referral-csv running ....`);
    const results = await this.userReferralService.exportCsvReferrer();
    return results;
  }
}

import { InjectQueue } from '@nestjs/bull';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { Queue } from 'bull';
import { ConfigService } from 'nestjs-config';
import { isCronJobHandlersEnabled, isProduction } from '../utils/cron';
import { UserReferralCSVProcessorQueueName } from './user-referral-csv.processor';

@Injectable()
export class UserReferralCronService {
  private readonly logger = new Logger(UserReferralCronService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectQueue('user-referral-csv') private userReferralCSVQueue: Queue
  ) {}

  // run every monday 1:00AM - SanDiego
  // @Cron(isProduction ? '0 08 * * 1' : CronExpression.EVERY_HOUR)
  // async cronExportCSVReferrer() {
  //   try {
  //     if (!isCronJobHandlersEnabled(this.config)) {
  //       return;
  //     }
  //     await this.userReferralCSVQueue.add(UserReferralCSVProcessorQueueName.PROCESS_USER_REFERRAL_CSV);
  //     return { success: true };
  //   } catch (err) {
  //     console.error('ERR cronExportCSVReferrer', err);
  //     this.logger.error(`ERR func cronExportCSVReferrer`);
  //     this.logger.log(JSON.stringify(err));
  //     return { success: false };
  //   }
  // }
}

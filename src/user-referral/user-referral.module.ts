import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { UserEntity } from '../auth/entities/user.entity';
import { SYSTEM_TAG } from '../utils/constants';
import { UserReferralEntity } from './entity/user-referral.entity';
import { UserReferralCSVProcessor } from './user-referral-csv.processor';
import { UserReferralController } from './user-referral.controller';
import { UserReferralCronService } from './user-referral.cron.service';
import { UserReferralService } from './user-referral.service';

let processors = [];
if (process.env.TAG !== SYSTEM_TAG.HEALTH_CHECK) {
  processors = [...processors, UserReferralCSVProcessor];
}

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([UserEntity, UserReferralEntity])],
  controllers: [UserReferralController],
  providers: [...processors, UserReferralService, UserReferralCronService],
})
export class UserReferralModule {}

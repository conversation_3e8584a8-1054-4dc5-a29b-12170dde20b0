import { Body, Controller, Get, Post, Request, UseGuards } from '@nestjs/common';
import { AuthGuard } from '../guards/auth.guard';
import { ClientGuard } from '../guards/client.guard';
import { BaseRequest } from '../types/core';
import { CreateUserReferralDto, CreateUserReferralsDto } from './dto/create-user-referrals.dto';
import { UserReferralCronService } from './user-referral.cron.service';
import { UserReferralService } from './user-referral.service';

@Controller('user-referral')
export class UserReferralController {
  constructor(
    private readonly userReferralService: UserReferralService,
    private readonly userReferralCronService: UserReferralCronService
  ) {}

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('validate-email')
  async validateEmailReferral(@Body() createUserReferralDto: CreateUserReferralDto) {
    return this.userReferralService.validateEmailReferral(createUserReferralDto.email);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('email')
  async postEmailReferral(@Body() createUserReferralDto: CreateUserReferralDto, @Request() req: BaseRequest) {
    return this.userReferralService.postEmailReferral(createUserReferralDto, req.user.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('check-email-referrer')
  async checkEmailReferral(@Body() createUserReferralDto: CreateUserReferralDto) {
    return this.userReferralService.checkEmailReferral(createUserReferralDto.email);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('emails')
  async postEmailsReferral(@Body() createUserReferralsDto: CreateUserReferralsDto, @Request() req: BaseRequest) {
    return this.userReferralService.postEmailsReferral(createUserReferralsDto, req.user.uid);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Post('validate-emails')
  async validateEmailsReferral(@Body() createUserReferralsDto: CreateUserReferralsDto) {
    return this.userReferralService.validateEmailsReferral(createUserReferralsDto?.emails);
  }

  @UseGuards(ClientGuard)
  @UseGuards(AuthGuard)
  @Get('get-referrer')
  async getReferrer(@Request() req: BaseRequest) {
    return this.userReferralService.getReferrerByEmail(req.user.email);
  }

  // @UseGuards(ClientGuard)
  // @UseGuards(AuthGuard)
  // @Get('export-csv')
  // async exportCsvReferral() {
  //   return this.userReferralCronService.cronExportCSVReferrer();
  // }
}

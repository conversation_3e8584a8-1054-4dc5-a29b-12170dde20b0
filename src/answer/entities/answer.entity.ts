import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PollQuestion } from 'src/question/entities/question.entity';

@Entity('PollAnswers')
export class PollAnswer {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  questionId: string;

  @Column()
  answerTitle: string;

  @Column()
  answerDescription: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @ManyToOne(() => PollQuestion, (question) => question.answers)
  question?: PollQuestion;
}

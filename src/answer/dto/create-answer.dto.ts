import { IsNotEmpty, <PERSON>Optional, <PERSON>String, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateAnswerDto {
  @IsNotEmpty()
  @IsUUID()
  questionId: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(42)
  answerTitle: string;

  @IsOptional()
  @IsString()
  @MaxLength(42)
  answerDescription: string;

  @IsOptional()
  createdBy: string;

  @IsOptional()
  deletedBy: string;

  @IsOptional()
  updatedBy: string;

  @IsOptional()
  updatedAt: Date;
}

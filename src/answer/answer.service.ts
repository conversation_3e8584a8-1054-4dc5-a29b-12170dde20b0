import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from 'nestjs-config';
import { Not, Repository } from 'typeorm';
import { PollQuestion } from 'src/question/entities/question.entity';
import { UserAnswer } from 'src/user-answer/entities/user-answer.entity';
import { CreateAnswerDto } from './dto/create-answer.dto';
import { UpdateAnswerDto } from './dto/update-answer.dto';
import { PollAnswer } from './entities/answer.entity';

@Injectable()
export class AnswerService {
  private readonly logger = new Logger(AnswerService.name);
  @InjectRepository(PollQuestion) private readonly pollQuestionRepo: Repository<PollQuestion>;
  @InjectRepository(PollAnswer) private readonly pollAnswerRepo: Repository<PollAnswer>;
  @InjectRepository(UserAnswer) private readonly userAnswerRepo: Repository<UserAnswer>;

  constructor(private readonly config: ConfigService) {}
  async createAnswer(payload: CreateAnswerDto, userId: string) {
    const isExisted = await this.pollAnswerRepo.findOne({
      questionId: payload.questionId,
      answerTitle: payload.answerTitle.trim(),
    });
    if (isExisted) {
      throw new BadRequestException(`Answer can not be the same`);
    }
    payload.createdBy = userId;
    try {
      return await this.pollAnswerRepo.save(this.pollAnswerRepo.create(payload));
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error);
    }
  }

  async getAllAnswers() {
    const answers = await this.pollAnswerRepo.find();
    return { answers };
  }

  async getAnswersBy(option: any) {
    const answers = await this.pollAnswerRepo.find(option);
    return { answers };
  }

  async findOne(id: string) {
    try {
      return this.pollAnswerRepo.findOne(id);
    } catch (e) {
      console.log(e);
      throw new BadRequestException(`Answers not found`);
    }
  }

  async updateAnswer(id: string, payload: UpdateAnswerDto, userId: string) {
    const isExisted = await this.findOne(id);
    if (!isExisted) {
      throw new NotFoundException(`Update Answer failed!`);
    }
    if (payload.answerTitle) {
      const answerExisted = await this.pollAnswerRepo.findOne({
        where: {
          questionId: isExisted.questionId,
          id: Not(id),
          answerTitle: payload.answerTitle,
        },
      });

      if (answerExisted) {
        throw new BadRequestException(`Answer can not be the same`);
      }
      payload.answerTitle = payload.answerTitle.trim();
    }

    payload.updatedAt = new Date();
    payload.updatedBy = userId;
    try {
      await this.pollAnswerRepo.update(id, payload);
      return await this.findOne(id);
    } catch (error) {
      throw new BadRequestException(error);
    }
  }

  async deleteAnswer(id: string) {
    const isExisted = await this.findOne(id);
    if (!isExisted) {
      throw new NotFoundException(`Answer ${id} not found`);
    }
    try {
      await this.pollAnswerRepo.delete(id);
      return { success: true };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(`Delete Answer failed!`);
    }
  }
}

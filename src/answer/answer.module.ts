import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { PollQuestion } from 'src/question/entities/question.entity';
import { SharedModule } from 'src/shared/shared.module';
import { UserAnswer } from 'src/user-answer/entities/user-answer.entity';
import { AnswerController } from './answer.controller';
import { AnswerService } from './answer.service';
import { PollAnswer } from './entities/answer.entity';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([UserEntity, PollAnswer, PollQuestion, UserAnswer])],
  controllers: [AnswerController],
  providers: [AnswerService],
})
export class AnswerModule {}

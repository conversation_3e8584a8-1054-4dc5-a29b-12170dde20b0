import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { PartnerEntity } from '../partners/entities/partner.entity';
import { BenefitsController } from './benefits.controller';
import { BenefitsService } from './benefits.service';
import { BenefitEntity } from './entities/benefit.entity';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([BenefitEntity, PartnerEntity])],
  controllers: [BenefitsController],
  providers: [BenefitsService],
})
export class BenefitsModule {}

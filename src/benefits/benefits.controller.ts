import { Body, Controller, Delete, Get, Param, Post, Put, Query, Req, Request, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { BenefitsService } from './benefits.service';
import { CreateBenefitDto } from './dto/create-benefit.dto';
import { UpdateBenefitDto } from './dto/update-benefit.dto';
import { SortBenefitDto } from './entities/benefit.entity';

@Controller('benefits')
export class BenefitsController {
  constructor(private readonly benefitsService: BenefitsService) {}

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Get('get-all') // paginate
  async getAll(@Request() req: any, @Query('country') country?: string) {
    return this.benefitsService.getAllPaginate(req.query, country);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post()
  create(@Body() createBenefitDto: CreateBenefitDto, @Request() req: BaseRequest) {
    return this.benefitsService.create(createBenefitDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Post('sort')
  async postUpdateSortOrderBenefit(@Req() request: BaseRequest, @Body() payload: SortBenefitDto) {
    return this.benefitsService.postUpdateSortOrderBenefit(payload);
  }

  @Get()
  findAll(@Request() req: any) {
    return this.benefitsService.findAll(req?.headers?.country);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.benefitsService.findOne(id);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Put(':id')
  update(@Param('id') id: string, @Body() updateBenefitDto: UpdateBenefitDto, @Request() req: BaseRequest) {
    return this.benefitsService.update(id, updateBenefitDto, req.user.uid);
  }

  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Request() req: BaseRequest) {
    return this.benefitsService.remove(id, req.user.uid);
  }
}

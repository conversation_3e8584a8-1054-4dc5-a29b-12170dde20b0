import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { isEmpty } from 'lodash';
import { ConfigService } from 'nestjs-config';
import { EntityManager, IsNull, Repository, getManager } from 'typeorm';
import * as uuid from 'uuid';
import { isUSCountry } from 'src/utils/transform';
import { FEATURE_KEY_VERSION } from '../api-versions/api-version.constants';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { CommonSubscriber } from '../subscriber/commonSubscriber';
import { ERROR_CODES } from '../utils/errors';
import { BENEFIT_ENTITY, BENEFIT_MESSAGE_ERROR } from './benefits.constants';
import { CreateBenefitDto } from './dto/create-benefit.dto';
import { UpdateBenefitDto } from './dto/update-benefit.dto';
import { BenefitEntity, SortBenefitDto } from './entities/benefit.entity';

const LINK_TYPE = {
  WEB_LINK: 'WEB_LINK',
  DEEP_LINK: 'DEEP_LINK',
};

@Injectable()
export class BenefitsService {
  constructor(
    private readonly config: ConfigService,
    private readonly apiVersionsService: ApiVersionsService,
    @InjectRepository(BenefitEntity)
    private readonly benefitEntityRepository: Repository<BenefitEntity>
  ) {}

  async postUpdateSortOrderBenefit(payload: SortBenefitDto): Promise<any> {
    try {
      return await getManager()
        .transaction(async (transactionalEntityManager: EntityManager) => {
          const listBenefit = payload.sortBenefitList;
          await Promise.all(
            listBenefit.map(async (benefit) => {
              const checkConstantSortBenefit = listBenefit.filter(
                (otherBenefit) => otherBenefit.sortOrder === benefit.sortOrder
              );
              if (checkConstantSortBenefit.length > 1) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.ORDER_INDEX_EXISTED,
                  errorMessage: `SortOrder exist more than one record`,
                });
              }

              const id = benefit.id;
              const benefitItem = await this.findOne(id);
              if (!benefitItem) {
                throw new BadRequestException({
                  internalErrorCode: ERROR_CODES.PRODUCT_NOT_FOUND,
                  errorMessage: `Benefit ${id} not found`,
                });
              }

              await transactionalEntityManager.update(
                BenefitEntity,
                { id: benefit.id },
                { sortOrder: benefit.sortOrder }
              );
            })
          );
        })
        .then(async () => {
          const data = await this.findAll();
          if (!isEmpty(data)) {
            const benefit = data[0];
            await this.apiVersionsService.updateVersion(
              FEATURE_KEY_VERSION.REWARD_PERK,
              benefit.countries,
              benefit.createdBy
            );
          }

          return data;
        });
    } catch (e) {
      return e;
    }
  }

  async create(createBenefitDto: CreateBenefitDto, createdBy: string) {
    this.checkValidateLinkType(createBenefitDto);
    const benefit = plainToClass(BenefitEntity, {
      ...createBenefitDto,
      id: uuid.v4(),
      createdBy,
      updatedBy: createdBy,
    });
    const data = await this.benefitEntityRepository.save(benefit);
    await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.REWARD_PERK, createBenefitDto.countries, createdBy);
    return data;
  }

  checkValidateLinkType(benefit: any) {
    const { linkType, url, deepLink } = benefit;
    if (linkType && linkType == LINK_TYPE.WEB_LINK && isEmpty(url)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.BENEFIT_NOT_FOUND,
        errorMessage: BENEFIT_MESSAGE_ERROR.URL_NOT_FOUND,
      });
    } else if (linkType && linkType == LINK_TYPE.DEEP_LINK && isEmpty(deepLink)) {
      throw new BadRequestException({
        internalErrorCode: ERROR_CODES.BENEFIT_NOT_FOUND,
        errorMessage: BENEFIT_MESSAGE_ERROR.DEEP_LINK_NOT_FOUND,
      });
    }
    return true;
  }

  findAll(country?: string) {
    const conditionCountry = this.getConditionCountryQuery(country);
    return this.benefitEntityRepository.find({
      where: conditionCountry,
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
      relations: ['partner'],
    });
  }

  async getAllPaginate({ ...options }: any, countryCode?: string) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const skip = (nPage - 1) * nTake;
    const conditionCountry = this.getConditionCountryQuery(countryCode);

    const [benefits, total] = await this.benefitEntityRepository.findAndCount({
      where: conditionCountry,
      order: { sortOrder: 'ASC', createdAt: 'DESC' },
      take: nTake,
      skip,
      relations: ['partner'],
    });

    if (total === 0) {
      return {
        total: 0,
        benefits: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      benefits,
    };
  }

  private getConditionCountryQuery(countryCode: string) {
    let conditionCountry = ' BenefitEntity.deletedAt IS NULL ';
    if (countryCode) {
      if (isUSCountry(countryCode)) {
        conditionCountry += ` AND (BenefitEntity.countries LIKE '%${countryCode}%' OR BenefitEntity.countries IS NULL) `;
      } else {
        conditionCountry += ` AND BenefitEntity.countries LIKE '%${countryCode}%' `;
      }
    } else {
      conditionCountry += ` AND (BenefitEntity.countries LIKE '%US%' OR BenefitEntity.countries IS NULL) `;
    }
    return conditionCountry;
  }

  findOne(id: string) {
    return this.benefitEntityRepository.findOne({
      where: { id },
      relations: ['partner'],
    });
  }

  async update(id: string, updateBenefitDto: UpdateBenefitDto, updatedBy: string) {
    this.checkValidateLinkType(updateBenefitDto);
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const benefit = await this.benefitEntityRepository.findOne({ id });
      if (!benefit) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.BENEFIT_NOT_FOUND,
          errorMessage: BENEFIT_MESSAGE_ERROR.BENEFIT_NOT_FOUND,
        });
      }
      await transactionalEntityManager.update(BenefitEntity, { id: benefit.id }, updateBenefitDto);
      await CommonSubscriber.activityLogCommandUpdate(
        {
          entity: {
            uuid: id,
            updatedBy: updatedBy,
          },
          manager: transactionalEntityManager,
        },
        BENEFIT_ENTITY,
        benefit,
        updateBenefitDto
      );
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.REWARD_PERK, benefit.countries, updatedBy);

      return true;
    });

    return this.benefitEntityRepository.findOne({ id });
  }

  async remove(id: string, updatedBy: string) {
    const res = await getManager().transaction(async (transactionalEntityManager: EntityManager) => {
      const benefit = await this.benefitEntityRepository.findOne({ id });
      if (!benefit) {
        throw new BadRequestException({
          internalErrorCode: ERROR_CODES.BENEFIT_NOT_FOUND,
          errorMessage: BENEFIT_MESSAGE_ERROR.BENEFIT_NOT_FOUND,
        });
      }
      await transactionalEntityManager.softDelete(BenefitEntity, { id });
      await CommonSubscriber.activityLogCommandDelete(
        {
          entity: {
            uuid: id,
            deletedBy: updatedBy,
          },
          manager: transactionalEntityManager,
          databaseEntity: benefit,
        },
        BENEFIT_ENTITY
      );
      await this.apiVersionsService.updateVersion(FEATURE_KEY_VERSION.REWARD_PERK, benefit.countries, updatedBy);
      return { success: true, message: BENEFIT_MESSAGE_ERROR.BENEFIT_DELETED };
    });

    return res;
  }
}

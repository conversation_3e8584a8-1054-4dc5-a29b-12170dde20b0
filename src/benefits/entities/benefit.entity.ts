import { IsArray, IsNotEmpty, IsNumber, IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PartnerEntity } from '../../partners/entities/partner.entity';

@Entity('Benefits')
export class BenefitEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  imageUrl: string;

  @Column()
  partnerId: string;

  @ManyToOne(() => PartnerEntity, (partner) => partner.benefits)
  partner: PartnerEntity;

  @Column()
  url: string;

  @Column()
  disabled: boolean;

  @Column()
  sortOrder: number;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  subTitle: string;

  @Column()
  buttonList: string;

  @Column()
  buttonDetail: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  countries: string;

  @Column()
  linkType: string;

  @Column()
  deepLink: string;
}

export class SortBenefitDto {
  @IsNotEmpty()
  @IsArray()
  sortBenefitList: SortBenefit[];
}

export class SortBenefit {
  @IsUUID()
  id: string;

  @IsNumber()
  @IsNotEmpty()
  sortOrder: number;
}

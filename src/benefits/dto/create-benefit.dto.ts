import { IsBoolean, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString, MaxLength } from 'class-validator';

export class CreateBenefitDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title: string;

  @IsOptional()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  imageUrl: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  partnerId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  url: string;

  @IsOptional()
  @IsString()
  subTitle: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  buttonList: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  buttonDetail: string;

  @IsOptional()
  @IsBoolean()
  disabled: boolean;

  @IsOptional()
  @IsNumber()
  sortOrder: number;

  @IsOptional()
  @IsString()
  countries: string;

  @IsNotEmpty()
  @IsString()
  linkType: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  deepLink: string;
}

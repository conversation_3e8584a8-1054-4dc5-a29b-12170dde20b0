export default {
  port: process.env.PORT,
  stage: process.env.STAGE,
  tag: process.env.TAG,
  cdmEndpoint: process.env.CDM_ENDPOINT,
  myRoundProEndpoint: process.env.MRP_ENDPOINT,
  cmsEndpoint: process.env.CMS_ENDPOINT,
  ebsConfigureEndpoint: process.env.EBS_CONFIGURE_ENDPOINT,
  clubTrackerEndpoint: process.env.CLUB_TRACKER_ENDPOINT,
  clubTrackerGolfEndpoint: process.env.CLUB_TRACKER_GOLF_ENDPOINT,
  ipGeoLocationEndpoint: process.env.IPGEO_LOCATION_ENDPOINT,
  ip2LocationEndpoint: process.env.IP2LOCATION_ENDPOINT,
  googleMapApiEndpoint: process.env.GOOGLE_MAP_API_ENDPOINT,
  deeplinkMyTMEndpoint: process.env.DEEPLINK_MYTM_ENDPOINT,
  clubTrackerInstance: process.env.CLUB_TRACKER_INSTANCE,
  ecomClubTrackerInstance: process.env.ECOM_CLUB_TRACKER_INSTANCE,
  auth0Domain: process.env.AUTH0_DOMAIN,
  auth0ClientId: process.env.AUTH0_CLIENT_ID,
  auth0ApiDomain: process.env.AUTH0_API_DOMAIN,
  auth0ClientSecret: process.env.AUTH0_CLIENT_SECRET,
  auth0Scope: process.env.AUTH0_CLIENT_SCOPE,
  cdmToken: process.env.CDM_TOKEN,
  cmsToken: process.env.CMS_TOKEN,
  jwtSecret: process.env.JWT_SECRET,
  systemId: process.env.DEFAULT_SYSTEM_ID,
  mRPToken: process.env.MRP_TOKEN,
  defaultRegionId: process.env.DEFAULT_REGION_ID,
  defaultCanRegionId: process.env.DEFAULT_CAN_REGION_ID,
  redisHost: process.env.REDIS_HOST,
  redisPort: process.env.REDIS_PORT,
  redisPassword: process.env.REDIS_PASSWORD,
  appleIAPPassword: process.env.APPLE_IPA_PASSWORD,
  googleIAPPublicKeyStrLive: process.env.GOOGLE_IAP_PUBLIC_KEY_STR_LIVE,
  iapSandboxAccountReplacing: process.env.IAP_SANDBOX_ACCOUNT_REPLACING,
  googleAccToken: process.env.GOOGLE_IAP_ACC_TOKEN,
  googleRefToken: process.env.GOOGLE_IAP_REF_TOKEN,
  googleClientID: process.env.GOOGLE_IAP_CLIENT_ID,
  googleClientSecret: process.env.GOOGLE_IAP_CLIENT_SECRET,
  klaviyoPublicToken: process.env.KLAVIYO_PUBLIC_TOKEN,
  klaviyoPrivateToken: process.env.KLAVIYO_PRIVATE_TOKEN,
  klaviyoCAPublicToken: process.env.KLAVIYO_CA_PUBLIC_TOKEN,
  klaviyoCAPrivateToken: process.env.KLAVIYO_CA_PRIVATE_TOKEN,
  klaviyoOtpEvent: process.env.KLAVIYO_OTP_EVENT,
  klaviyoOtpSMSEvent: process.env.KLAVIYO_OTP_SMS_EVENT,
  klaviyoOtpSMSListSubcriberId: process.env.KLAVIYO_OTP_SMS_LIST_SUBCRIBER_ID,
  phoneSmsOtp: process.env.PHONE_SMS_OTP,
  klaviyoEndpoint: process.env.KLAVIYO_ENDPOINT,
  klaviyoPushNotificationAt: process.env.KLAVIYO_PUSH_NOTIFICATION_AT,
  mfeEndpoint: process.env.MFE_ENDPOINT,
  mfeApiKey: process.env.MFE_API_KEY,
  mfeApiUserName: process.env.MFE_API_USERNAME,
  ttbTimeUnit: process.env.TTB_TIME_UNIT,
  ttbReturningTimeUnit: process.env.TTB_RETURNING_TIME_UNIT,
  ttbReturningAmount: process.env.TTB_RETURNING_AMOUNT,
  ttbSpecsMismatchedAmount: process.env.TTB_SPECS_MISMATCHED_AMOUNT,
  ttbSpecsMismatchedUnit: process.env.TTB_SPECS_MISMATCHED_UNIT,
  ttbDeliveryDefaultAmount: process.env.TTB_DELIVERY_DEFAULT_AMOUNT,
  ttbDeliveryDefaultUnit: process.env.TTB_DELIVERY_DEFAULT_UNIT,
  ttbDeepLink: process.env.TTB_DEEP_LINK,
  ttbFTPHost: process.env.TTB_FTP_HOST,
  ttbFTPUser: process.env.TTB_FTP_USER,
  ttbFTPPassword: process.env.TTB_FTP_PASSWORD,
  ttbFTPRootPath: process.env.TTB_FTP_ROOT_PATH,
  ttbUPSFTPHost: process.env.TTB_UPS_FTP_HOST,
  ttbUPSFTPUser: process.env.TTB_UPS_FTP_USER,
  ttbUPSFTPPassword: process.env.TTB_UPS_FTP_PASSWORD,
  ttbUPSFTPRootPath: process.env.TTB_UPS_FTP_ROOT_PATH,
  ttbReturningLabelUrl: process.env.TTB_RETURNING_LABEL_URL,
  ttbPushNotificationAt: process.env.TTB_PUSH_NOTIFICATION_AT,
  ecomEndpoint: process.env.ECOM_ENDPOINT,
  ecomCANEndpoint: process.env.ECOM_CAN_ENDPOINT,
  ecomClientId: process.env.ECOM_CLIENT_ID,
  ecomBMUser: process.env.ECOM_BM_USER,
  ecomBMPassword: process.env.ECOM_BM_PASSWORD,
  ecomBMCdmUser: process.env.ECOM_BM_CDM_USER,
  ecomBMCdmPassword: process.env.ECOM_BM_CDM_PASSWORD,
  ecomBMClientSecret: process.env.ECOM_BM_CLIENT_SECRET,
  ecomMyTMSite: process.env.ECOM_MYTM_SITE,
  ecomMyTMCASite: process.env.ECOM_MYTM_CA_SITE,
  ecomCategoryId: process.env.ECOM_CATEGORY_ID,
  ecomTeamTMSite: process.env.ECOM_TEAM_TM_SITE,
  ecomTeamTMCASite: process.env.ECOM_TEAM_TM_CA_SITE,
  ecomTeamTMCategoryId: process.env.ECOM_TEAM_TM_CATEGORY_ID,
  ecomTeamTMShopCategoryId: process.env.ECOM_TEAM_TM_SHOP_CATEGORY_ID,
  ecomTeamTMHomeProductCategoryId: process.env.ECOM_TEAM_TM_HOME_PRODUCT_CATEGORY_ID,
  ecomTeamTMRewardsCategoryId: process.env.ECOM_TEAM_TM_REWARDS_CATEGORY_ID,
  deEndpoint: process.env.DE_ENDPOINT,
  deClientId: process.env.DE_CLIENT_ID,
  siEndpoint: process.env.SI_ENDPOINT,
  siJWTSecret: process.env.SI_JWT_SECRET,
  sentryDNS: process.env.SENTRY_DNS,
  sentryEnv: process.env.SENTRY_ENV,
  sentryRelease: process.env.SENTRY_RELEASE,
  forceProPlusPlanForEmails: process.env.FORCE_PRO_PLUS_PLAN_FOR_EMAILS,
  forceProPlusPlanForAllUsers: process.env.FORCE_PRO_PLUS_PLAN_FOR_ALL_USERS,
  disableForceProPlusPlanForEmails: process.env.DISABLE_FORCE_PRO_PLUS_PLAN_FOR_EMAILS,
  translations: process.env.TRANSLATIONS,
  homeExploreRandomSortOrderCron: process.env.HOME_EXPLORER_RANDOM_SORT_ORDER_CRON,
  cdmUsername: process.env.CDM_USERNAME,
  cdmPassword: process.env.CDM_PASSWORD,
  clubRecommenderEndpoint: process.env.CLUB_RECOMMENDER_ENDPOINT,
  insightEndpoint: process.env.INSIGHT_ENDPOINT,
  tourTrashCtaLink: process.env.TOUR_TRASH_CTA_LINK,
  tourTrashPushNotificationAt: process.env.TOUR_TRASH_PUSH_NOTIFICATION_AT,
  adminEmail: process.env.ADMIN_EMAIL,
  userEmail: process.env.USER_EMAIL,
  adminPortalEndpoint: process.env.ADMIN_PORTAL_ENDPOINT,
  SeventeentrackToken: process.env.SEVENTEENTRACK_TOKEN,
  replacePlanFromAccessCode: process.env.REPLACE_PLAN_FROM_ACCESS_CODE,
  tosEndpoint: process.env.TOS_ENDPOINT,
  cronJobHandlers: process.env.CRON_JOB_HANDLERS,
  arccosEndpoint: process.env.ARCCOS_ENDPOINT,
  arccosAppId: process.env.ARCCOS_APP_ID,
  arccosSecretKey: process.env.ARCCOS_SECRET_KEY,
  secretOTP: process.env.SECRET_OTP,
  prefetchContentQueueLimits: process.env.PREFETCH_CONTENT_QUEUE_LIMITS,
  prefetchContentProcessorTimeout: process.env.PREFETCH_CONTENT_PROCESSOR_TIMEOUT,
  cleanUpUsersSecretKey: process.env.CLEAN_UP_USERS_SECRET_KEY,
  appleIssuerId: process.env.APPLE_ISSUER_ID,
  appleKeyId: process.env.APPLE_KEY_ID,
  appStoreEndpoint: process.env.APP_STORE_ENDPOINT,
  appleVendorNumber: process.env.APPLE_VENDOR_NUMBER,
  applePreviousNumberDaysToDownload: process.env.APPLE_PREVIOUS_NUMBER_DAYS_TO_DOWNLOAD,
  appleAuthKeyFileName: process.env.APPLE_AUTH_KEY_FILE_NAME,
  googleBucketName: process.env.GOOGLE_BUCKET_NAME,
  googlePackageName: process.env.GOOGLE_PACKAGE_NAME,
  randomDrillVideosTTL: process.env.RANDOM_DRILL_VIDEOS_TTL,
  blackListDomainEmail: process.env.BLACK_LIST_DOMAIN_EMAIL,
  prefetchNotificationQueueLimits: process.env.PREFETCH_NOTIFICATION_QUEUE_LIMITS,
  twilioAccountSID: process.env.TWILIO_ACCOUNT_SID,
  twilioToken: process.env.TWILIO_TOKEN,
  twilioMessagigServiceId: process.env.TWILIO_MESSAGING_SERVICE_ID,
  twilioVerifyServiceId: process.env.TWILIO_VERIFY_SERVICE_ID,
  httpBasicUserName: process.env.HTTP_BASIC_USER_NAME,
  httpBasicPassword: process.env.HTTP_BASIC_PASSWORD,
  emailReceivedEcomDowngrade: process.env.EMAIL_RECEIVED_ECOM_DOWNGRADE,
  tourTrashPushNotifyNonWinner: process.env.TOUR_TRASH_PUSH_NOTIFY_NON_WINNER,
  forceCanExclProductDrops: process.env.FORCE_CAN_EXCL_PRODUCT_DROPS,
  azureBlobSASToken: process.env.AZURE_BLOB_SAS_TOKEN,
  azureBlobContainer: process.env.AZURE_BLOB_CONTAINER,
  azureBlobAccountName: process.env.AZURE_BLOB_ACCOUNT_NAME,
  azureCDN: process.env.AZURE_CDN,
  upsServiceURL: process.env.UPS_SERVICE_URL,
  upsUserName: process.env.UPS_USER_NAME,
  upsPassword: process.env.UPS_PASSWORD,
  upsToken: process.env.UPS_TOKEN,
  upsPaymentType: process.env.UPS_PAYMENT_TYPE,
  upsPaymentAccountNumber: process.env.UPS_PAYMENT_ACCOUNT_NUMBER,
  upsShipToName: process.env.UPS_SHIP_TO_NAME,
  upsShipToAttentionName: process.env.UPS_SHIP_TO_ATTENTION_NAME,
  upsShipToPhone: process.env.UPS_SHIP_TO_PHONE,
  upsShipToAddressLine: process.env.UPS_SHIP_TO_ADDRESS_LINE,
  upsShipToAddressCity: process.env.UPS_SHIP_TO_ADDRESS_CITY,
  upsShipToAddressState: process.env.UPS_SHIP_TO_ADDRESS_STATE,
  upsShipToAddressPostalCode: process.env.UPS_SHIP_TO_ADDRESS_POSTAL_CODE,
  upsShipToAddressCountryCode: process.env.UPS_SHIP_TO_ADDRESS_COUNTRY_CODE,
  upsShipperName: process.env.UPS_SHIPPER_NAME,
  upsShipperAttentionName: process.env.UPS_SHIPPER_ATTENTION_NAME,
  upsShipperPhone: process.env.UPS_SHIPPER_PHONE,
  upsShipperNumber: process.env.UPS_SHIPPER_NUMBER,
  upsShipperAddressLine: process.env.UPS_SHIPPER_ADDRESS_LINE,
  upsShipperAddressCity: process.env.UPS_SHIPPER_ADDRESS_CITY,
  upsShipperAddressState: process.env.UPS_SHIPPER_ADDRESS_STATE,
  upsShipperAddressPostalCode: process.env.UPS_SHIPPER_ADDRESS_POSTAL_CODE,
  upsShipperAddressCountryCode: process.env.UPS_SHIPPER_ADDRESS_COUNTRY_CODE,
  upsShipmentReturnService: process.env.UPS_SHIPMENT_RETURN_SERVICE,
  upsShipmentService: process.env.UPS_SHIPMENT_SERVICE,
  upsShipmentPackingCode: process.env.UPS_SHIPMENT_PACKING_CODE,
  secretTTBDataKey: process.env.SECRET_TTB_DATA_KEY,
  secretApiIpGeoLocationKey: process.env.SECRET_API_IPGEO_LOCATION_KEY,
  googleMapApiKey: process.env.GOOGLE_MAP_API_KEY,
  ip2LocationKey: process.env.IP2LOCATION_KEY,
  ttbReturnLabelService: process.env.TTB_RETURN_LABEL_SERVICE,
  sendGridApiKey: process.env.SEND_GRID_API_KEY,
  sendGridFrom: process.env.SEND_GRID_FROM,
  annexEndpoint: process.env.ANNEXCLOUD_ENDPOINT,
  annexUSClientId: process.env.ANNEXTCLOUND_US_CLIENT_ID,
  annexUSSecret: process.env.ANNEXTCLOUND_US_SECRET,
  annexCAClientId: process.env.ANNEXTCLOUND_CA_CLIENT_ID,
  annexCASecret: process.env.ANNEXTCLOUND_CA_SECRET,
};

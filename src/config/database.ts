export default {
  type: 'mssql',
  host: process.env.DATABASE_HOST,
  port: parseInt(process.env.DATABASE_PORT, 10),
  username: process.env.DATABASE_USERNAME,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE,
  entities: ['dist/**/*.entity{.ts,.js}'],
  migrations: ['dist/migrations/*.js', __dirname + '../migrations/*.{ts,js}'],
  subscribers: ['dist/subscriber/*.js', __dirname + '../subscriber/*.{ts,js}'],
  options: {
    encrypt: true,
    enableArithAbort: true,
    requestTimeout: 600000,
  },
  cli: {
    migrationsDir: 'src/migrations',
    subscribersDir: 'src/subscriber',
  },
  synchronize: false,
  migrationsRun: true,
  logging: false,
  migrationsTransactionMode: 'each',
};

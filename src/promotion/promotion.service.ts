import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { CmsService } from 'src/cms/cms.service';
import { EcomService } from 'src/ecom/ecom.service';
import { TourTrashStatus } from 'src/tourtrash/entities/tour-trash.entity';
import { TourTrashService } from 'src/tourtrash/tour-trash.service';
import { isUSCountry } from 'src/utils/transform';
import { promotionStatusEnum } from './dto/request.dto';
import { PromotionImagesEntity } from './entities/promotion-image.entity';
import { PromotionEntity } from './entities/promotion.entity';

@Injectable()
export class PromotionService {
  constructor(
    @InjectRepository(PromotionEntity)
    private readonly promotionRepo: Repository<PromotionEntity>,
    private cmsService: CmsService,
    private tourTrashService: TourTrashService,
    @Inject(forwardRef(() => EcomService)) private ecomService: EcomService,
    @InjectRepository(PromotionImagesEntity)
    private readonly promotionImageRepo: Repository<PromotionImagesEntity>
  ) {}

  async create(userId: string, { images, ...data }: any) {
    const id = v4();
    const promotion = await this.promotionRepo.create({
      id,
      ...data,
      createdBy: userId,
    });

    const result = await this.promotionRepo.save(promotion);
    await Promise.all(
      images.map((image, index) =>
        this.promotionImageRepo.save({
          id: v4(),
          imageUrl: image,
          promotionId: id,
          sort: index,
        })
      )
    );

    return result;
  }

  async update(id: string, userId: string, { images, ...data }: any) {
    await this.promotionRepo.update(id, {
      ...data,
      updatedBy: userId,
    });

    if (images) {
      await this.promotionImageRepo.delete({ promotionId: id });
      await Promise.all(
        images.map((image, index) =>
          this.promotionImageRepo.save({
            id: v4(),
            imageUrl: image,
            promotionId: id,
            sort: index,
          })
        )
      );
    }

    return { success: true, message: 'Promotion successfully updated.' };
  }

  async getOne(conditions: object) {
    return await this.promotionRepo.findOne(conditions);
  }

  async getAll({ ...options }: any) {
    const nTake = options?.take ? parseInt(options?.take, 10) : 10;
    const nPage = options?.page ? parseInt(options?.page, 10) : 1;
    const query = this.promotionRepo.createQueryBuilder('p').where({});
    if (options?.status) {
      query.andWhere('p.status IN (:...statusParams)', { statusParams: options?.status.split(',') });
    }
    if (options?.feature) {
      query.andWhere('p.feature IN (:...featureParams)', { featureParams: options?.feature.split(',') });
    }
    if (options?.country) {
      const country = options?.country?.trim().toUpperCase();
      if (isUSCountry(country)) {
        query.andWhere(`(p.countries LIKE '%${country}%' OR p.countries IS NULL)`);
      } else {
        query.andWhere(`p.countries LIKE '%${country}%'`);
      }
    } else {
      query.andWhere(`(p.countries LIKE '%US%' OR p.countries IS NULL)`);
    }
    const [promotion, total] = await query
      .leftJoinAndSelect('p.images', 'PromotionImages')
      .orderBy('p.status', 'ASC')
      .addOrderBy('p.createdAt', 'DESC')
      .addOrderBy('PromotionImages.sort', 'ASC')
      .take(nTake)
      .skip((nPage - 1) * nTake)
      .getManyAndCount();
    if (total === 0) {
      return {
        total: 0,
        promotion: [],
      };
    }

    return {
      total,
      take: nTake,
      page: nPage,
      promotion,
    };
  }

  async getPromotion(userId: string, { take, ...options }: any) {
    let queryRegion = '1=1';
    let country = null;
    if (options?.country) {
      country = options?.country?.trim().toUpperCase();
      if (isUSCountry(country)) {
        queryRegion = `(p.countries LIKE '%${country}%' OR p.countries IS NULL)`;
      } else {
        queryRegion = `p.countries LIKE '%${country}%'`;
      }
      delete options.country;
    } else {
      queryRegion = `(p.countries LIKE '%US%' OR p.countries IS NULL)`;
    }
    const promotions = await this.promotionRepo
      .createQueryBuilder('p')
      .where({ status: promotionStatusEnum.ACTIVE, show: true, ...options })
      .andWhere(queryRegion)
      .innerJoinAndSelect('p.images', 'PromotionImages')
      .select([
        'p.id',
        'p.title',
        'p.description',
        'p.videoType',
        'p.videoId',
        'p.ctaText',
        'p.ctaLink',
        'p.feature',
        'p.status',
        'p.show',
        'p.category',
        'p.textColor',
        'p.colorOpacity',
        'p.buttonAlign',
        'p.textAlign',
        'p.countries',
        'PromotionImages.imageUrl',
      ])
      .orderBy('PromotionImages.sort', 'ASC')
      .getMany();

    return await Promise.all(
      promotions.map(async (promotion) => {
        const extraData = await this.requestContent(promotion?.ctaLink, userId, country);
        return {
          ...promotion,
          extraData,
          contentFormat: extraData?.contentFormat,
        };
      })
    );
  }

  async requestContent(ctaLink: string, userId: string, region?: string) {
    if (!ctaLink || ctaLink.includes('http')) {
      return;
    }
    const listDetections = ctaLink.split('/').filter((v) => !!v || v !== '');
    // format CTALink content: ['content', 'videos', 'id'];
    if (listDetections.includes('content')) {
      if (listDetections.length >= 3) {
        const contentFormat = listDetections[1];
        const content: any = await this.cmsService.getCmsContent(Number(listDetections[2]), listDetections[1]);
        return {
          ...content?.data,
          contentFormat,
        };
      }
    }

    // format CTALink Product: ['product', 'id'];
    if (listDetections.includes('product')) {
      if (listDetections.length >= 2) {
        const productDetail = await this.ecomService.getProduct(listDetections[1], region);
        return productDetail;
      }
    }

    // format CTALink TourTrash: ['tourtrash', 'id'];
    if (listDetections.includes('tourtrash') || listDetections.includes('tourTrash')) {
      if (listDetections.length >= 2) {
        const tourTrashDetail = await this.tourTrashService.getTourTrashById(listDetections[1], [
          'product',
          'product.images',
          'userTourTrashes',
        ]);
        let isJoined: boolean | undefined;
        if (tourTrashDetail?.status === TourTrashStatus.ACTIVE) {
          isJoined =
            tourTrashDetail.userTourTrashes.length > 0 &&
            tourTrashDetail.userTourTrashes.findIndex(
              (element) => element.userId.toUpperCase() === userId.toUpperCase()
            ) >= 0;
        }
        const result = {
          ...tourTrashDetail,
          joined: isJoined !== undefined ? isJoined : undefined,
        };
        delete result.userTourTrashes;

        return result;
      }
    }
  }

  async delete(id: string) {
    await this.promotionRepo.softDelete(id);
    return { success: true, message: 'Promotion successfully deleted.' };
  }

  async createPromotionImage(userId: string, data: any) {
    const promotionImage = await this.promotionImageRepo.create({
      ...data,
      id: v4(),
      createdBy: userId,
    });
    return await this.promotionImageRepo.save(promotionImage);
  }

  async updatePromotionImage(id: string, userId: string, data: any) {
    await this.promotionImageRepo.update(id, {
      ...data,
      updatedBy: userId,
    });

    return { success: true, message: 'Promotion Image successfully updated.' };
  }

  async getOnePromotionImage(conditions: object) {
    return await this.promotionImageRepo.findOne(conditions);
  }

  async getAllPromotionImage(take = 10, page = 1, promotionId?: string) {
    const query = this.promotionImageRepo.createQueryBuilder('c');
    if (promotionId) {
      query.where({
        promotionId,
      });
    }

    const [promotionImages, total] = await query
      .orderBy({
        'c.createdAt': 'DESC',
      })
      .take(take)
      .skip((page - 1) * take)
      .getManyAndCount();

    if (total === 0) {
      return {
        total: 0,
        images: [],
      };
    }

    return {
      total,
      take,
      page,
      images: promotionImages,
    };
  }

  async deletePromotionImage(id: string) {
    await this.promotionImageRepo.delete(id);
    return { success: true, message: 'Promotion Image successfully deleted.' };
  }
}

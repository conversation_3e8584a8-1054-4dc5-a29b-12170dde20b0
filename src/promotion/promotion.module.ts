import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CmsService } from 'src/cms/cms.service';
import { SharedModule } from 'src/shared/shared.module';
import { UserTourTrashEntity } from 'src/tourtrash/entities/user-tour-trash.entity';
import { TourTrashService } from 'src/tourtrash/tour-trash.service';
import { PromotionImagesEntity } from './entities/promotion-image.entity';
import { PromotionEntity } from './entities/promotion.entity';
import { PromotionController } from './promotion.controller';
import { PromotionService } from './promotion.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([PromotionEntity, PromotionImagesEntity, UserTourTrashEntity])],
  controllers: [PromotionController],
  providers: [PromotionService, CmsService, TourTrashService],
})
export class PromotionModule {}

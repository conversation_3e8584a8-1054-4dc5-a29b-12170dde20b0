import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { Request } from 'express';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { BaseRequest } from 'src/types/core';
import { ERROR_CODES } from 'src/utils/errors';
import { PromotionDto, PromotionImageDto, UpdatePromotionDto, UpdatePromotionImageDto } from './dto/request.dto';
import { PromotionService } from './promotion.service';

@Controller('promotion')
@UseGuards(ClientGuard)
@UseGuards(AuthGuard)
export class PromotionController {
  constructor(private promotionService: PromotionService) {}

  @Get('/')
  async getPromotion(@Req() req: any) {
    if (req?.country) {
      req.query['country'] = req.country;
    }
    return await this.promotionService.getPromotion(req.user.uid, req.query);
  }

  @Get('get-all')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async getAll(@Req() req: Request) {
    return await this.promotionService.getAll(req.query);
  }

  @Post()
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async create(@Req() request: BaseRequest, @Body() promotionDto: PromotionDto) {
    return await this.promotionService.create(request.user.uid, promotionDto);
  }

  @Put('/:id')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async update(@Param('id') id: string, @Req() request: BaseRequest, @Body() promotionDto: UpdatePromotionDto) {
    const promotion = await this.promotionService.getOne({ id });
    if (!promotion) {
      throw new BadRequestException({
        errorMessage: 'Promotion not found!',
        internalErrorCode: ERROR_CODES.PROMOTION_NOT_FOUND,
      });
    }
    return await this.promotionService.update(id, request.user.uid, promotionDto);
  }

  @Delete('/:id')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async delete(@Param('id') id: string) {
    const promotion = await this.promotionService.getOne({ id });
    if (!promotion) {
      throw new BadRequestException({
        errorMessage: 'Promotion not found!',
        internalErrorCode: ERROR_CODES.PROMOTION_NOT_FOUND,
      });
    }

    return await this.promotionService.delete(id);
  }

  @Get('/images')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async getAllPromotionImage(@Query('take') take, @Query('page') page, @Query('promotionId') promotionId) {
    return await this.promotionService.getAllPromotionImage(take, page, promotionId);
  }

  @Post('/images')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async createPromotionImage(@Req() request: BaseRequest, @Body() promotionImgDto: PromotionImageDto) {
    return await this.promotionService.createPromotionImage(request.user.uid, promotionImgDto);
  }

  @Get('/images/:id')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async getPromotionImage(@Param('id') id: string) {
    const promotionImg = await this.promotionService.getOnePromotionImage({ id });
    if (!promotionImg) {
      throw new BadRequestException({
        errorMessage: 'Promotion image not found!',
        internalErrorCode: ERROR_CODES.PROMOTION_IMAGE_NOT_FOUND,
      });
    }
    return promotionImg;
  }

  @Put('/images/:id')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async updatePromotionImg(
    @Param('id') id: string,
    @Req() request: BaseRequest,
    @Body() promotionImgDto: UpdatePromotionImageDto
  ) {
    const promotionImg = await this.promotionService.getOnePromotionImage({ id });
    if (!promotionImg) {
      throw new BadRequestException({
        errorMessage: 'Promotion image not found!',
        internalErrorCode: ERROR_CODES.PROMOTION_IMAGE_NOT_FOUND,
      });
    }

    return await this.promotionService.updatePromotionImage(id, request.user.uid, promotionImgDto);
  }

  @Delete('/images/:id')
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  async deletePromotionImg(@Param('id') id: string) {
    const promotionImg = await this.promotionService.getOnePromotionImage({ id });
    if (!promotionImg) {
      throw new BadRequestException({
        errorMessage: 'Promotion image not found!',
        internalErrorCode: ERROR_CODES.PROMOTION_IMAGE_NOT_FOUND,
      });
    }

    return await this.promotionService.deletePromotionImage(id);
  }

  @Get('/:id')
  async get(@Param('id') id: string) {
    const promotion = await this.promotionService.getOne({ id });
    if (!promotion) {
      throw new BadRequestException({
        errorMessage: 'Promotion not found!',
        internalErrorCode: ERROR_CODES.PROMOTION_NOT_FOUND,
      });
    }
    return promotion;
  }
}

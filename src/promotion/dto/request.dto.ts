import { PartialType } from '@nestjs/mapped-types';
import { IsArray, IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';

export enum promotionStatusEnum {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  CLOSED = 'CLOSED',
}
export class PromotionDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  description: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  videoType: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  videoId: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  ctaText: string;

  @IsOptional()
  @IsString()
  @MaxLength(255)
  ctaLink: string;

  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  feature: string;

  @IsOptional()
  @IsEnum(promotionStatusEnum)
  status: string;

  @IsOptional()
  @IsBoolean()
  show: string;

  @IsNotEmpty()
  @IsArray()
  images: string[];

  @IsOptional()
  @IsString()
  @MaxLength(255)
  category: string;

  @IsOptional()
  @IsString()
  textColor: string;

  @IsOptional()
  @IsNumber()
  colorOpacity: number;

  @IsOptional()
  @IsString()
  buttonAlign: string;

  @IsOptional()
  @IsString()
  textAlign: string;

  @IsString()
  @IsOptional()
  countries: string;
}

export class UpdatePromotionDto extends PartialType(PromotionDto) {}

export class PromotionImageDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  imageUrl: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  promotionId: string;
}

export class UpdatePromotionImageDto extends PartialType(PromotionImageDto) {}

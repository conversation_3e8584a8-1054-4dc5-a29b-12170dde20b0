import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  OneToMany,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PromotionImagesEntity } from './promotion-image.entity';

@Entity('Promotions')
export class PromotionEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  videoType: string;

  @Column()
  videoId: string;

  @Column()
  ctaText: string;

  @Column()
  ctaLink: string;

  @Column()
  feature: string;

  @Column()
  status: string;

  @Column()
  show: number;

  @OneToMany(() => PromotionImagesEntity, (images) => images.promotion, { onUpdate: 'CASCADE', onDelete: 'CASCADE' })
  @JoinColumn({ name: 'id', referencedColumnName: 'promotionId' })
  images: PromotionImagesEntity['imageUrl'];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;

  @Column()
  category: string;

  @Column()
  textColor: string;

  @Column('decimal', { precision: 6, scale: 2 })
  colorOpacity: number;

  @Column()
  textAlign: string;

  @Column()
  buttonAlign: string;

  @Column()
  countries: string;
}

import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PromotionEntity } from './promotion.entity';

@Entity('PromotionImages')
export class PromotionImagesEntity {
  @PrimaryColumn()
  id: string;

  @Column()
  imageUrl: string;

  @Column()
  promotionId: string;

  @ManyToOne(() => PromotionEntity, (promotion) => promotion.images)
  @JoinColumn({ name: 'promotionId', referencedColumnName: 'id' })
  promotion: PromotionEntity;

  @Column()
  sort: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  createdBy: string;

  @Column()
  updatedBy: string;

  @Column()
  deletedBy: string;
}

import { Module } from '@nestjs/common';
import { ClientService } from 'src/client/client.service';
import { SharedModule } from 'src/shared/shared.module';
import { LoggingController } from './logging.controller';
import { LoggingService } from './logging.service';

@Module({
  imports: [SharedModule],
  controllers: [LoggingController],
  providers: [LoggingService, ClientService],
})
export class LoggingModule {}

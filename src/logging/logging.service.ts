import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as _ from 'lodash';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { CdmService } from 'src/cdm/cdm.service';
import { SubscriptionEventLog } from 'src/payment/entities/subscription-event-log.entity';
import { ActivityLogEntity } from 'src/subscriber/entities/activity-logs.entity';
import { LogEntity } from './entities/log.entity';

@Injectable()
export class LoggingService {
  constructor(
    @Inject(forwardRef(() => CdmService)) private cdmService: CdmService,
    @InjectRepository(LogEntity) private readonly logRepo: Repository<LogEntity>,
    @InjectRepository(SubscriptionEventLog) private readonly subscriptionEventLogRepo: Repository<SubscriptionEventLog>,
    @InjectRepository(ActivityLogEntity) private readonly activityLogRepo: Repository<ActivityLogEntity>
  ) {}

  async save(data: any) {
    // delete data.unified_receipt;
    let event = data.event;
    if (!event) {
      event = data.notification_type ? `APPLE_${data.notification_type}` : null;
    }
    if (!event) {
      event = data.packageNameAndroid ? 'ANDROID_PURCHASE' : 'UNKNOWN';
    }
    delete data.event;
    let log = await this.logRepo.create({
      id: v4(),
      event,
      data: JSON.stringify(data),
    });
    log = await this.logRepo.save(log);
    return { ...log, data: JSON.parse(log.data) };
  }

  async get(event: string, take = 10, skip = 0) {
    const conditions: any = {};
    if (event) {
      conditions.event = event;
    }
    const logs = await this.logRepo.find({
      where: conditions,
      order: {
        createdAt: 'DESC',
      },
      skip,
      take,
    });
    return logs.map((log) => ({ ...log, data: JSON.parse(log.data) }));
  }

  async getAllSubscriptionLogs(event: string, take = 10, skip = 0, userId = null) {
    const conditions: any = {};
    if (event) {
      conditions.eventName = event;
    }
    if (userId) {
      conditions.userId = userId;
    }
    const logs = await this.subscriptionEventLogRepo.find({
      where: conditions,
      order: {
        createdAt: 'DESC',
      },
      skip,
      take,
    });
    return logs.map((log) => ({
      ...log,
      notifyData: JSON.parse(log.notifyData),
      receiptData: JSON.parse(log.receiptData),
    }));
  }

  async clear() {
    return this.logRepo.delete({});
  }

  async getAllActivityLogs(take = 10, page = 1, optionSearch: { module: string; itemId: string; modifiedBy: string }) {
    const conditions: any = {};
    if (optionSearch?.module) {
      conditions.module = optionSearch?.module;
    }

    if (optionSearch?.itemId) {
      conditions.itemId = optionSearch?.itemId;
    }

    if (optionSearch?.modifiedBy) {
      conditions.modifiedBy = optionSearch?.modifiedBy;
    }

    const [logs, total] = await this.activityLogRepo.findAndCount({
      where: conditions,
      order: {
        modifiedAt: 'DESC',
      },
      relations: ['user'],
      skip: (Number(page) - 1) * Number(take),
      take,
    });

    if (logs.length === 0) {
      return {
        total: 0,
        data: [],
      };
    }

    const emails = logs.map((val) => {
      if (val.user) {
        return val.user.email;
      }
      return null;
    });

    const users = await this.cdmService.getConsumerByEmails(emails);

    return {
      total,
      take: Number(take),
      page: Number(page),
      data: logs.map((log) => {
        const user =
          users &&
          users.length > 0 &&
          users.find((x: any) => x.id.toUpperCase() === (log?.user?.cdmUID || '').toUpperCase());
        const result = {
          ...log,
          prevColumnValue: JSON.parse(log.prevColumnValue),
          nextColumnValue: JSON.parse(log.nextColumnValue),
          fullName: this.getFullNameFromUser(user),
          email: log?.user?.email,
        };
        delete result.user;

        return result;
      }),
    };
  }

  private getFullNameFromUser(user: any) {
    let fullName = null;
    if (user && (!_.isNil(user?.firstName) || !_.isNil(user?.lastName))) {
      fullName = `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
    }

    return fullName;
  }
}

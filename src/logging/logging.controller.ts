import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { AccessedRoles, Role } from 'src/auth/roles.decorator';
import { AccessedClients } from 'src/client/clients.decorator';
import { AuthGuard } from 'src/guards/auth.guard';
import { CLIENTS, ClientGuard } from 'src/guards/client.guard';
import { LoggingService } from './logging.service';

@Controller('logging')
export class LoggingController {
  constructor(private readonly loggingService: LoggingService) {}

  @Post('save')
  async save(@Body() data: any): Promise<any> {
    return await this.loggingService.save(data);
  }

  @Post('clear')
  async clear(): Promise<any> {
    return await this.loggingService.clear();
  }

  @Get('all')
  async get(@Query('take') take, @Query('skip') skip, @Query('event') event): Promise<any> {
    return await this.loggingService.get(event, take, skip);
  }

  @Get('subscriptions/all')
  async getAllSubscriptionLogs(
    @Query('take') take,
    @Query('skip') skip,
    @Query('event') event,
    @Query('userId') userId
  ): Promise<any> {
    return await this.loggingService.getAllSubscriptionLogs(event, take, skip, userId);
  }

  @Get('activity/all')
  @AccessedClients(CLIENTS.ADMIN_PORTAL)
  @UseGuards(ClientGuard)
  @AccessedRoles(Role.ADMIN, Role.SUPER_ADMIN)
  @UseGuards(AuthGuard)
  async getAllActivityLogs(
    @Query('take') take,
    @Query('page') page,
    @Query('module') module,
    @Query('itemId') itemId,
    @Query('modifiedBy') modifiedBy
  ): Promise<any> {
    return await this.loggingService.getAllActivityLogs(take, page, { module, itemId, modifiedBy });
  }
}

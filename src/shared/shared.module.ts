import { BullModule } from '@nestjs/bull';
import { CACHE_MANAGER, CacheModule, Inject, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as redisStore from 'cache-manager-ioredis';
import { ConfigService } from 'nestjs-config';
import { CountryEntity } from 'src/admin/entities/country.entity';
import { FeatureCountryEntity } from 'src/admin/entities/feature-country.entity';
import { ImageEntity } from 'src/admin/entities/image.entity';
import { TextEntity } from 'src/admin/entities/text.entity';
import { UserPermissionsEntity } from 'src/auth/entities/user-permissions.entity';
import { NotificationEntity } from 'src/notification/entities/notification.entity';
import { UserNotificationEntity } from 'src/notification/entities/user-notification.entity';
import { NOTIFICATION_QUEUE_NAME } from 'src/notification/notification.constants';
import { NotificationService } from 'src/notification/notification.service';
import { PermissionEntity } from 'src/permission/entities/permission.entity';
import { PermissionService } from 'src/permission/permission.service';
import { PlayService } from 'src/play/play.service';
import { AccessCodeService } from '../accesscode/access-code.service';
import { AccessCodeEntity } from '../accesscode/entities/access-code.entity';
import { ExtensionUserEntity } from '../accesscode/entities/extension-user.entity';
import { ExternalToolEntity } from '../admin/entities/external-tool.entity';
import { HomeWidgetEntity } from '../admin/entities/home-widget.entity';
import { LaunchFeatureEntity } from '../admin/entities/launch-feature.entity';
import { LaunchFeatureService } from '../admin/launch-feature.service';
import { ApiVersionsService } from '../api-versions/api-version.service';
import { ApiVersionsEntity } from '../api-versions/entities/api-versions.entity';
import { ArccosService } from '../arccos/arccos.service';
import { AuthService } from '../auth/auth.service';
import { BlacklistedTokenEntity } from '../auth/entities/blacklisted-token.entity';
import { UserBlackListEntity } from '../auth/entities/user-black-list.entity';
import { UserCourseLogsEntity } from '../auth/entities/user-course-logs.entity';
import { UserNotifyAdminEntity } from '../auth/entities/user-notify-admin.entity';
import { UserOnboardingStep } from '../auth/entities/user-onboarding-step.entity';
import { UserOtp } from '../auth/entities/user-otp.entity';
import { UserEntity } from '../auth/entities/user.entity';
import { OTPService } from '../auth/otp.service';
import { CdmService } from '../cdm/cdm.service';
import { ClientService } from '../client/client.service';
import { ClientEntity } from '../client/entities/client.entity';
import { ContentCacheService } from '../content/content.cache.service';
import { DeService } from '../content/de.service';
import { ContentTagThumbnailEntity } from '../content/entities/content-tag-thumbnail.entity';
import { DismissedContentsEntity } from '../content/entities/dismissed-content.entity';
import { FavoriteContentEntity } from '../content/entities/favorite-content.entity';
import { ViewedContentEntity } from '../content/entities/viewed-content.entity';
import { EcomService } from '../ecom/ecom.service';
import { EcomNotificationEntity } from '../ecom/entities/ecom-notification.entity';
import { MemberShopOrderEntity, MemberShopOrderTrackingNumberEntity } from '../ecom/entities/member-shop.entity';
import { GameProfile } from '../game-profile/entities/game-profile.entity';
import { KlaviyoModule } from '../klaviyo/klaviyo.module';
import { LogEntity } from '../logging/entities/log.entity';
import { LoggingService } from '../logging/logging.service';
import { AnnexService } from '../loyalty/annex.service';
import { LoyaltyActionEntity } from '../loyalty/entities/loyalty-action.entity';
import { LoyaltyService } from '../loyalty/loyalty.service';
import { MailModule } from '../mail/mail.module';
import { MfeService } from '../mfe/mfe.service';
import { MiscService } from '../misc/misc.service';
import { MrpService } from '../mrp/mrp.service';
import { SubscriptionEventLog } from '../payment/entities/subscription-event-log.entity';
import { SubscriptionEntity } from '../payment/entities/subscription.entity';
import { ActivityLogEntity } from '../subscriber/entities/activity-logs.entity';
import { ResponseEntity } from '../survey/entities/response.entity';
import { ProductImageEntity } from '../tourtrash/entities/product-image.entity';
import { ProductEntity } from '../tourtrash/entities/product.entity';
import { TourTrashEntity } from '../tourtrash/entities/tour-trash.entity';
import { UserTourTrashEntity } from '../tourtrash/entities/user-tour-trash.entity';
import { TTBConfigThresholdEntity } from '../ttb/entities/ttb-config-threshold.entity';
import { TTBFraudInfoEntity } from '../ttb/entities/ttb-fraud-info.entity';
import { TTBFraudEntity } from '../ttb/entities/ttb-fraud.entity';
import { TTBLogThresholdEntity } from '../ttb/entities/ttb-log-threshold.entity';
import { TTBPushEntity } from '../ttb/entities/ttb-push.entity';
import { TTBEntity } from '../ttb/entities/ttb.entity';
import { TTBService } from '../ttb/ttb.service';
import { UserProfileService } from '../user-profile/user-profile.service';
import { UserReferralEntity } from '../user-referral/entity/user-referral.entity';
import { UserReferralService } from '../user-referral/user-referral.service';
import redisInstance from '../utils/redis';
import { SeventeentrackService } from './services/seventeentrack.service';
import { TwilioService } from './services/twilio.service';

function getCacheModuleConfig() {
  const options: any = {
    store: redisStore,
    redisInstance,
  };
  return options;
}

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      UserBlackListEntity,
      UserNotifyAdminEntity,
      ClientEntity,
      BlacklistedTokenEntity,
      UserOtp,
      UserOnboardingStep,
      EcomNotificationEntity,
      LogEntity,
      HomeWidgetEntity,
      SubscriptionEntity,
      SubscriptionEventLog,
      ActivityLogEntity,
      AccessCodeEntity,
      ExtensionUserEntity,
      LaunchFeatureEntity,
      MemberShopOrderEntity,
      FavoriteContentEntity,
      ViewedContentEntity,
      DismissedContentsEntity,
      ContentTagThumbnailEntity,
      TourTrashEntity,
      ProductEntity,
      ProductImageEntity,
      ExternalToolEntity,
      TTBEntity,
      TTBFraudEntity,
      TTBFraudInfoEntity,
      TTBConfigThresholdEntity,
      TTBLogThresholdEntity,
      TTBPushEntity,
      NotificationEntity,
      UserNotificationEntity,
      UserPermissionsEntity,
      PermissionEntity,
      MemberShopOrderTrackingNumberEntity,
      CountryEntity,
      FeatureCountryEntity,
      UserReferralEntity,
      LoyaltyActionEntity,
      ImageEntity,
      TextEntity,
      UserCourseLogsEntity,
      ResponseEntity,
      UserTourTrashEntity,
      ApiVersionsEntity,
      GameProfile,
    ]),
    CacheModule.registerAsync({
      useFactory: getCacheModuleConfig,
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      {
        name: NOTIFICATION_QUEUE_NAME,
        defaultJobOptions: {
          removeOnComplete: true,
        },
      },
      {
        name: 'cdm',
        defaultJobOptions: {
          removeOnComplete: true,
        },
      },
      {
        name: 'klaviyo-sync',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'ttb-fraud',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'ttb-notify-admin',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'member-shop-notify',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'ttb-threshold',
        defaultJobOptions: {
          removeOnComplete: true,
          removeOnFail: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'user-referral-csv',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'ecom-shipment',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'ecom-delivering',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'ecom-alert',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'member-shop-notify',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'ecom-shipment-overdue',
        defaultJobOptions: {
          removeOnComplete: false,
          removeOnFail: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'tourtrash',
        defaultJobOptions: {
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'klaviyo-track',
        defaultJobOptions: {
          removeOnComplete: false,
          attempts: 10,
          backoff: 30000,
        },
      },
      {
        name: 'upload-s3',
        defaultJobOptions: {
          removeOnComplete: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'country-syncing',
        defaultJobOptions: {
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'mail-otp',
        defaultJobOptions: {
          removeOnComplete: false,
          attempts: 3,
          backoff: 30000,
        },
      },
      {
        name: 'bull-dashboard-jobs',
        defaultJobOptions: {
          removeOnComplete: false,
        },
      }
    ),
    MailModule,
    KlaviyoModule,
  ],
  controllers: [],
  providers: [
    AuthService,
    CdmService,
    ClientService,
    OTPService,
    MrpService,
    EcomService,
    DeService,
    MfeService,
    TTBService,
    LoggingService,
    MiscService,
    SeventeentrackService,
    LaunchFeatureService,
    ArccosService,
    ContentCacheService,
    AccessCodeService,
    NotificationService,
    PlayService,
    PermissionService,
    TwilioService,
    UserReferralService,
    UserProfileService,
    AnnexService,
    LoyaltyService,
    ApiVersionsService,
  ],
  exports: [
    PlayService,
    AuthService,
    CdmService,
    ClientService,
    OTPService,
    MrpService,
    MailModule,
    EcomService,
    DeService,
    MfeService,
    LoggingService,
    MiscService,
    SeventeentrackService,
    LaunchFeatureService,
    ArccosService,
    TTBService,
    NotificationService,
    ContentCacheService,
    PermissionService,
    TwilioService,
    UserReferralService,
    UserProfileService,
    AnnexService,
    LoyaltyService,
    ApiVersionsService,
    TypeOrmModule.forFeature([
      UserEntity,
      UserBlackListEntity,
      UserNotifyAdminEntity,
      ClientEntity,
      BlacklistedTokenEntity,
      UserOtp,
      UserOnboardingStep,
      EcomNotificationEntity,
      LogEntity,
      HomeWidgetEntity,
      SubscriptionEventLog,
      SubscriptionEntity,
      ActivityLogEntity,
      LaunchFeatureEntity,
      FavoriteContentEntity,
      ViewedContentEntity,
      DismissedContentsEntity,
      ContentTagThumbnailEntity,
      ExternalToolEntity,
      ExtensionUserEntity,
      TTBFraudEntity,
      TTBFraudInfoEntity,
      TTBEntity,
      TTBPushEntity,
      TTBConfigThresholdEntity,
      TTBLogThresholdEntity,
      UserPermissionsEntity,
      MemberShopOrderTrackingNumberEntity,
      CountryEntity,
      FeatureCountryEntity,
      UserReferralEntity,
      LoyaltyActionEntity,
      ImageEntity,
      TextEntity,
      UserCourseLogsEntity,
      ResponseEntity,
      UserTourTrashEntity,
      ApiVersionsEntity,
      GameProfile,
    ]),
    CacheModule.registerAsync({
      useFactory: getCacheModuleConfig,
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      {
        name: NOTIFICATION_QUEUE_NAME,
        defaultJobOptions: {
          removeOnComplete: true,
        },
      },
      {
        name: 'cdm',
        defaultJobOptions: {
          removeOnComplete: true,
        },
      },
      {
        name: 'klaviyo-sync',
        defaultJobOptions: {
          removeOnComplete: true,
        },
      }
    ),
    KlaviyoModule,
  ],
})
export class SharedModule {
  constructor(@Inject(CACHE_MANAGER) cacheManager) {
    const client = cacheManager.store.getClient();
    client.on('error', (error) => {
      console.error(error);
    });
  }
}

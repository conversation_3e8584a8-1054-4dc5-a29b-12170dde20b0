import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from 'nestjs-config';
import twilio from 'twilio';
import { ERROR_CODES } from 'src/utils/errors';

export const CHANNEL_VERIFY = {
  SMS: 'sms',
};
@Injectable()
export class TwilioService {
  private readonly logger = new Logger(TwilioService.name);
  private verifyServiceId: any;
  constructor(private readonly config: ConfigService) {
    this.verifyServiceId = config.get('app.twilioVerifyServiceId');
  }
  initClient() {
    const accountSid = this.config.get('app.twilioAccountSID');
    const authToken = this.config.get('app.twilioToken');

    const clientTwilio = twilio(accountSid, authToken);
    return clientTwilio;
  }
  async createOtp(to: string) {
    const client = this.initClient();
    try {
      this.logger.log(`START CREATE OTP`);
      const verificationRequest = await client.verify.v2
        .services(this.verifyServiceId)
        .verifications.create({ to, channel: CHANNEL_VERIFY.SMS });
      console.log({ verificationRequest });
      return { success: true, data: verificationRequest };
    } catch (error) {
      console.log(error);
      return { success: false, internalErrorCode: ERROR_CODES.OTP_SEND_FAIL };
    }
  }

  async verifyOtp(to: string, token: string) {
    this.logger.log(`START VERIFY OTP`);
    const client = this.initClient();
    const verifyServiceId = this.config.get('app.twilioVerifyServiceId');
    try {
      const checkResult = await client.verify.v2
        .services(verifyServiceId)
        .verificationChecks.create({ to, code: token });
      console.log({ checkResult });
      if (checkResult.valid) {
        return { success: true };
      }
      return { success: false, internalErrorCode: ERROR_CODES.OTP_WRONG };
    } catch (error) {
      console.log(error);
      if (error.status === 400) {
        return { success: false, internalErrorCode: ERROR_CODES.OTP_INVALID_PHONE_NUMBER };
      }
      if (error.status === 404) {
        return { success: false, internalErrorCode: ERROR_CODES.OTP_NOT_FOUND };
      }
      return { success: false, internalErrorCode: ERROR_CODES.OTP_VERIFY_FAIL };
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { ConfigService } from 'nestjs-config';

export enum TRACKING_CARRIERS {
  FEDEX = 'FEDEX',
  UPS = 'UPS',
}

const CARRIER_EXT = [
  {
    key: '21051',
    _canTrack: '1',
    _country: '2105',
    _url: 'http://www.usps.com/',
    _name: 'United States Postal Service',
  },
];

@Injectable()
export class SeventeentrackService {
  endpoint = 'https://www.17track.net';
  apiEndpoint = 'https://api.17track.net';
  private readonly logger = new Logger(SeventeentrackService.name);
  constructor(private readonly config: ConfigService) {}

  getHeaders() {
    return { '17token': this.config.get('app.SeventeentrackToken') };
  }

  async getAllCarriers(): Promise<Array<any>> {
    const { data } = await axios.get(`${this.endpoint}/en/apicarrier`);
    return data;
  }

  async registerTrackingNumber(number: string, carrier: string, rejectError = false): Promise<string | null> {
    const matchCarrier = await this.getMatchCarrier(carrier);
    if (!matchCarrier) {
      return null;
    }
    const response = await axios.post(
      `${this.apiEndpoint}/track/v1/register`,
      [
        {
          number,
          carrier: matchCarrier.key,
          final_carrier: 0,
          auto_detection: false,
        },
      ],
      { headers: this.getHeaders() }
    );
    const rejected = response?.data?.data?.rejected;
    if (rejectError && rejected && rejected.length && rejected[0]?.error?.code === -18019908) {
      return null;
    }
    return response.status === 200 ? `${matchCarrier.key}` : null;
  }

  async getMatchCarrier(carrierName: string) {
    const normalizedCarrierName = carrierName.toUpperCase();
    const carriers = await this.getAllCarriers();
    let matchCarrier = carriers.find((item) => item._name.toUpperCase() === normalizedCarrierName);
    if (!matchCarrier) {
      matchCarrier = CARRIER_EXT.find((item) => item._name.toUpperCase() === normalizedCarrierName);
      if (!matchCarrier) {
        this.logger.error(new Error(`Can not find carrier with name: ${carrierName}`));
        return null;
      }
    }
    return matchCarrier;
  }

  async getTrackingNumberInfo(number: string, carrier: string, enableRegisterIfNeeded = true) {
    const response = await axios.post(
      `${this.apiEndpoint}/track/v1/gettrackinfo`,
      [
        {
          number,
        },
      ],
      { headers: this.getHeaders() }
    );
    const rejected = response?.data?.data?.rejected;
    const accepted = response?.data?.data?.accepted;
    if (rejected && rejected?.length && rejected[0]?.error?.code === -18019902 && enableRegisterIfNeeded) {
      await this.registerTrackingNumber(number, carrier);
      return this.getTrackingNumberInfo(number, carrier);
    }
    if (accepted && accepted?.length) {
      return accepted[0];
    }
    return null;
  }
  async getTrackingNumberInfoV2(number: string, version = 'v2.2') {
    const response = await axios.post(
      `${this.apiEndpoint}/track/${version}/gettrackinfo`,
      [
        {
          number,
        },
      ],
      { headers: this.getHeaders() }
    );
    const accepted = response?.data?.data?.accepted;
    if (accepted && accepted?.length) {
      return accepted[0];
    }
    return null;
  }

  isTrackingNumberDelivered(trackingNumberInfo: any) {
    return trackingNumberInfo?.track?.e === 40;
  }
}

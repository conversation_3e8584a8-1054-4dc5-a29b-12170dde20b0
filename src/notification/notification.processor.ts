import { Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirebaseMessagingService } from '@aginix/nestjs-firebase-admin';
import { Job } from 'bull';
import { messaging } from 'firebase-admin';
import { ConfigService } from 'nestjs-config';
import { Repository } from 'typeorm';
import { v4 } from 'uuid';
import { UserEntity } from 'src/auth/entities/user.entity';
import { UserNotificationEntity } from './entities/user-notification.entity';
import { NOTIFICATION_QUEUE_NAME } from './notification.constants';

type PushNotificationJob = Job<{
  userIds: string[];
  title: string;
  message: string;
  ctaLink: string;
  senderId: string;
  notificationId: string;
}>;
export enum NotificationProcessorQueueName {
  PUSH = 'push',
}

@Processor(NOTIFICATION_QUEUE_NAME)
export class NotificationProcessor {
  private readonly logger = new Logger(NotificationProcessor.name);
  constructor(
    private readonly config: ConfigService,
    private firebaseMessaging: FirebaseMessagingService,
    @InjectRepository(UserNotificationEntity) private readonly userNotiRepo: Repository<UserNotificationEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>
  ) {}
  @Process(NotificationProcessorQueueName.PUSH)
  async push(job: PushNotificationJob): Promise<any> {
    this.logger.log(`Admin push notification ${job.data.title}`);
    const userIds = job.data.userIds;
    for (const userId of userIds) {
      try {
        const userNotification = new UserNotificationEntity();
        userNotification.id = v4();
        userNotification.userId = userId;
        userNotification.isRead = false;
        userNotification.hide = false;
        userNotification.createdAt = new Date();
        userNotification.senderId = job.data.senderId;
        userNotification.notificationId = job.data.notificationId;
        this.userNotiRepo.save(userNotification);
        const user = await this.userRepo.findOne({ id: userId });
        if (user.fcmToken) {
          this.pushNotification(job, user.fcmToken).catch((e) => e);
        }
      } catch (error) {
        this.logger.error(error);
      }
    }
  }
  async pushNotification(job: PushNotificationJob, fcmToken: string) {
    try {
      const message: messaging.Message = {
        data: {
          deepLink: job.data.ctaLink,
        },
        token: fcmToken,
        notification: {
          title: job.data.title || 'TaylorMade',
          body: job.data.message,
        },
      };
      await this.firebaseMessaging.send(message).then((r) => r);
    } catch (e) {
      console.log(e);
      return null;
    }
  }
}

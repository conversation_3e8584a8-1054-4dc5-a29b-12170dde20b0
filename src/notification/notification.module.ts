import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SharedModule } from 'src/shared/shared.module';
import { NotificationEntity } from './entities/notification.entity';
import { UserNotificationEntity } from './entities/user-notification.entity';
import { NotificationController } from './notification.controller';
import { NotificationProcessor } from './notification.processor';
import { NotificationService } from './notification.service';

@Module({
  imports: [SharedModule, TypeOrmModule.forFeature([NotificationEntity, UserNotificationEntity])],
  controllers: [NotificationController],
  providers: [NotificationProcessor, NotificationService],
  exports: [NotificationService],
})
export class NotificationModule {}

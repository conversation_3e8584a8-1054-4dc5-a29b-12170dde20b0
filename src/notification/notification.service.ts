import { InjectQueue } from '@nestjs/bull';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Queue } from 'bull';
import { parseInt } from 'lodash';
import _ from 'lodash';
import { ConfigService } from 'nestjs-config';
import { Repository, getRepository } from 'typeorm';
import { v4 } from 'uuid';
import { UserEntity } from 'src/auth/entities/user.entity';
import { ERROR_CODES } from 'src/utils/errors';
import { PLANS } from 'src/utils/plans';
import { NotificationEntity } from './entities/notification.entity';
import { UserNotificationEntity } from './entities/user-notification.entity';
import { NOTIFICATION_QUEUE_NAME, USER_NOTIFICATION_LIMIT } from './notification.constants';
import { NotificationProcessorQueueName } from './notification.processor';
import { NotificationType } from './notification.type';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  constructor(
    private readonly config: ConfigService,
    @InjectRepository(NotificationEntity) private readonly notificationRepo: Repository<NotificationEntity>,
    @InjectRepository(UserNotificationEntity) private readonly userNotiRepo: Repository<UserNotificationEntity>,
    @InjectRepository(UserEntity) private readonly userRepo: Repository<UserEntity>,
    @InjectQueue(NOTIFICATION_QUEUE_NAME) private notificationQueue: Queue
  ) {
    this.config = config;
  }

  async getNotificationsMyTMPApp(userId: string, take: number, page: number) {
    try {
      const query = this.userNotiRepo.createQueryBuilder('un').where({ userId });
      const nPage = page > 0 ? parseInt(page.toString()) : 1;
      const nTake = take > 0 ? (take < 100 ? parseInt(take.toString()) : 100) : 1;
      const [notifications, total] = await query
        .leftJoinAndSelect('un.sender', 'sender')
        .leftJoinAndSelect('un.notification', 'notification')
        .orderBy('un.createdAt', 'DESC')
        .take(nTake)
        .skip((nPage - 1) * nTake)
        .getManyAndCount();

      return { total, take: nTake, page: nPage, notifications };
    } catch (e) {
      console.log(e);

      return null;
    }
  }

  async getTotalUnreadNotification(userId: string) {
    try {
      let total = 0;
      total = await this.userNotiRepo.count({ userId, isRead: false });
      return { total };
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async updateNotificationRead(userId: string, userNotificationId: string, payload: any) {
    try {
      if (payload) {
        const notification = await this.userNotiRepo.findOne({ id: userNotificationId, userId });
        if (!notification) {
          return new BadRequestException({
            internalErrorCode: ERROR_CODES.NOTIFICATION_NOT_FOUND,
            errorMessage: 'Notification is not found!',
          });
        }
        if (payload.isRead) {
          const updatedAt = new Date();
          const notificationUpdate = { ...payload, updatedAt };
          await this.userNotiRepo.update({ id: userNotificationId }, notificationUpdate);
          return { success: true };
        }
      }
      return { success: false, errorMessage: 'Payload is required' };
    } catch (e) {
      console.log(e);
      return null;
    }
  }

  async saveNotificationFromOtherService(userId: string, ctaLink: string, variables: any) {
    try {
      if (userId) {
        const userNotification = new UserNotificationEntity();
        userNotification.id = v4();
        userNotification.userId = userId;
        userNotification.isRead = false;
        userNotification.hide = false;
        userNotification.createdAt = new Date();
        // Can get more message from Default Notification_Message Table to localize add to variables
        const title = variables?.title || 'Message notification';
        const message = variables?.message || 'Title notification';
        // Check duplicate Notification
        let isNotificationExist = await this.notificationRepo.findOne({ name: title, message, ctaLink });
        // If not exist Notification => Create new notification
        if (!isNotificationExist) {
          isNotificationExist = await this.createNotification(variables, ctaLink);
        }
        userNotification.notificationId = isNotificationExist.id;
        return await this.userNotiRepo.save(userNotification);
      }
      return false;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async createNotification(variables: any, ctaLink: string) {
    try {
      const notification = new NotificationEntity();
      notification.id = v4();
      notification.name = variables?.title || 'Message notification';
      notification.message = variables?.message || 'Title notification';
      notification.variables = JSON.stringify(variables);
      notification.notificationType = NotificationType.SYSTEM_NOTIFICATION;
      if (variables?.service && variables.service === 'SWING_INDEX') {
        notification.notificationType = NotificationType.APP_COACH_NOTIFICATION;
      }
      notification.ctaLink = ctaLink;
      notification.createdAt = new Date();
      const savedNotification = await this.notificationRepo.save(notification);
      if (savedNotification?.id) {
        return savedNotification;
      }
      return null;
    } catch (error) {
      console.log(error);

      return null;
    }
  }

  async sendNotification(target: string[] = [], ctaLink: string, variables: any, sender: string): Promise<any> {
    try {
      // await this.fetchPermission();
      if (target.length) {
        const targetLevels = [];
        const targetEmails = [];
        target.map((emailOrLevel) => {
          const email = isEmail(emailOrLevel);
          if (email) {
            targetEmails.push(email);
          } else {
            const level = parseInt(emailOrLevel);
            const checkValid = [0, PLANS.level_one_annual.level, PLANS.level_two_annual.level].includes(level);
            if (checkValid) {
              targetLevels.push(level);
            } else {
              return {
                internalErrorCode: ERROR_CODES.SEND_NOTIFICATION_ERROR,
                errorMessage: `Can not send Notification to these user!`,
              };
            }
          }
        });

        const queryLevel = targetLevels.length > 0 ? `user.myTMSubscriptionLevel IN (:...levels)` : '';
        const queryEmail = targetEmails.length > 0 ? `user.email IN (:...emails)` : '';
        if (targetLevels.length > 0 || targetEmails.length > 0) {
          const condition = targetEmails.length > 0 && targetLevels.length > 0 ? 'OR' : '';
          const [targetUsers, count] = await this.userRepo
            .createQueryBuilder('user')
            .select(['user.id'])
            .where(`${queryEmail} ${condition} ${queryLevel}`, {
              emails: targetEmails,
              levels: targetLevels,
            })
            .limit(USER_NOTIFICATION_LIMIT)
            .getManyAndCount();

          // Can get more message from Default Notification_Message Table to localize add to variables
          const title = variables?.title || 'Message notification';
          const message = variables?.message || 'Title notification';
          let isNotificationExist = await this.notificationRepo.findOne({ name: title, message });
          // If not exist Notification => Create new notification
          if (!isNotificationExist) {
            isNotificationExist = await this.createNotification(variables, ctaLink);
          }

          const chunkTargetUsers = _.chunk(targetUsers, this.config.get('app.prefetchNotificationQueueLimits') || 100);
          for (const chunkUsers of chunkTargetUsers) {
            this.notificationQueue.add(
              NotificationProcessorQueueName.PUSH,
              {
                userIds: chunkUsers.map((user) => user.id),
                message: isNotificationExist.message,
                title: isNotificationExist.name,
                ctaLink: ctaLink,
                senderId: sender,
                notificationId: isNotificationExist.id,
              },
              { delay: 0 }
            );
          }

          return {
            success: true,
          };
        }
      }
      return {
        internalErrorCode: ERROR_CODES.SEND_NOTIFICATION_ERROR,
        errorMessage: `Target should not be empty!`,
      };
    } catch (error) {
      this.logger.error(error);
      return null;
    }
  }
}
function isEmail(email: string) {
  const rExp = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
  if (rExp.test(email)) {
    return email;
  }
  return false;
}

import { Body, Controller, Get, Param, Post, Put, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { BaseRequest } from 'src/types/core';
import { NotificationService } from './notification.service';

@Controller('notification')
export class NotificationController {
  constructor(private notificationService: NotificationService) {}

  @UseGuards(AuthGuard)
  @Get('user/notifications')
  async getNotifications(@Req() request: BaseRequest, @Query('take') take, @Query('page') page) {
    return this.notificationService.getNotificationsMyTMPApp(request.user.uid, take, page);
  }

  @UseGuards(AuthGuard)
  @Get('user/total-notification-unread')
  async getTotalUnreadByUser(@Req() request: BaseRequest) {
    return this.notificationService.getTotalUnreadNotification(request.user.uid);
  }

  @UseGuards(AuthGuard)
  @Put('user/update/:id')
  async updateNotificationRead(@Req() request: BaseRequest, @Body() body: any, @Param() params) {
    return this.notificationService.updateNotificationRead(request.user.uid, params.id, body);
  }
}

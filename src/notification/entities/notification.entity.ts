import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { NotificationType } from '../notification.type';

@Entity('Notification')
export class NotificationEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  ctaLink: string;

  @Column()
  name: string;

  @Column()
  message: string;

  @Column()
  notificationType: NotificationType;

  @Column()
  variables: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}

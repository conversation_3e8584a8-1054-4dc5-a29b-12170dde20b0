import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from 'src/auth/entities/user.entity';
import { NotificationEntity } from './notification.entity';

@Entity('UserNotification')
export class UserNotificationEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column()
  userId: string;

  @Column()
  notificationId: string;

  @Column()
  senderId: string;

  @Column()
  isRead: boolean;

  @ManyToOne(() => NotificationEntity)
  @JoinColumn({ name: 'notificationId', referencedColumnName: 'id' })
  notification: NotificationEntity;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'userId' })
  user: UserEntity;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'senderId' })
  sender: UserEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  @Column()
  hide: boolean;
}

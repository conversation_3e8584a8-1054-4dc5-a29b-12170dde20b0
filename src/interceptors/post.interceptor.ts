import { <PERSON><PERSON><PERSON>ler, ExecutionContext, HttpStatus, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class PostInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    if (request && request.method === 'POST') {
      context.switchToHttp().getResponse().status(HttpStatus.OK);
    }
    return next.handle();
  }
}

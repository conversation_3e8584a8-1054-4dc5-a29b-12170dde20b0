import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { ContextType, HttpArgumentsHost, RpcArgumentsHost, WsArgumentsHost } from '@nestjs/common/interfaces';
import { InjectSentry, SentryService } from '@ntegral/nestjs-sentry';
import { Scope } from '@sentry/hub';
import { Handlers } from '@sentry/node';
import axios from 'axios';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class SentryInterceptor implements NestInterceptor {
  constructor(@InjectSentry() private readonly client: SentryService) {
    axios.interceptors.response.use(
      (res) => res,
      (error) => {
        this.client.instance().setExtra('axios', error);
        this.client.instance().captureException(error);
        return Promise.reject(error);
      }
    );
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      tap(null, (exception) => {
        if (SentryInterceptor.shouldReport()) {
          this.client.instance().withScope((scope) => {
            switch (context.getType<ContextType>()) {
              case 'http':
                return this.captureHttpException(scope, context.switchToHttp(), exception);
              case 'rpc':
                return this.captureRpcException(scope, context.switchToRpc(), exception);
              case 'ws':
                return this.captureWsException(scope, context.switchToWs(), exception);
            }
          });
        }
      })
    );
  }

  private captureHttpException(scope: Scope, http: HttpArgumentsHost, exception: any): void {
    const data = Handlers.parseRequest({}, http.getRequest(), {});

    scope.setExtra('req', data.request);

    if (data.extra) {
      scope.setExtras(data.extra);
    }
    if (data.user) {
      scope.setUser(data.user);
    }

    this.client.instance().captureException(exception);
  }

  private captureRpcException(scope: Scope, rpc: RpcArgumentsHost, exception: any): void {
    scope.setExtra('rpc_data', rpc.getData());

    this.client.instance().captureException(exception);
  }

  private captureWsException(scope: Scope, ws: WsArgumentsHost, exception: any): void {
    scope.setExtra('ws_client', ws.getClient());
    scope.setExtra('ws_data', ws.getData());

    this.client.instance().captureException(exception);
  }

  static shouldReport() {
    return true;
  }
}
